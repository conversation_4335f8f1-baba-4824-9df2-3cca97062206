// Local type for targets with status
export type TargetWithStatus = {
	id: string;
	listId: string;
	name: string;
	tornId: string;
	status: string;
	profilePicture?: string;
	createdAt: string;
	updatedAt: string;
};

// Filter types
export type StatusFilter = "all" | "okay" | "hospitalized" | "error";
export type SortBy = "smart" | "name" | "status" | "added" | "tornId";
export type SortOrder = "asc" | "desc";

// Torn chain response type (partial)
export type TornChain = {
	current: number;
	max: number;
	timeout: number;
	cooldown: number;
	end: number;
};

// Chain info type
export type ChainInfo = {
	chain: TornChain;
};

// War info type
export type WarInfo = {
	wars?: {
		ranked?: {
			factions: Array<{
				id: number;
				name: string;
			}>;
		};
	};
};

// List type
export type TargetList = {
	id: string;
	name: string;
	type?: string;
	isExternal?: boolean;
	userId?: string | null;
};

// Cooldown data type
export type CooldownData = {
	remaining?: number;
};

// Permissions type (simplified)
export type Permissions = Record<string, boolean>;

// Status info type for UI styling
export type StatusInfo = {
	variant: "success" | "destructive" | "secondary" | "outline";
	icon: React.ComponentType<{ className?: string }>;
	color: string;
	bgColor: string;
	borderColor: string;
};

// Chain status info type
export type ChainStatusInfo = {
	color: string;
	bgColor: string;
	borderColor: string;
};

// Props for various components
export type AddTargetDialogProps = {
	selectedList: string;
	onSuccess: () => void;
};

export type ChainStatusCardProps = {
	chainInfo: ChainInfo | null | undefined;
	chainRemaining: number | null;
};

export type TargetCardProps = {
	target: TargetWithStatus;
	currentStatus: string;
	selectedList: string;
	onRemoveTarget: (tornId: string) => Promise<void>;
	isRemoving: boolean;
};

export type TargetFilterProps = {
	selectedList: string;
	setSelectedList: (list: string) => void;
	searchTerm: string;
	setSearchTerm: (term: string) => void;
	statusFilter: StatusFilter;
	setStatusFilter: (filter: StatusFilter) => void;
	viewMode: "grid" | "list";
	setViewMode: (mode: "grid" | "list") => void;
	lists: TargetList[] | undefined;
	listsLoading: boolean;
	warInfo: WarInfo | undefined;
	enemyFactionId: string | null;
	localCooldown: number;
	onRefresh: () => void;
	isRefreshing: boolean;
	targetStats: TargetStats;
};

export type TargetStats = {
	all: number;
	okay: number;
	hospitalized: number;
	error: number;
};
