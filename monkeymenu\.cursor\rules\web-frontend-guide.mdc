---
description: 
globs: apps/web/**
alwaysApply: false
---
# Web Frontend Development Guide

The web application is a React frontend located in [apps/web](mdc:apps/web) built with modern React patterns and TanStack ecosystem.

## Entry Point

The main application entry point is [apps/web/src/main.tsx](mdc:apps/web/src/main.tsx), which sets up:
- React root rendering
- TanStack Router configuration
- Global providers (Query, Auth)

## Key Directories

### Components
- [apps/web/src/components/ui](mdc:apps/web/src/components/ui) - Reusable UI components (Radix-based)
- [apps/web/src/components/navbar](mdc:apps/web/src/components/navbar) - Navigation components
- [apps/web/src/components/permissions](mdc:apps/web/src/components/permissions) - Permission-based components
- [apps/web/src/components/demo](mdc:apps/web/src/components/demo) - Demo/example components

### Routing
- [apps/web/src/routes](mdc:apps/web/src/routes) - File-based routing structure
- [apps/web/src/routes/(auth)](mdc:apps/web/src/routes/(auth)) - Authentication routes
- [apps/web/src/routes/(faction)](mdc:apps/web/src/routes/(faction)) - Faction-related routes
- [apps/web/src/routes/(protected)](mdc:apps/web/src/routes/(protected)) - Protected routes
- [apps/web/src/routeTree.gen.ts](mdc:apps/web/src/routeTree.gen.ts) - Generated route tree (auto-generated)

### Utilities
- [apps/web/src/lib](mdc:apps/web/src/lib) - Shared utilities and configurations
- [apps/web/src/hooks](mdc:apps/web/src/hooks) - Custom React hooks

### Styling
- [apps/web/src/styles](mdc:apps/web/src/styles) - Styling configuration
- [apps/web/src/styles/themes](mdc:apps/web/src/styles/themes) - Theme definitions
- [apps/web/src/index.css](mdc:apps/web/src/index.css) - Global styles and Tailwind imports

## Development Commands

```bash
# Start development server
pnpm dev:web

# Build for production
turbo -F web build

# Type checking
pnpm check-types

# Linting and formatting
pnpm check
```

## Key Technologies

### Routing
- **TanStack Router**: File-based routing with type safety
- Route groups for organization: `(auth)`, `(faction)`, `(protected)`
- Auto-generated route tree for type safety

### State Management
- **TanStack Query**: Server state management and caching
- **TanStack Store**: Client state management
- **TanStack Form**: Form state and validation

### UI Components
- **Radix UI**: Unstyled, accessible component primitives
- **Tailwind CSS**: Utility-first styling
- **Lucide React**: Icon library
- **class-variance-authority**: Component variant management

### Authentication
- **better-auth**: Client-side authentication integration
- Type-safe auth hooks and components

## Development Patterns

- Use file-based routing with TanStack Router
- Organize components by feature/domain
- Use Radix UI for accessible base components
- Implement proper loading and error states
- Use TanStack Query for server state
- Follow Tailwind CSS utility patterns
- Implement proper TypeScript types throughout
