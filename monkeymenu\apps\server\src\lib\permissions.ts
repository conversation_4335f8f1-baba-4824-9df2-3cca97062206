import {
	FACTION_ROLES,
	PERMISSIONS,
	TORN_POSITION_MAPPING,
	type UserPermissionContext,
} from "@monkeymenu/shared";
import { and, eq } from "drizzle-orm";
import type { DrizzleD1Database } from "drizzle-orm/d1";
import {
	factionRole,
	permission,
	rolePermission,
	userRole,
} from "../db/schema/permissions";

// Function to get role from Torn faction position
export function getRoleFromTornPosition(
	tornPosition: string | null | undefined,
): keyof typeof FACTION_ROLES {
	if (!tornPosition) return "RECRUIT";

	const roleKey = TORN_POSITION_MAPPING[
		tornPosition as keyof typeof TORN_POSITION_MAPPING
	] as keyof typeof FACTION_ROLES;
	return roleKey || "RECRUIT"; // Default to recruit if position not found
}

// Helper function to get database role name from Torn position
export function getDbRoleFromTornPosition(
	tornPosition: string | null | undefined,
): string {
	const roleKey = getRoleFromTornPosition(tornPosition);
	return FACTION_ROLES[roleKey].name;
}

/**
 * Get a user's permission context including their role and permissions
 */
export async function getUserPermissionContext(
	db: DrizzleD1Database,
	userId: string,
): Promise<UserPermissionContext> {
	const result = await db
		.select({
			roleId: userRole.roleId,
			roleName: factionRole.name,
			roleLevel: factionRole.hierarchyLevel,
			permissionName: permission.name,
		})
		.from(userRole)
		.innerJoin(factionRole, eq(userRole.roleId, factionRole.id))
		.leftJoin(rolePermission, eq(factionRole.id, rolePermission.roleId))
		.leftJoin(permission, eq(rolePermission.permissionId, permission.id))
		.where(and(eq(userRole.userId, userId), eq(userRole.isActive, true)))
		.all();

	if (result.length === 0) {
		// User has no role assigned
		return {
			userId,
			roleId: null,
			roleName: null,
			roleLevel: 0,
			permissions: [],
		};
	}

	// Check for multiple active roles (data integrity issue)
	const uniqueRoles = new Set(result.map((r) => r.roleId));
	if (uniqueRoles.size > 1) {
		console.error(
			`Data integrity error: User ${userId} has multiple active roles: ${Array.from(uniqueRoles).join(", ")}`,
		);
		throw new Error(
			"Multiple active roles detected for user. Please contact an administrator.",
		);
	}

	// Extract role info from first result (all results should have same role info)
	const firstResult = result[0];
	const permissions = result
		.map((r) => r.permissionName)
		.filter((perm): perm is string => perm !== null);

	return {
		userId,
		roleId: firstResult.roleId,
		roleName: firstResult.roleName,
		roleLevel: firstResult.roleLevel,
		permissions: [...new Set(permissions)], // Remove duplicates
	};
}

/**
 * Check if a user can perform an action on a resource they own vs any resource
 */
export function canPerformAction(
	userPermissions: UserPermissionContext,
	ownPermission: string,
	anyPermission: string,
	isOwner: boolean,
): boolean {
	if (userPermissions.permissions.includes(anyPermission)) {
		return true;
	}
	if (isOwner && userPermissions.permissions.includes(ownPermission)) {
		return true;
	}
	return false;
}

/**
 * Default permission assignments for each role
 * This can be used for seeding the database or as a reference
 */
export const DEFAULT_ROLE_PERMISSIONS = {
	[FACTION_ROLES.SYSTEM_ADMIN.name]: [
		// All permissions - System Admin has complete access
		...Object.values(PERMISSIONS).map((p) => p.name),
	],
	[FACTION_ROLES.LEADER.name]: [
		// All permissions except Discord management - Leader has complete admin access
		PERMISSIONS.GUIDES_VIEW.name,
		PERMISSIONS.GUIDES_MANAGE.name,
		PERMISSIONS.DASHBOARD_VIEW.name,
		PERMISSIONS.ANNOUNCEMENTS_VIEW.name,
		PERMISSIONS.ANNOUNCEMENTS_MANAGE.name,
		PERMISSIONS.ADMIN_VIEW.name,
		PERMISSIONS.ADMIN_SUSPEND_USERS.name,
		PERMISSIONS.ADMIN_RECHECK_API_KEYS.name,
		PERMISSIONS.ADMIN_DELETE_USER.name,
		// Banking permissions for Leaders
		PERMISSIONS.BANKING_VIEW.name,
		PERMISSIONS.BANKING_REQUEST.name,
		PERMISSIONS.BANKING_MANAGE_REQUESTS.name,
		// Target Finder Permissions for Leaders
		PERMISSIONS.TARGET_FINDER_VIEW.name,
		PERMISSIONS.TARGET_FINDER_MANAGE_SHARED_LISTS.name,
	],
	[FACTION_ROLES.CO_LEADER.name]: [
		// Most permissions except highest admin functions
		PERMISSIONS.GUIDES_VIEW.name,
		PERMISSIONS.GUIDES_MANAGE.name,
		PERMISSIONS.DASHBOARD_VIEW.name,
		PERMISSIONS.ANNOUNCEMENTS_VIEW.name,
		PERMISSIONS.ANNOUNCEMENTS_MANAGE.name,
		PERMISSIONS.ADMIN_VIEW.name,
		PERMISSIONS.ADMIN_SUSPEND_USERS.name,
		PERMISSIONS.ADMIN_RECHECK_API_KEYS.name,
		PERMISSIONS.ADMIN_DELETE_USER.name,
		// Banking permissions for Co-Leaders
		PERMISSIONS.BANKING_VIEW.name,
		PERMISSIONS.BANKING_REQUEST.name,
		PERMISSIONS.BANKING_MANAGE_REQUESTS.name,
		// Target Finder Permissions for Co-Leaders
		PERMISSIONS.TARGET_FINDER_VIEW.name,
		PERMISSIONS.TARGET_FINDER_MANAGE_SHARED_LISTS.name,
	],
	[FACTION_ROLES.MONKEY_MENTOR.name]: [
		// Senior admin role with full guide management and admin permissions
		PERMISSIONS.GUIDES_VIEW.name,
		PERMISSIONS.GUIDES_MANAGE.name,
		PERMISSIONS.DASHBOARD_VIEW.name,
		PERMISSIONS.ANNOUNCEMENTS_VIEW.name,
		PERMISSIONS.ANNOUNCEMENTS_MANAGE.name,
		PERMISSIONS.ADMIN_VIEW.name,
		PERMISSIONS.ADMIN_SUSPEND_USERS.name,
		PERMISSIONS.ADMIN_RECHECK_API_KEYS.name,
		PERMISSIONS.ADMIN_DELETE_USER.name,
		// Banking permissions for Monkey Mentors
		PERMISSIONS.BANKING_VIEW.name,
		PERMISSIONS.BANKING_REQUEST.name,
		PERMISSIONS.BANKING_MANAGE_REQUESTS.name,
		// Target Finder Permissions for Monkey Mentors
		PERMISSIONS.TARGET_FINDER_VIEW.name,
		PERMISSIONS.TARGET_FINDER_MANAGE_SHARED_LISTS.name,
	],
	[FACTION_ROLES.GORILLA.name]: [
		// Can view guides and announcements
		PERMISSIONS.GUIDES_VIEW.name,
		PERMISSIONS.DASHBOARD_VIEW.name,
		PERMISSIONS.ANNOUNCEMENTS_VIEW.name,
		// Banking permissions for Gorillas
		PERMISSIONS.BANKING_VIEW.name,
		PERMISSIONS.BANKING_REQUEST.name,
		PERMISSIONS.BANKING_MANAGE_REQUESTS.name,
		// Target Finder Permissions for Gorillas
		PERMISSIONS.TARGET_FINDER_VIEW.name,
	],
	[FACTION_ROLES.PRIMATE_LIAISON.name]: [
		// Moderate content, view sensitive data
		PERMISSIONS.GUIDES_VIEW.name,
		PERMISSIONS.DASHBOARD_VIEW.name,
		PERMISSIONS.ANNOUNCEMENTS_VIEW.name,
		// Banking permissions for Primate Liaisons
		PERMISSIONS.BANKING_VIEW.name,
		PERMISSIONS.BANKING_REQUEST.name,
		PERMISSIONS.BANKING_MANAGE_REQUESTS.name,
		// Target Finder Permissions for Primate Liaisons
		PERMISSIONS.TARGET_FINDER_VIEW.name,
	],
	[FACTION_ROLES.BABOON.name]: [
		// Standard member permissions
		PERMISSIONS.GUIDES_VIEW.name,
		PERMISSIONS.DASHBOARD_VIEW.name,
		PERMISSIONS.ANNOUNCEMENTS_VIEW.name,
		// Banking permissions for Baboons
		PERMISSIONS.BANKING_VIEW.name,
		PERMISSIONS.BANKING_REQUEST.name,
		// Target Finder Permissions for Baboons
		PERMISSIONS.TARGET_FINDER_VIEW.name,
	],
	[FACTION_ROLES.ORANGUTAN.name]: [
		// Basic viewing permissions
		PERMISSIONS.GUIDES_VIEW.name,
		PERMISSIONS.DASHBOARD_VIEW.name,
		PERMISSIONS.ANNOUNCEMENTS_VIEW.name,
		// Banking permissions for Orangutans
		PERMISSIONS.BANKING_VIEW.name,
		PERMISSIONS.BANKING_REQUEST.name,
		// Target Finder Permissions for Orangutans
		PERMISSIONS.TARGET_FINDER_VIEW.name,
	],
	[FACTION_ROLES.CHIMPANZEE.name]: [
		// Basic viewing permissions
		PERMISSIONS.GUIDES_VIEW.name,
		PERMISSIONS.DASHBOARD_VIEW.name,
		PERMISSIONS.ANNOUNCEMENTS_VIEW.name,
		// Banking permissions for Chimpanzees
		PERMISSIONS.BANKING_VIEW.name,
		PERMISSIONS.BANKING_REQUEST.name,
		// Target Finder Permissions for Chimpanzees
		PERMISSIONS.TARGET_FINDER_VIEW.name,
	],
	[FACTION_ROLES.RECRUIT.name]: [
		// Very limited permissions
		PERMISSIONS.GUIDES_VIEW.name,
		PERMISSIONS.DASHBOARD_VIEW.name,
		PERMISSIONS.ANNOUNCEMENTS_VIEW.name,
		// Banking permissions for Recruits
		PERMISSIONS.BANKING_VIEW.name,
		PERMISSIONS.BANKING_REQUEST.name,
		// Target Finder Permissions for Recruits (view only)
		PERMISSIONS.TARGET_FINDER_VIEW.name,
	],
} as const;

/**
 * Check if a Torn user ID should be assigned System Administrator role
 * Based on SYSTEM_ADMIN_TORN_IDS environment variable (comma-separated list)
 */
export function isSystemAdminTornId(
	tornUserId: string | number | null | undefined,
	env?: { SYSTEM_ADMIN_TORN_IDS?: string },
): boolean {
	if (!tornUserId || !env?.SYSTEM_ADMIN_TORN_IDS) {
		return false;
	}

	const adminIds = env.SYSTEM_ADMIN_TORN_IDS.split(",")
		.map((id) => id.trim())
		.filter((id) => id.length > 0);

	return adminIds.includes(String(tornUserId));
}

/**
 * Get the appropriate role for a user based on their Torn faction position and admin status
 */
export function getUserRoleFromTornData(
	tornPosition: string | null | undefined,
	tornUserId: string | number | null | undefined,
	env?: { SYSTEM_ADMIN_TORN_IDS?: string },
): keyof typeof FACTION_ROLES {
	// Check if user should be System Admin first
	if (isSystemAdminTornId(tornUserId, env)) {
		return "SYSTEM_ADMIN";
	}

	// Otherwise, use normal faction position mapping
	return getRoleFromTornPosition(tornPosition);
}

/**
 * Helper function to get database role name with System Admin check
 */
export function getDbRoleFromTornData(
	tornPosition: string | null | undefined,
	tornUserId: string | number | null | undefined,
	env?: { SYSTEM_ADMIN_TORN_IDS?: string },
): string {
	const roleKey = getUserRoleFromTornData(tornPosition, tornUserId, env);
	return FACTION_ROLES[roleKey].name;
}
