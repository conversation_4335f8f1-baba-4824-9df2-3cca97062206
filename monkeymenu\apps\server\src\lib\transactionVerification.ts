import { and, eq, gt, isNotNull, lt } from "drizzle-orm";
import type { DBInstance } from "../db/index";
import { user } from "../db/schema/auth";
import { withdrawalRequest } from "../db/schema/banking";
import { tornUser } from "../db/schema/torn";
import { decrypt } from "./crypto";
import { DiscordNotificationService } from "./discord-notification-service";
import { createTornAPIWithSuspension } from "./tornApi";
import type { AppBindings } from "./types";
import { WebSocketService } from "./websocket-service";

// Constants for transaction verification
const VERIFICATION_WINDOW_MINUTES = 60; // Look for transactions up to 60 minutes old
const SKIP_RECENT_MINUTES = 2; // Skip transactions from the last 2 minutes to avoid race conditions
const MAX_TRANSACTION_AGE_SECONDS = VERIFICATION_WINDOW_MINUTES * 60;
const MIN_TRANSACTION_AGE_SECONDS = SKIP_RECENT_MINUTES * 60;

interface WithdrawalToVerify {
	id: string;
	amount: number;
	requestedById: string;
	createdAt: Date;
	requesterApiKey: string;
	requesterTornId: string;
	requesterName: string;
}

/**
 * Main function to run transaction verification for all pending withdrawals
 */
export async function runTransactionVerification(
	db: DBInstance,
	env: AppBindings["Bindings"],
): Promise<{
	total: number;
	verified: number;
	failed: number;
	errors: string[];
}> {
	console.log("[TransactionVerification] Starting transaction verification...");

	const results = {
		total: 0,
		verified: 0,
		failed: 0,
		errors: [] as string[],
	};

	try {
		// Get all ACCEPTED withdrawals that need verification
		const pendingWithdrawals = await getPendingWithdrawals(db);
		results.total = pendingWithdrawals.length;

		console.log(
			`[TransactionVerification] Found ${pendingWithdrawals.length} withdrawals to verify`,
		);

		// Process each withdrawal
		for (const withdrawal of pendingWithdrawals) {
			try {
				const verificationResult = await verifyWithdrawalTransaction(
					db,
					env,
					withdrawal,
				);

				if (verificationResult.success) {
					results.verified++;
					console.log(
						`[TransactionVerification] Successfully verified withdrawal ${withdrawal.id}`,
					);
				} else {
					results.failed++;
					console.log(
						`[TransactionVerification] Failed to verify withdrawal ${withdrawal.id}: ${verificationResult.reason}`,
					);
				}

				// Add small delay to avoid API rate limits
				await new Promise((resolve) => setTimeout(resolve, 1000));
			} catch (error) {
				results.failed++;
				const errorMessage = `Error processing withdrawal ${withdrawal.id}: ${
					error instanceof Error ? error.message : "Unknown error"
				}`;
				results.errors.push(errorMessage);
				console.error(`[TransactionVerification] ${errorMessage}`);
			}
		}

		console.log(
			`[TransactionVerification] Completed: ${results.verified} verified, ${results.failed} failed out of ${results.total} total`,
		);
	} catch (error) {
		const errorMessage = `Fatal error in transaction verification: ${
			error instanceof Error ? error.message : "Unknown error"
		}`;
		results.errors.push(errorMessage);
		console.error(`[TransactionVerification] ${errorMessage}`);
	}

	return results;
}

/**
 * Get all ACCEPTED withdrawals that need verification
 */
async function getPendingWithdrawals(
	db: DBInstance,
): Promise<WithdrawalToVerify[]> {
	const withdrawals = await db
		.select({
			id: withdrawalRequest.id,
			amount: withdrawalRequest.amount,
			requestedById: withdrawalRequest.requestedById,
			createdAt: withdrawalRequest.createdAt,
			requesterApiKey: tornUser.tornApiKey,
			requesterTornId: tornUser.tornUserId,
			requesterName: user.name,
		})
		.from(withdrawalRequest)
		.innerJoin(user, eq(withdrawalRequest.requestedById, user.id))
		.innerJoin(tornUser, eq(withdrawalRequest.requestedById, tornUser.id))
		.where(
			and(
				eq(withdrawalRequest.status, "ACCEPTED"),
				isNotNull(tornUser.tornApiKey),
				isNotNull(tornUser.tornUserId),
				// Only check withdrawals older than SKIP_RECENT_MINUTES
				lt(
					withdrawalRequest.createdAt,
					new Date(Date.now() - MIN_TRANSACTION_AGE_SECONDS * 1000),
				),
				// Only check withdrawals up to VERIFICATION_WINDOW_MINUTES old
				gt(
					withdrawalRequest.createdAt,
					new Date(Date.now() - MAX_TRANSACTION_AGE_SECONDS * 1000),
				),
			),
		);

	return withdrawals.map((w) => ({
		id: w.id,
		amount: w.amount,
		requestedById: w.requestedById,
		createdAt: w.createdAt,
		requesterApiKey: w.requesterApiKey || "",
		requesterTornId: w.requesterTornId || "",
		requesterName: w.requesterName || "Unknown User",
	}));
}

/**
 * Verify a single withdrawal transaction against Torn API
 */
async function verifyWithdrawalTransaction(
	db: DBInstance,
	env: AppBindings["Bindings"],
	withdrawal: WithdrawalToVerify,
): Promise<{ success: boolean; reason: string }> {
	try {
		// Decrypt the API key
		const apiKey = decrypt(withdrawal.requesterApiKey);

		// Create Torn API client
		const tornApi = createTornAPIWithSuspension(
			apiKey,
			db,
			withdrawal.requestedById,
			env,
		);

		// Fetch recent faction transactions
		const transactions = await tornApi.getFactionTransactions();

		// Get list of already used transaction IDs to avoid duplicates
		const usedTransactionIds = await getUsedTransactionIds(db);

		// Look for matching transaction
		const matchingTransaction = findMatchingTransaction(
			transactions,
			withdrawal,
			usedTransactionIds,
		);

		if (matchingTransaction) {
			// Update withdrawal as completed
			await markWithdrawalCompleted(db, env, withdrawal, matchingTransaction);
			return { success: true, reason: "Transaction verified and completed" };
		}

		// Mark as cancelled if no match found
		await markWithdrawalCancelled(db, env, withdrawal);
		return {
			success: false,
			reason: "No matching transaction found - marked as cancelled",
		};
	} catch (error) {
		const errorMessage =
			error instanceof Error ? error.message : "Unknown error";
		console.error(
			`[TransactionVerification] Error verifying withdrawal ${withdrawal.id}: ${errorMessage}`,
		);
		return { success: false, reason: errorMessage };
	}
}

/**
 * Get all transaction IDs that have already been used
 */
async function getUsedTransactionIds(db: DBInstance): Promise<Set<string>> {
	const existingTransactions = await db
		.select({ transactionId: withdrawalRequest.transactionId })
		.from(withdrawalRequest)
		.where(
			and(
				eq(withdrawalRequest.status, "COMPLETED"),
				isNotNull(withdrawalRequest.transactionId),
			),
		);

	return new Set(
		existingTransactions
			.map((w) => w.transactionId)
			.filter((id): id is string => id !== null),
	);
}

/**
 * Find a matching transaction in the faction transaction log
 */
function findMatchingTransaction(
	transactions: Record<string, { news: string; timestamp: number }>,
	withdrawal: WithdrawalToVerify,
	usedTransactionIds: Set<string>,
): { id: string; transaction: { news: string; timestamp: number } } | null {
	for (const [id, transaction] of Object.entries(transactions)) {
		// Skip if transaction ID already used
		if (usedTransactionIds.has(id)) continue;

		// Parse the transaction news
		const amountRegex = /\$([0-9,]+)/;
		const userIdRegex = /XID=(\d+)/;

		const amountMatch = amountRegex.exec(transaction.news);
		const userIdMatch = userIdRegex.exec(transaction.news);

		if (!amountMatch?.[1] || !userIdMatch?.[1]) continue;

		const amount = Number.parseInt(amountMatch[1].replace(/,/g, ""), 10);
		const userId = userIdMatch[1];

		// Check if this transaction matches our withdrawal
		if (amount === withdrawal.amount && userId === withdrawal.requesterTornId) {
			return { id, transaction };
		}
	}

	return null;
}

/**
 * Mark withdrawal as completed and broadcast update
 */
async function markWithdrawalCompleted(
	db: DBInstance,
	env: AppBindings["Bindings"],
	withdrawal: WithdrawalToVerify,
	matchingTransaction: {
		id: string;
		transaction: { news: string; timestamp: number };
	},
): Promise<void> {
	// Update withdrawal status
	const updatedWithdrawal = await db
		.update(withdrawalRequest)
		.set({
			status: "COMPLETED",
			processedAt: new Date(matchingTransaction.transaction.timestamp * 1000),
			updatedAt: new Date(),
			transactionId: matchingTransaction.id,
		})
		.where(eq(withdrawalRequest.id, withdrawal.id))
		.returning()
		.get();

	// Broadcast WebSocket update
	try {
		const wsService = new WebSocketService();
		await wsService.broadcastWithdrawalUpdated({
			id: withdrawal.id,
			amount: withdrawal.amount,
			status: "COMPLETED",
			requestedByName: withdrawal.requesterName,
		});
	} catch (error) {
		console.error(
			`[TransactionVerification] Failed to broadcast completion for ${withdrawal.id}:`,
			error,
		);
	}

	// Update Discord embed to show completion (admins need to see transfer was successful)
	if (updatedWithdrawal?.discordMessageId) {
		try {
			const discordService = new DiscordNotificationService(env);
			const discordResult = await discordService.updateWithdrawalEmbed({
				discordMessageId: updatedWithdrawal.discordMessageId,
				newStatus: "COMPLETED",
				requestId: withdrawal.id,
				amount: withdrawal.amount,
				processedBy: "System (Auto-verified)",
			});

			if (!discordResult.success) {
				console.warn(
					`[TransactionVerification] Failed to update Discord embed for completed withdrawal ${withdrawal.id}: ${discordResult.error}`,
				);
			} else {
				console.log(
					`[TransactionVerification] Successfully updated Discord embed for completed withdrawal ${withdrawal.id}`,
				);
			}
		} catch (error) {
			console.error(
				`[TransactionVerification] Failed to update Discord embed for ${withdrawal.id}:`,
				error,
			);
		}
	}

	// Note: No DM notification for COMPLETED status to avoid spam
	// Users already know money is coming from the ACCEPTED notification

	console.log(
		`[TransactionVerification] Marked withdrawal ${withdrawal.id} as COMPLETED with transaction ${matchingTransaction.id}`,
	);
}

/**
 * Mark withdrawal as cancelled and broadcast update
 */
async function markWithdrawalCancelled(
	db: DBInstance,
	env: AppBindings["Bindings"],
	withdrawal: WithdrawalToVerify,
): Promise<void> {
	// Update withdrawal status
	const updatedWithdrawal = await db
		.update(withdrawalRequest)
		.set({
			status: "CANCELLED",
			updatedAt: new Date(),
		})
		.where(eq(withdrawalRequest.id, withdrawal.id))
		.returning()
		.get();

	// Broadcast WebSocket update
	try {
		const wsService = new WebSocketService();
		await wsService.broadcastWithdrawalUpdated({
			id: withdrawal.id,
			amount: withdrawal.amount,
			status: "CANCELLED",
			requestedByName: withdrawal.requesterName,
		});
	} catch (error) {
		console.error(
			`[TransactionVerification] Failed to broadcast cancellation for ${withdrawal.id}:`,
			error,
		);
	}

	// Update Discord embed to show cancellation (admins need to see when verification fails)
	if (updatedWithdrawal?.discordMessageId) {
		try {
			const discordService = new DiscordNotificationService(env);
			const discordResult = await discordService.updateWithdrawalEmbed({
				discordMessageId: updatedWithdrawal.discordMessageId,
				newStatus: "CANCELLED",
				requestId: withdrawal.id,
				amount: withdrawal.amount,
				processedBy: "System (Auto-cancelled)",
			});

			if (!discordResult.success) {
				console.warn(
					`[TransactionVerification] Failed to update Discord embed for cancelled withdrawal ${withdrawal.id}: ${discordResult.error}`,
				);
			} else {
				console.log(
					`[TransactionVerification] Successfully updated Discord embed for cancelled withdrawal ${withdrawal.id}`,
				);
			}
		} catch (error) {
			console.error(
				`[TransactionVerification] Failed to update Discord embed for ${withdrawal.id}:`,
				error,
			);
		}
	}

	// Send DM notification
	try {
		const discordService = new DiscordNotificationService(env);
		const dmResult = await discordService.sendWithdrawalDMNotification({
			userId: withdrawal.requestedById,
			withdrawalId: withdrawal.id,
			amount: withdrawal.amount,
			status: "CANCELLED",
			userName: withdrawal.requesterName,
		});

		if (!dmResult.success) {
			console.warn(
				`[TransactionVerification] Failed to send DM notification for cancelled withdrawal ${withdrawal.id}: ${dmResult.error}`,
			);
		} else {
			console.log(
				`[TransactionVerification] Successfully sent DM notification for cancelled withdrawal ${withdrawal.id}`,
			);
		}
	} catch (error) {
		console.error(
			`[TransactionVerification] Failed to send DM notification for ${withdrawal.id}:`,
			error,
		);
	}

	console.log(
		`[TransactionVerification] Marked withdrawal ${withdrawal.id} as CANCELLED`,
	);
}
