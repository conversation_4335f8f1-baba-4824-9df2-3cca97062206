import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { trpc } from "@/lib/trpc-client";
import { EXTERNAL_URLS } from "@monkeymenu/shared";
import { useMutation } from "@tanstack/react-query";
import { AlertCircle, ExternalLink, RefreshCw } from "lucide-react";
import { toast } from "sonner";

interface SuspensionAlertProps {
	reason?: string | null;
	suspendedAt?: Date | null;
	suspensionType?: "admin" | "api_error" | null;
	onAccessRestored?: () => void;
}

export function SuspensionAlert({
	reason,
	suspendedAt,
	suspensionType,
	onAccessRestored,
}: SuspensionAlertProps) {
	const restoreAccessMutation = useMutation(
		trpc.user.restoreUser.mutationOptions({
			onSuccess: (result) => {
				if (result.success) {
					toast.success(result.message);
					onAccessRestored?.();
				} else {
					toast.error(result.message);
				}
			},
			onError: (error) => {
				toast.error(`Failed to restore access: ${error.message}`);
			},
		}),
	);

	const isAdminSuspension = suspensionType === "admin";
	const canTryRestore = suspensionType === "api_error";

	return (
		<Alert variant="destructive" className="mb-4">
			<AlertCircle className="h-4 w-4" />
			<AlertTitle>Access Suspended</AlertTitle>
			<AlertDescription className="mt-2 space-y-3">
				<div>
					<p className="font-medium">
						Your access to faction features has been{" "}
						{isAdminSuspension
							? "suspended by an administrator"
							: "temporarily suspended"}
						.
					</p>
					{reason && (
						<p className="mt-1 text-muted-foreground text-sm">
							Reason: {reason}
						</p>
					)}
					{suspendedAt && (
						<p className="text-muted-foreground text-sm">
							Suspended at: {suspendedAt.toLocaleString()}
						</p>
					)}
				</div>

				{isAdminSuspension ? (
					<div className="space-y-2">
						<p className="text-sm">
							This suspension was applied by an administrator for policy
							violations or other administrative reasons.
						</p>
						<p className="text-muted-foreground text-sm">
							Please contact an administrator if you believe this suspension was
							applied in error or if you would like to discuss the suspension.
						</p>
					</div>
				) : (
					<div className="space-y-2">
						<p className="text-sm">
							This suspension was automatically applied due to an issue with
							your Torn API key. Common causes include:
						</p>
						<ul className="list-inside list-disc space-y-1 text-muted-foreground text-sm">
							<li>API key has been paused by you in Torn</li>
							<li>Account inactivity (7+ days)</li>
							<li>API key permissions changed</li>
							<li>Account in federal jail</li>
						</ul>
						<p className="text-muted-foreground text-sm">
							Fix the API key issue and try again to restore access
							automatically.
						</p>
					</div>
				)}

				<div className="flex flex-col gap-2 sm:flex-row">
					{!isAdminSuspension && (
						<Button
							variant="outline"
							size="sm"
							asChild
							className="flex items-center gap-2"
						>
							<a
								href={EXTERNAL_URLS.TORN_API_SETTINGS}
								target="_blank"
								rel="noopener noreferrer"
							>
								<ExternalLink className="h-3 w-3" />
								Check API Settings
							</a>
						</Button>
					)}

					{canTryRestore ? (
						<Button
							size="sm"
							onClick={() => restoreAccessMutation.mutate()}
							disabled={restoreAccessMutation.isPending}
							className="flex items-center gap-2"
						>
							<RefreshCw className="h-3 w-3" />
							{restoreAccessMutation.isPending ? "Checking..." : "Try Again"}
						</Button>
					) : (
						<div className="text-muted-foreground text-sm">
							{isAdminSuspension
								? "Contact an admin to restore your access."
								: "Contact an admin if the issue persists."}
						</div>
					)}
				</div>
			</AlertDescription>
		</Alert>
	);
}
