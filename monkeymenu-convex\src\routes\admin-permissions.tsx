import { createFileRoute } from '@tanstack/react-router';
import { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';

export const Route = createFileRoute('/admin-permissions')({
  component: AdminPermissionsPage,
});

function AdminPermissionsPage() {
  const [loading, setLoading] = useState(false);
  
  // Get current user debug info
  const userDebug = useQuery(api.adminHelpers.getCurrentUserDebug);
  
  // Mutations
  const grantFullPermissions = useMutation(api.adminHelpers.grantMeFullPermissions);
  const resetPermissions = useMutation(api.adminHelpers.resetMyPermissions);
  
  const handleGrantFullPermissions = async () => {
    setLoading(true);
    try {
      const result = await grantFullPermissions();
      alert(`Success! Granted ${result.newPermissions.length} permissions. Refresh the page to see changes.`);
      window.location.reload();
    } catch (error) {
      alert(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleResetPermissions = async () => {
    setLoading(true);
    try {
      const result = await resetPermissions();
      alert(`Success! Reset to ${result.newPermissions.length} default permissions. Refresh the page to see changes.`);
      window.location.reload();
    } catch (error) {
      alert(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  if (!userDebug) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
      </div>
    );
  }

  if ('error' in userDebug) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h2 className="text-lg font-semibold text-red-800 mb-2">Error</h2>
          <p className="text-red-600">{userDebug.error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">🔑 Permission Management</h1>
        <p className="text-gray-600">Manage your user permissions for testing</p>
      </div>

      {/* Current User Info */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Current User Info</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Username</label>
            <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-sm">
              {userDebug.user.username}
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">User ID</label>
            <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-sm font-mono">
              {userDebug.user._id}
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Clerk ID</label>
            <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-sm font-mono">
              {userDebug.user.clerkId}
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-sm">
              {userDebug.user.isActive ? '🟢 Active' : '🔴 Inactive'}
            </div>
          </div>
        </div>
      </div>

      {/* Current Permissions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Current Permissions ({userDebug.user.permissions.length})
        </h2>
        
        {userDebug.user.permissions.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
            {userDebug.user.permissions.map((permission: string) => (
              <div key={permission} className="px-3 py-2 bg-blue-50 border border-blue-200 rounded-md text-sm font-mono text-blue-800">
                {permission}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-gray-500 italic">No permissions assigned</div>
        )}
      </div>

      {/* Permission Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Permission Actions</h2>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
            <div>
              <h3 className="font-semibold text-green-800">Grant Full Permissions</h3>
              <p className="text-sm text-green-600">
                Grants admin.all and all specific permissions for testing all features
              </p>
            </div>
            <button
              onClick={handleGrantFullPermissions}
              disabled={loading}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 transition-colors"
            >
              {loading ? 'Granting...' : 'Grant Full Access'}
            </button>
          </div>
          
          <div className="flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div>
              <h3 className="font-semibold text-yellow-800">Reset to Default</h3>
              <p className="text-sm text-yellow-600">
                Resets permissions back to default (banking.view only)
              </p>
            </div>
            <button
              onClick={handleResetPermissions}
              disabled={loading}
              className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 disabled:opacity-50 transition-colors"
            >
              {loading ? 'Resetting...' : 'Reset Permissions'}
            </button>
          </div>
        </div>
      </div>

      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <p className="text-sm text-blue-800">
          <strong>Note:</strong> After changing permissions, refresh your browser or navigate to different pages to see the changes take effect.
        </p>
      </div>
    </div>
  );
}