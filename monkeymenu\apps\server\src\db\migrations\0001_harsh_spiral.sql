CREATE TABLE `cached_chain_report` (
	`chain_id` integer PRIMARY KEY NOT NULL,
	`war_id` integer NOT NULL,
	`report_data` text NOT NULL,
	`start_time` integer NOT NULL,
	`end_time` integer NOT NULL,
	`chain_length` integer NOT NULL,
	`total_respect` integer NOT NULL,
	`attacker_count` integer NOT NULL,
	`cached_at` integer DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE INDEX `idx_chain_war_id` ON `cached_chain_report` (`war_id`);--> statement-breakpoint
CREATE INDEX `idx_chain_start_time` ON `cached_chain_report` (`start_time`);--> statement-breakpoint
CREATE INDEX `idx_chain_end_time` ON `cached_chain_report` (`end_time`);--> statement-breakpoint
CREATE TABLE `cached_war_attacks` (
	`id` text PRIMARY KEY NOT NULL,
	`war_id` integer NOT NULL,
	`attack_data` text NOT NULL,
	`attacker_id` integer NOT NULL,
	`defender_id` integer NOT NULL,
	`attacker_faction_id` integer,
	`defender_faction_id` integer,
	`timestamp` integer NOT NULL,
	`chain_id` integer,
	`result` text NOT NULL,
	`respect` integer DEFAULT 0,
	`cached_at` integer DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE INDEX `idx_attack_war_id` ON `cached_war_attacks` (`war_id`);--> statement-breakpoint
CREATE INDEX `idx_attack_timestamp` ON `cached_war_attacks` (`timestamp`);--> statement-breakpoint
CREATE INDEX `idx_attack_attacker_id` ON `cached_war_attacks` (`attacker_id`);--> statement-breakpoint
CREATE INDEX `idx_attack_defender_id` ON `cached_war_attacks` (`defender_id`);--> statement-breakpoint
CREATE INDEX `idx_attack_chain_id` ON `cached_war_attacks` (`chain_id`);--> statement-breakpoint
CREATE INDEX `idx_attack_result` ON `cached_war_attacks` (`result`);--> statement-breakpoint
CREATE INDEX `idx_attack_war_chain` ON `cached_war_attacks` (`war_id`,`chain_id`);--> statement-breakpoint
CREATE INDEX `idx_attack_war_attacker` ON `cached_war_attacks` (`war_id`,`attacker_id`);--> statement-breakpoint
CREATE INDEX `idx_attack_war_timestamp` ON `cached_war_attacks` (`war_id`,`timestamp`);--> statement-breakpoint
CREATE TABLE `cached_war_report` (
	`war_id` integer PRIMARY KEY NOT NULL,
	`report_data` text NOT NULL,
	`faction_ids` text NOT NULL,
	`start_time` integer NOT NULL,
	`end_time` integer,
	`winner` integer,
	`is_completed` integer DEFAULT false,
	`cached_at` integer DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`last_updated` integer DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE INDEX `idx_war_start_time` ON `cached_war_report` (`start_time`);--> statement-breakpoint
CREATE INDEX `idx_war_end_time` ON `cached_war_report` (`end_time`);--> statement-breakpoint
CREATE INDEX `idx_war_is_completed` ON `cached_war_report` (`is_completed`);--> statement-breakpoint
CREATE INDEX `idx_war_faction_ids` ON `cached_war_report` (`faction_ids`);--> statement-breakpoint
CREATE TABLE `cached_war_stats` (
	`id` text PRIMARY KEY NOT NULL,
	`war_id` integer NOT NULL,
	`faction_id` integer NOT NULL,
	`stats_type` text NOT NULL,
	`timeframe` text,
	`stats_data` text NOT NULL,
	`cached_at` integer DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `idx_war_stats_unique` ON `cached_war_stats` (`war_id`,`faction_id`,`stats_type`,`timeframe`);--> statement-breakpoint
CREATE INDEX `idx_war_stats_war_id` ON `cached_war_stats` (`war_id`);--> statement-breakpoint
CREATE INDEX `idx_war_stats_faction_id` ON `cached_war_stats` (`faction_id`);--> statement-breakpoint
CREATE INDEX `idx_war_stats_type` ON `cached_war_stats` (`stats_type`);