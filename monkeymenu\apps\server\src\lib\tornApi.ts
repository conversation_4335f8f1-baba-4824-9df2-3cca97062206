import { TORN_API } from "@monkeymenu/shared";
import type {
	Torn<PERSON>ttack,
	TornChainInfo,
	TornChainReport,
} from "@monkeymenu/shared";
import type { DrizzleD1Database } from "drizzle-orm/d1";
import {
	getSuspensionReason,
	shouldSuspendAccessForError,
	suspendUserAccess,
} from "./tornUserUtils";

const API_BASE_URL = TORN_API.BASE_URL;
const API_V1_BASE_URL = TORN_API.V1_BASE_URL;
const REQUEST_TIMEOUT_MS = TORN_API.TIMEOUT_MS;
const MAX_RETRIES = TORN_API.MAX_RETRIES;
const RETRY_DELAY_MS = TORN_API.RETRY_DELAY_MS;

export interface TornAPIErrorResponse {
	code: number;
	error: string;
}

export interface TornAPIResponse {
	player_id?: number;
	faction?: {
		faction_id?: number;
		position?: string;
	};
	error?: TornAPIErrorResponse;
}

export interface FactionBalanceResponse {
	factionBalance: {
		money: number;
		points: number;
	};
	error?: TornAPIErrorResponse;
}

export interface FactionTransactionsResponse {
	fundsnews: Record<
		string,
		{
			news: string;
			timestamp: number;
		}
	>;
	error?: TornAPIErrorResponse;
}

export interface UserEventsResponse {
	events?: Record<
		string,
		{
			timestamp: number;
			event: string;
		}
	>;
	error?: TornAPIErrorResponse;
}

// Add types for Ranked Wars
export interface RankedWarFaction {
	id: number;
	name: string;
	score: number;
	chain: number;
}

export interface RankedWar {
	id: number;
	start: number;
	end: number;
	target: number;
	winner: number;
	factions: RankedWarFaction[];
}

// Ranked War Report types
export interface RankedWarReportFactionMember {
	id: number;
	name: string;
	level: number;
	attacks: number;
	score: number;
}

export interface RankedWarReportFactionRewards {
	respect: number;
	points: number;
	items: {
		id: number;
		name: string;
		quantity: number;
	}[];
}

export interface RankedWarReportFactionRank {
	before: string;
	after: string;
}

export interface RankedWarReportFaction {
	id: number;
	name: string;
	score: number;
	attacks: number;
	rank: RankedWarReportFactionRank;
	rewards: RankedWarReportFactionRewards;
	members: RankedWarReportFactionMember[];
}

export interface RankedWarReport {
	id: number;
	start: number;
	end: number;
	winner: number;
	forfeit: boolean;
	factions: RankedWarReportFaction[];
}

function sleep(ms: number) {
	return new Promise((resolve) => setTimeout(resolve, ms));
}

class TornAPIError extends Error {
	constructor(
		public code: number,
		message: string,
	) {
		super(message);
		this.name = "TornAPIError";
	}
}

export class TornAPI {
	constructor(private readonly apiKey: string) {}

	private async request<T>(endpoint: string, selections: string[]): Promise<T> {
		const url = new URL(`${API_BASE_URL}${endpoint}`);
		url.searchParams.set("key", this.apiKey);
		if (selections.length > 0) {
			url.searchParams.set("selections", selections.join(","));
		}

		let lastError: unknown = null;
		for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
			const controller = new AbortController();
			const timeoutId = setTimeout(
				() => controller.abort(),
				REQUEST_TIMEOUT_MS,
			);
			try {
				const response = await fetch(url.toString(), {
					method: "GET",
					signal: controller.signal,
					headers: {
						Accept: "application/json",
					},
				});

				let responseBody: string | undefined;
				try {
					responseBody = await response.text();
				} catch (err) {
					throw new Error("Failed to read response body from Torn API");
				}

				let json: TornAPIResponse;
				try {
					json = JSON.parse(responseBody);
				} catch (parseError) {
					throw new Error(
						`Torn API response was not valid JSON (status ${response.status}): ${responseBody?.slice(0, 200)}`,
					);
				}

				if (!response.ok) {
					if (json?.error) {
						throw new TornAPIError(
							json.error.code,
							`Torn API Error: ${json.error.error} (Code: ${json.error.code})`,
						);
					}
					throw new Error(`Torn API request failed: ${response.status}`);
				}
				// Also check for Torn API error object even if response.ok is true
				if (json.error) {
					throw new TornAPIError(
						json.error.code,
						`Torn API Error: ${json.error.error} (Code: ${json.error.code})`,
					);
				}
				return json as T;
			} catch (error) {
				lastError = error;
				if (error instanceof Error) {
					if (error.name === "AbortError") {
						if (attempt === MAX_RETRIES) {
							throw new Error(
								"Torn API request timed out after multiple attempts",
							);
						}
					} else if (
						error.message.includes("Failed to read response body") ||
						error.message.includes("not valid JSON") ||
						error instanceof TornAPIError
					) {
						// Do not retry for these errors
						throw error;
					}
				}
				if (attempt < MAX_RETRIES) {
					await sleep(RETRY_DELAY_MS * attempt); // Exponential backoff
				}
			} finally {
				clearTimeout(timeoutId);
			}
		}
		throw lastError instanceof Error
			? lastError
			: new Error("Unknown error occurred while calling Torn API");
	}

	// Add a new method for v1 API requests
	/**
	 * Make a request to Torn API v1 (for legacy endpoints)
	 * Used for endpoints/selections that are only available in API v1
	 */
	private async requestV1<T>(
		endpoint: string,
		selections: string[],
	): Promise<T> {
		const url = new URL(`${API_V1_BASE_URL}${endpoint}`);
		url.searchParams.set("key", this.apiKey);
		if (selections.length > 0) {
			url.searchParams.set("selections", selections.join(","));
		}

		let lastError: unknown = null;
		for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
			const controller = new AbortController();
			const timeoutId = setTimeout(
				() => controller.abort(),
				REQUEST_TIMEOUT_MS,
			);
			try {
				const response = await fetch(url.toString(), {
					method: "GET",
					signal: controller.signal,
					headers: {
						Accept: "application/json",
					},
				});

				let responseBody: string | undefined;
				try {
					responseBody = await response.text();
				} catch (err) {
					throw new Error("Failed to read response body from Torn API");
				}

				let json: TornAPIResponse;
				try {
					json = JSON.parse(responseBody);
				} catch (parseError) {
					throw new Error(
						`Torn API response was not valid JSON (status ${response.status}): ${responseBody?.slice(0, 200)}`,
					);
				}

				if (!response.ok) {
					if (json?.error) {
						throw new TornAPIError(
							json.error.code,
							`Torn API Error: ${json.error.error} (Code: ${json.error.code})`,
						);
					}
					throw new Error(`Torn API request failed: ${response.status}`);
				}
				// Also check for Torn API error object even if response.ok is true
				if (json.error) {
					throw new TornAPIError(
						json.error.code,
						`Torn API Error: ${json.error.error} (Code: ${json.error.code})`,
					);
				}
				return json as T;
			} catch (error) {
				lastError = error;
				if (error instanceof Error) {
					if (error.name === "AbortError") {
						if (attempt === MAX_RETRIES) {
							throw new Error(
								"Torn API request timed out after multiple attempts",
							);
						}
					} else if (
						error.message.includes("Failed to read response body") ||
						error.message.includes("not valid JSON") ||
						error instanceof TornAPIError
					) {
						// Do not retry for these errors
						throw error;
					}
				}
				if (attempt < MAX_RETRIES) {
					await sleep(RETRY_DELAY_MS * attempt); // Exponential backoff
				}
			} finally {
				clearTimeout(timeoutId);
			}
		}
		throw lastError instanceof Error
			? lastError
			: new Error("Unknown error occurred while calling Torn API");
	}

	async getUserProfile(): Promise<TornAPIResponse> {
		return this.request<TornAPIResponse>("/user/", ["profile"]);
	}

	async getFactionBalance(): Promise<{ money: number; points: number }> {
		const response = await this.request<FactionBalanceResponse>("/user/", [
			"factionbalance",
		]);
		return response.factionBalance;
	}

	/**
	 * Get faction fund transactions (money transfers)
	 * Note: Uses API v1 because 'fundsnews' selection is only available in v1 (API error code 22)
	 */
	async getFactionTransactions(): Promise<
		FactionTransactionsResponse["fundsnews"]
	> {
		// Use API v1 for faction transactions as fundsnews is only available in v1
		const response = await this.requestV1<FactionTransactionsResponse>(
			"/faction/",
			["fundsnews"],
		);
		return response.fundsnews;
	}

	async getUserEvents(from?: number): Promise<UserEventsResponse["events"]> {
		const selections = ["events"];
		const params = new URLSearchParams();
		if (from) {
			params.append("from", from.toString());
		}

		const endpoint = `/user/${params.toString() ? `?${params.toString()}` : ""}`;
		const response = await this.request<UserEventsResponse>(
			endpoint,
			selections,
		);

		// Check if events field is present in the response
		if (response.events === undefined) {
			throw new Error(
				"Torn API response missing 'events' field. This may indicate insufficient API key permissions or an API issue.",
			);
		}

		return response.events;
	}

	async getFactionRankedWars(factionId: number): Promise<RankedWar[]> {
		type RankedWarsResponse = {
			rankedwars: RankedWar[];
			_metadata?: unknown;
		};

		const data = await this.request<RankedWarsResponse>(
			`/faction/${factionId}/rankedwars`,
			[],
		);
		return data.rankedwars;
	}

	async getRankedWarReport(warId: number): Promise<RankedWarReport> {
		type WarReportResponse = { rankedwarreport: RankedWarReport };
		const data = await this.request<WarReportResponse>(
			`/faction/${warId}/rankedwarreport`,
			[],
		);
		return data.rankedwarreport;
	}

	async getFactionChains(
		limit = 100,
		sort: "ASC" | "DESC" = "DESC",
		to?: number,
		from?: number,
	): Promise<TornChainInfo[]> {
		// Torn API v2 endpoint: /faction/chains
		// Build query params directly in endpoint string since this.request only handles selections
		let endpoint = `/faction/chains?limit=${limit}&sort=${sort}`;
		if (to) endpoint += `&to=${to}`;
		if (from) endpoint += `&from=${from}`;
		type ChainsResponse = {
			chains: TornChainInfo[];
			_metadata?: unknown;
		};
		const response = await this.request<ChainsResponse>(endpoint, []);
		return response.chains;
	}

	async getChainReport(chainId: number): Promise<TornChainReport> {
		// Endpoint: /faction/{chainId}/chainreport
		const endpoint = `/faction/${chainId}/chainreport`;
		type ChainReportResponse = { chainreport: TornChainReport };
		const response = await this.request<ChainReportResponse>(endpoint, []);
		return response.chainreport;
	}

	async getFactionAttacks(
		limit = 100,
		sort: "ASC" | "DESC" = "DESC",
		to?: number,
		from?: number,
	): Promise<TornAttack[]> {
		let endpoint = `/faction/attacks?limit=${limit}&sort=${sort}`;
		if (to) endpoint += `&to=${to}`;
		if (from) endpoint += `&from=${from}`;
		type AttacksResponse = { attacks: TornAttack[]; _metadata?: unknown };
		const response = await this.request<AttacksResponse>(endpoint, []);
		return response.attacks;
	}

	// Add more endpoint methods here as needed, reusing this.request
}

/**
 * Enhanced TornAPI class that can automatically suspend user access
 * when problematic API errors are encountered
 */
export class TornAPIWithSuspension extends TornAPI {
	constructor(
		apiKey: string,
		private db: DrizzleD1Database,
		private userId: string,
		private env?: { DISCORD_BOT_TOKEN?: string; DISCORD_GUILD_ID?: string },
	) {
		super(apiKey);
	}

	async getUserProfile(): Promise<TornAPIResponse> {
		try {
			const result = await super.getUserProfile();
			return result;
		} catch (error) {
			// If it's a TornAPIError, check if it should trigger suspension
			if (
				error instanceof TornAPIError &&
				shouldSuspendAccessForError(error.code)
			) {
				const reason = getSuspensionReason(error.code);
				await suspendUserAccess(
					this.db,
					this.userId,
					reason,
					error.code,
					this.env,
				);
				console.log(
					`User ${this.userId} access suspended due to API error ${error.code}`,
				);
			}

			throw error;
		}
	}

	async getFactionBalance(): Promise<{ money: number; points: number }> {
		try {
			const result = await super.getFactionBalance();
			return result;
		} catch (error) {
			// If it's a TornAPIError, check if it should trigger suspension
			if (
				error instanceof TornAPIError &&
				shouldSuspendAccessForError(error.code)
			) {
				const reason = getSuspensionReason(error.code);
				await suspendUserAccess(
					this.db,
					this.userId,
					reason,
					error.code,
					this.env,
				);
				console.log(
					`User ${this.userId} access suspended due to API error ${error.code}`,
				);
			}

			throw error;
		}
	}

	async getFactionTransactions(): Promise<
		FactionTransactionsResponse["fundsnews"]
	> {
		try {
			// Use the parent class method which now calls v1 API
			const result = await super.getFactionTransactions();
			return result;
		} catch (error) {
			// If it's a TornAPIError, check if it should trigger suspension
			if (
				error instanceof TornAPIError &&
				shouldSuspendAccessForError(error.code)
			) {
				const reason = getSuspensionReason(error.code);
				await suspendUserAccess(
					this.db,
					this.userId,
					reason,
					error.code,
					this.env,
				);
				console.log(
					`User ${this.userId} access suspended due to API error ${error.code}`,
				);
			}

			throw error;
		}
	}

	async getUserEvents(from?: number): Promise<UserEventsResponse["events"]> {
		try {
			const result = await super.getUserEvents(from);
			return result;
		} catch (error) {
			// If it's a TornAPIError, check if it should trigger suspension
			if (
				error instanceof TornAPIError &&
				shouldSuspendAccessForError(error.code)
			) {
				const reason = getSuspensionReason(error.code);
				await suspendUserAccess(
					this.db,
					this.userId,
					reason,
					error.code,
					this.env,
				);
				console.log(
					`User ${this.userId} access suspended due to API error ${error.code}`,
				);
			}

			throw error;
		}
	}

	async getFactionRankedWars(factionId: number): Promise<RankedWar[]> {
		try {
			const result = await super.getFactionRankedWars(factionId);
			return result;
		} catch (error) {
			// If it's a TornAPIError, check if it should trigger suspension
			if (
				error instanceof TornAPIError &&
				shouldSuspendAccessForError(error.code)
			) {
				const reason = getSuspensionReason(error.code);
				await suspendUserAccess(
					this.db,
					this.userId,
					reason,
					error.code,
					this.env,
				);
				console.log(
					`User ${this.userId} access suspended due to API error ${error.code}`,
				);
			}

			throw error;
		}
	}

	async getRankedWarReport(warId: number): Promise<RankedWarReport> {
		try {
			const result = await super.getRankedWarReport(warId);
			return result;
		} catch (error) {
			// If it's a TornAPIError, check if it should trigger suspension
			if (
				error instanceof TornAPIError &&
				shouldSuspendAccessForError(error.code)
			) {
				const reason = getSuspensionReason(error.code);
				await suspendUserAccess(
					this.db,
					this.userId,
					reason,
					error.code,
					this.env,
				);
				console.log(
					`User ${this.userId} access suspended due to API error ${error.code}`,
				);
			}

			throw error;
		}
	}

	async getFactionChains(
		limit = 100,
		sort: "ASC" | "DESC" = "DESC",
		to?: number,
		from?: number,
	): Promise<TornChainInfo[]> {
		try {
			const result = await super.getFactionChains(limit, sort, to, from);
			return result;
		} catch (error) {
			// If it's a TornAPIError, check if it should trigger suspension
			if (
				error instanceof TornAPIError &&
				shouldSuspendAccessForError(error.code)
			) {
				const reason = getSuspensionReason(error.code);
				await suspendUserAccess(
					this.db,
					this.userId,
					reason,
					error.code,
					this.env,
				);
				console.log(
					`User ${this.userId} access suspended due to API error ${error.code}`,
				);
			}

			throw error;
		}
	}

	async getChainReport(chainId: number): Promise<TornChainReport> {
		try {
			const result = await super.getChainReport(chainId);
			return result;
		} catch (error) {
			// If it's a TornAPIError, check if it should trigger suspension
			if (
				error instanceof TornAPIError &&
				shouldSuspendAccessForError(error.code)
			) {
				const reason = getSuspensionReason(error.code);
				await suspendUserAccess(
					this.db,
					this.userId,
					reason,
					error.code,
					this.env,
				);
				console.log(
					`User ${this.userId} access suspended due to API error ${error.code}`,
				);
			}

			throw error;
		}
	}

	async getFactionAttacks(
		limit = 100,
		sort: "ASC" | "DESC" = "DESC",
		to?: number,
		from?: number,
	): Promise<TornAttack[]> {
		try {
			const result = await super.getFactionAttacks(limit, sort, to, from);
			return result;
		} catch (error) {
			// If it's a TornAPIError, check if it should trigger suspension
			if (
				error instanceof TornAPIError &&
				shouldSuspendAccessForError(error.code)
			) {
				const reason = getSuspensionReason(error.code);
				await suspendUserAccess(
					this.db,
					this.userId,
					reason,
					error.code,
					this.env,
				);
				console.log(
					`User ${this.userId} access suspended due to API error ${error.code}`,
				);
			}

			throw error;
		}
	}
}

export function createTornAPI(apiKey: string): TornAPI {
	return new TornAPI(apiKey);
}

/**
 * Creates a TornAPI instance with suspension monitoring for user-specific operations
 */
export function createTornAPIWithSuspension(
	apiKey: string,
	db: DrizzleD1Database,
	userId: string,
	env?: { DISCORD_BOT_TOKEN?: string; DISCORD_GUILD_ID?: string },
): TornAPIWithSuspension {
	return new TornAPIWithSuspension(apiKey, db, userId, env);
}
