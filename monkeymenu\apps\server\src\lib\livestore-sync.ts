import { makeDurableObject, makeWorker } from "@livestore/sync-cf/cf-worker";
import { eq } from "drizzle-orm";
import { drizzle } from "drizzle-orm/d1";
import { session } from "../db/schema/auth";
import type { AppBindings } from "./types";

// Define the LiveStore Durable Object for WebSocket connections
export class LiveStoreWebSocketServer extends makeDurableObject({
	onPush: async (message) => {
		console.log("[LiveStore] onPush", message.batch.length, "events");
	},
	onPull: async (message) => {
		console.log("[LiveStore] onPull", message);
	},
}) {
	// Helper method to validate session token against database
	private async validateSessionToken(
		token: string,
		env: AppBindings["Bindings"],
	): Promise<{ isValid: boolean; userId?: string; sessionId?: string }> {
		// Skip validation for anonymous tokens
		if (token === "anonymous_token") {
			return { isValid: true };
		}

		try {
			// Basic format validation first
			const sessionTokenPattern = /^[a-zA-Z0-9\-_]{20,}$/;
			if (!sessionTokenPattern.test(token)) {
				console.warn("[LiveStore] Invalid session token format");
				return { isValid: false };
			}

			// Database validation - check if session exists and is not expired
			const db = drizzle(env.DB);
			const now = new Date();

			const sessionResult = await db
				.select({
					id: session.id,
					userId: session.userId,
					expiresAt: session.expiresAt,
				})
				.from(session)
				.where(eq(session.token, token))
				.limit(1);

			if (sessionResult.length === 0) {
				console.warn("[LiveStore] Session token not found in database");
				return { isValid: false };
			}

			const sessionData = sessionResult[0];

			// Check if session has expired
			if (sessionData.expiresAt < now) {
				console.warn("[LiveStore] Session token has expired", {
					sessionId: sessionData.id,
					expiresAt: sessionData.expiresAt,
				});
				return { isValid: false };
			}

			console.log("[LiveStore] Session token validation passed", {
				userId: sessionData.userId,
				sessionId: sessionData.id,
			});

			return {
				isValid: true,
				userId: sessionData.userId,
				sessionId: sessionData.id,
			};
		} catch (error) {
			console.error("[LiveStore] Session validation error:", error);
			return { isValid: false };
		}
	}

	// Optional: Track session usage for monitoring and analytics
	private async updateSessionLastUsed(
		sessionId: string,
		env: AppBindings["Bindings"],
	): Promise<void> {
		try {
			const db = drizzle(env.DB);
			await db
				.update(session)
				.set({ updatedAt: new Date() })
				.where(eq(session.id, sessionId));
		} catch (error) {
			// Don't fail the request if session tracking fails
			console.warn("[LiveStore] Failed to update session last used:", error);
		}
	}
	// Add the worker methods to the Durable Object
	async fetch(request: Request): Promise<Response> {
		console.log("[LiveStore] Durable Object handling request:", request.url);

		// Validate the auth payload with security checks
		const url = new URL(request.url);
		const payload = url.searchParams.get("payload");

		if (payload) {
			// Security: Check payload size to prevent DoS attacks
			const MAX_PAYLOAD_SIZE = 1024; // 1KB limit for auth payload
			if (payload.length > MAX_PAYLOAD_SIZE) {
				console.warn("[LiveStore] Payload size exceeds limit:", payload.length);
				return new Response("Payload too large", { status: 413 });
			}

			try {
				const decodedPayload = decodeURIComponent(payload);

				// Additional size check after URL decoding
				if (decodedPayload.length > MAX_PAYLOAD_SIZE) {
					console.warn(
						"[LiveStore] Decoded payload size exceeds limit:",
						decodedPayload.length,
					);
					return new Response("Payload too large", { status: 413 });
				}

				const parsedPayload = JSON.parse(decodedPayload);

				// Validate payload structure and types
				if (!parsedPayload || typeof parsedPayload !== "object") {
					console.warn("[LiveStore] Invalid payload structure: not an object");
					return new Response("Invalid payload structure", { status: 400 });
				}

				// Validate required fields and types
				if (
					!parsedPayload.authToken ||
					typeof parsedPayload.authToken !== "string"
				) {
					console.warn("[LiveStore] Invalid or missing auth token");
					return new Response("No valid auth token provided", { status: 401 });
				}

				// Security: Validate auth token format and length
				const authToken = parsedPayload.authToken.trim();
				if (authToken.length === 0 || authToken.length > 500) {
					console.warn(
						"[LiveStore] Auth token length invalid:",
						authToken.length,
					);
					return new Response("Invalid auth token format", { status: 401 });
				}

				// Validate session token using database lookup
				const validation = await this.validateSessionToken(
					authToken,
					this.env as unknown as AppBindings["Bindings"],
				);

				if (!validation.isValid) {
					console.warn("[LiveStore] Session token validation failed");
					return new Response("Invalid or expired session token", {
						status: 401,
					});
				}

				console.log("[LiveStore] Request with valid session token", {
					userId: validation.userId,
					sessionId: validation.sessionId,
				});

				// Optional: Track session usage for analytics
				if (validation.sessionId) {
					this.updateSessionLastUsed(
						validation.sessionId,
						this.env as unknown as AppBindings["Bindings"],
					);
				}
			} catch (error) {
				console.error("[LiveStore] Failed to parse payload:", error);
				// Don't expose internal error details in response
				return new Response("Invalid payload format", { status: 400 });
			}
		}

		// Call the parent class method to handle the LiveStore sync
		return (
			super.fetch?.(request) ||
			new Response("Method not available", { status: 500 })
		);
	}
}

// Export the worker configuration for LiveStore sync (not used in Durable Object mode)
export const livestoreSyncWorker = makeWorker({
	validatePayload: (payload: unknown) => {
		// Enhanced auth validation for better-auth session tokens
		const typedPayload = payload as { authToken?: string; userId?: string };

		if (!typedPayload?.authToken) {
			throw new Error("No auth token provided");
		}

		const authToken = typedPayload.authToken.trim();

		// Allow anonymous tokens
		if (authToken === "anonymous_token") {
			console.log("[LiveStore] Anonymous access granted");
			return;
		}

		// Validate session token format (better-auth tokens are typically UUIDs or similar)
		const sessionTokenPattern = /^[a-zA-Z0-9\-_]{20,}$/;
		if (!sessionTokenPattern.test(authToken)) {
			throw new Error("Invalid session token format");
		}

		// TODO: Add database validation to check if session token exists and is valid
		// This would require access to the database instance in this context

		console.log("[LiveStore] Session token validation passed", {
			userId: typedPayload.userId ? "***" : "anonymous",
		});
	},
	enableCORS: true,
});
