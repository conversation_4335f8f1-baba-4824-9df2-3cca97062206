import { HasPermission } from "@/components/permissions/PermissionGuards";
import { PageContainer } from "@/components/ui/page-container";
import { PERMISSION_NAMES } from "@monkeymenu/shared";
import { createFileRoute } from "@tanstack/react-router";
import { Wars } from "./-components/wars/";

export const Route = createFileRoute("/(faction)/wars")({
	component: WarsPage,
});

function WarsPage() {
	return (
		<PageContainer>
			<HasPermission permission={PERMISSION_NAMES.WARS_VIEW}>
				<Wars />
			</HasPermission>
		</PageContainer>
	);
}
