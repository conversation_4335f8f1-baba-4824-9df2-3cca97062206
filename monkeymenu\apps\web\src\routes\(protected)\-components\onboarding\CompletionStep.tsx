import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { SiDiscord } from "@icons-pack/react-simple-icons";
import { useNavigate } from "@tanstack/react-router";
import { ArrowRight, CheckCircle } from "lucide-react";
import type { StepComponentProps } from "./types";

interface CompletionStepProps extends StepComponentProps {
	isDiscordLinked: boolean;
}

export function CompletionStep({
	onPrevStep,
	isDiscordLinked,
}: CompletionStepProps) {
	const navigate = useNavigate();

	return (
		<Card>
			<CardContent className="space-y-6 p-8">
				{/* Header */}
				<div className="space-y-3 text-center">
					<div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-500/10">
						<CheckCircle className="h-8 w-8 text-green-500" />
					</div>
					<h2 className="font-bold text-2xl tracking-tight">
						Welcome to the Family!
					</h2>
					<p className="mx-auto max-w-md text-muted-foreground leading-relaxed">
						Your MonkeyMenu account is set up and ready to use
					</p>
				</div>

				{/* Status Summary */}
				<div className="space-y-3">
					<div className="flex items-center justify-center gap-2 rounded-lg bg-green-50 p-3 text-green-600 dark:bg-green-950/20 dark:text-green-400">
						<CheckCircle className="h-4 w-4" />
						<span className="font-medium text-sm">
							Account verified & role assigned
						</span>
					</div>
					{isDiscordLinked && (
						<div className="flex items-center justify-center gap-2 rounded-lg bg-indigo-50 p-3 text-indigo-600 dark:bg-indigo-950/20 dark:text-indigo-400">
							<SiDiscord className="h-4 w-4" />
							<span className="font-medium text-sm">Discord connected</span>
						</div>
					)}
				</div>
			</CardContent>

			<CardFooter className="flex gap-3 p-8 pt-0">
				<Button
					type="button"
					variant="outline"
					onClick={onPrevStep}
					className="flex-1"
				>
					Back
				</Button>
				<Button
					onClick={() => navigate({ to: "/dashboard" })}
					className="flex-1"
				>
					Dashboard
					<ArrowRight className="ml-2 h-4 w-4" />
				</Button>
			</CardFooter>
		</Card>
	);
}
