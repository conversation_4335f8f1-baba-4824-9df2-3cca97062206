import { useState } from 'react';
import { Id } from '../../../convex/_generated/dataModel';

interface User {
  _id: Id<"users">;
  tornId: number;
  username: string;
  email?: string;
  faction?: string;
  level?: number;
  permissions: string[];
  isActive: boolean;
  createdAt: number;
  lastSeen: number;
}

export function UserManagement() {
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showInactive, setShowInactive] = useState(false);
  const [permissionToAdd, setPermissionToAdd] = useState('');

  const users = [] as any;
  const permissions = [] as any;
  
  const updateUserPermissions = () => {};
  const toggleUserStatus = () => {};

  const filteredUsers = users?.filter((user: any) =>
    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.tornId.toString().includes(searchTerm) ||
    user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.faction?.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const handleAddPermission = async () => {
    if (!selectedUser || !permissionToAdd) return;

    try {
      await updateUserPermissions();
      setPermissionToAdd('');
      // Refresh selected user data
      const updatedUser = users?.find((u: any) => u._id === selectedUser._id);
      if (updatedUser) setSelectedUser(updatedUser);
    } catch (error) {
      console.error('Failed to add permission:', error);
      alert('Failed to add permission');
    }
  };

  const handleRemovePermission = async () => {
    if (!selectedUser) return;

    try {
      await updateUserPermissions();
      // Refresh selected user data
      const updatedUser = users?.find((u: any) => u._id === selectedUser._id);
      if (updatedUser) setSelectedUser(updatedUser);
    } catch (error) {
      console.error('Failed to remove permission:', error);
      alert('Failed to remove permission');
    }
  };

  const handleToggleUserStatus = async () => {
    try {
      await toggleUserStatus();
    } catch (error) {
      console.error('Failed to toggle user status:', error);
      alert('Failed to toggle user status');
    }
  };

  if (users === undefined || permissions === undefined) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-10 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-2">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">User Management</h2>
          <p className="text-sm text-gray-600">
            Showing {filteredUsers.length} of {users.length} users
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={showInactive}
              onChange={(e) => setShowInactive(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700">Show inactive</span>
          </label>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <input
          type="text"
          placeholder="Search users by username, Torn ID, email, or faction..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Users List */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Users</h3>
            </div>
            
            <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
              {filteredUsers.map((user: any) => (
                <div
                  key={user._id}
                  className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                    selectedUser?._id === user._id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                  }`}
                  onClick={() => setSelectedUser(user)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3">
                        <div className="flex-1">
                          <h4 className="text-sm font-medium text-gray-900 flex items-center gap-2">
                            {user.username}
                            {!user.isActive && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Inactive
                              </span>
                            )}
                          </h4>
                          <div className="text-xs text-gray-500 space-y-1">
                            <div>Torn ID: {user.tornId}</div>
                            {user.faction && <div>Faction: {user.faction}</div>}
                            {user.level && <div>Level: {user.level}</div>}
                            <div>Permissions: {user.permissions.length}</div>
                          </div>
                        </div>
                        
                        <div className="text-right">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleToggleUserStatus();
                            }}
                            className={`text-xs px-2 py-1 rounded ${
                              user.isActive
                                ? 'bg-red-100 text-red-800 hover:bg-red-200'
                                : 'bg-green-100 text-green-800 hover:bg-green-200'
                            }`}
                          >
                            {user.isActive ? 'Deactivate' : 'Activate'}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              {filteredUsers.length === 0 && (
                <div className="p-8 text-center">
                  <p className="text-gray-500">No users found matching your search.</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* User Details Panel */}
        <div className="lg:col-span-1">
          {selectedUser ? (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">User Details</h3>
              </div>
              
              <div className="p-6 space-y-6">
                {/* Basic Info */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Basic Information</h4>
                  <div className="space-y-2 text-sm">
                    <div><strong>Username:</strong> {selectedUser.username}</div>
                    <div><strong>Torn ID:</strong> {selectedUser.tornId}</div>
                    {selectedUser.email && <div><strong>Email:</strong> {selectedUser.email}</div>}
                    {selectedUser.faction && <div><strong>Faction:</strong> {selectedUser.faction}</div>}
                    {selectedUser.level && <div><strong>Level:</strong> {selectedUser.level}</div>}
                    <div><strong>Status:</strong> 
                      <span className={`ml-1 ${selectedUser.isActive ? 'text-green-600' : 'text-red-600'}`}>
                        {selectedUser.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    <div><strong>Joined:</strong> {new Date(selectedUser.createdAt).toLocaleDateString()}</div>
                    <div><strong>Last Seen:</strong> {new Date(selectedUser.lastSeen).toLocaleDateString()}</div>
                  </div>
                </div>

                {/* Permissions */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Permissions</h4>
                  
                  {/* Current Permissions */}
                  <div className="space-y-2 mb-4">
                    {selectedUser.permissions.length > 0 ? (
                      selectedUser.permissions.map((permission) => (
                        <div key={permission} className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded">
                          <span className="text-sm text-gray-700">{permission}</span>
                          <button
                            onClick={() => handleRemovePermission()}
                            className="text-red-600 hover:text-red-800 text-xs"
                          >
                            Remove
                          </button>
                        </div>
                      ))
                    ) : (
                      <p className="text-sm text-gray-500">No permissions assigned</p>
                    )}
                  </div>

                  {/* Add Permission */}
                  <div className="space-y-2">
                    <select
                      value={permissionToAdd}
                      onChange={(e) => setPermissionToAdd(e.target.value)}
                      className="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Select permission to add</option>
                      {permissions
                        .filter((p: any) => !selectedUser.permissions.includes(p.name))
                        .map((permission: any) => (
                          <option key={permission.name} value={permission.name}>
                            {permission.name} - {permission.description}
                          </option>
                        ))}
                    </select>
                    <button
                      onClick={handleAddPermission}
                      disabled={!permissionToAdd}
                      className="w-full text-sm bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Add Permission
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="text-center text-gray-500">
                <svg className="w-12 h-12 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <p>Select a user to view details and manage permissions</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}