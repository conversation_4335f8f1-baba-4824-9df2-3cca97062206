// Comprehensive logging system for the application

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  CRITICAL = 4,
}

export interface LogEntry {
  id: string;
  timestamp: number;
  level: LogLevel;
  message: string;
  component?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  data?: Record<string, any>;
  stack?: string;
}

export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableStorage: boolean;
  enableRemote: boolean;
  maxStorageEntries: number;
  remoteEndpoint?: string;
  enableBuffering: boolean;
  bufferSize: number;
  flushInterval: number;
}

// Logger class
export class Logger {
  private static instance: Logger;
  private config: LoggerConfig;
  private entries: LogEntry[] = [];
  private buffer: LogEntry[] = [];
  private flushTimer?: NodeJS.Timeout;
  private sessionId: string;

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: process.env.NODE_ENV === 'production' ? LogLevel.WARN : LogLevel.DEBUG,
      enableConsole: process.env.NODE_ENV === 'development',
      enableStorage: true,
      enableRemote: process.env.NODE_ENV === 'production',
      maxStorageEntries: 1000,
      enableBuffering: true,
      bufferSize: 50,
      flushInterval: 30000, // 30 seconds
      ...config,
    };

    this.sessionId = this.generateSessionId();
    
    if (this.config.enableBuffering) {
      this.startFlushTimer();
    }

    // Handle page unload to flush remaining logs
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.flush();
      });
    }
  }

  static getInstance(config?: Partial<LoggerConfig>): Logger {
    if (!this.instance) {
      this.instance = new Logger(config);
    }
    return this.instance;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateLogId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getCurrentUserId(): string | undefined {
    // Integrate with your auth system
    try {
      // Placeholder - would integrate with Clerk or auth system
      return 'current-user-id';
    } catch {
      return undefined;
    }
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.config.level;
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    component?: string,
    action?: string,
    data?: Record<string, any>,
    error?: Error
  ): LogEntry {
    return {
      id: this.generateLogId(),
      timestamp: Date.now(),
      level,
      message,
      component,
      action,
      userId: this.getCurrentUserId(),
      sessionId: this.sessionId,
      data,
      stack: error?.stack,
    };
  }

  private addEntry(entry: LogEntry): void {
    // Add to main entries array
    this.entries.push(entry);
    
    // Maintain max entries limit
    if (this.entries.length > this.config.maxStorageEntries) {
      this.entries.shift();
    }

    // Add to buffer if buffering is enabled
    if (this.config.enableBuffering) {
      this.buffer.push(entry);
      
      // Auto-flush if buffer is full
      if (this.buffer.length >= this.config.bufferSize) {
        this.flush();
      }
    }

    // Log to console if enabled
    if (this.config.enableConsole) {
      this.logToConsole(entry);
    }

    // Send to remote if not buffering
    if (this.config.enableRemote && !this.config.enableBuffering) {
      this.sendToRemote([entry]);
    }
  }

  private logToConsole(entry: LogEntry): void {
    const timestamp = new Date(entry.timestamp).toISOString();
    const prefix = `[${timestamp}] [${LogLevel[entry.level]}]`;
    const componentInfo = entry.component ? ` [${entry.component}]` : '';
    const actionInfo = entry.action ? ` [${entry.action}]` : '';
    const logMessage = `${prefix}${componentInfo}${actionInfo} ${entry.message}`;

    const consoleMethod = this.getConsoleMethod(entry.level);
    
    if (entry.data || entry.stack) {
      console[consoleMethod](logMessage, {
        data: entry.data,
        stack: entry.stack,
        userId: entry.userId,
        sessionId: entry.sessionId,
      });
    } else {
      console[consoleMethod](logMessage);
    }
  }

  private getConsoleMethod(level: LogLevel): 'log' | 'info' | 'warn' | 'error' {
    switch (level) {
      case LogLevel.DEBUG:
      case LogLevel.INFO:
        return 'info';
      case LogLevel.WARN:
        return 'warn';
      case LogLevel.ERROR:
      case LogLevel.CRITICAL:
        return 'error';
      default:
        return 'log';
    }
  }

  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.config.flushInterval);
  }

  private async sendToRemote(entries: LogEntry[]): Promise<void> {
    if (!this.config.remoteEndpoint || entries.length === 0) return;

    try {
      await fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          entries,
          sessionId: this.sessionId,
          timestamp: Date.now(),
        }),
      });
    } catch (error) {
      // Fallback to console if remote logging fails
      console.error('Failed to send logs to remote endpoint:', error);
    }
  }

  // Public logging methods
  debug(message: string, component?: string, action?: string, data?: Record<string, any>): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return;
    const entry = this.createLogEntry(LogLevel.DEBUG, message, component, action, data);
    this.addEntry(entry);
  }

  info(message: string, component?: string, action?: string, data?: Record<string, any>): void {
    if (!this.shouldLog(LogLevel.INFO)) return;
    const entry = this.createLogEntry(LogLevel.INFO, message, component, action, data);
    this.addEntry(entry);
  }

  warn(message: string, component?: string, action?: string, data?: Record<string, any>): void {
    if (!this.shouldLog(LogLevel.WARN)) return;
    const entry = this.createLogEntry(LogLevel.WARN, message, component, action, data);
    this.addEntry(entry);
  }

  error(message: string, component?: string, action?: string, data?: Record<string, any>, error?: Error): void {
    if (!this.shouldLog(LogLevel.ERROR)) return;
    const entry = this.createLogEntry(LogLevel.ERROR, message, component, action, data, error);
    this.addEntry(entry);
  }

  critical(message: string, component?: string, action?: string, data?: Record<string, any>, error?: Error): void {
    if (!this.shouldLog(LogLevel.CRITICAL)) return;
    const entry = this.createLogEntry(LogLevel.CRITICAL, message, component, action, data, error);
    this.addEntry(entry);
    
    // Force immediate flush for critical errors
    this.flush();
  }

  // Flush buffered logs
  flush(): void {
    if (this.buffer.length === 0) return;

    const logsToFlush = [...this.buffer];
    this.buffer = [];

    if (this.config.enableRemote) {
      this.sendToRemote(logsToFlush);
    }
  }

  // Performance logging
  time(label: string, component?: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      this.debug(`Timer ${label}: ${duration.toFixed(2)}ms`, component, 'performance', {
        duration,
        startTime,
        endTime,
      });
    };
  }

  // API call logging
  logApiCall(
    method: string,
    endpoint: string,
    status: number,
    duration: number,
    component?: string,
    data?: Record<string, any>
  ): void {
    const level = status >= 400 ? LogLevel.ERROR : LogLevel.INFO;
    const message = `API ${method} ${endpoint} - ${status} (${duration}ms)`;
    
    if (level === LogLevel.ERROR) {
      this.error(message, component, 'api-call', { method, endpoint, status, duration, ...data });
    } else {
      this.info(message, component, 'api-call', { method, endpoint, status, duration, ...data });
    }
  }

  // User action logging
  logUserAction(action: string, component?: string, data?: Record<string, any>): void {
    this.info(`User action: ${action}`, component, 'user-action', data);
  }

  // Navigation logging
  logNavigation(from: string, to: string, component?: string): void {
    this.info(`Navigation from ${from} to ${to}`, component, 'navigation', { from, to });
  }

  // Get logs
  getLogs(level?: LogLevel, component?: string, limit?: number): LogEntry[] {
    let filteredLogs = this.entries;

    if (level !== undefined) {
      filteredLogs = filteredLogs.filter(entry => entry.level >= level);
    }

    if (component) {
      filteredLogs = filteredLogs.filter(entry => entry.component === component);
    }

    if (limit) {
      filteredLogs = filteredLogs.slice(-limit);
    }

    return filteredLogs.sort((a, b) => b.timestamp - a.timestamp);
  }

  // Get log statistics
  getLogStats(): Record<string, number> {
    const stats: Record<string, number> = {};
    
    Object.values(LogLevel).forEach(level => {
      if (typeof level === 'number') {
        stats[LogLevel[level]] = this.entries.filter(e => e.level === level).length;
      }
    });
    
    return stats;
  }

  // Clear logs
  clearLogs(): void {
    this.entries = [];
    this.buffer = [];
  }

  // Update configuration
  updateConfig(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config };
    
    if (this.config.enableBuffering && !this.flushTimer) {
      this.startFlushTimer();
    } else if (!this.config.enableBuffering && this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = undefined;
      this.flush(); // Flush remaining logs
    }
  }

  // Destroy logger
  destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flush();
  }
}

// React hook for logging
export function useLogger(component?: string) {
  const logger = Logger.getInstance();

  return {
    debug: (message: string, action?: string, data?: Record<string, any>) =>
      logger.debug(message, component, action, data),
    info: (message: string, action?: string, data?: Record<string, any>) =>
      logger.info(message, component, action, data),
    warn: (message: string, action?: string, data?: Record<string, any>) =>
      logger.warn(message, component, action, data),
    error: (message: string, action?: string, data?: Record<string, any>, error?: Error) =>
      logger.error(message, component, action, data, error),
    critical: (message: string, action?: string, data?: Record<string, any>, error?: Error) =>
      logger.critical(message, component, action, data, error),
    time: (label: string) => logger.time(label, component),
    logUserAction: (action: string, data?: Record<string, any>) =>
      logger.logUserAction(action, component, data),
    logApiCall: (method: string, endpoint: string, status: number, duration: number, data?: Record<string, any>) =>
      logger.logApiCall(method, endpoint, status, duration, component, data),
  };
}

// Initialize default logger
export const logger = Logger.getInstance();