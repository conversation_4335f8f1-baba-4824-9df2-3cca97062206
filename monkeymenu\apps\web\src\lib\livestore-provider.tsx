import { makePersistedAdapter } from "@livestore/adapter-web";
import LiveStoreSharedWorker from "@livestore/adapter-web/shared-worker?sharedworker";
import { LiveStoreProvider } from "@livestore/react";
import type { ReactNode } from "react";
import { useMemo } from "react";
import { unstable_batchedUpdates as batchUpdates } from "react-dom";
import LiveStoreWorker from "../livestore/livestore.worker?worker";
import { schema } from "../livestore/schema";

interface MonkeyMenuLiveStoreProviderProps {
	children: ReactNode;
	/** Optional user id – if undefined we treat the visitor as anonymous. */
	userId?: string;
	/** The auth token to use for LiveStore sync. */
	authToken: string;
}

export function MonkeyMenuLiveStoreProvider({
	children,
	userId,
	authToken,
}: MonkeyMenuLiveStoreProviderProps) {
	// Create adapter with OPFS storage but with better error handling
	const adapter = useMemo(
		() =>
			makePersistedAdapter({
				storage: { type: "opfs" },
				worker: LiveStoreWorker,
				sharedWorker: LiveStoreSharedWorker,
			}),
		[],
	);

	// Resolve store + auth token purely from the supplied userId
	const storeId = userId ? `user_${userId}` : "anonymous_user";

	return (
		<LiveStoreProvider
			schema={schema}
			adapter={adapter}
			storeId={storeId}
			batchUpdates={batchUpdates}
			renderLoading={(stage) => (
				<div className="flex min-h-screen items-center justify-center">
					<div className="text-center">
						<div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-primary border-b-2" />
						<p className="text-muted-foreground text-sm">
							Loading LiveStore ({stage.stage})...
						</p>
						{(stage.stage as string) === "WaitingForLock" && (
							<p className="mt-2 text-muted-foreground text-xs">
								Waiting for tab synchronization...
							</p>
						)}
					</div>
				</div>
			)}
			renderError={(error) => {
				const err = error as Error;
				return (
					<div className="flex min-h-screen items-center justify-center">
						<div className="text-center">
							<div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
								<span className="text-destructive text-xl">⚠</span>
							</div>
							<p className="mb-2 text-destructive text-sm">
								LiveStore connection failed
							</p>
							<p className="text-muted-foreground text-xs">{err.message}</p>
							<button
								type="button"
								onClick={() => window.location.reload()}
								className="mt-4 rounded-md bg-primary px-4 py-2 text-primary-foreground text-sm"
							>
								Reload Page
							</button>
						</div>
					</div>
				);
			}}
			syncPayload={{
				authToken,
				...(userId && { userId }),
			}}
		>
			{children}
		</LiveStoreProvider>
	);
}
