#!/usr/bin/env tsx

import { execSync } from "node:child_process";
import { readFileSync } from "node:fs";

console.log("🔄 Syncing secrets from <PERSON><PERSON><PERSON> to Cloudflare Worker...");

try {
	// First, sync fresh secrets from Doppler to .dev.vars
	console.log("📥 Downloading latest secrets from Doppler...");
	execSync("doppler secrets download --no-file --format env > .dev.vars", {
		stdio: "inherit",
	});

	// Count secrets in the file
	const secretsContent = readFileSync(".dev.vars", "utf8");
	const secretCount = secretsContent
		.trim()
		.split("\n")
		.filter((line) => line.trim()).length;

	console.log(`📋 Found ${secretCount} secrets to sync`);

	// Bulk upload all secrets from .dev.vars
	console.log("📤 Bulk uploading secrets...");
	execSync("wrangler secret bulk .dev.vars", {
		stdio: "inherit",
	});

	console.log("🎉 All secrets synced successfully to <PERSON>flare Worker!");
} catch (error) {
	console.error("❌ Failed to sync secrets:", (error as Error).message);
	process.exit(1);
}
