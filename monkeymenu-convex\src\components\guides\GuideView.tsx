import React, { useEffect } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import rehypeRaw from 'rehype-raw';
import 'highlight.js/styles/github.css';

interface GuideViewProps {
  guideId: Id<"guides">;
  onBack: () => void;
  onEdit?: (guide: any) => void;
  canEdit?: boolean;
}

export const GuideView: React.FC<GuideViewProps> = ({
  guideId,
  onBack,
  onEdit,
  canEdit = false,
}) => {
  const guide = useQuery(api.guides.getGuideById, { id: guideId });
  const incrementViewCount = useMutation(api.guides.incrementViewCount);

  useEffect(() => {
    if (guide) {
      incrementViewCount({ id: guideId });
    }
  }, [guide, guideId, incrementViewCount]);

  if (guide === undefined) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 animate-pulse">
        <div className="h-8 bg-gray-200 rounded mb-4"></div>
        <div className="h-4 bg-gray-200 rounded mb-2 w-1/3"></div>
        <div className="space-y-2 mb-6">
          <div className="h-4 bg-gray-200 rounded"></div>
          <div className="h-4 bg-gray-200 rounded"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        </div>
      </div>
    );
  }

  if (!guide) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Guide Not Found</h2>
        <p className="text-gray-600 mb-4">The guide you're looking for doesn't exist or has been removed.</p>
        <button
          onClick={onBack}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Back to Guides
        </button>
      </div>
    );
  }

  const handleEdit = () => {
    if (onEdit) {
      onEdit(guide);
    }
  };


  return (
    <div className="bg-white rounded-lg shadow-md">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <button
            onClick={onBack}
            className="text-blue-600 hover:text-blue-800 flex items-center gap-2"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back to Guides
          </button>
          {canEdit && (
            <button
              onClick={handleEdit}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Edit Guide
            </button>
          )}
        </div>

        <h1 className="text-3xl font-bold text-gray-900 mb-4">{guide.title}</h1>
        
        <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
          <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
            {guide.category}
          </span>
          <span>By {guide.author?.username || 'Unknown'}</span>
          <span>{guide.viewCount} views</span>
          <span>
            {new Date(guide.createdAt).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })}
          </span>
          {guide.updatedAt !== guide.createdAt && (
            <span>
              Updated {new Date(guide.updatedAt).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </span>
          )}
          {!guide.isPublished && (
            <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full">
              Draft
            </span>
          )}
        </div>

        {guide.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-4">
            {guide.tags.map((tag: any, index: number) => (
              <span
                key={index}
                className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-sm"
              >
                #{tag}
              </span>
            ))}
          </div>
        )}
      </div>

      {/* Content */}
      <div className="px-6 py-6">
        <div className="prose prose-lg max-w-none">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeHighlight, rehypeRaw]}
            components={{
              code: ({node, inline, className, children, ...props}) => {
                const match = /language-(\w+)/.exec(className || '');
                return !inline && match ? (
                  <pre className={`${className} bg-gray-900 text-white p-4 rounded-lg overflow-x-auto`} {...props}>
                    <code>{children}</code>
                  </pre>
                ) : (
                  <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono" {...props}>
                    {children}
                  </code>
                );
              },
              h1: ({children}) => <h1 className="text-3xl font-bold mb-6 mt-8 text-gray-900 first:mt-0">{children}</h1>,
              h2: ({children}) => <h2 className="text-2xl font-bold mb-4 mt-8 text-gray-900">{children}</h2>,
              h3: ({children}) => <h3 className="text-xl font-bold mb-3 mt-6 text-gray-900">{children}</h3>,
              h4: ({children}) => <h4 className="text-lg font-bold mb-2 mt-4 text-gray-900">{children}</h4>,
              p: ({children}) => <p className="mb-4 text-gray-700 leading-relaxed">{children}</p>,
              ul: ({children}) => <ul className="mb-4 list-disc list-inside text-gray-700 space-y-1">{children}</ul>,
              ol: ({children}) => <ol className="mb-4 list-decimal list-inside text-gray-700 space-y-1">{children}</ol>,
              li: ({children}) => <li className="mb-1">{children}</li>,
              blockquote: ({children}) => (
                <blockquote className="border-l-4 border-blue-500 pl-6 mb-4 italic text-gray-600 bg-blue-50 py-4 rounded-r-lg">
                  {children}
                </blockquote>
              ),
              a: ({children, href}) => (
                <a 
                  href={href} 
                  className="text-blue-600 hover:text-blue-800 underline decoration-blue-600/30 hover:decoration-blue-800" 
                  target="_blank" 
                  rel="noopener noreferrer"
                >
                  {children}
                </a>
              ),
              table: ({children}) => (
                <div className="overflow-x-auto mb-4">
                  <table className="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg">
                    {children}
                  </table>
                </div>
              ),
              thead: ({children}) => <thead className="bg-gray-50">{children}</thead>,
              tbody: ({children}) => <tbody className="bg-white divide-y divide-gray-200">{children}</tbody>,
              tr: ({children}) => <tr>{children}</tr>,
              th: ({children}) => <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{children}</th>,
              td: ({children}) => <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{children}</td>,
              hr: () => <hr className="my-8 border-gray-200" />,
              img: ({src, alt}) => (
                <img 
                  src={src} 
                  alt={alt} 
                  className="max-w-full h-auto rounded-lg shadow-md my-4" 
                />
              ),
            }}
          >
            {guide.content}
          </ReactMarkdown>
        </div>
      </div>
    </div>
  );
};