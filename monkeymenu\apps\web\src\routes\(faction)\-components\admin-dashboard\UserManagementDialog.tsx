import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	DialogFooter,
	Di<PERSON>Header,
	DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SearchInput } from "@/components/ui/search-input";
import { Textarea } from "@/components/ui/textarea";
import { useUserRoles } from "@/hooks/usePermissions";
import { trpc } from "@/lib/trpc-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Users } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { UserTable } from "./UserTable";
import type { UserManagementDialogProps } from "./types";

export function UserManagementDialog({
	isOpen,
	onClose,
}: UserManagementDialogProps) {
	const [userSearch, setUserSearch] = useState("");
	const [suspendUserId, setSuspendUserId] = useState<string | null>(null);
	const [suspendReason, setSuspendReason] = useState("");
	const [deleteUserId, setDeleteUserId] = useState<string | null>(null);
	const [deleteConfirm, setDeleteConfirm] = useState("");

	const queryClient = useQueryClient();
	const userRoles = useUserRoles();

	// Suspend user mutation
	const suspendUser = useMutation(
		trpc.admin.suspendUser.mutationOptions({
			onSuccess: () => {
				toast.success("User suspended successfully");
				queryClient.invalidateQueries();
				setSuspendUserId(null);
				setSuspendReason("");
			},
			onError: (error) => {
				toast.error(`Failed to suspend user: ${error.message}`);
			},
		}),
	);

	// Restore user mutation
	const restoreUser = useMutation(
		trpc.admin.restoreUser.mutationOptions({
			onSuccess: () => {
				toast.success("User restored successfully");
				queryClient.invalidateQueries();
			},
			onError: (error) => {
				toast.error(`Failed to restore user: ${error.message}`);
			},
		}),
	);

	// Delete user mutation
	const deleteUser = useMutation(
		trpc.admin.deleteUser.mutationOptions({
			onSuccess: () => {
				toast.success("User deleted successfully");
				queryClient.invalidateQueries();
				setDeleteUserId(null);
				setDeleteConfirm("");
			},
			onError: (error) => {
				toast.error(`Failed to delete user: ${error.message}`);
			},
		}),
	);

	// Recheck user API key mutation
	const recheckUserApiKey = useMutation(
		trpc.admin.recheckUserApiKey.mutationOptions({
			onSuccess: (data) => {
				if (data.success) {
					toast.success(data.message);
				} else {
					toast.warning(data.message);
				}
				queryClient.invalidateQueries();
			},
			onError: (error) => {
				toast.error(`Failed to recheck API key: ${error.message}`);
			},
		}),
	);

	return (
		<>
			<Dialog open={isOpen} onOpenChange={onClose}>
				<DialogContent className="max-h-[90vh] w-full max-w-4xl overflow-y-auto px-4 sm:px-6">
					<DialogHeader>
						<DialogTitle className="flex items-center gap-2">
							<Users className="h-5 w-5" />
							User Management
						</DialogTitle>
						<DialogDescription>
							Manage user access and account status
						</DialogDescription>
					</DialogHeader>

					<div className="min-w-0 space-y-4">
						<SearchInput
							placeholder="Search users by name, email, or role..."
							value={userSearch}
							onChange={(e) => setUserSearch(e.target.value)}
							onClear={() => setUserSearch("")}
							className="w-full max-w-md"
						/>

						<UserTable
							userRoles={userRoles.data}
							isLoading={userRoles.isLoading}
							searchQuery={userSearch}
							onSuspendUser={(userId) => setSuspendUserId(userId)}
							onRestoreUser={(userId) => restoreUser.mutate({ userId })}
							onDeleteUser={(userId) => setDeleteUserId(userId)}
							onRecheckApiKey={(userId) => recheckUserApiKey.mutate({ userId })}
							isRecheckingApiKey={recheckUserApiKey.isPending}
							isRestoring={restoreUser.isPending}
						/>
					</div>
				</DialogContent>
			</Dialog>

			{/* Suspend User Dialog */}
			<Dialog
				open={!!suspendUserId}
				onOpenChange={() => setSuspendUserId(null)}
			>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Suspend User</DialogTitle>
						<DialogDescription>
							Provide a reason for suspension. This action can be reversed.
						</DialogDescription>
					</DialogHeader>
					<div className="space-y-4">
						<div>
							<Label htmlFor="suspend-reason">Suspension Reason</Label>
							<Textarea
								id="suspend-reason"
								value={suspendReason}
								onChange={(e) => setSuspendReason(e.target.value)}
								placeholder="Explain why this user is being suspended..."
							/>
						</div>
					</div>
					<DialogFooter>
						<Button variant="outline" onClick={() => setSuspendUserId(null)}>
							Cancel
						</Button>
						<Button
							variant="destructive"
							onClick={() => {
								if (suspendUserId && suspendReason.trim()) {
									suspendUser.mutate({
										userId: suspendUserId,
										reason: suspendReason.trim(),
									});
								}
							}}
							disabled={!suspendReason.trim() || suspendUser.isPending}
						>
							Suspend User
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Delete User Dialog */}
			<Dialog open={!!deleteUserId} onOpenChange={() => setDeleteUserId(null)}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Delete User</DialogTitle>
						<DialogDescription>
							This action cannot be undone. Type "DELETE" to confirm.
						</DialogDescription>
					</DialogHeader>
					<div className="space-y-4">
						<div>
							<Label htmlFor="delete-confirm">Type "DELETE" to confirm</Label>
							<Input
								id="delete-confirm"
								value={deleteConfirm}
								onChange={(e) => setDeleteConfirm(e.target.value)}
								placeholder="DELETE"
							/>
						</div>
					</div>
					<DialogFooter>
						<Button variant="outline" onClick={() => setDeleteUserId(null)}>
							Cancel
						</Button>
						<Button
							variant="destructive"
							onClick={() => {
								if (deleteUserId && deleteConfirm === "DELETE") {
									deleteUser.mutate({
										userId: deleteUserId,
										reason: "Deleted by admin via dashboard",
									});
								}
							}}
							disabled={deleteConfirm !== "DELETE" || deleteUser.isPending}
						>
							Delete User
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
}
