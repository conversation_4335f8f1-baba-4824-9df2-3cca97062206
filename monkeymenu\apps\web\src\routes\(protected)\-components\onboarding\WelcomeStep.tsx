import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { ArrowR<PERSON>, Zap } from "lucide-react";
import type { StepComponentProps } from "./types";

interface WelcomeStepProps extends StepComponentProps {
	isDiscordLinked: boolean;
}

export function WelcomeStep({ onNextStep, isDiscordLinked }: WelcomeStepProps) {
	return (
		<Card>
			<CardContent className="space-y-6 p-8">
				{/* Header */}
				<div className="space-y-3 text-center">
					<div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
						<Zap className="h-8 w-8 text-primary" />
					</div>
					<h2 className="font-bold text-2xl tracking-tight">
						You're Almost Ready
					</h2>
					<p className="mx-auto max-w-md text-muted-foreground leading-relaxed">
						{isDiscordLinked
							? "Great! Your Discord account is connected. Let's finish setting up your account."
							: "We just need to configure a few things to get your faction tools ready."}
					</p>
				</div>

				{/* Setup Info */}
				<div className="rounded-lg bg-muted/50 p-4">
					<div className="flex items-center justify-center gap-6 text-sm">
						<div className="text-center">
							<div className="font-medium">2 min</div>
							<div className="text-muted-foreground text-xs">Setup time</div>
						</div>
						<div className="text-center">
							<div className="font-medium">5 steps</div>
							<div className="text-muted-foreground text-xs">Quick setup</div>
						</div>
					</div>
				</div>
			</CardContent>

			<CardFooter className="p-8 pt-0">
				<Button onClick={onNextStep} className="w-full" size="lg">
					Get Started
					<ArrowRight className="ml-2 h-4 w-4" />
				</Button>
			</CardFooter>
		</Card>
	);
}
