// Simple interface that matches what we actually use from the profile response
interface UserData {
	id: string;
	name: string | null;
	email: string;
	image: string | null;
	createdAt: string;
}

interface TornUserData {
	tornApiKey: string | null;
	tornApiKeyVerified: boolean | null;
	tornUserId: string | null;
	accessSuspended: boolean | null;
}

export interface ProfileData {
	user: UserData;
	tornUser: TornUserData | null;
}

export interface ProfileComponentProps {
	profile?: ProfileData | null;
	isLoading?: boolean;
}

export interface EditableProfileProps {
	isEditing: boolean;
	setIsEditing: (editing: boolean) => void;
	profile?: ProfileData | null;
	onProfileUpdate?: () => void;
}

export interface DiscordIntegrationProps {
	isLinked: boolean;
	isLinking: boolean;
	onConnect: () => Promise<void>;
	onSync: () => Promise<void>;
	onJoinServer: () => void;
}

export interface ApiKeyManagementProps {
	hasApiKey: boolean;
	isVerified: boolean;
	isSuspended: boolean;
	tornUserId?: number;
	onApiKeyUpdate?: () => void;
}
