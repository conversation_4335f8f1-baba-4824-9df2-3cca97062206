import { useState, useMemo } from 'react';
import { useQuery, useAction } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { WarCard } from './WarCard';
import { CreateWarDialog } from './CreateWarDialog';
import { WarDialogContent } from './WarDialogContent';
import { ErrorBoundary } from './ErrorBoundary';
import { usePermissions } from '../../hooks/usePermissions';
import { Id } from '../../../convex/_generated/dataModel';

export function WarsList() {
  const { canViewWars, canCreateWars } = usePermissions();
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'ended'>('all');
  const [selectedWarId, setSelectedWarId] = useState<Id<"wars"> | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedWar, setSelectedWar] = useState<{war: any; tornWarId: number} | null>(null);
  
  // Action to fetch wars
  const fetchWars = useAction(api.wars.fetchWarsFromTornAPI);

  // Get wars based on filter
  const allWars = useQuery(api.wars.getWars, 
    statusFilter === 'all' ? {} : { status: statusFilter }
  );

  // Get active wars for quick stats
  const activeWars = useQuery(api.wars.getActiveWars);

  // Get war details for selected war
  const selectedWarDetails = useQuery(
    api.wars.getWarWithAttacks,
    selectedWarId ? { warId: selectedWarId } : "skip"
  );

  const wars = useMemo(() => {
    return allWars || [];
  }, [allWars]);

  const handleViewDetails = (warId: Id<"wars">) => {
    setSelectedWarId(selectedWarId === warId ? null : warId);
  };

  const handleViewAdvancedAnalytics = (war: any) => {
    console.log('Full war object:', war);
    console.log('War start time:', new Date(war.startTime));
    console.log('War end time:', war.endTime ? new Date(war.endTime) : 'Ongoing');
    console.log('War age in days:', (Date.now() - war.startTime) / (1000 * 60 * 60 * 24));
    
    // Check if the war has a tornWarId from the Torn API
    if (!war.tornWarId) {
      alert('This war does not have a Torn war ID and cannot be analyzed. Please ensure wars are created from the Torn API.');
      return;
    }
    
    // Check war age and warn user
    const warAgeInDays = (Date.now() - war.startTime) / (1000 * 60 * 60 * 24);
    if (warAgeInDays > 30) {
      const proceed = confirm(`This war is ${Math.round(warAgeInDays)} days old. Very old wars may not be accessible through the Torn API. Continue anyway?`);
      if (!proceed) return;
    }
    
    setSelectedWar({ war, tornWarId: war.tornWarId });
  };

  // Handle war fetching
  const handleFetchWars = async () => {
    console.log('Fetch Wars button clicked - starting war fetch...');
    try {
      console.log('Calling fetchWars action...');
      const result = await fetchWars({});
      console.log('War fetch result:', result);
      
      if (result.success) {
        alert(`Wars fetched successfully! ${result.message}`);
      }
    } catch (error) {
      console.error('Failed to fetch wars:', error);
      alert('Failed to fetch wars: ' + (error?.message || error));
    }
  };

  if (!canViewWars()) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Restricted</h2>
          <p className="text-gray-600">You don't have permission to view wars.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">⚔️ War Reports</h1>
            <p className="text-gray-600">Track faction wars, performance, and statistics</p>
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={handleFetchWars}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            >
              📥 Fetch Wars
            </button>
            {canCreateWars() && (
              <button
                onClick={() => setIsCreateDialogOpen(true)}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors"
              >
                ⚔️ Start New War
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-green-600 text-sm">⚔️</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">Active Wars</p>
              <p className="text-2xl font-bold text-green-600">
                {activeWars?.length || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 text-sm">📊</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">Total Wars</p>
              <p className="text-2xl font-bold text-blue-600">
                {wars.length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <span className="text-purple-600 text-sm">🏆</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">Win Rate</p>
              <p className="text-2xl font-bold text-purple-600">
                {wars.length > 0 
                  ? Math.round((wars.filter(w => w.status === 'ended' && w.ourScore > w.enemyScore).length / wars.filter(w => w.status === 'ended').length) * 100) || 0
                  : 0}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">🔍 Filter Wars</h3>
          <div className="flex space-x-2">
            {(['all', 'active', 'ended'] as const).map((status) => (
              <button
                key={status}
                onClick={() => setStatusFilter(status)}
                className={`px-4 py-2 text-sm rounded-md transition-colors ${
                  statusFilter === status
                    ? 'bg-blue-100 text-blue-800 border border-blue-200'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {status === 'all' ? 'All Wars' : 
                 status === 'active' ? '⚔️ Active' :
                 '🏁 Ended'}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Wars List */}
      {allWars === undefined ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : wars.length > 0 ? (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {wars.map((war) => (
              <ErrorBoundary key={war._id}>
                <WarCard
                  war={war}
                  onViewDetails={handleViewDetails}
                  onViewAdvancedAnalytics={handleViewAdvancedAnalytics}
                />
              </ErrorBoundary>
            ))}
          </div>

          {/* War Details Modal */}
          {selectedWarDetails && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
              <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl font-bold text-gray-900">
                      War Details: {selectedWarDetails.war.factionName} vs {selectedWarDetails.war.enemyFactionName}
                    </h2>
                    <button
                      onClick={() => setSelectedWarId(null)}
                      className="text-gray-400 hover:text-gray-600 text-2xl"
                    >
                      ×
                    </button>
                  </div>

                  {/* War Stats */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{selectedWarDetails.war.ourScore}</div>
                      <div className="text-sm text-gray-600">Our Hits</div>
                    </div>
                    <div className="text-center p-4 bg-red-50 rounded-lg">
                      <div className="text-2xl font-bold text-red-600">{selectedWarDetails.war.enemyScore}</div>
                      <div className="text-sm text-gray-600">Enemy Hits</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{selectedWarDetails.attacks.length}</div>
                      <div className="text-sm text-gray-600">Total Attacks</div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">
                        {selectedWarDetails.attacks.reduce((sum, a) => sum + a.respect, 0).toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-600">Total Respect</div>
                    </div>
                  </div>

                  {/* Recent Attacks */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Attacks</h3>
                    <div className="max-h-96 overflow-y-auto">
                      <div className="space-y-2">
                        {selectedWarDetails.attacks.slice(0, 50).map((attack, index) => (
                          <div key={index} className={`p-3 rounded-lg border ${
                            attack.result === 'win' ? 'bg-green-50 border-green-200' :
                            attack.result === 'loss' ? 'bg-red-50 border-red-200' :
                            'bg-gray-50 border-gray-200'
                          }`}>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <span className={
                                  attack.result === 'win' ? 'text-green-600' :
                                  attack.result === 'loss' ? 'text-red-600' :
                                  'text-gray-600'
                                }>
                                  {attack.result === 'win' ? '✅' :
                                   attack.result === 'loss' ? '❌' :
                                   attack.result === 'timeout' ? '⏰' : '🏃'}
                                </span>
                                <span className="font-medium">{attack.attackerName}</span>
                                <span className="text-gray-500">vs</span>
                                <span>{attack.defenderName}</span>
                              </div>
                              <div className="flex items-center space-x-2 text-sm text-gray-600">
                                <span className="font-medium">{attack.respect.toLocaleString()} respect</span>
                                <span>•</span>
                                <span>{new Date(attack.timestamp).toLocaleTimeString()}</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">⚔️</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No wars found
          </h3>
          <p className="text-gray-600 mb-4">
            {statusFilter === 'active' 
              ? "No active wars at the moment."
              : statusFilter === 'ended'
              ? "No ended wars to display."
              : "No wars have been recorded yet."
            }
          </p>
        </div>
      )}

      {/* Create War Dialog */}
      <CreateWarDialog
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
      />

      {/* Advanced War Analytics Dialog */}
      {selectedWar && (
        <WarDialogContent
          war={selectedWar.war}
          warId={selectedWar.tornWarId}
          onClose={() => setSelectedWar(null)}
        />
      )}
    </div>
  );
}