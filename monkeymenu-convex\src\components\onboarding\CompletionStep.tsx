import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { CheckCircle, ArrowRight } from "lucide-react";
import { useNavigate } from "@tanstack/react-router";
import type { StepComponentProps } from "./types";

interface CompletionStepProps extends StepComponentProps {
  isDiscordLinked?: boolean;
}

export function CompletionStep({ 
  onPrevStep, 
  isDiscordLinked = false 
}: CompletionStepProps) {
  const navigate = useNavigate();

  const handleFinish = () => {
    navigate({ to: "/dashboard" });
  };

  return (
    <Card>
      <CardContent className="space-y-6 p-8">
        <div className="space-y-2 text-center">
          <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mb-4">
            <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
          </div>
          <h2 className="font-bold text-2xl tracking-tight">You're All Set!</h2>
          <p className="mx-auto max-w-md text-muted-foreground leading-relaxed">
            Your MonkeyMenu account has been configured successfully. You can now
            access all the features available to your faction role.
          </p>
        </div>

        <div className="space-y-4">
          <div className="text-center">
            <h3 className="font-semibold text-lg mb-3">What's Available:</h3>
            <div className="space-y-2 text-sm text-muted-foreground">
              <div className="flex items-center justify-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                Faction Banking & Withdrawals
              </div>
              <div className="flex items-center justify-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                Target Finder & Analytics
              </div>
              <div className="flex items-center justify-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                Announcements & Updates
              </div>
              {isDiscordLinked && (
                <div className="flex items-center justify-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  Discord Notifications
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>

      <CardFooter className="flex gap-3 p-8 pt-0">
        <Button
          type="button"
          variant="outline"
          onClick={onPrevStep}
          className="flex-1"
        >
          Back
        </Button>
        <Button
          onClick={handleFinish}
          className="flex-1"
        >
          Go to Dashboard
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  )
}