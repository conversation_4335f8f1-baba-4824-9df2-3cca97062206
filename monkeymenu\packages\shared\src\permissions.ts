// Shared permission constants and types for MonkeyMenu monorepo

// Faction role hierarchy (higher number = higher rank)
export const FACTION_ROLES = {
	SYSTEM_ADMIN: {
		name: "system-admin",
		displayName: "System Administrator",
		level: 10,
	},
	LEADER: { name: "leader", displayName: "Leader", level: 9 },
	CO_LEADER: { name: "co-leader", displayName: "Co-leader", level: 8 },
	MONKEY_MENTOR: {
		name: "monkey-mentor",
		displayName: "Monkey Mentor",
		level: 7,
	},
	GORILLA: { name: "gorilla", displayName: "Gorilla", level: 6 },
	PRIMATE_LIAISON: {
		name: "primate-liaison",
		displayName: "Primate Liaison",
		level: 5,
	},
	BABOON: { name: "baboon", displayName: "Baboon", level: 4 },
	ORANGUTAN: { name: "orangutan", displayName: "Orangutan", level: 3 },
	CHIMPANZEE: { name: "chimpanzee", displayName: "Chimpanzee", level: 2 },
	RECRUIT: { name: "recruit", displayName: "Recruit", level: 1 },
} as const;

// Permission constants
export const PERMISSIONS = {
	// Guide permissions
	GUIDES_VIEW: {
		name: "guides.view",
		displayName: "View Guides",
		category: "guides",
	},
	GUIDES_MANAGE: {
		name: "guides.manage",
		displayName: "Manage Guides",
		category: "guides",
	},

	// Faction Dashboard permissions
	DASHBOARD_VIEW: {
		name: "dashboard.view",
		displayName: "View Faction Dashboard",
		category: "dashboard",
	},

	// Announcement permissions
	ANNOUNCEMENTS_VIEW: {
		name: "announcements.view",
		displayName: "View Announcements",
		category: "announcements",
	},
	ANNOUNCEMENTS_MANAGE: {
		name: "announcements.manage",
		displayName: "Manage Announcements",
		category: "announcements",
	},

	// Administration permissions
	ADMIN_VIEW: {
		name: "admin.view",
		displayName: "View Admin Dashboard",
		category: "admin",
	},
	ADMIN_SUSPEND_USERS: {
		name: "admin.users.suspend",
		displayName: "Suspend/Restore Users",
		category: "admin",
	},
	ADMIN_RECHECK_API_KEYS: {
		name: "admin.users.recheck",
		displayName: "Recheck User API Keys",
		category: "admin",
	},
	ADMIN_DELETE_USER: {
		name: "admin.users.delete",
		displayName: "Delete Users",
		category: "admin",
	},

	// Discord command permissions
	DISCORD_MANAGE_VERIFICATION: {
		name: "discord.manage.verification",
		displayName: "Manage Discord Verification",
		category: "discord",
	},

	// Banking permissions
	BANKING_VIEW: {
		name: "banking.view",
		displayName: "View Banking",
		category: "banking",
	},
	BANKING_REQUEST: {
		name: "banking.request",
		displayName: "Create Withdrawal Requests",
		category: "banking",
	},
	BANKING_MANAGE_REQUESTS: {
		name: "banking.requests.manage",
		displayName: "Manage Withdrawal Requests",
		category: "banking",
	},

	// Target Finder permissions
	TARGET_FINDER_VIEW: {
		name: "target.finder.view",
		displayName: "View Target Finder",
		category: "target_finder",
	},
	TARGET_FINDER_MANAGE_SHARED_LISTS: {
		name: "target.finder.manage.shared_lists",
		displayName: "Manage Shared Target Lists",
		category: "target_finder",
	},

	// War permissions
	WARS_VIEW: {
		name: "wars.view",
		displayName: "View War Reports",
		category: "wars",
	},
} as const;

// Permission strings for easy use in frontend
export const PERMISSION_NAMES = {
	// Guides permissions
	GUIDES_VIEW: PERMISSIONS.GUIDES_VIEW.name,
	GUIDES_MANAGE: PERMISSIONS.GUIDES_MANAGE.name,

	// Faction Dashboard permissions
	DASHBOARD_VIEW: PERMISSIONS.DASHBOARD_VIEW.name,

	// Announcement permissions
	ANNOUNCEMENTS_VIEW: PERMISSIONS.ANNOUNCEMENTS_VIEW.name,
	ANNOUNCEMENTS_MANAGE: PERMISSIONS.ANNOUNCEMENTS_MANAGE.name,

	// Administration permissions
	ADMIN_VIEW: PERMISSIONS.ADMIN_VIEW.name,
	ADMIN_SUSPEND_USERS: PERMISSIONS.ADMIN_SUSPEND_USERS.name,
	ADMIN_RECHECK_API_KEYS: PERMISSIONS.ADMIN_RECHECK_API_KEYS.name,
	ADMIN_DELETE_USER: PERMISSIONS.ADMIN_DELETE_USER.name,

	// Discord command permissions
	DISCORD_MANAGE_VERIFICATION: PERMISSIONS.DISCORD_MANAGE_VERIFICATION.name,

	// Banking permissions
	BANKING_VIEW: PERMISSIONS.BANKING_VIEW.name,
	BANKING_REQUEST: PERMISSIONS.BANKING_REQUEST.name,
	BANKING_MANAGE_REQUESTS: PERMISSIONS.BANKING_MANAGE_REQUESTS.name,

	// Target Finder permissions
	TARGET_FINDER_VIEW: PERMISSIONS.TARGET_FINDER_VIEW.name,
	TARGET_FINDER_MANAGE_SHARED_LISTS:
		PERMISSIONS.TARGET_FINDER_MANAGE_SHARED_LISTS.name,

	// War permissions
	WARS_VIEW: PERMISSIONS.WARS_VIEW.name,
} as const;

// Role levels for easy access
export const ROLE_LEVELS = {
	SYSTEM_ADMIN: FACTION_ROLES.SYSTEM_ADMIN.level,
	LEADER: FACTION_ROLES.LEADER.level,
	CO_LEADER: FACTION_ROLES.CO_LEADER.level,
	MONKEY_MENTOR: FACTION_ROLES.MONKEY_MENTOR.level,
	GORILLA: FACTION_ROLES.GORILLA.level,
	PRIMATE_LIAISON: FACTION_ROLES.PRIMATE_LIAISON.level,
	BABOON: FACTION_ROLES.BABOON.level,
	ORANGUTAN: FACTION_ROLES.ORANGUTAN.level,
	CHIMPANZEE: FACTION_ROLES.CHIMPANZEE.level,
	RECRUIT: FACTION_ROLES.RECRUIT.level,
} as const;

// Types
export type PermissionName = keyof typeof PERMISSIONS;
export type RoleName = keyof typeof FACTION_ROLES;
export type PermissionString = (typeof PERMISSIONS)[PermissionName]["name"];

// User permission context type
export interface UserPermissionContext {
	userId: string | null; // Allow null for unauthenticated users
	roleId?: number | null;
	roleName: string | null;
	roleLevel: number;
	permissions: string[];
}

// Utility functions for permission checking
export function hasPermission(
	userPermissions: string[],
	requiredPermission: string,
): boolean {
	return userPermissions.includes(requiredPermission);
}

export function hasMinimumRole(
	userRoleLevel: number,
	minimumLevel: number,
): boolean {
	return userRoleLevel >= minimumLevel;
}

export function canPerformAction(
	userPermissions: string[],
	requiredPermission: string,
	ownerId?: string,
	userId?: string,
): boolean {
	// Check if user has the permission
	if (hasPermission(userPermissions, requiredPermission)) {
		return true;
	}

	// For "own" permissions, check if user is the owner
	if (requiredPermission.includes(".own") && ownerId && userId) {
		return ownerId === userId;
	}

	return false;
}
