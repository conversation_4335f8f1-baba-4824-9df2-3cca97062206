import { HasPermission } from "@/components/permissions/PermissionGuards";
import { But<PERSON> } from "@/components/ui/button";
import { PERMISSION_NAMES } from "@monkeymenu/shared";
import { Plus } from "lucide-react";

interface GuideHeaderProps {
	onCreateClick: () => void;
}

export function GuideHeader({ onCreateClick }: GuideHeaderProps) {
	return (
		<div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
			<div>
				<h1 className="font-bold text-3xl text-foreground">
					📚 Faction Guides
				</h1>
				<p className="text-muted-foreground">
					Knowledge base and tutorials for faction members
				</p>
			</div>
			<div className="flex gap-2">
				<HasPermission permission={PERMISSION_NAMES.GUIDES_MANAGE}>
					<Button onClick={onCreateClick} className="gap-2">
						<Plus className="h-4 w-4" />
						Create Guide
					</Button>
				</HasPermission>
			</div>
		</div>
	);
}
