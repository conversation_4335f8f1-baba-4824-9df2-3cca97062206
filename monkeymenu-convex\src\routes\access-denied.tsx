import { createFileRoute, useSearch, Link } from '@tanstack/react-router';
import { useSession } from '../hooks/useSession';

interface AccessDeniedSearch {
  reason?: 'faction' | 'suspended' | 'api-key' | 'permission' | 'role';
  details?: string;
}

export const Route = createFileRoute('/access-denied')({
  component: AccessDenied,
  validateSearch: (search): AccessDeniedSearch => ({
    reason: search.reason as AccessDeniedSearch['reason'],
    details: search.details as string,
  }),
});

function AccessDenied() {
  const { reason, details } = useSearch({ from: '/access-denied' });
  const { convexUser } = useSession();

  const getContent = () => {
    switch (reason) {
      case 'faction':
        return {
          icon: '🏛️',
          title: 'Faction Access Required',
          message: 'This application is exclusively for Menacing Monkeys faction members.',
          details: convexUser?.factionName 
            ? `You are currently in: ${convexUser.factionName}`
            : 'You are not currently in any faction.',
          actions: (
            <div className="space-y-3">
              <p className="text-sm text-gray-600">
                To gain access, you need to:
              </p>
              <ul className="text-sm text-gray-600 list-disc list-inside space-y-1">
                <li>Join the Menacing Monkeys faction in Torn</li>
                <li>Update your profile with the correct faction information</li>
                <li>Contact a faction leader if you believe this is an error</li>
              </ul>
            </div>
          )
        };

      case 'suspended':
        return {
          icon: '🚫',
          title: 'Account Suspended',
          message: 'Your access to MonkeyMenu has been temporarily suspended.',
          details: details || 'No additional details provided.',
          actions: (
            <div className="space-y-3">
              <p className="text-sm text-gray-600">
                If you believe this is an error, please contact a faction administrator.
              </p>
            </div>
          )
        };

      case 'api-key':
        return {
          icon: '🔑',
          title: 'API Key Required',
          message: 'Your Torn API key needs to be verified to access this application.',
          details: 'A valid and verified Torn API key is required for all features.',
          actions: (
            <div className="space-y-3">
              <Link 
                to="/onboarding"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Complete Setup
              </Link>
              <p className="text-sm text-gray-600">
                You'll be guided through the API key setup process.
              </p>
            </div>
          )
        };

      case 'permission':
        return {
          icon: '🔒',
          title: 'Insufficient Permissions',
          message: 'You don\'t have the required permissions to access this feature.',
          details: details || 'Contact an administrator if you need access to this feature.',
          actions: (
            <div className="space-y-3">
              <p className="text-sm text-gray-600">
                Your current permissions:
              </p>
              <div className="bg-gray-100 p-3 rounded-lg">
                <code className="text-xs">
                  {convexUser?.permissions?.join(', ') || 'No permissions assigned'}
                </code>
              </div>
            </div>
          )
        };

      case 'role':
        return {
          icon: '👑',
          title: 'Insufficient Role Level',
          message: 'You need a higher role to access this feature.',
          details: details || 'Contact an administrator if you believe you should have access.',
          actions: (
            <div className="space-y-3">
              <p className="text-sm text-gray-600">
                Your current role: <strong>{convexUser?.role || 'No role assigned'}</strong>
              </p>
              {details && (
                <p className="text-sm text-gray-600">
                  Required role: <strong>{details}</strong>
                </p>
              )}
            </div>
          )
        };

      default:
        return {
          icon: '⚠️',
          title: 'Access Denied',
          message: 'You don\'t have permission to access this resource.',
          details: 'Please contact an administrator if you believe this is an error.',
          actions: (
            <div className="space-y-3">
              <p className="text-sm text-gray-600">
                If you continue to experience issues, please reach out for support.
              </p>
            </div>
          )
        };
    }
  };

  const content = getContent();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-8">
        <div className="text-center">
          <div className="text-6xl mb-4">{content.icon}</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {content.title}
          </h1>
          <p className="text-gray-600 mb-4">
            {content.message}
          </p>
          
          {content.details && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <p className="text-sm text-yellow-800">
                {content.details}
              </p>
            </div>
          )}

          <div className="mb-6">
            {content.actions}
          </div>

          <div className="border-t pt-4">
            <Link 
              to="/dashboard"
              className="text-blue-600 hover:text-blue-800 text-sm transition-colors"
            >
              ← Back to Dashboard
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}