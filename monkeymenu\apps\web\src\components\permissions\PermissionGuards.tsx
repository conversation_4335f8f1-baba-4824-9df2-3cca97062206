import {
	useCanPerformAction,
	useHasMinimumRole,
	useHasPermission,
} from "@/hooks/usePermissions";
import type { ReactNode } from "react";

interface PermissionGuardProps {
	children: ReactNode;
	fallback?: ReactNode;
}

interface PermissionProps extends PermissionGuardProps {
	permission: string;
}

interface RoleProps extends PermissionGuardProps {
	minimumLevel: number;
}

interface OwnershipProps extends PermissionGuardProps {
	ownPermission: string;
	anyPermission: string;
	isOwner: boolean;
}

/**
 * Component that renders children only if user has the specified permission
 */
export function HasPermission({
	children,
	permission,
	fallback = null,
}: PermissionProps) {
	const { hasPermission, isLoading } = useHasPermission(permission);

	if (isLoading) {
		return <>{fallback}</>;
	}

	return hasPermission ? <>{children}</> : <>{fallback}</>;
}

/**
 * Component that renders children only if user has minimum role level
 */
export function HasMinimumRole({
	children,
	minimumLevel,
	fallback = null,
}: RoleProps) {
	const { hasMinimumRole, isLoading } = useHasMinimumRole(minimumLevel);

	if (isLoading) {
		return <>{fallback}</>;
	}

	return hasMinimumRole ? <>{children}</> : <>{fallback}</>;
}

/**
 * Component that renders children only if user can perform action with ownership context
 */
export function CanPerformAction({
	children,
	ownPermission,
	anyPermission,
	isOwner,
	fallback = null,
}: OwnershipProps) {
	const { canPerform, isLoading } = useCanPerformAction(
		ownPermission,
		anyPermission,
		isOwner,
	);

	if (isLoading) {
		return <>{fallback}</>;
	}

	return canPerform ? <>{children}</> : <>{fallback}</>;
}

/**
 * Simple permission-based route protection
 */
interface ProtectedRouteProps extends PermissionGuardProps {
	permission?: string;
	minimumRole?: number;
}

export function ProtectedRoute({
	children,
	permission,
	minimumRole,
	fallback = (
		<div className="p-6 text-center">
			<h2 className="mb-2 font-semibold text-xl">Access Denied</h2>
			<p className="text-muted-foreground">
				You don't have permission to view this content.
			</p>
		</div>
	),
}: ProtectedRouteProps) {
	if (permission) {
		return (
			<HasPermission permission={permission} fallback={fallback}>
				{children}
			</HasPermission>
		);
	}

	if (minimumRole !== undefined && minimumRole !== null) {
		return (
			<HasMinimumRole minimumLevel={minimumRole} fallback={fallback}>
				{children}
			</HasMinimumRole>
		);
	}

	// No protection specified, render children
	return <>{children}</>;
}
