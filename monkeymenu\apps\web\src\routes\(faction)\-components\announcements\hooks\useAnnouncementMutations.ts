import { trpc } from "@/lib/trpc-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export function useAnnouncements() {
	return useQuery({
		...trpc.announcements.getAll.queryOptions(),
		refetchOnWindowFocus: true,
		refetchInterval: 5 * 60 * 1000,
		refetchIntervalInBackground: false,
	});
}

export function useAnnouncementMutations() {
	const queryClient = useQueryClient();

	const createMutation = useMutation(
		trpc.announcements.create.mutationOptions({
			onSuccess: () => {
				queryClient.invalidateQueries({
					queryKey: [["announcements", "getAll"]],
				});
				queryClient.invalidateQueries({
					queryKey: [["announcements", "getById"]],
				});
				toast.success(
					"Announcement created successfully and posted to Discord!",
				);
			},
			onError: (error) => {
				toast.error(`Failed to create announcement: ${error.message}`);
			},
		}),
	);

	const updateMutation = useMutation(
		trpc.announcements.update.mutationOptions({
			onSuccess: () => {
				queryClient.invalidateQueries({
					queryKey: [["announcements", "getAll"]],
				});
				queryClient.invalidateQueries({
					queryKey: [["announcements", "getById"]],
				});
				toast.success("Announcement updated successfully");
			},
			onError: (error) => {
				toast.error(`Failed to update announcement: ${error.message}`);
			},
		}),
	);

	const deleteMutation = useMutation(
		trpc.announcements.delete.mutationOptions({
			onSuccess: () => {
				queryClient.invalidateQueries({
					queryKey: [["announcements", "getAll"]],
				});
				queryClient.invalidateQueries({
					queryKey: [["announcements", "getById"]],
				});
				toast.success("Announcement deleted successfully");
			},
			onError: (error) => {
				toast.error(`Failed to delete announcement: ${error.message}`);
			},
		}),
	);

	return {
		createMutation,
		updateMutation,
		deleteMutation,
	};
}
