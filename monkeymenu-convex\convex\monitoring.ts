import { action, mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { getCurrentUser, requirePermission } from "./lib/permissions";

// Get users with verified API keys for monitoring
export const getUsersWithVerifiedApiKeys = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query("users")
      .filter((q) => 
        q.and(
          q.eq(q.field("isActive"), true),
          q.neq(q.field("tornApiKey"), undefined),
          q.eq(q.field("tornApiKeyVerified"), true),
          q.eq(q.field("accessSuspended"), false)
        )
      )
      .collect();
  },
});

// Update last overdose event timestamp for a user
export const updateLastOverdoseEventTimestamp = mutation({
  args: {
    userId: v.id("users"),
    timestamp: v.number(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.userId, {
      lastOverdoseEventTimestamp: args.timestamp,
    });
  },
});

// Send overdose notification via Discord
export const sendOverdoseNotification = action({
  args: {
    userName: v.string(),
    userTornId: v.string(),
    timestamp: v.number(),
    eventText: v.string(),
  },
  handler: async (ctx, args) => {
    // Format the notification message
    const embed = {
      title: "🚨 Overdose Detected",
      description: `**${args.userName}** [${args.userTornId}] has overdosed!`,
      color: 0xff0000, // Red color for alerts
      fields: [
        {
          name: "Event",
          value: args.eventText,
          inline: false,
        },
        {
          name: "Time",
          value: new Date(args.timestamp * 1000).toLocaleString(),
          inline: true,
        },
        {
          name: "Torn Profile",
          value: `[View Profile](https://www.torn.com/profiles.php?XID=${args.userTornId})`,
          inline: true,
        },
      ],
      timestamp: new Date().toISOString(),
    };

    // Here we would send to Discord channel - for now just log
    console.log(`🚨 OVERDOSE ALERT: ${args.userName} [${args.userTornId}] - ${args.eventText}`);
    
    // TODO: Integrate with actual Discord webhook/bot to send to notifications channel
    // This would use the DISCORD_NOTIFICATIONS_CHANNEL_ID from environment
    
    return { success: true };
  },
});

// Get Torn user events via API
const getTornUserEvents = async (apiKey: string, fromTimestamp?: number): Promise<any> => {
  const url = fromTimestamp 
    ? `https://api.torn.com/user/?selections=events&key=${apiKey}&from=${fromTimestamp}`
    : `https://api.torn.com/user/?selections=events&key=${apiKey}`;
    
  const response = await fetch(url);
  const data = await response.json();
  
  if (data.error) {
    throw new Error(`Torn API Error: ${data.error.error}`);
  }
  
  return data.events || {};
};

// Main overdose detection cron job
export const runOverdoseDetectionCron = action({
  args: {},
  handler: async (ctx) => {
    console.log("🔍 Starting overdose detection task...");
    
    try {
      // Get all users with verified API keys
      const users = await ctx.runQuery("monitoring:getUsersWithVerifiedApiKeys" as any, {});
      
      if (!users || users.length === 0) {
        console.log("No users found with verified API keys for overdose detection.");
        return;
      }

      console.log(`Processing overdose detection for ${users.length} users`);
      
      for (const user of users) {
        if (!user.tornApiKey || !user.tornUserId) continue;
        
        console.log(`Processing user: ${user.name} (Torn ID: ${user.tornUserId})`);
        
        try {
          // Calculate timestamp to fetch from
          const lastTimestamp = user.lastOverdoseEventTimestamp;
          const lastUnixTimestamp = lastTimestamp ? Math.floor(lastTimestamp / 1000) : 0;
          const fetchFromTimestamp = lastUnixTimestamp > 0 ? lastUnixTimestamp + 1 : undefined;
          
          // Fetch events from Torn API
          const events = await getTornUserEvents(user.tornApiKey, fetchFromTimestamp);
          
          if (!events || Object.keys(events).length === 0) {
            console.log(`No new events found for user ${user.name} since ${fetchFromTimestamp || "beginning"}`);
            continue;
          }
          
          let latestEventTimestamp = lastUnixTimestamp;
          
          // Sort events by timestamp
          const sortedEventKeys = Object.keys(events).sort(
            (a, b) => events[a].timestamp - events[b].timestamp
          );
          
          for (const eventId of sortedEventKeys) {
            const event = events[eventId];
            if (!event) continue;
            
            // Update latest timestamp
            if (event.timestamp > latestEventTimestamp) {
              latestEventTimestamp = event.timestamp;
            }
            
            // Check for overdose events
            const eventText = event.event.toLowerCase();
            if (eventText.includes("overdosed")) {
              // Skip if already processed
              if (fetchFromTimestamp && event.timestamp < fetchFromTimestamp) {
                continue;
              }
              
              console.log(`🚨 Overdose detected for ${user.name}: "${event.event}"`);
              
              // Send Discord notification
              await ctx.runAction("monitoring:sendOverdoseNotification" as any, {
                userName: user.name,
                userTornId: user.tornUserId,
                timestamp: event.timestamp,
                eventText: event.event,
              });
            }
          }
          
          // Update last processed timestamp
          if (latestEventTimestamp > lastUnixTimestamp) {
            await ctx.runMutation("monitoring:updateLastOverdoseEventTimestamp" as any, {
              userId: user._id,
              timestamp: latestEventTimestamp * 1000, // Convert to milliseconds
            });
            console.log(`Updated last processed timestamp for ${user.name} to ${latestEventTimestamp}`);
          }
          
        } catch (error) {
          console.error(`Error processing overdose detection for user ${user.name}:`, error);
          
          // Handle API key errors
          if (error instanceof Error && error.message.includes("API key invalid")) {
            console.warn(`API key for user ${user.name} might be invalid.`);
          }
        }
      }
      
      console.log("✅ Overdose detection task completed");
      
    } catch (error) {
      console.error("❌ Error in overdose detection cron:", error);
      throw error;
    }
  },
});

// User verification and role updates cron job
export const runUserVerificationCron = action({
  args: {},
  handler: async (ctx) => {
    console.log("👤 Starting user verification and role updates task...");
    
    try {
      // Get all users with verified API keys that need verification
      const users = await ctx.runQuery("monitoring:getUsersWithVerifiedApiKeys" as any, {});
      
      if (!users || users.length === 0) {
        console.log("No users found with verified API keys for verification.");
        return;
      }

      console.log(`Processing user verification for ${users.length} users`);
      let totalProcessed = 0;
      let totalFailed = 0;
      let totalDiscordSynced = 0;
      
      for (const user of users) {
        if (!user.tornApiKey || !user.tornUserId) continue;
        
        console.log(`Processing user: ${user.name} (Torn ID: ${user.tornUserId})`);
        
        try {
          // Re-verify user's Torn API and update faction info
          const verificationResult = await verifyUserTornInfo(user.tornApiKey, user._id);
          totalProcessed++;
          
          if (verificationResult.success) {
            console.log(`✅ Successfully verified user ${user.name}: ${verificationResult.message}`);
            
            // Sync Discord roles if user has Discord linked
            const discordUser = await ctx.runQuery("discord:getDiscordUser" as any, {
              userId: user._id,
            });
            
            if (discordUser) {
              try {
                const botToken = process.env.DISCORD_BOT_TOKEN;
                const guildId = process.env.DISCORD_GUILD_ID;
                
                if (botToken && guildId) {
                  const isInFaction = Boolean(
                    user.tornApiKeyVerified &&
                    user.tornUserId &&
                    !user.accessSuspended
                  );
                  
                  const roleSync = await ctx.runAction("discord:syncDiscordRoles" as any, {
                    guildId: guildId,
                    userId: discordUser.discordId,
                    roleName: user.role,
                    botToken: botToken,
                    isInFaction: isInFaction,
                  });
                  
                  if (roleSync.success) {
                    totalDiscordSynced++;
                    console.log(`🎮 Discord roles synced for ${user.name}: ${roleSync.message}`);
                  } else {
                    console.warn(`⚠️ Discord role sync failed for ${user.name}: ${roleSync.message}`);
                  }
                }
              } catch (discordError) {
                console.error(`Discord sync error for user ${user.name}:`, discordError);
              }
            }
          } else {
            totalFailed++;
            console.warn(`⚠️ Verification failed for user ${user.name}: ${verificationResult.message}`);
          }
          
        } catch (error) {
          totalFailed++;
          console.error(`Error processing user verification for ${user.name}:`, error);
        }
      }
      
      const discordSyncRate = totalProcessed > 0 ? Math.round((totalDiscordSynced / totalProcessed) * 100) : 0;
      
      console.log(`✅ User verification task completed:
- Users processed: ${totalProcessed}
- Failed: ${totalFailed}
- Discord synced: ${totalDiscordSynced}
- Discord sync rate: ${discordSyncRate}%`);
      
    } catch (error) {
      console.error("❌ Error in user verification cron:", error);
      throw error;
    }
  },
});

// Discord role audit cron job - cleans up users with faction roles who aren't verified
export const runDiscordRoleAuditCron = action({
  args: {},
  handler: async (ctx) => {
    console.log("🛡️ Starting Discord role audit task...");
    
    try {
      const botToken = process.env.DISCORD_BOT_TOKEN;
      const guildId = process.env.DISCORD_GUILD_ID;
      
      if (!botToken || !guildId) {
        console.log("Skipping Discord audit - missing Discord configuration");
        return;
      }
      
      // Get all faction role IDs that we manage
      const DISCORD_ROLE_MAPPING = {
        "system-admin": "1376990512560476202",
        "leader": "1325385710945046679",
        "co-leader": "1330265362033541192",
        "monkey-mentor": "1369953001719988234",
        "gorilla": "1325381427595841556",
        "primate-liaison": "1369953069017600052",
        "baboon": "1325381512421445642",
        "orangutan": "1325381581749354516",
        "chimpanzee": "1325381637969674261",
        "recruit": "1376990392725147688",
      };
      
      const BASIC_FACTION_ROLE_ID = "1324623178806591599";
      const allFactionRoleIds = [...Object.values(DISCORD_ROLE_MAPPING), BASIC_FACTION_ROLE_ID];
      
      // Get all guild members with any faction roles
      const guildResponse = await fetch(`https://discord.com/api/v10/guilds/${guildId}/members?limit=1000`, {
        headers: { Authorization: `Bot ${botToken}` },
      });
      
      if (!guildResponse.ok) {
        throw new Error(`Failed to fetch guild members: ${guildResponse.status}`);
      }
      
      const members = await guildResponse.json() as Array<{ user: { id: string }, roles: string[] }>;
      const membersWithFactionRoles = members.filter(member => 
        member.roles.some(roleId => allFactionRoleIds.includes(roleId))
      );
      
      console.log(`Found ${membersWithFactionRoles.length} members with faction roles to audit`);
      
      let totalAudited = 0;
      let totalRolesRemoved = 0;
      
      for (const member of membersWithFactionRoles) {
        try {
          // Check if this Discord user is linked and verified in our system
          const discordUser = await ctx.runQuery("discord:getDiscordUserByDiscordId" as any, {
            discordId: member.user.id,
          });
          
          if (!discordUser) {
            // User not linked - remove all faction roles
            console.log(`Removing faction roles from unlinked user ${member.user.id}`);
            await removeAllFactionRoles(member.user.id, member.roles, allFactionRoleIds, guildId, botToken);
            totalRolesRemoved++;
            totalAudited++;
            continue;
          }
          
          // Get user info to check verification status
          const user = await ctx.runQuery("users:getUserById" as any, {
            id: discordUser.userId,
          });
          
          if (!user || !user.tornApiKeyVerified || user.accessSuspended) {
            // User not verified or suspended - remove all faction roles
            console.log(`Removing faction roles from unverified/suspended user ${member.user.id} (${user?.name || 'unknown'})`);
            await removeAllFactionRoles(member.user.id, member.roles, allFactionRoleIds, guildId, botToken);
            totalRolesRemoved++;
          }
          
          totalAudited++;
          
        } catch (error) {
          console.error(`Error auditing member ${member.user.id}:`, error);
        }
      }
      
      console.log(`✅ Discord role audit completed:
- Members audited: ${totalAudited}
- Roles removed: ${totalRolesRemoved}`);
      
    } catch (error) {
      console.error("❌ Error in Discord role audit cron:", error);
      throw error;
    }
  },
});

// Helper function to verify user Torn info (simplified version for cron)
const verifyUserTornInfo = async (apiKey: string, userId: string): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await fetch(`https://api.torn.com/user/?selections=basic,profile&key=${apiKey}`);
    const data = await response.json();
    
    if (data.error) {
      return { success: false, message: `Torn API Error: ${data.error.error}` };
    }
    
    // Basic validation - user exists and has required data
    if (data.name && data.player_id) {
      return { success: true, message: `User ${data.name} verified successfully` };
    } else {
      return { success: false, message: "Invalid user data received from Torn API" };
    }
  } catch (error) {
    return { success: false, message: `API request failed: ${error instanceof Error ? error.message : "Unknown error"}` };
  }
};

// Helper function to remove all faction roles from a Discord user
const removeAllFactionRoles = async (
  userId: string, 
  currentRoles: string[], 
  factionRoleIds: string[], 
  guildId: string, 
  botToken: string
): Promise<void> => {
  const rolesToRemove = currentRoles.filter(roleId => factionRoleIds.includes(roleId));
  
  for (const roleId of rolesToRemove) {
    try {
      const response = await fetch(
        `https://discord.com/api/v10/guilds/${guildId}/members/${userId}/roles/${roleId}`,
        {
          method: "DELETE",
          headers: { Authorization: `Bot ${botToken}` },
        }
      );
      
      if (!response.ok) {
        console.error(`Failed to remove role ${roleId} from user ${userId}: ${response.status}`);
      }
    } catch (error) {
      console.error(`Error removing role ${roleId} from user ${userId}:`, error);
    }
  }
};