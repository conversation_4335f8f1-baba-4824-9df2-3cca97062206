import { PERMISSIONS } from "@monkeymenu/shared";
import { and, eq } from "drizzle-orm";
import {
	type CommandContext,
	CommandOptionType,
	SlashCommand,
	type SlashCreator,
} from "slash-create/web";
import { initDb } from "../../../db";
import { user as userTable } from "../../../db/schema/auth";
import { account as accountTable } from "../../../db/schema/auth";
import { withdrawalRequest } from "../../../db/schema/banking";
import { tornUser } from "../../../db/schema/torn";
import { DiscordNotificationService } from "../../../lib/discord-notification-service";
import {
	checkDiscordUserPermission,
	createAccountNotLinkedEmbed,
	createPermissionErrorEmbed,
} from "../../../lib/discord-permissions";
import { pushLiveStoreEvents } from "../../../lib/livestore-events";
import type { AppBindings } from "../../../lib/types";
import { WebSocketService } from "../../../lib/websocket-service";
import { handleWithdrawalRateLimitingAndCleanup } from "../../../routers/banking";

export default class WithdrawCommand extends SlashCommand {
	constructor(creator: SlashCreator) {
		super(creator, {
			name: "withdraw",
			description: "Request a withdrawal from the faction bank",
			options: [
				{
					type: CommandOptionType.INTEGER,
					name: "amount",
					description: "The amount you want to withdraw",
					required: true,
					min_value: 1,
				},
			],
		});
	}

	async run(ctx: CommandContext) {
		const amount = ctx.options.amount as number;
		const discordUserId = ctx.user.id;
		const discordUserTag = `${ctx.user.username}#${ctx.user.discriminator}`;

		// Initial processing embed
		const processingEmbed = {
			color: 0xffa500, // Orange
			title: "🔄 Processing Withdrawal Request",
			description: "Please wait while we process your request...",
			fields: [
				{
					name: "💰 Amount",
					value: `$${amount.toLocaleString()}`,
					inline: true,
				},
				{
					name: "👤 Requested by",
					value: discordUserTag,
					inline: true,
				},
				{
					name: "⏰ Status",
					value: "🔄 Processing...",
					inline: true,
				},
			],
			footer: {
				text: "MonkeyMenu Banking System",
			},
			timestamp: new Date().toISOString(),
		};

		// Send initial response immediately
		await ctx.send({
			embeds: [processingEmbed],
			ephemeral: true,
		});

		try {
			// Get environment from creator options
			const env = (this.creator.options as { env?: AppBindings["Bindings"] })
				.env;
			if (!env) {
				throw new Error("Environment not available in creator options");
			}
			const db = initDb(env);

			// Check permissions first - this includes account linking check
			const permissionCheck = await checkDiscordUserPermission(
				db,
				discordUserId,
				PERMISSIONS.BANKING_REQUEST.name,
			);

			if (!permissionCheck.isLinked) {
				return await ctx.editOriginal({
					embeds: [createAccountNotLinkedEmbed(discordUserTag)],
				});
			}

			if (!permissionCheck.hasPermission) {
				return await ctx.editOriginal({
					embeds: [
						createPermissionErrorEmbed(
							discordUserTag,
							PERMISSIONS.BANKING_REQUEST.name,
							"Chimpanzee or higher",
						),
					],
				});
			}

			// Get additional user data needed for withdrawal
			const userAccount = await db
				.select({
					userId: userTable.id,
					tornUserId: tornUser.id,
					tornApiKeyVerified: tornUser.tornApiKeyVerified,
				})
				.from(accountTable)
				.innerJoin(userTable, eq(accountTable.userId, userTable.id))
				.leftJoin(tornUser, eq(userTable.id, tornUser.id))
				.where(
					and(
						eq(accountTable.providerId, "discord"),
						eq(accountTable.accountId, discordUserId),
					),
				)
				.get();

			// This should not happen since we already checked permissions, but safety check
			if (!userAccount) {
				return await ctx.editOriginal({
					embeds: [createAccountNotLinkedEmbed(discordUserTag)],
				});
			}

			if (!userAccount.tornApiKeyVerified) {
				// Update embed with API key error
				const apiKeyEmbed = {
					color: 0xff8c00, // Dark orange
					title: "⚠️ API Key Not Verified",
					description:
						"Your Torn API key needs to be verified before you can make withdrawals.",
					fields: [
						{
							name: "💰 Requested Amount",
							value: `$${amount.toLocaleString()}`,
							inline: true,
						},
						{
							name: "⚠️ Status",
							value: "API Key Required",
							inline: true,
						},
						{
							name: "📋 Next Steps",
							value:
								"Visit [MonkeyMenu Settings](https://monkeymenu.app/settings) to verify your API key",
							inline: false,
						},
					],
					footer: {
						text: "MonkeyMenu Banking System",
					},
					timestamp: new Date().toISOString(),
				};

				return await ctx.editOriginal({
					embeds: [apiKeyEmbed],
				});
			}

			// Handle rate limiting and cleanup (same as website)
			try {
				await handleWithdrawalRateLimitingAndCleanup(db, userAccount.userId);
			} catch (error) {
				// Rate limiting error
				const rateLimitEmbed = {
					color: 0xff4444, // Red
					title: "⏱️ Rate Limit Exceeded",
					description: "You are making withdrawal requests too frequently.",
					fields: [
						{
							name: "💰 Requested Amount",
							value: `$${amount.toLocaleString()}`,
							inline: true,
						},
						{
							name: "⏱️ Status",
							value: "Rate Limited",
							inline: true,
						},
						{
							name: "📋 Error Details",
							value:
								error instanceof Error ? error.message : "Rate limit exceeded",
							inline: false,
						},
						{
							name: "💡 Solution",
							value: "Please wait before making another withdrawal request.",
							inline: false,
						},
					],
					footer: {
						text: "MonkeyMenu Banking System",
					},
					timestamp: new Date().toISOString(),
				};

				return await ctx.editOriginal({
					embeds: [rateLimitEmbed],
				});
			}

			// Create withdrawal request
			const requestId = crypto.randomUUID();

			const newWithdrawal = await db
				.insert(withdrawalRequest)
				.values({
					id: requestId,
					amount: amount,
					status: "PENDING",
					initiatedByDiscordId: discordUserId,
					initiatedByDiscordTag: discordUserTag,
					requestedById: userAccount.userId,
					createdAt: new Date(),
					updatedAt: new Date(),
				})
				.returning()
				.get();

			// Get user's name for notifications
			const userData = await db
				.select({
					name: userTable.name,
					tornUserId: tornUser.tornUserId,
				})
				.from(userTable)
				.leftJoin(tornUser, eq(userTable.id, tornUser.id))
				.where(eq(userTable.id, userAccount.userId))
				.get();

			// LiveStore event for withdrawal created
			try {
				await pushLiveStoreEvents(env, [
					{
						name: "v1.WithdrawalCreated",
						data: {
							id: newWithdrawal.id,
							amount: newWithdrawal.amount,
							requestedById: newWithdrawal.requestedById,
							requestedByName: userData?.name || discordUserTag,
							requestedByTornId: userData?.tornUserId
								? Number(userData.tornUserId)
								: undefined,
							createdAt: newWithdrawal.createdAt,
						},
					},
				]);
			} catch (error) {
				console.error("Failed to push LiveStore withdrawal created:", error);
			}
			// Legacy WebSocket broadcast
			try {
				const wsService = new WebSocketService();
				await wsService.broadcastWithdrawalCreated({
					id: newWithdrawal.id,
					amount: newWithdrawal.amount,
					requestedByName: userData?.name || discordUserTag,
				});
			} catch (error) {
				console.error("Failed to broadcast withdrawal creation:", error);
			}

			// Send Discord notification to banking channel
			try {
				const discordService = new DiscordNotificationService(env);
				const discordResult =
					await discordService.sendBankingRequestNotification({
						userName: userData?.name || discordUserTag,
						amount: amount,
						requestId: newWithdrawal.id,
						requestingUserId: userAccount.userId,
						requesterTornId: userData?.tornUserId?.toString(),
					});

				// If Discord notification was successful, save the message ID for future updates
				if (discordResult.success && discordResult.discordMessageId) {
					await db
						.update(withdrawalRequest)
						.set({ discordMessageId: discordResult.discordMessageId })
						.where(eq(withdrawalRequest.id, newWithdrawal.id));

					console.log(
						`Successfully saved Discord message ID ${discordResult.discordMessageId} for withdrawal ${newWithdrawal.id}`,
					);
				} else if (!discordResult.success) {
					console.warn(
						`Discord notification failed for withdrawal ${newWithdrawal.id}: ${discordResult.error}`,
					);
				}
			} catch (error) {
				console.error("Failed to send Discord notification:", error);
				// Don't fail the request if Discord notification fails
			}

			// Success embed
			const successEmbed = {
				color: 0x00ff00, // Green
				title: "✅ Withdrawal Request Submitted",
				description:
					"Your withdrawal request has been successfully submitted and is pending admin approval.",
				fields: [
					{
						name: "💰 Amount",
						value: `$${amount.toLocaleString()}`,
						inline: true,
					},
					{
						name: "👤 Requested by",
						value: discordUserTag,
						inline: true,
					},
					{
						name: "✅ Status",
						value: "Pending Approval",
						inline: true,
					},
					{
						name: "🆔 Request ID",
						value: `\`${requestId}\``,
						inline: false,
					},
					{
						name: "📋 What's Next?",
						value:
							"• Your request is now in the admin approval queue\n" +
							"• You will be notified when it's processed\n" +
							"• Check the [MonkeyMenu Banking Dashboard](https://monkeymenu.app/dashboard/banking) for updates",
						inline: false,
					},
				],
				footer: {
					text: "MonkeyMenu Banking System",
				},
				timestamp: new Date().toISOString(),
			};

			return await ctx.editOriginal({
				embeds: [successEmbed],
			});
		} catch (error) {
			console.error("Error processing withdrawal command:", error);

			// Error embed
			const errorEmbed = {
				color: 0xff0000, // Red
				title: "❌ Processing Error",
				description:
					"An unexpected error occurred while processing your withdrawal request.",
				fields: [
					{
						name: "💰 Requested Amount",
						value: `$${amount.toLocaleString()}`,
						inline: true,
					},
					{
						name: "❌ Status",
						value: "Processing Failed",
						inline: true,
					},
					{
						name: "📋 Next Steps",
						value:
							"Please try again in a few moments. If the problem persists, contact an admin.",
						inline: false,
					},
				],
				footer: {
					text: "MonkeyMenu Banking System",
				},
				timestamp: new Date().toISOString(),
			};

			return await ctx.editOriginal({
				embeds: [errorEmbed],
			});
		}
	}
}
