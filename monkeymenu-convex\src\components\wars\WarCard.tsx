
import { Id } from '../../../convex/_generated/dataModel';

interface WarCardProps {
  war: {
    _id: Id<"wars">;
    factionName: string;
    enemyFactionName: string;
    startTime: number;
    endTime?: number;
    status: string;
    ourScore: number;
    enemyScore: number;
  };
  onViewDetails?: (warId: Id<"wars">) => void;
  onViewAdvancedAnalytics?: (war: any) => void;
  showActions?: boolean;
}

const statusStyles = {
  active: 'bg-green-100 text-green-800 border-green-200',
  ended: 'bg-gray-100 text-gray-800 border-gray-200',
  pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
};

const statusIcons = {
  active: '⚔️',
  ended: '🏁',
  pending: '⏳',
};

export function WarCard({ war, onViewDetails, onViewAdvancedAnalytics, showActions = true }: WarCardProps) {
  const formatDuration = (start: number, end?: number) => {
    const endTime = end || Date.now();
    const duration = endTime - start;
    
    const days = Math.floor(duration / (1000 * 60 * 60 * 24));
    const hours = Math.floor((duration % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) {
      return `${days}d ${hours}h`;
    }
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const formatDateTime = (timestamp: number) => {
    console.log("formatDateTime debug:", {
      rawTimestamp: timestamp,
      date: new Date(timestamp),
      isoString: new Date(timestamp).toISOString(),
      isValid: !isNaN(timestamp) && timestamp > 0
    });
    
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getWarResult = () => {
    if (war.status !== 'ended') return null;
    
    if (war.ourScore > war.enemyScore) return 'won';
    if (war.ourScore < war.enemyScore) return 'lost';
    return 'tied';
  };

  const warResult = getWarResult();

  return (
    <div className={`bg-white rounded-lg shadow-sm border-2 p-4 hover:shadow-md transition-shadow ${
      statusStyles[war.status as keyof typeof statusStyles] || statusStyles.ended
    }`}>
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-2">
          <span className="text-lg">
            {statusIcons[war.status as keyof typeof statusIcons] || statusIcons.ended}
          </span>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {war.factionName} vs {war.enemyFactionName}
            </h3>
            <div className="text-sm text-gray-600">
              Started {formatDateTime(war.startTime)}
            </div>
          </div>
        </div>
        
        <div className={`px-3 py-1 text-sm font-medium rounded-full capitalize ${
          statusStyles[war.status as keyof typeof statusStyles] || statusStyles.ended
        }`}>
          {war.status}
        </div>
      </div>

      {/* Score Display */}
      <div className="mb-4">
        <div className="flex items-center justify-center space-x-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{war.ourScore}</div>
            <div className="text-sm text-gray-600">{war.factionName}</div>
          </div>
          
          <div className="text-2xl text-gray-400">vs</div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{war.enemyScore}</div>
            <div className="text-sm text-gray-600">{war.enemyFactionName}</div>
          </div>
        </div>

        {/* War Result */}
        {warResult && (
          <div className="text-center mt-2">
            <span className={`px-3 py-1 text-sm font-medium rounded-full ${
              warResult === 'won' ? 'bg-green-100 text-green-800' :
              warResult === 'lost' ? 'bg-red-100 text-red-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {warResult === 'won' ? '🎉 Victory!' : 
               warResult === 'lost' ? '💀 Defeat' : 
               '🤝 Tied'}
            </span>
          </div>
        )}
      </div>

      {/* War Info */}
      <div className="space-y-2 text-sm text-gray-600">
        <div className="flex justify-between">
          <span>Duration:</span>
          <span className="font-medium">
            {formatDuration(war.startTime, war.endTime)}
          </span>
        </div>
        
        {war.endTime && (
          <div className="flex justify-between">
            <span>Ended:</span>
            <span className="font-medium">
              {formatDateTime(war.endTime)}
            </span>
          </div>
        )}
        
        <div className="flex justify-between">
          <span>Total Hits:</span>
          <span className="font-medium">
            {war.ourScore + war.enemyScore}
          </span>
        </div>
      </div>

      {/* Actions */}
      {showActions && (
        <div className="flex justify-end gap-2 mt-4 pt-4 border-t border-gray-100">
          <button
            onClick={() => onViewDetails?.(war._id)}
            className="px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
          >
            📊 Basic
          </button>
          <button
            onClick={() => onViewAdvancedAnalytics?.(war)}
            className="px-3 py-2 text-sm bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors"
          >
            🔬 Analytics
          </button>
        </div>
      )}
    </div>
  );
}