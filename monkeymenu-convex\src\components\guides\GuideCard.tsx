
import React from 'react';
import { Id } from '../../../convex/_generated/dataModel';
import { getCategoryDisplay, extractPreviewText, formatRelativeTime, calculateReadingTime } from './utils';

interface GuideCardProps {
  guide: {
    _id: Id<"guides">;
    title: string;
    content: string;
    category: string;
    isPublished: boolean;
    viewCount: number;
    tags: string[];
    createdAt: number;
    author: { username: string } | null;
  };
  onEdit?: (guide: any) => void;
  onDelete?: (guideId: Id<"guides">) => void;
  onView?: (guideId: Id<"guides">) => void;
  showActions?: boolean;
  viewMode?: 'grid' | 'list';
}

export const GuideCard: React.FC<GuideCardProps> = ({
  guide,
  onEdit,
  onDelete,
  onView,
  showActions = false,
  viewMode = 'grid',
}) => {
  const handleView = () => {
    if (onView) {
      onView(guide._id);
    }
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onEdit) {
      onEdit(guide);
    }
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDelete && confirm('Are you sure you want to delete this guide?')) {
      onDelete(guide._id);
    }
  };

  const readingTime = calculateReadingTime(guide.content);

  if (viewMode === 'list') {
    return (
      <div 
        className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer border-l-4 border-blue-500"
        onClick={handleView}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-start gap-4">
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{guide.title}</h3>
                <p className="text-gray-700 mb-3 line-clamp-2">
                  {extractPreviewText(guide.content, 200)}
                </p>
                
                <div className="flex items-center flex-wrap gap-3 text-sm text-gray-600">
                  <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                    {getCategoryDisplay(guide.category)}
                  </span>
                  <span>By {guide.author?.username || 'Unknown'}</span>
                  <span>{guide.viewCount} views</span>
                  <span>{readingTime} min read</span>
                  <span>{formatRelativeTime(guide.createdAt)}</span>
                  {!guide.isPublished && (
                    <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                      Draft
                    </span>
                  )}
                </div>

                {guide.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-3">
                    {guide.tags.slice(0, 5).map((tag, index) => (
                      <span
                        key={index}
                        className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs"
                      >
                        #{tag}
                      </span>
                    ))}
                    {guide.tags.length > 5 && (
                      <span className="text-gray-500 text-xs">
                        +{guide.tags.length - 5} more
                      </span>
                    )}
                  </div>
                )}
              </div>
              
              <div className="flex flex-col gap-2">
                {showActions && (
                  <div className="flex gap-2">
                    <button
                      onClick={handleEdit}
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium px-3 py-1 rounded border border-blue-200 hover:bg-blue-50"
                    >
                      Edit
                    </button>
                    <button
                      onClick={handleDelete}
                      className="text-red-600 hover:text-red-800 text-sm font-medium px-3 py-1 rounded border border-red-200 hover:bg-red-50"
                    >
                      Delete
                    </button>
                  </div>
                )}
                <span className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                  Read More →
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Grid view (default)
  return (
    <div 
      className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer"
      onClick={handleView}
    >
      <div className="flex justify-between items-start mb-3">
        <div className="flex-1">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">{guide.title}</h3>
          <div className="flex items-center gap-3 text-sm text-gray-600 mb-2">
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
              {getCategoryDisplay(guide.category)}
            </span>
            <span>{guide.author?.username || 'Unknown'}</span>
            <span>{guide.viewCount} views</span>
            <span>{readingTime} min read</span>
            <span>
              {formatRelativeTime(guide.createdAt)}
            </span>
          </div>
        </div>
        {showActions && (
          <div className="flex gap-2">
            <button
              onClick={handleEdit}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Edit
            </button>
            <button
              onClick={handleDelete}
              className="text-red-600 hover:text-red-800 text-sm font-medium"
            >
              Delete
            </button>
          </div>
        )}
      </div>

      <p className="text-gray-700 mb-4">
        {extractPreviewText(guide.content)}
      </p>

      <div className="flex items-center justify-between">
        <div className="flex flex-wrap gap-1">
          {guide.tags.map((tag, index) => (
            <span
              key={index}
              className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs"
            >
              #{tag}
            </span>
          ))}
        </div>
        
        <div className="flex items-center gap-2">
          {!guide.isPublished && (
            <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">
              Draft
            </span>
          )}
          <span className="text-blue-600 hover:text-blue-800 text-sm font-medium">
            Read More →
          </span>
        </div>
      </div>
    </div>
  );
};