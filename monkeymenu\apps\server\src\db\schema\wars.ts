import { sql } from "drizzle-orm";
import {
	index,
	integer,
	sqliteTable,
	text,
	uniqueIndex,
} from "drizzle-orm/sqlite-core";

// Cache for war reports (completed wars only)
export const cachedWarReport = sqliteTable(
	"cached_war_report",
	{
		warId: integer("war_id").primaryKey(),
		reportData: text("report_data").notNull(), // JSON string of the full report
		factionIds: text("faction_ids").notNull(), // Comma-separated faction IDs involved
		startTime: integer("start_time").notNull(),
		endTime: integer("end_time"), // null if ongoing
		winner: integer("winner"), // null if ongoing
		isCompleted: integer("is_completed", { mode: "boolean" }).default(false),
		cachedAt: integer("cached_at", { mode: "timestamp" })
			.notNull()
			.default(sql`CURRENT_TIMESTAMP`),
		lastUpdated: integer("last_updated", { mode: "timestamp" })
			.notNull()
			.default(sql`CURRENT_TIMESTAMP`),
	},
	(table) => [
		index("idx_war_start_time").on(table.startTime),
		index("idx_war_end_time").on(table.endTime),
		index("idx_war_is_completed").on(table.isCompleted),
		index("idx_war_faction_ids").on(table.factionIds),
	],
);

// Cache for chain reports within wars
export const cachedChainReport = sqliteTable(
	"cached_chain_report",
	{
		chainId: integer("chain_id").primaryKey(),
		warId: integer("war_id").notNull(),
		reportData: text("report_data").notNull(), // JSON string of the full chain report
		startTime: integer("start_time").notNull(),
		endTime: integer("end_time").notNull(),
		chainLength: integer("chain_length").notNull(),
		totalRespect: integer("total_respect").notNull(),
		attackerCount: integer("attacker_count").notNull(),
		cachedAt: integer("cached_at", { mode: "timestamp" })
			.notNull()
			.default(sql`CURRENT_TIMESTAMP`),
	},
	(table) => [
		index("idx_chain_war_id").on(table.warId),
		index("idx_chain_start_time").on(table.startTime),
		index("idx_chain_end_time").on(table.endTime),
	],
);

// Cache for attack data during wars
export const cachedWarAttacks = sqliteTable(
	"cached_war_attacks",
	{
		id: text("id").primaryKey(), // Use attack ID from API
		warId: integer("war_id").notNull(),
		attackData: text("attack_data").notNull(), // JSON string of the attack
		attackerId: integer("attacker_id").notNull(),
		defenderId: integer("defender_id").notNull(),
		attackerFactionId: integer("attacker_faction_id"),
		defenderFactionId: integer("defender_faction_id"),
		timestamp: integer("timestamp").notNull(),
		chainId: integer("chain_id"), // null for non-chain attacks
		result: text("result").notNull(),
		respect: integer("respect").default(0),
		cachedAt: integer("cached_at", { mode: "timestamp" })
			.notNull()
			.default(sql`CURRENT_TIMESTAMP`),
	},
	(table) => [
		index("idx_attack_war_id").on(table.warId),
		index("idx_attack_timestamp").on(table.timestamp),
		index("idx_attack_attacker_id").on(table.attackerId),
		index("idx_attack_defender_id").on(table.defenderId),
		index("idx_attack_chain_id").on(table.chainId),
		index("idx_attack_result").on(table.result),
		// Composite indexes for common queries
		index("idx_attack_war_chain").on(table.warId, table.chainId),
		index("idx_attack_war_attacker").on(table.warId, table.attackerId),
		index("idx_attack_war_timestamp").on(table.warId, table.timestamp),
	],
);

// Cache for combined statistics to avoid recalculation
export const cachedWarStats = sqliteTable(
	"cached_war_stats",
	{
		id: text("id").primaryKey(), // Composite key like "war_53100_1234" for war stats for faction
		warId: integer("war_id").notNull(),
		factionId: integer("faction_id").notNull(),
		statsType: text("stats_type").notNull(), // "overall", "chains", "attacks", "timeline"
		timeframe: text("timeframe"), // "hourly", "daily", or null for overall
		statsData: text("stats_data").notNull(), // JSON string of computed stats
		cachedAt: integer("cached_at", { mode: "timestamp" })
			.notNull()
			.default(sql`CURRENT_TIMESTAMP`),
	},
	(table) => [
		uniqueIndex("idx_war_stats_unique").on(
			table.warId,
			table.factionId,
			table.statsType,
			table.timeframe,
		),
		index("idx_war_stats_war_id").on(table.warId),
		index("idx_war_stats_faction_id").on(table.factionId),
		index("idx_war_stats_type").on(table.statsType),
	],
);

// Export types for inference
export type CachedWarReport = typeof cachedWarReport.$inferSelect;
export type CachedChainReport = typeof cachedChainReport.$inferSelect;
export type CachedWarAttacks = typeof cachedWarAttacks.$inferSelect;
export type CachedWarStats = typeof cachedWarStats.$inferSelect;
