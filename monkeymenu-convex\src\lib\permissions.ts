// Permission system constants matching the original app EXACTLY
export const PERMISSION_NAMES = {
  // Guides permissions (matches original)
  GUIDES_VIEW: "guides.view",
  GUIDES_MANAGE: "guides.manage",
  
  // Dashboard permissions (matches original)
  DASHBOARD_VIEW: "dashboard.view",
  
  // Announcements permissions (matches original)
  ANNOUNCEMENTS_VIEW: "announcements.view",
  ANNOUNCEMENTS_MANAGE: "announcements.manage",
  
  // Admin permissions (matches original)
  ADMIN_VIEW: "admin.view",
  ADMIN_SUSPEND_USERS: "admin.users.suspend",
  ADMIN_RECHECK_API_KEYS: "admin.users.recheck",
  ADMIN_DELETE_USER: "admin.users.delete",
  
  // Discord permissions (matches original)
  DISCORD_MANAGE_VERIFICATION: "discord.manage.verification",
  
  // Banking permissions (matches original)
  BANKING_VIEW: "banking.view",
  BANKING_REQUEST: "banking.request",
  BAN<PERSON>ING_MANAGE_REQUESTS: "banking.requests.manage",
  
  // Target Finder permissions (matches original)
  TARGET_FINDER_VIEW: "target.finder.view",
  TARGET_FINDER_MANAGE_SHARED_LISTS: "target.finder.manage.shared_lists",
  
  // Wars permissions (matches original)
  WARS_VIEW: "wars.view",
} as const;

// Faction role hierarchy (matches original app exactly)
export const FACTION_ROLES = {
  SYSTEM_ADMIN: { name: "system-admin", displayName: "System Administrator", level: 10 },
  LEADER: { name: "leader", displayName: "Leader", level: 9 },
  CO_LEADER: { name: "co-leader", displayName: "Co-leader", level: 8 },
  MONKEY_MENTOR: { name: "monkey-mentor", displayName: "Monkey Mentor", level: 7 },
  GORILLA: { name: "gorilla", displayName: "Gorilla", level: 6 },
  PRIMATE_LIAISON: { name: "primate-liaison", displayName: "Primate Liaison", level: 5 },
  BABOON: { name: "baboon", displayName: "Baboon", level: 4 },
  ORANGUTAN: { name: "orangutan", displayName: "Orangutan", level: 3 },
  CHIMPANZEE: { name: "chimpanzee", displayName: "Chimpanzee", level: 2 },
  RECRUIT: { name: "recruit", displayName: "Recruit", level: 1 },
} as const;

export const ROLE_LEVELS = {
  SYSTEM_ADMIN: FACTION_ROLES.SYSTEM_ADMIN.level,
  LEADER: FACTION_ROLES.LEADER.level,
  CO_LEADER: FACTION_ROLES.CO_LEADER.level,
  MONKEY_MENTOR: FACTION_ROLES.MONKEY_MENTOR.level,
  GORILLA: FACTION_ROLES.GORILLA.level,
  PRIMATE_LIAISON: FACTION_ROLES.PRIMATE_LIAISON.level,
  BABOON: FACTION_ROLES.BABOON.level,
  ORANGUTAN: FACTION_ROLES.ORANGUTAN.level,
  CHIMPANZEE: FACTION_ROLES.CHIMPANZEE.level,
  RECRUIT: FACTION_ROLES.RECRUIT.level,
} as const;

export type PermissionName = typeof PERMISSION_NAMES[keyof typeof PERMISSION_NAMES];
export type RoleLevel = typeof ROLE_LEVELS[keyof typeof ROLE_LEVELS];