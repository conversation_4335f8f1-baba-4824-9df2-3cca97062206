import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';

interface TrendChartProps {
  metric: 'users' | 'transactions' | 'wars' | 'guides';
  title: string;
  days?: number;
}

export function TrendChart({ metric, title, days = 30 }: TrendChartProps) {
  const trendData = useQuery(api.analytics.getActivityTrends, { metric, days });

  if (!trendData) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4 w-1/3"></div>
          <div className="h-48 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  const maxValue = Math.max(...trendData.data.map((d: any) => d.value));
  const minValue = Math.min(...trendData.data.map((d: any) => d.value));
  const range = maxValue - minValue || 1;

  // Simple SVG chart
  const chartWidth = 400;
  const chartHeight = 200;
  const padding = 40;

  const points = trendData.data.map((point: any, index: any) => {
    const x = padding + (index / (trendData.data.length - 1)) * (chartWidth - 2 * padding);
    const y = chartHeight - padding - ((point.value - minValue) / range) * (chartHeight - 2 * padding);
    return `${x},${y}`;
  }).join(' ');

  const pathD = `M ${points.split(' ').map((point: any, index: any) => {
    const [x, y] = point.split(',');
    return index === 0 ? `M ${x} ${y}` : `L ${x} ${y}`;
  }).join(' ')}`;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        <div className="text-sm text-gray-600">
          Total: {trendData.total} | Avg: {trendData.average}
        </div>
      </div>
      
      <div className="relative">
        <svg
          width="100%"
          height="200"
          viewBox={`0 0 ${chartWidth} ${chartHeight}`}
          className="overflow-visible"
        >
          {/* Grid lines */}
          {[0, 0.25, 0.5, 0.75, 1].map((ratio) => {
            const y = chartHeight - padding - ratio * (chartHeight - 2 * padding);
            return (
              <g key={ratio}>
                <line
                  x1={padding}
                  y1={y}
                  x2={chartWidth - padding}
                  y2={y}
                  stroke="#e5e7eb"
                  strokeWidth="1"
                />
                <text
                  x={padding - 10}
                  y={y + 4}
                  textAnchor="end"
                  className="text-xs fill-gray-500"
                >
                  {Math.round(minValue + ratio * range)}
                </text>
              </g>
            );
          })}
          
          {/* Chart line */}
          <path
            d={pathD}
            fill="none"
            stroke="#3b82f6"
            strokeWidth="2"
            className="drop-shadow-sm"
          />
          
          {/* Data points */}
          {trendData.data.map((point: any, index: any) => {
            const x = padding + (index / (trendData.data.length - 1)) * (chartWidth - 2 * padding);
            const y = chartHeight - padding - ((point.value - minValue) / range) * (chartHeight - 2 * padding);
            
            return (
              <circle
                key={index}
                cx={x}
                cy={y}
                r="3"
                fill="#3b82f6"
                className="hover:r-4 transition-all cursor-pointer"
              >
                <title>{`${point.date}: ${point.value}`}</title>
              </circle>
            );
          })}
        </svg>
      </div>
      
      {/* Recent data points */}
      <div className="mt-4 grid grid-cols-7 gap-1 text-xs">
        {trendData.data.slice(-7).map((point: any, index: any) => (
          <div key={index} className="text-center">
            <div className="text-gray-500">{new Date(point.date).toLocaleDateString('en-US', { weekday: 'short' })}</div>
            <div className="font-medium text-gray-900">{point.value}</div>
          </div>
        ))}
      </div>
    </div>
  );
}