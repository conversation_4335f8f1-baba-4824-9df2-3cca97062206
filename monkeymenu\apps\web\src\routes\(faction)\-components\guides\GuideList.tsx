import { HasPermission } from "@/components/permissions/PermissionGuards";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { PERMISSION_NAMES } from "@monkeymenu/shared";
import { BookOpen, Plus } from "lucide-react";
import { GuideCard } from "./GuideCard";
import type { ViewMode } from "./types";

interface GuideData {
	guide: {
		id: number;
		title: string;
		content: string;
		category: string;
		createdAt: string;
		updatedAt: string;
	};
	author?: {
		name: string;
	};
}

interface GuideListProps {
	guides: GuideData[];
	viewMode: ViewMode;
	isLoading: boolean;
	error: unknown;
	searchQuery: string;
	selectedCategory: string;
	onView: (guide: GuideData) => void;
	onEdit: (guide: GuideData) => void;
	onDelete: (id: number) => void;
	onCreateFirst: () => void;
}

export function GuideList({
	guides,
	viewMode,
	isLoading,
	error,
	searchQuery,
	selectedCategory,
	onView,
	onEdit,
	onDelete,
	onCreateFirst,
}: GuideListProps) {
	if (isLoading) {
		return (
			<div className="flex items-center justify-center py-12">
				<div className="text-center">
					<div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-primary border-b-2" />
					<p className="text-muted-foreground">Loading guides...</p>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<Card>
				<CardContent className="py-12 text-center">
					<p className="mb-2 text-red-500">Error loading guides</p>
					<p className="text-muted-foreground text-sm">
						{error instanceof Error ? error.message : "An error occurred"}
					</p>
				</CardContent>
			</Card>
		);
	}

	if (guides.length === 0) {
		return (
			<Card>
				<CardContent className="py-12 text-center">
					<BookOpen className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
					<h3 className="mb-2 font-semibold text-lg">
						{searchQuery || selectedCategory !== "all"
							? "No guides found"
							: "No guides yet"}
					</h3>
					<p className="mb-4 text-muted-foreground">
						{searchQuery || selectedCategory !== "all"
							? "Try adjusting your search or filter criteria"
							: "Be the first to create a guide for the faction"}
					</p>
					{!searchQuery && selectedCategory === "all" && (
						<HasPermission permission={PERMISSION_NAMES.GUIDES_MANAGE}>
							<Button onClick={onCreateFirst} className="gap-2">
								<Plus className="h-4 w-4" />
								Create First Guide
							</Button>
						</HasPermission>
					)}
				</CardContent>
			</Card>
		);
	}

	return (
		<div
			className={
				viewMode === "grid"
					? "grid gap-6 sm:grid-cols-2 lg:grid-cols-3"
					: "space-y-4"
			}
		>
			{guides.map((guide) => (
				<GuideCard
					key={guide.guide.id}
					guide={guide}
					viewMode={viewMode}
					onView={onView}
					onEdit={onEdit}
					onDelete={onDelete}
				/>
			))}
		</div>
	);
}
