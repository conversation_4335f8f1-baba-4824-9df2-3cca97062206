#!/bin/bash

# Discord Worker Deployment Script
# This script deploys the Discord Worker to Cloudflare

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default environment
ENVIRONMENT=${1:-staging}

echo -e "${YELLOW}🤖 Deploying Discord Worker to ${ENVIRONMENT}...${NC}"

# Check if required environment variables are set
if [[ -z "$DISCORD_APPLICATION_ID" || -z "$DISCORD_BOT_TOKEN" || -z "$DISCORD_PUBLIC_KEY" || -z "$CONVEX_URL" ]]; then
    echo -e "${RED}❌ Missing required environment variables:${NC}"
    echo "   - DISCORD_APPLICATION_ID"
    echo "   - DISCORD_BOT_TOKEN"
    echo "   - DISCORD_PUBLIC_KEY"
    echo "   - CONVEX_URL"
    echo ""
    echo "Please set these variables before deploying."
    exit 1
fi

# Build TypeScript
echo -e "${YELLOW}🔨 Building TypeScript...${NC}"
npx tsc

# Sync Discord commands
echo -e "${YELLOW}📡 Syncing Discord slash commands...${NC}"
tsx sync-commands.ts

# Deploy to Cloudflare Workers
echo -e "${YELLOW}☁️  Deploying to Cloudflare Workers...${NC}"
if [[ $ENVIRONMENT == "production" ]]; then
    wrangler deploy --env production
elif [[ $ENVIRONMENT == "staging" ]]; then
    wrangler deploy --env staging
else
    wrangler deploy --env development
fi

echo -e "${GREEN}✅ Discord Worker deployed successfully to ${ENVIRONMENT}!${NC}"

# Show next steps
echo -e "${YELLOW}📋 Next steps:${NC}"
echo "1. Update Discord Application Interaction Endpoint URL:"
if [[ $ENVIRONMENT == "production" ]]; then
    echo "   https://discord-bot.monkeymenu.com/discord/interactions"
elif [[ $ENVIRONMENT == "staging" ]]; then
    echo "   https://discord-bot-staging.monkeymenu.com/discord/interactions"
else
    echo "   https://discord-bot-dev.monkeymenu.com/discord/interactions"
fi
echo "2. Test the bot in your Discord server"
echo "3. Monitor Cloudflare Worker logs for any issues"
