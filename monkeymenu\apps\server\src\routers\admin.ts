import { PERMISSIONS } from "@monkeymenu/shared";
import { TRPCError } from "@trpc/server";
import { desc, eq, like, ne, or, sql } from "drizzle-orm";
import { z } from "zod";
import { user } from "../db/schema/auth";
import { guide } from "../db/schema/guides";
import { factionRole, permission, userRole } from "../db/schema/permissions";
import { tornUser } from "../db/schema/torn";

import { permissionProcedure, requirePermission, router } from "../lib/trpc";

export const adminRouter = router({
	// Get system stats (for admin dashboard)
	getSystemStats: permissionProcedure
		.use(requirePermission(PERMISSIONS.ADMIN_VIEW.name))
		.query(async ({ ctx }) => {
			try {
				// Get actual counts from database
				const [userCount, roleCount, permissionCount, guideCount] =
					await Promise.all([
						// Count real users only (exclude system users)
						ctx.db
							.select({ count: sql<number>`count(*)` })
							.from(user)
							.where(ne(user.email, "<EMAIL>")),
						ctx.db.select({ count: sql<number>`count(*)` }).from(factionRole),
						ctx.db.select({ count: sql<number>`count(*)` }).from(permission),
						ctx.db.select({ count: sql<number>`count(*)` }).from(guide),
					]);

				const stats = {
					totalUsers: userCount[0]?.count || 0,
					totalRoles: roleCount[0]?.count || 0,
					totalPermissions: permissionCount[0]?.count || 0,
					totalGuides: guideCount[0]?.count || 0,
				};

				return stats;
			} catch (error) {
				console.error("Failed to get system stats:", error);
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to retrieve system statistics",
					cause: error,
				});
			}
		}),

	// Get permission audit log (ADMIN_VIEW permission required)
	getPermissionAuditLog: permissionProcedure
		.use(requirePermission(PERMISSIONS.ADMIN_VIEW.name))
		.query(async ({ ctx }) => {
			try {
				// Get recent role assignments with user details
				const recentAssignments = await ctx.db
					.select({
						id: userRole.id,
						assignedAt: userRole.assignedAt,
						isActive: userRole.isActive,
						assignedByUserId: userRole.assignedBy,
						assignedToUser: {
							id: user.id,
							name: user.name,
							email: user.email,
						},
						role: {
							id: factionRole.id,
							name: factionRole.name,
							displayName: factionRole.displayName,
							hierarchyLevel: factionRole.hierarchyLevel,
						},
					})
					.from(userRole)
					.innerJoin(user, eq(userRole.userId, user.id))
					.innerJoin(factionRole, eq(userRole.roleId, factionRole.id))
					.orderBy(desc(userRole.assignedAt))
					.limit(50);

				// Get the assignedBy user names in a separate query to avoid complex joins
				const userIds = recentAssignments
					.map((r) => r.assignedByUserId)
					.filter((id): id is string => id !== null);

				const assignedByUsers =
					userIds.length > 0
						? await ctx.db
								.select({ id: user.id, name: user.name })
								.from(user)
								.where(
									sql`${user.id} IN (${sql.join(
										userIds.map((id) => sql`${id}`),
										sql`, `,
									)})`,
								)
						: [];

				const assignedByMap = new Map(
					assignedByUsers.map((u) => [u.id, u.name]),
				);

				// Combine the data
				const enrichedAssignments = recentAssignments.map((assignment) => ({
					...assignment,
					assignedBy: assignment.assignedByUserId
						? {
								id: assignment.assignedByUserId,
								name:
									assignedByMap.get(assignment.assignedByUserId) || "Unknown",
							}
						: null,
				}));

				return enrichedAssignments;
			} catch (error) {
				console.error("Failed to get audit log:", error);
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to retrieve permission audit log",
					cause: error,
				});
			}
		}),

	// Get all users for admin management
	getAllUsers: permissionProcedure
		.use(requirePermission(PERMISSIONS.ADMIN_VIEW.name))
		.input(
			z.object({
				search: z.string().optional(),
				limit: z.number().min(1).max(100).default(50),
				offset: z.number().min(0).default(0),
			}),
		)
		.query(async ({ ctx, input }) => {
			try {
				// Build where condition for search
				const searchCondition = input.search
					? or(
							like(user.name, `%${input.search}%`),
							like(user.email, `%${input.search}%`),
						)
					: undefined;

				// Get users with search filter
				const users = await ctx.db
					.select({
						id: user.id,
						name: user.name,
						email: user.email,
						image: user.image,
						createdAt: user.createdAt,
					})
					.from(user)
					.where(searchCondition)
					.orderBy(desc(user.createdAt))
					.limit(input.limit)
					.offset(input.offset);

				// Get total count with same search filter
				const totalCountResult = await ctx.db
					.select({ count: sql<number>`count(*)` })
					.from(user)
					.where(searchCondition);

				const totalCount = totalCountResult;

				// Get additional data for each user
				const enrichedUsers = await Promise.all(
					users.map(async (u) => {
						const [tornUserData, activeRole] = await Promise.all([
							ctx.db
								.select({
									tornUserId: tornUser.tornUserId,
									tornFactionId: tornUser.tornFactionId,
									verified: tornUser.tornApiKeyVerified,
								})
								.from(tornUser)
								.where(eq(tornUser.id, u.id))
								.get(),
							ctx.db
								.select({
									role: {
										id: factionRole.id,
										name: factionRole.name,
										displayName: factionRole.displayName,
										hierarchyLevel: factionRole.hierarchyLevel,
									},
									assignedAt: userRole.assignedAt,
								})
								.from(userRole)
								.innerJoin(factionRole, eq(userRole.roleId, factionRole.id))
								.where(
									sql`${userRole.userId} = ${u.id} AND ${userRole.isActive} = 1`,
								)
								.get(),
						]);

						return {
							user: u,
							tornUser: tornUserData || null,
							role: activeRole || null,
						};
					}),
				);

				return {
					users: enrichedUsers,
					total: totalCount[0]?.count || 0,
					hasMore: input.offset + input.limit < (totalCount[0]?.count || 0),
				};
			} catch (error) {
				console.error("Failed to get all users:", error);
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to retrieve users",
					cause: error,
				});
			}
		}),

	// Suspend a user (requires ADMIN_SUSPEND_USERS permission)
	suspendUser: permissionProcedure
		.use(requirePermission(PERMISSIONS.ADMIN_SUSPEND_USERS.name))
		.input(
			z.object({
				userId: z.string(),
				reason: z.string().min(1, "Suspension reason is required"),
			}),
		)
		.mutation(async ({ ctx, input }) => {
			try {
				// Check if user exists
				const targetUser = await ctx.db
					.select({ id: user.id, name: user.name })
					.from(user)
					.where(eq(user.id, input.userId))
					.get();

				if (!targetUser) {
					throw new TRPCError({
						code: "NOT_FOUND",
						message: "User not found",
					});
				}

				// Update the torn_user table to suspend access (admin suspension)
				await ctx.db
					.update(tornUser)
					.set({
						accessSuspended: true,
						accessSuspensionReason: input.reason,
						accessSuspendedAt: new Date(),
						suspensionType: "admin",
					})
					.where(eq(tornUser.id, input.userId));

				// Remove Discord roles if user has Discord linked and bot is configured
				let discordSyncMessage = "";
				if (ctx.env.DISCORD_BOT_TOKEN && ctx.env.DISCORD_GUILD_ID) {
					try {
						// Import the sync function from the main app
						const { syncUserDiscordRoles } = await import("../index");
						const discordResult = await syncUserDiscordRoles(
							ctx.db,
							input.userId,
							ctx.env.DISCORD_GUILD_ID,
							ctx.env.DISCORD_BOT_TOKEN,
							false, // User is now suspended - not in faction
						);

						if (
							discordResult.success &&
							discordResult.message !== "Roles already correct"
						) {
							discordSyncMessage = ` Discord roles updated: ${discordResult.message}.`;
						}
					} catch (discordError) {
						console.error(
							"Discord role sync error during suspension:",
							discordError,
						);
						// Don't fail the entire operation for Discord sync issues
					}
				}

				return {
					success: true,
					message: `User ${targetUser.name} has been suspended.${discordSyncMessage}`,
				};
			} catch (error) {
				console.error("Failed to suspend user:", error);
				if (error instanceof TRPCError) {
					throw error;
				}
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to suspend user",
					cause: error,
				});
			}
		}),

	// Restore a suspended user (requires ADMIN_SUSPEND_USERS permission)
	restoreUser: permissionProcedure
		.use(requirePermission(PERMISSIONS.ADMIN_SUSPEND_USERS.name))
		.input(
			z.object({
				userId: z.string(),
			}),
		)
		.mutation(async ({ ctx, input }) => {
			try {
				// Check if user exists
				const targetUser = await ctx.db
					.select({ id: user.id, name: user.name })
					.from(user)
					.where(eq(user.id, input.userId))
					.get();

				if (!targetUser) {
					throw new TRPCError({
						code: "NOT_FOUND",
						message: "User not found",
					});
				}

				// Update the torn_user table to restore access
				await ctx.db
					.update(tornUser)
					.set({
						accessSuspended: false,
						accessSuspensionReason: null,
						accessSuspendedAt: null,
						suspensionType: null,
					})
					.where(eq(tornUser.id, input.userId));

				// Restore Discord roles if user has Discord linked and bot is configured
				let discordSyncMessage = "";
				if (ctx.env.DISCORD_BOT_TOKEN && ctx.env.DISCORD_GUILD_ID) {
					try {
						// Import the sync function from the main app
						const { syncUserDiscordRoles } = await import("../index");
						const discordResult = await syncUserDiscordRoles(
							ctx.db,
							input.userId,
							ctx.env.DISCORD_GUILD_ID,
							ctx.env.DISCORD_BOT_TOKEN,
							true, // User is now restored - back in faction
						);

						if (
							discordResult.success &&
							discordResult.message !== "Roles already correct"
						) {
							discordSyncMessage = ` Discord roles updated: ${discordResult.message}.`;
						}
					} catch (discordError) {
						console.error(
							"Discord role sync error during restoration:",
							discordError,
						);
						// Don't fail the entire operation for Discord sync issues
					}
				}

				return {
					success: true,
					message: `User ${targetUser.name} access has been restored.${discordSyncMessage}`,
				};
			} catch (error) {
				console.error("Failed to restore user:", error);
				if (error instanceof TRPCError) {
					throw error;
				}
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to restore user access",
					cause: error,
				});
			}
		}),

	// Recheck user API key (requires ADMIN_RECHECK_API_KEYS permission)
	recheckUserApiKey: permissionProcedure
		.use(requirePermission(PERMISSIONS.ADMIN_RECHECK_API_KEYS.name))
		.input(
			z.object({
				userId: z.string(),
			}),
		)
		.mutation(async ({ ctx, input }) => {
			try {
				// Check if user exists and get their API key
				const targetUser = await ctx.db
					.select({
						id: user.id,
						name: user.name,
						apiKey: tornUser.tornApiKey,
						suspensionType: tornUser.suspensionType,
						accessSuspended: tornUser.accessSuspended,
					})
					.from(user)
					.leftJoin(tornUser, eq(user.id, tornUser.id))
					.where(eq(user.id, input.userId))
					.get();

				if (!targetUser) {
					throw new TRPCError({
						code: "NOT_FOUND",
						message: "User not found",
					});
				}

				if (!targetUser.apiKey) {
					throw new TRPCError({
						code: "BAD_REQUEST",
						message: "User has no API key to recheck",
					});
				}

				// Use verifyAndUpdateUserTornInfo for comprehensive verification and role updates
				const { verifyAndUpdateUserTornInfo } = await import(
					"../lib/tornUserUtils"
				);

				const verificationResult = await verifyAndUpdateUserTornInfo(
					ctx.db,
					input.userId,
					targetUser.apiKey,
					ctx.env,
				);

				if (verificationResult.success) {
					// If user was suspended for API errors, restore them
					if (
						targetUser.accessSuspended &&
						targetUser.suspensionType === "api_error"
					) {
						await ctx.db
							.update(tornUser)
							.set({
								accessSuspended: false,
								accessSuspensionReason: null,
								accessSuspendedAt: null,
								suspensionType: null,
							})
							.where(eq(tornUser.id, input.userId));
					}

					// Sync Discord roles if user has Discord linked and bot is configured
					let discordSyncMessage = "";
					if (ctx.env.DISCORD_BOT_TOKEN && ctx.env.DISCORD_GUILD_ID) {
						try {
							// Import the sync function from the main app
							const { syncUserDiscordRoles } = await import("../index");
							const discordResult = await syncUserDiscordRoles(
								ctx.db,
								input.userId,
								ctx.env.DISCORD_GUILD_ID,
								ctx.env.DISCORD_BOT_TOKEN,
								true, // User is now verified and in faction
							);

							if (
								discordResult.success &&
								discordResult.message !== "Roles already correct"
							) {
								discordSyncMessage = ` Discord roles updated: ${discordResult.message}.`;
							}
						} catch (discordError) {
							console.error(
								"Discord role sync error during admin recheck:",
								discordError,
							);
							// Don't fail the entire operation for Discord sync issues
						}
					}

					const wasRestored =
						targetUser.accessSuspended &&
						targetUser.suspensionType === "api_error"
							? " and access restored"
							: "";
					return {
						success: true,
						message: `${targetUser.name}'s API key verified successfully${wasRestored}. ${verificationResult.message}${discordSyncMessage}`,
					};
				}
				// Verification failed - if they weren't suspended before, don't suspend them now for admin rechecks
				// Just update the check timestamp
				await ctx.db
					.update(tornUser)
					.set({
						tornApiKeyLastCheckedAt: new Date(),
					})
					.where(eq(tornUser.id, input.userId));

				return {
					success: false,
					message: `${targetUser.name}'s API key verification failed: ${verificationResult.message}`,
				};
			} catch (error) {
				console.error("Failed to recheck user API key:", error);
				if (error instanceof TRPCError) {
					throw error;
				}
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to recheck user API key",
					cause: error,
				});
			}
		}),

	// Delete a user (requires ADMIN_DELETE_USER permission - most dangerous operation)
	deleteUser: permissionProcedure
		.use(requirePermission(PERMISSIONS.ADMIN_DELETE_USER.name))
		.input(
			z.object({
				userId: z.string(),
				reason: z.string().min(1, "Deletion reason is required"),
			}),
		)
		.mutation(async ({ ctx, input }) => {
			try {
				// Check if user exists
				const targetUser = await ctx.db
					.select({ id: user.id, name: user.name })
					.from(user)
					.where(eq(user.id, input.userId))
					.get();

				if (!targetUser) {
					throw new TRPCError({
						code: "NOT_FOUND",
						message: "User not found",
					});
				}

				// Prevent self-deletion
				if (input.userId === ctx.session.userId) {
					throw new TRPCError({
						code: "FORBIDDEN",
						message: "Cannot delete your own account",
					});
				}

				// Delete the user (cascade will handle related records)
				await ctx.db.delete(user).where(eq(user.id, input.userId));

				return {
					success: true,
					message: `User ${targetUser.name} has been permanently deleted`,
				};
			} catch (error) {
				console.error("Failed to delete user:", error);
				if (error instanceof TRPCError) {
					throw error;
				}
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to delete user",
					cause: error,
				});
			}
		}),
});
