import { Server, ServerRequestHandler } from '../server';
/**
 * A generic server that uses Requests and Responses.
 */
export declare class GenericServer extends Server {
    private _handler?;
    constructor();
    /**
     * The endpoint handler for the server. This will return the response to send.
     * @example
     * export const server = new GenericServer();
     * creator.withServer(server);
     * return await server.endpoint(request);
     */
    readonly endpoint: (request: Request) => Promise<Response>;
    /** @private */
    createEndpoint(path: string, handler: ServerRequestHandler): void;
}
