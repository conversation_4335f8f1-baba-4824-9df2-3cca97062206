import type { GuideCategory } from "./types";

// Guide categories
export const GUIDE_CATEGORIES: GuideCategory[] = [
	{ id: "getting-started", label: "Getting Started", emoji: "🚀" },
	{ id: "banking", label: "Banking", emoji: "💰" },
	{ id: "target-finder", label: "Target Finder", emoji: "🎯" },
	{ id: "general", label: "General", emoji: "📚" },
];

// Helper function to get category info
export function getCategoryInfo(categoryId: string): GuideCategory {
	return (
		GUIDE_CATEGORIES.find((cat) => cat.id === categoryId) ||
		GUIDE_CATEGORIES[GUIDE_CATEGORIES.length - 1]
	);
}

// Helper function to extract preview text
export function getPreviewText(content: string, maxLength = 150): string {
	// Remove markdown formatting for preview
	const plainText = content
		.replace(/#{1,6}\s+/g, "") // Remove headers
		.replace(/\*\*(.*?)\*\*/g, "$1") // Remove bold
		.replace(/\*(.*?)\*/g, "$1") // Remove italic
		.replace(/`(.*?)`/g, "$1") // Remove inline code
		.replace(/\[(.*?)\]\(.*?\)/g, "$1") // Remove links
		.replace(/\n+/g, " ") // Replace newlines with spaces
		.trim();

	return plainText.length > maxLength
		? `${plainText.substring(0, maxLength)}...`
		: plainText;
}
