import { HasPermission } from "@/components/permissions/PermissionGuards";
import { usePermissions, useUserRoles } from "@/hooks/usePermissions";
import { useAdminLiveStoreSync } from "@/lib/admin-livestore-bridge";
import { trpc } from "@/lib/trpc-client";
import { adminSystemStats$, suspendedUserCount$ } from "@/livestore/queries";
import { useStore } from "@livestore/react";
import { PERMISSION_NAMES, ROLE_LEVELS } from "@monkeymenu/shared";
import { useQuery } from "@tanstack/react-query";
import { Link } from "@tanstack/react-router";
import { Settings, Shield, UserX, Users } from "lucide-react";
import { useMemo, useState } from "react";
import { AdminStats } from "./AdminStats";
import { AdminTools } from "./AdminTools";
import { SystemInfoDialog } from "./SystemInfoDialog";
import { UserManagementDialog } from "./UserManagementDialog";
import type { AdminStatConfig, AdminToolConfig } from "./types";
import { isUserSuspended } from "./utils";

// Define a strongly-typed interface for LiveStore count queries
interface LiveStoreCountResult {
	count: number;
}

// New interface for admin system stats fetched from LiveStore
interface AdminSystemStats {
	totalUsers?: number;
	totalRoles?: number;
	totalPermissions?: number;
	totalGuides?: number;
}

/**
 * Safely extract the `count` value from a LiveStore query result.
 * Returns `0` when the structure is not as expected.
 */
function extractLiveStoreCount(data: unknown): number {
	if (Array.isArray(data) && data.length > 0) {
		const first = data[0] as Partial<LiveStoreCountResult>;
		if (typeof first.count === "number") {
			return first.count;
		}
	}
	return 0;
}

export function AdminDashboard() {
	const [userManagementOpen, setUserManagementOpen] = useState(false);
	const [systemInfoOpen, setSystemInfoOpen] = useState(false);

	const permissions = usePermissions();
	const userRoles = useUserRoles();
	const isAdmin =
		(permissions.data?.roleLevel ?? 0) >= ROLE_LEVELS.MONKEY_MENTOR;

	// Initialize LiveStore sync for admin data (only if user is admin and permissions are loaded)
	useAdminLiveStoreSync(isAdmin && permissions.isSuccess);

	// Get system stats from LiveStore with tRPC fallback
	const { store } = useStore();
	const liveStoreStats = store.query(adminSystemStats$) as AdminSystemStats[];
	const liveStoreSuspendedCount = store.query(
		suspendedUserCount$,
	) as LiveStoreCountResult[];

	// Get system stats with polling (fallback)
	const systemStats = useQuery({
		...trpc.admin.getSystemStats.queryOptions(),
		enabled: isAdmin,
		refetchOnWindowFocus: true,
		refetchInterval: 5 * 60 * 1000, // 5 minutes
		refetchIntervalInBackground: false,
	});

	// Calculate suspended users from LiveStore or fallback to userRoles
	const suspendedUsers = useMemo(() => {
		// Prefer LiveStore data if available
		if (
			Array.isArray(liveStoreSuspendedCount) &&
			liveStoreSuspendedCount.length > 0
		) {
			return extractLiveStoreCount(liveStoreSuspendedCount);
		}
		// Fallback to userRoles calculation
		if (!userRoles.data) return 0;
		return userRoles.data.filter((assignment) => isUserSuspended(assignment))
			.length;
	}, [liveStoreSuspendedCount, userRoles.data]);

	// Use LiveStore stats if available, otherwise fallback to tRPC
	const currentStats = useMemo(() => {
		if (Array.isArray(liveStoreStats) && liveStoreStats.length > 0) {
			const stats = liveStoreStats[0] as AdminSystemStats;
			return {
				totalUsers: stats?.totalUsers || 0,
				totalRoles: stats?.totalRoles || 0,
				totalPermissions: stats?.totalPermissions || 0,
				totalGuides: stats?.totalGuides || 0,
			};
		}
		return (
			systemStats.data || {
				totalUsers: userRoles.data?.length || 0,
				totalRoles: 0,
				totalPermissions: 0,
				totalGuides: 0,
			}
		);
	}, [liveStoreStats, systemStats.data, userRoles.data]);

	// Admin tools following the main dashboard pattern
	const adminTools: AdminToolConfig[] = [
		{
			title: "User Management",
			description: "Suspend, restore, or delete user accounts",
			icon: Users,
			category: "Management",
			priority: 1,
			onClick: () => setUserManagementOpen(true),
			available: true,
		},
		{
			title: "System Information",
			description: "View roles, permissions, and system status",
			icon: Settings,
			category: "Information",
			priority: 2,
			onClick: () => setSystemInfoOpen(true),
			available: true,
		},
	]
		.filter((tool) => tool.available)
		.sort((a, b) => a.priority - b.priority);

	// Quick stats for admin
	const adminStats: AdminStatConfig[] = [
		{
			title: "Total Users",
			value: currentStats.totalUsers,
			icon: Users,
			isLoading: systemStats.isLoading || userRoles.isLoading,
			description: "Active faction members",
		},
		{
			title: "Suspended Users",
			value: suspendedUsers,
			icon: UserX,
			isLoading: userRoles.isLoading,
			description:
				suspendedUsers > 0 ? "Users with suspended access" : "All users active",
		},
		{
			title: "Your Admin Level",
			value: `Level ${permissions.data?.roleLevel || 0}`,
			icon: Shield,
			isLoading: permissions.isLoading,
			description: permissions.data?.roleName || "No role assigned",
		},
	];

	return (
		<HasPermission permission={PERMISSION_NAMES.ADMIN_VIEW}>
			<div className="space-y-6">
				{/* Header Section */}
				<div>
					<h1 className="font-bold text-3xl text-foreground">
						🛡️ Admin Dashboard
					</h1>
					<p className="text-muted-foreground">
						Monitor and manage your faction with powerful admin tools
					</p>
				</div>

				{/* Quick Admin Stats */}
				<AdminStats stats={adminStats} />

				{/* Admin Tools Section */}
				<AdminTools tools={adminTools} />

				{/* Footer Info */}
				<div className="text-center text-muted-foreground text-sm">
					<p>
						Need help with admin tasks? Check our{" "}
						<Link to="/guides" className="underline hover:text-foreground">
							admin guide
						</Link>{" "}
						or contact system administrators.
					</p>
				</div>
			</div>

			{/* Dialogs */}
			<UserManagementDialog
				isOpen={userManagementOpen}
				onClose={() => setUserManagementOpen(false)}
			/>
			<SystemInfoDialog
				isOpen={systemInfoOpen}
				onClose={() => setSystemInfoOpen(false)}
			/>
		</HasPermission>
	);
}
