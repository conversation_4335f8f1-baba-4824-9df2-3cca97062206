import { HasPermission } from "@/components/permissions/PermissionGuards";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
	<PERSON>,
	<PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>er,
	<PERSON><PERSON><PERSON>er,
	CardTitle,
} from "@/components/ui/card";
import { PERMISSION_NAMES } from "@monkeymenu/shared";
import {
	BookOpen,
	Calendar,
	Clock,
	Edit,
	Eye,
	Trash2,
	User,
} from "lucide-react";
import type { ViewMode } from "./types";
import { getCategoryInfo, getPreviewText } from "./utils";

interface GuideData {
	guide: {
		id: number;
		title: string;
		content: string;
		category: string;
		createdAt: string;
		updatedAt: string;
	};
	author?: {
		name: string;
	};
}

interface GuideCardProps {
	guide: GuideData;
	viewMode: ViewMode;
	onView: (guide: GuideData) => void;
	onEdit: (guide: GuideData) => void;
	onDelete: (id: number) => void;
}

export function GuideCard({
	guide,
	viewMode,
	onView,
	onEdit,
	onDelete,
}: GuideCardProps) {
	const categoryInfo = getCategoryInfo(guide.guide.category);

	const handleDelete = () => {
		if (confirm("Are you sure you want to delete this guide?")) {
			onDelete(guide.guide.id);
		}
	};

	return (
		<Card className="group transition-shadow hover:shadow-md">
			<CardHeader className="pb-3">
				<div className="flex items-start justify-between gap-2">
					<div className="min-w-0 flex-1">
						<div className="mb-2 flex items-center gap-2">
							<Badge variant="secondary" className="text-xs">
								{categoryInfo.emoji} {categoryInfo.label}
							</Badge>
						</div>
						<CardTitle className="line-clamp-2 text-lg leading-tight">
							{guide.guide.title}
						</CardTitle>
					</div>
					<div className="flex gap-1 opacity-0 transition-opacity group-hover:opacity-100">
						<Button
							variant="ghost"
							size="sm"
							onClick={() => onView(guide)}
							className="h-8 w-8 p-0"
						>
							<Eye className="h-4 w-4" />
						</Button>
						<HasPermission permission={PERMISSION_NAMES.GUIDES_MANAGE}>
							<Button
								variant="ghost"
								size="sm"
								onClick={() => onEdit(guide)}
								className="h-8 w-8 p-0"
							>
								<Edit className="h-4 w-4" />
							</Button>
						</HasPermission>
						<HasPermission permission={PERMISSION_NAMES.GUIDES_MANAGE}>
							<Button
								variant="ghost"
								size="sm"
								onClick={handleDelete}
								className="h-8 w-8 p-0 text-destructive hover:text-destructive"
							>
								<Trash2 className="h-4 w-4" />
							</Button>
						</HasPermission>
					</div>
				</div>
				<div className="flex items-center gap-4 text-muted-foreground text-xs">
					<div className="flex items-center gap-1">
						<User className="h-3 w-3" />
						{guide.author?.name || "Unknown"}
					</div>
					<div className="flex items-center gap-1">
						<Calendar className="h-3 w-3" />
						{new Date(guide.guide.createdAt).toLocaleDateString()}
					</div>
					{guide.guide.updatedAt !== guide.guide.createdAt && (
						<div className="flex items-center gap-1">
							<Clock className="h-3 w-3" />
							Updated
						</div>
					)}
				</div>
			</CardHeader>
			{viewMode === "grid" && (
				<CardContent className="pt-0">
					<p className="line-clamp-3 text-muted-foreground text-sm">
						{getPreviewText(guide.guide.content)}
					</p>
				</CardContent>
			)}
			<CardFooter className="pt-0">
				<Button
					variant="outline"
					className="w-full gap-2"
					onClick={() => onView(guide)}
				>
					<BookOpen className="h-4 w-4" />
					Read Guide
				</Button>
			</CardFooter>
		</Card>
	);
}
