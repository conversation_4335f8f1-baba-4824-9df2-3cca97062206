# LiveStore Authentication Security Upgrade

## Overview

This document outlines the changes made to upgrade LiveStore authentication from deterministic token generation to proper session-based authentication using better-auth.

## Changes Made

### Client-Side (`apps/web/src/lib/livestore-provider.tsx`)

1. **Session Token Retrieval**: Replaced deterministic token generation with proper session token fetching from better-auth
   - Added `useEffect` to fetch session token when `userId` changes
   - Use `authClient.getSession()` to get actual session tokens
   - Fallback to anonymous token on error or missing session

2. **Security Improvements**: 
   - Real session tokens instead of predictable `session_${userId}` format
   - Proper error handling with fallback mechanisms
   - Better logging for debugging authentication issues

### Server-Side (`apps/server/src/lib/livestore-sync.ts`)

1. **Enhanced Token Validation**: 
   - Added `validateSessionToken()` method with **full database validation**
   - Checks session existence and expiration against better-auth database
   - Improved token format validation using regex patterns
   - Better error messages and security warnings
   - Session usage tracking for monitoring

2. **Session-Based Security**:
   - Validates better-auth session token format
   - Allows anonymous tokens for unauthenticated users
   - Enhanced logging with user context

3. **Worker Configuration**:
   - Updated `makeWorker` validation to handle session tokens
   - Added support for `userId` in payload validation
   - Improved error handling and logging

## Security Benefits

1. **No More Predictable Tokens**: Eliminates collision-prone deterministic token generation
2. **Full Database Session Validation**: Validates sessions against database with expiration checking
3. **Real-time Session Status**: Automatically rejects expired or revoked sessions
4. **Enhanced Input Validation**: Multiple layers of security checks including size limits and format validation
5. **Session Usage Tracking**: Monitors session usage for analytics and security
6. **Better Error Handling**: Graceful fallbacks and detailed logging for debugging

## TODO: Complete Implementation

### High Priority

1. **✅ Database Session Validation** (Server-side): **COMPLETED**
   - Added full database validation in `validateSessionToken()` method
   - Checks session existence and expiration against better-auth session table
   - Returns detailed session information (userId, sessionId)
   - Includes session usage tracking for analytics

2. **✅ Fix Client-Side Type Issues**: **COMPLETED**
   - Resolved `syncPayload` type compatibility with LiveStore
   - All TypeScript compilation errors fixed

### Medium Priority

3. **JWT Token Support** (if needed):
   ```typescript
   // Add JWT verification for signed tokens
   import jwt from 'jsonwebtoken';
   
   const decoded = jwt.verify(token, env.BETTER_AUTH_SECRET);
   ```

4. **Rate Limiting**: Add rate limiting for session validation requests

5. **Session Refresh**: Handle session token refresh when tokens expire

### Low Priority

6. **Metrics and Monitoring**: Add authentication metrics for monitoring
7. **Advanced Session Features**: Support for device tracking, session revocation
8. **Audit Logging**: Log authentication events for security auditing

## Configuration Required

Ensure the following environment variables are set:

```bash
# Better Auth Configuration
BETTER_AUTH_SECRET=your-secret-key
BETTER_AUTH_URL=https://your-domain.com
CORS_ORIGIN=https://your-frontend-domain.com

# Database Connection (already configured)
DATABASE_URL=your-database-url
```

## Testing Recommendations

1. **Test Anonymous Access**: Verify anonymous users can still access LiveStore
2. **Test Authenticated Access**: Verify authenticated users get proper session tokens
3. **Test Token Expiration**: Ensure expired tokens are properly rejected
4. **Test Error Scenarios**: Verify graceful handling of malformed tokens

## Migration Notes

- **Backward Compatibility**: The changes maintain backward compatibility with anonymous users
- **Gradual Rollout**: Session validation can be enhanced incrementally
- **Monitoring**: Watch for authentication errors during initial deployment

## Security Considerations

- Session tokens are now cryptographically secure (UUIDs from better-auth)
- Multiple validation layers prevent various attack vectors
- Proper error handling prevents information leakage
- Anonymous access remains supported for public features

This upgrade significantly improves the security posture of LiveStore authentication while maintaining functionality and user experience. 