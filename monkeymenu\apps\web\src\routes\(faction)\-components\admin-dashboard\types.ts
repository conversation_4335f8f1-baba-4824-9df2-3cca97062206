export interface AdminToolConfig {
	title: string;
	description: string;
	icon: React.ComponentType<{ className?: string }>;
	category: string;
	priority: number;
	onClick: () => void;
	available: boolean;
}

export interface AdminStatConfig {
	title: string;
	value: string | number;
	icon: React.ComponentType<{ className?: string }>;
	isLoading: boolean;
	description: string;
}

export interface UserRoleAssignment {
	user: {
		id: string;
		name: string | null;
		email: string | null;
	};
	role: {
		id: number;
		name: string;
		displayName: string;
		hierarchyLevel: number;
	};
	suspensionStatus?: {
		accessSuspended: boolean | null;
		accessSuspensionReason: string | null;
		accessSuspendedAt: string | null; // This comes from the API as a string
		suspensionType: "admin" | "api_error" | null;
	} | null;
}

export interface RoleWithPermissions {
	role: {
		id: number;
		name: string;
		displayName: string;
		hierarchyLevel: number;
	};
	permissions: Array<{
		name: string;
	}>;
}

export interface SystemStats {
	totalUsers: number;
}

export interface UserManagementDialogProps {
	isOpen: boolean;
	onClose: () => void;
}

export interface SystemInfoDialogProps {
	isOpen: boolean;
	onClose: () => void;
}

export interface AdminStatsProps {
	stats: AdminStatConfig[];
}

export interface AdminToolsProps {
	tools: AdminToolConfig[];
}

export interface UserTableProps {
	userRoles: UserRoleAssignment[] | undefined;
	isLoading: boolean;
	searchQuery: string;
	onSuspendUser: (userId: string) => void;
	onRestoreUser: (userId: string) => void;
	onDeleteUser: (userId: string) => void;
	onRecheckApiKey: (userId: string) => void;
	isRecheckingApiKey: boolean;
	isRestoring: boolean;
}

export interface RoleHierarchyProps {
	roles: RoleWithPermissions[] | undefined;
	isLoading: boolean;
	currentUserRoleName?: string | null;
}

export interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
	className?: string;
}
