import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { AlertCircle, Target } from "lucide-react";
import { TargetCard } from "./TargetCard";
import { TargetListItem } from "./TargetListItem";
import type { TargetWithStatus } from "./types";

interface TargetContentProps {
	selectedList: string;
	isLoading: boolean;
	hasError: boolean;
	errorMessage?: string;
	filteredTargets: TargetWithStatus[];
	allTargets: TargetWithStatus[];
	searchTerm: string;
	statusFilter: string;
	viewMode: "grid" | "list";
	localStatuses: Record<string, string>;
	onRemoveTarget: (tornId: string) => Promise<void>;
	isRemoving: boolean;
	onRefresh: () => void;
	onClearFilters: () => void;
}

export function TargetContent({
	selectedList,
	isLoading,
	hasError,
	errorMessage,
	filteredTargets,
	allTargets,
	searchTerm,
	statusFilter,
	viewMode,
	localStatuses,
	onRemoveTarget,
	isRemoving,
	onRefresh,
	onClearFilters,
}: TargetContentProps) {
	// No list selected
	if (selectedList === "") {
		return (
			<Card>
				<CardContent className="py-12 text-center">
					<Target className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
					<h3 className="mb-2 font-semibold text-lg">Select a Target List</h3>
					<p className="text-muted-foreground">
						Choose a target list to view and manage your targets
					</p>
				</CardContent>
			</Card>
		);
	}

	// Loading state
	if (isLoading) {
		return (
			<div className="flex items-center justify-center py-12">
				<div className="text-center">
					<div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-primary border-b-2" />
					<p className="text-muted-foreground">Loading targets...</p>
				</div>
			</div>
		);
	}

	// Error state
	if (hasError) {
		return (
			<Card>
				<CardContent className="py-12 text-center">
					<AlertCircle className="mx-auto mb-4 h-12 w-12 text-red-500" />
					<h3 className="mb-2 font-semibold text-lg text-red-600">
						Error loading targets
					</h3>
					<p className="text-muted-foreground text-sm">{errorMessage}</p>
					<Button variant="outline" onClick={onRefresh} className="mt-4">
						Try Again
					</Button>
				</CardContent>
			</Card>
		);
	}

	// No targets found
	if (filteredTargets.length === 0) {
		return (
			<Card>
				<CardContent className="py-12 text-center">
					<Target className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
					<h3 className="mb-2 font-semibold text-lg">No targets found</h3>
					<p className="text-muted-foreground">
						{allTargets.length === 0
							? "This list doesn't contain any targets yet."
							: "No targets match your current filters."}
					</p>
					{searchTerm || statusFilter !== "all" ? (
						<Button variant="outline" onClick={onClearFilters} className="mt-4">
							Clear Filters
						</Button>
					) : null}
				</CardContent>
			</Card>
		);
	}

	// Render targets
	return (
		<div
			className={
				viewMode === "grid"
					? "grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
					: "space-y-4"
			}
		>
			{filteredTargets.map((target) => {
				const currentStatus = localStatuses[target.tornId] ?? target.status;

				if (viewMode === "grid") {
					return (
						<TargetCard
							key={target.id}
							target={target}
							currentStatus={currentStatus}
							selectedList={selectedList}
							onRemoveTarget={onRemoveTarget}
							isRemoving={isRemoving}
						/>
					);
				}

				return (
					<TargetListItem
						key={target.id}
						target={target}
						currentStatus={currentStatus}
						selectedList={selectedList}
						onRemoveTarget={onRemoveTarget}
						isRemoving={isRemoving}
					/>
				);
			})}
		</div>
	);
}
