import { TORN_API_ERRORS, getTornAPIErrorMessage } from "@monkeymenu/shared";
import { eq } from "drizzle-orm";
import type { DrizzleD1Database } from "drizzle-orm/d1";
import { factionRole, userRole } from "../db/schema/permissions";
import { tornUser } from "../db/schema/torn";
import { decrypt, encrypt } from "./crypto";
import { getDbRoleFromTornData } from "./permissions";
import {
	TornAPI,
	type TornAPIResponse,
	TornAPIWithSuspension,
} from "./tornApi";

export interface UpdateUserTornInfoOptions {
	apiKey?: string | null;
	verified: boolean;
	tornUserId: string | number | null;
	tornFactionId: string | number | null;
	tornApiKeyLastCheckedAt: Date | null;
	// Optional suspension fields for comprehensive updates
	accessSuspended?: boolean;
	accessSuspensionReason?: string | null;
	accessSuspendedAt?: Date | null;
	suspensionType?: "admin" | "api_error" | null;
}

export interface SuspensionUpdateOptions {
	accessSuspended: boolean;
	accessSuspensionReason?: string | null;
	accessSuspendedAt?: Date | null;
	suspensionType?: "admin" | "api_error" | null;
}

/**
 * Validates suspension data integrity rules:
 * - If accessSuspended is true, accessSuspensionReason must be provided and not empty
 * - If accessSuspended is false, accessSuspensionReason should be null (will be normalized)
 * - Automatically handles accessSuspendedAt timestamp
 *
 * @param options - Suspension update options to validate
 * @returns Validation result with normalized options
 */
export function validateSuspensionData(options: SuspensionUpdateOptions): {
	isValid: boolean;
	error?: string;
	normalizedOptions: Required<SuspensionUpdateOptions>;
} {
	const {
		accessSuspended,
		accessSuspensionReason,
		accessSuspendedAt,
		suspensionType,
	} = options;

	if (accessSuspended) {
		// When suspending, reason and type are required
		if (!accessSuspensionReason || accessSuspensionReason.trim() === "") {
			return {
				isValid: false,
				error:
					"accessSuspensionReason is required when accessSuspended is true",
				normalizedOptions: {
					accessSuspended,
					accessSuspensionReason: null,
					accessSuspendedAt: null,
					suspensionType: null,
				},
			};
		}

		if (
			!suspensionType ||
			(suspensionType !== "admin" && suspensionType !== "api_error")
		) {
			return {
				isValid: false,
				error:
					"suspensionType must be 'admin' or 'api_error' when accessSuspended is true",
				normalizedOptions: {
					accessSuspended,
					accessSuspensionReason: null,
					accessSuspendedAt: null,
					suspensionType: null,
				},
			};
		}

		return {
			isValid: true,
			normalizedOptions: {
				accessSuspended: true,
				accessSuspensionReason: accessSuspensionReason.trim(),
				accessSuspendedAt: accessSuspendedAt || new Date(),
				suspensionType: suspensionType,
			},
		};
	}

	// When not suspended, clear suspension data
	return {
		isValid: true,
		normalizedOptions: {
			accessSuspended: false,
			accessSuspensionReason: null,
			accessSuspendedAt: null,
			suspensionType: null,
		},
	};
}

export async function updateUserTornInfo(
	db: DrizzleD1Database,
	userId: string,
	options: UpdateUserTornInfoOptions,
): Promise<void> {
	const {
		apiKey,
		verified,
		tornUserId,
		tornFactionId,
		tornApiKeyLastCheckedAt,
		accessSuspended,
		accessSuspensionReason,
		accessSuspendedAt,
		suspensionType,
	} = options;
	// Use upsert: insert if not exists, else update

	// Only process API key if one is provided
	let encryptedApiKey: string | undefined;
	if (apiKey?.trim()) {
		try {
			// Always encrypt the API key before storing
			encryptedApiKey = encrypt(apiKey);
		} catch (error) {
			console.error("Failed to process API key:", error);
			throw new Error("Failed to securely store API key");
		}
	}

	// Validate and normalize suspension data if provided
	let suspensionData: Partial<Required<SuspensionUpdateOptions>> = {};
	if (accessSuspended !== undefined) {
		// For updateUserTornInfo, if suspensionType is not provided but accessSuspended is true,
		// default to "admin" type for manual updates
		const validation = validateSuspensionData({
			accessSuspended,
			accessSuspensionReason,
			accessSuspendedAt,
			suspensionType: suspensionType || (accessSuspended ? "admin" : null),
		});

		if (!validation.isValid) {
			throw new Error(`Invalid suspension data: ${validation.error}`);
		}

		suspensionData = validation.normalizedOptions;
	}

	// Prepare base values
	const values = {
		id: userId,
		tornApiKey: encryptedApiKey ?? null,
		tornApiKeyVerified: verified,
		tornUserId: tornUserId ? String(tornUserId) : null,
		tornFactionId: tornFactionId ? String(tornFactionId) : null,
		tornApiKeyLastCheckedAt: tornApiKeyLastCheckedAt,
		...suspensionData,
	};

	// Prepare update set (only include suspension data if it was provided)
	const updateSet: Partial<typeof tornUser.$inferInsert> = {
		tornApiKeyVerified: verified,
		tornUserId: tornUserId ? String(tornUserId) : null,
		tornFactionId: tornFactionId ? String(tornFactionId) : null,
		tornApiKeyLastCheckedAt: tornApiKeyLastCheckedAt,
	};

	// Only include tornApiKey in update if encryptedApiKey is defined
	if (encryptedApiKey !== undefined) {
		updateSet.tornApiKey = encryptedApiKey;
	}

	if (accessSuspended !== undefined) {
		Object.assign(updateSet, suspensionData);
	}

	await db
		.insert(tornUser)
		.values(values)
		.onConflictDoUpdate({
			target: tornUser.id,
			set: updateSet,
		})
		.run();
}

/**
 * Comprehensive background verification that re-verifies API keys
 * and updates user roles based on current Torn faction position
 */
export async function verifyAndUpdateUserTornInfo(
	db: DrizzleD1Database,
	userId: string,
	encryptedApiKey: string,
	env?: {
		SYSTEM_ADMIN_TORN_IDS?: string;
		DISCORD_BOT_TOKEN?: string;
		DISCORD_GUILD_ID?: string;
	},
	isRestoration = false,
): Promise<{ success: boolean; message: string }> {
	try {
		// Guard clause: Check if API key is empty or not set
		if (!encryptedApiKey || encryptedApiKey.trim() === "") {
			console.warn(`No API key found for user ${userId}`);
			return {
				success: false,
				message: "No API key on file",
			};
		}

		// Decrypt the API key
		let decryptedApiKey: string;
		try {
			decryptedApiKey = decrypt(encryptedApiKey);
		} catch (error) {
			console.error(`Failed to decrypt API key for user ${userId}:`, error);
			return {
				success: false,
				message: "Failed to decrypt stored API key",
			};
		}

		// Verify with Torn API
		// For restoration, use regular TornAPI to prevent automatic re-suspension
		const tornApi = isRestoration
			? new TornAPI(decryptedApiKey)
			: new TornAPIWithSuspension(decryptedApiKey, db, userId, env);
		let data: TornAPIResponse;

		try {
			data = await tornApi.getUserProfile();
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : "Unknown error occurred";
			console.warn(
				`API verification failed for user ${userId}: ${errorMessage}`,
			);

			// Update verification status to false but keep the key
			await updateUserTornInfo(db, userId, {
				verified: false,
				tornUserId: null,
				tornFactionId: null,
				tornApiKeyLastCheckedAt: new Date(),
			});

			return {
				success: false,
				message: `API verification failed: ${errorMessage}`,
			};
		}

		// Check for API errors
		if (data.error) {
			console.warn(
				`Torn API error for user ${userId}: ${data.error.error} (Code: ${data.error.code})`,
			);

			// For restoration, don't automatically suspend again - just return failure
			if (isRestoration) {
				return {
					success: false,
					message: `API verification failed: ${data.error.error} (Code: ${data.error.code}). Please update your API key.`,
				};
			}

			// Check if this error should trigger suspension (only for non-restoration calls)
			if (shouldSuspendAccessForError(data.error.code)) {
				const reason = getSuspensionReason(data.error.code);
				await suspendUserAccess(db, userId, reason, data.error.code, env);
				console.log(
					`User ${userId} access suspended due to API error ${data.error.code}`,
				);
			} else {
				// For non-suspension errors, just mark as unverified
				await updateUserTornInfo(db, userId, {
					verified: false,
					tornUserId: null,
					tornFactionId: null,
					tornApiKeyLastCheckedAt: new Date(),
				});
			}

			return {
				success: false,
				message: `Torn API Error: ${data.error.error} (Code: ${data.error.code})`,
			};
		}

		const tornUserIdResponse = data.player_id;
		const tornFactionIdResponse = data.faction?.faction_id;
		const tornFactionPosition = data.faction?.position;

		// Validate required data
		if (!tornUserIdResponse) {
			await updateUserTornInfo(db, userId, {
				verified: false,
				tornUserId: null,
				tornFactionId: null,
				tornApiKeyLastCheckedAt: new Date(),
			});

			return {
				success: false,
				message: "Failed to retrieve User ID from Torn API",
			};
		}

		// Check if this Torn User ID is already associated with another user account
		// For background verification, this is more of a warning since they may have had legitimate access before
		const existingTornUser = await db
			.select({
				userId: tornUser.id,
				tornUserId: tornUser.tornUserId,
			})
			.from(tornUser)
			.where(eq(tornUser.tornUserId, String(tornUserIdResponse)))
			.get();

		if (existingTornUser && existingTornUser.userId !== userId) {
			// In background verification, mark this user as unverified due to conflict
			await updateUserTornInfo(db, userId, {
				verified: false,
				tornUserId: tornUserIdResponse, // Keep the ID for admin reference
				tornFactionId: null,
				tornApiKeyLastCheckedAt: new Date(),
			});

			return {
				success: false,
				message: `Torn account conflict: ID ${tornUserIdResponse} is linked to another user. Manual review required.`,
			};
		}

		if (!tornFactionIdResponse || tornFactionIdResponse === 0) {
			await updateUserTornInfo(db, userId, {
				verified: false,
				tornUserId: tornUserIdResponse,
				tornFactionId: null,
				tornApiKeyLastCheckedAt: new Date(),
			});

			return {
				success: false,
				message: "User is not currently in a faction",
			};
		}

		// Verify user is in the correct faction (53100)
		const requiredFactionId = "53100";
		if (String(tornFactionIdResponse) !== requiredFactionId) {
			await updateUserTornInfo(db, userId, {
				verified: false,
				tornUserId: tornUserIdResponse,
				tornFactionId: tornFactionIdResponse, // Store their actual faction for reference
				tornApiKeyLastCheckedAt: new Date(),
			});

			return {
				success: false,
				message: `User is in faction #${tornFactionIdResponse}, but access requires faction #${requiredFactionId}`,
			};
		}

		// If we reach here, verification is successful
		// For restoration, clear the suspension status along with updating Torn info
		const updateOptions: UpdateUserTornInfoOptions = {
			verified: true,
			tornUserId: tornUserIdResponse,
			tornFactionId: tornFactionIdResponse,
			tornApiKeyLastCheckedAt: new Date(),
		};

		// If this is a restoration, also clear suspension status
		if (isRestoration) {
			updateOptions.accessSuspended = false;
			updateOptions.accessSuspensionReason = null;
			updateOptions.accessSuspendedAt = null;
			updateOptions.suspensionType = null;
		}

		// Update basic Torn info (successful verification)
		await updateUserTornInfo(db, userId, updateOptions);

		// Update role based on Torn faction position and admin status
		let roleUpdateMessage = "";
		if (tornFactionPosition || tornUserIdResponse) {
			try {
				// Get the database role name with System Admin check
				const dbRoleName = getDbRoleFromTornData(
					tornFactionPosition,
					tornUserIdResponse,
					env,
				);

				if (dbRoleName) {
					// Look up the role in the database
					const role = await db
						.select()
						.from(factionRole)
						.where(eq(factionRole.name, dbRoleName))
						.get();

					if (role) {
						// Check if user already has a role assigned
						const existingUserRole = await db
							.select()
							.from(userRole)
							.where(eq(userRole.userId, userId))
							.get();

						if (existingUserRole) {
							// Update existing role if it's different
							if (existingUserRole.roleId !== role.id) {
								await db
									.update(userRole)
									.set({
										roleId: role.id,
										assignedAt: new Date(),
									})
									.where(eq(userRole.userId, userId))
									.run();

								roleUpdateMessage = ` Role updated to ${role.displayName} based on Torn faction position.`;
							} else {
								roleUpdateMessage = ` Role ${role.displayName} confirmed as current.`;
							}
						} else {
							// Assign new role
							await db.insert(userRole).values({
								userId: userId,
								roleId: role.id,
								assignedAt: new Date(),
							});

							roleUpdateMessage = ` Assigned ${role.displayName} role based on Torn faction position.`;
						}
					} else {
						console.warn(
							`Role not found for Torn position: ${tornFactionPosition} (mapped to: ${dbRoleName})`,
						);
						roleUpdateMessage = " Role will be assigned manually by an admin.";
					}
				}
			} catch (error) {
				console.error(`Failed to update role for user ${userId}:`, error);
				roleUpdateMessage =
					" Role update failed, will be done manually by an admin.";
			}
		}

		return {
			success: true,
			message: `Verification successful.${roleUpdateMessage}`,
		};
	} catch (error) {
		console.error(`Unexpected error verifying user ${userId}:`, error);

		// Update timestamp to prevent constant retries
		await updateUserTornInfo(db, userId, {
			verified: false,
			tornUserId: null,
			tornFactionId: null,
			tornApiKeyLastCheckedAt: new Date(),
		});

		return {
			success: false,
			message: "Unexpected error during verification",
		};
	}
}

/**
 * Suspends user access due to API key issues and records the reason
 */
export async function suspendUserAccess(
	db: DrizzleD1Database,
	userId: string,
	reason: string,
	tornApiErrorCode?: number,
	env?: {
		DISCORD_BOT_TOKEN?: string;
		DISCORD_GUILD_ID?: string;
	},
): Promise<void> {
	// Validate suspension data integrity - API error suspensions are automatic
	const validation = validateSuspensionData({
		accessSuspended: true,
		accessSuspensionReason: reason,
		accessSuspendedAt: new Date(),
		suspensionType: "api_error",
	});

	if (!validation.isValid) {
		throw new Error(`Invalid suspension data: ${validation.error}`);
	}

	const suspensionData = {
		...validation.normalizedOptions,
		lastTornApiError: tornApiErrorCode ?? null,
		lastTornApiErrorAt: tornApiErrorCode ? new Date() : null,
	};

	await db
		.update(tornUser)
		.set(suspensionData)
		.where(eq(tornUser.id, userId))
		.run();

	// Sync Discord roles if environment credentials are provided
	if (env?.DISCORD_BOT_TOKEN && env?.DISCORD_GUILD_ID) {
		try {
			// Import the sync function from the main app
			const { syncUserDiscordRoles } = await import("../index");
			const discordResult = await syncUserDiscordRoles(
				db,
				userId,
				env.DISCORD_GUILD_ID,
				env.DISCORD_BOT_TOKEN,
				false, // User is now suspended - not in faction
			);

			if (
				discordResult.success &&
				discordResult.message !== "Roles already correct"
			) {
				console.log(
					`[API Suspension] Discord roles updated for user ${userId}: ${discordResult.message}`,
				);
			}
		} catch (discordError) {
			console.error(
				`[API Suspension] Discord role sync error for user ${userId}:`,
				discordError,
			);
			// Don't fail the suspension for Discord sync issues
		}
	}

	console.log(
		`Access suspended for user ${userId}: ${reason} (type: api_error)`,
	);
}

/**
 * Restores user access after resolving API key issues
 */
export async function restoreUserAccess(
	db: DrizzleD1Database,
	userId: string,
): Promise<void> {
	// Validate restoration data integrity
	const validation = validateSuspensionData({
		accessSuspended: false,
	});

	if (!validation.isValid) {
		throw new Error(`Invalid restoration data: ${validation.error}`);
	}

	await db
		.update(tornUser)
		.set(validation.normalizedOptions)
		.where(eq(tornUser.id, userId))
		.run();

	console.log(`Access restored for user ${userId}`);
}

/**
 * Updates user suspension status with data integrity validation
 * This is a general-purpose function that enforces business rules
 *
 * Examples:
 * - Suspend: updateUserSuspensionStatus(db, userId, { accessSuspended: true, accessSuspensionReason: "API key invalid" })
 * - Restore: updateUserSuspensionStatus(db, userId, { accessSuspended: false })
 */
export async function updateUserSuspensionStatus(
	db: DrizzleD1Database,
	userId: string,
	suspensionOptions: SuspensionUpdateOptions,
): Promise<void> {
	// Validate suspension data integrity
	const validation = validateSuspensionData(suspensionOptions);

	if (!validation.isValid) {
		throw new Error(`Invalid suspension data: ${validation.error}`);
	}

	await db
		.update(tornUser)
		.set(validation.normalizedOptions)
		.where(eq(tornUser.id, userId))
		.run();

	const action = validation.normalizedOptions.accessSuspended
		? "suspended"
		: "restored";
	const reason = validation.normalizedOptions.accessSuspensionReason
		? ` (${validation.normalizedOptions.accessSuspensionReason})`
		: "";
	console.log(`Access ${action} for user ${userId}${reason}`);
}

/**
 * Checks if a Torn API error should result in immediate access suspension
 */
export function shouldSuspendAccessForError(errorCode: number): boolean {
	// These errors indicate the API key is temporarily or permanently unusable
	const suspensionErrors: number[] = [
		TORN_API_ERRORS.KEY_PAUSED_BY_OWNER,
		TORN_API_ERRORS.KEY_DISABLED_INACTIVITY,
		TORN_API_ERRORS.KEY_OWNER_IN_FEDERAL_JAIL,
		TORN_API_ERRORS.ACCESS_LEVEL_INSUFFICIENT,
		TORN_API_ERRORS.INCORRECT_KEY,
		TORN_API_ERRORS.KEY_READ_ERROR,
	];

	return suspensionErrors.includes(errorCode);
}

/**
 * Gets a human-readable reason for access suspension based on error code
 */
export function getSuspensionReason(errorCode: number): string {
	switch (errorCode) {
		case TORN_API_ERRORS.KEY_PAUSED_BY_OWNER:
			return "API key was paused by owner";
		case TORN_API_ERRORS.KEY_DISABLED_INACTIVITY:
			return "API key disabled due to inactivity";
		case TORN_API_ERRORS.KEY_OWNER_IN_FEDERAL_JAIL:
			return "API key owner is in federal jail";
		case TORN_API_ERRORS.ACCESS_LEVEL_INSUFFICIENT:
			return "API key access level insufficient";
		case TORN_API_ERRORS.INCORRECT_KEY:
			return "API key is incorrect or invalid";
		case TORN_API_ERRORS.KEY_READ_ERROR:
			return "Error reading API key from database";
		default:
			// For other error codes, use the general error message function
			return getTornAPIErrorMessage(errorCode);
	}
}
