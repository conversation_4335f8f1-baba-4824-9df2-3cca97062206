declare module "@livestore/sync-cf" {
	import type { SyncBackendConstructor } from "@livestore/common";

	/** Options for establishing a WebSocket sync backend against the Cloudflare Durable Object live sync endpoint. */
	export interface WsSyncOptions {
		/** Full websocket (ws/wss) URL pointing to the /api/livestore endpoint. */
		url: string;
	}

	/** Returns a LiveStore sync backend implementation that communicates with the Cloudflare Worker via WebSocket. */
	export const makeCfSync: (
		options: WsSyncOptions,
	) => SyncBackendConstructor<unknown>;
}
