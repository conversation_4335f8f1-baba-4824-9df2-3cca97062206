// Shared utility functions

// String utilities
export function truncateString(
	str: string,
	length: number,
	suffix = "...",
): string {
	if (str.length <= length) return str;
	return str.slice(0, length - suffix.length) + suffix;
}

export function slugify(text: string): string {
	return text
		.toLowerCase()
		.replace(/[^\w\s-]/g, "") // Remove special characters
		.replace(/[\s_-]+/g, "-") // Replace spaces and underscores with hyphens
		.replace(/^-+|-+$/g, ""); // Remove leading/trailing hyphens
}

export function capitalize(str: string): string {
	return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

export function getInitials(
	name: string | null | undefined,
	email: string | null | undefined,
): string {
	if (name && name.length > 0) {
		return name
			.split(" ")
			.filter((part) => part.length > 0) // Filter out empty parts
			.slice(0, 2) // Limit to first two name parts
			.map((part) => [...part][0]) // Unicode-aware first character extraction
			.join("")
			.toUpperCase();
	}
	if (email && email.length > 0) {
		return email.split("@")[0].slice(0, 2).toUpperCase();
	}
	return "??";
}

// Date utilities
export function formatDate(date: string | Date): string {
	const d = new Date(date);
	return d.toLocaleDateString();
}

export function formatDateTime(date: string | Date): string {
	const d = new Date(date);
	return d.toLocaleString();
}

export function timeAgo(date: string | Date): string {
	const now = new Date();
	const past = new Date(date);
	const diffInSeconds = Math.floor((now.getTime() - past.getTime()) / 1000);

	if (diffInSeconds < 60) return "just now";
	if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
	if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
	if (diffInSeconds < 2592000)
		return `${Math.floor(diffInSeconds / 86400)}d ago`;
	if (diffInSeconds < 31536000)
		return `${Math.floor(diffInSeconds / 2592000)}mo ago`;
	return `${Math.floor(diffInSeconds / 31536000)}y ago`;
}

// Validation utilities
export function isValidEmail(email: string): boolean {
	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
	return emailRegex.test(email);
}

export function isValidUrl(url: string): boolean {
	try {
		new URL(url);
		return true;
	} catch {
		return false;
	}
}

// Array utilities
export function unique<T>(array: T[]): T[] {
	return [...new Set(array)];
}

export function groupBy<T extends Record<PropertyKey, unknown>>(
	array: T[],
	key: keyof T,
): Record<string, T[]> {
	return array.reduce(
		(groups, item) => {
			const group = String(item[key]);
			groups[group] = groups[group] || [];
			groups[group].push(item);
			return groups;
		},
		{} as Record<string, T[]>,
	);
}

// Object utilities
export function pick<T extends object, K extends keyof T>(
	obj: T,
	keys: K[],
): Pick<T, K> {
	const result = {} as Pick<T, K>;
	for (const key of keys) {
		if (key in obj) {
			result[key] = obj[key];
		}
	}
	return result;
}

export function omit<T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> {
	const result = { ...obj };
	for (const key of keys) {
		delete result[key];
	}
	return result;
}

// Sleep utility for delays
export function sleep(ms: number): Promise<void> {
	return new Promise((resolve) => setTimeout(resolve, ms));
}

// Environment utilities
export function isServer(): boolean {
	return (
		typeof globalThis !== "undefined" &&
		typeof (globalThis as { window?: unknown }).window === "undefined"
	);
}

export function isClient(): boolean {
	return (
		typeof globalThis !== "undefined" &&
		typeof (globalThis as { window?: unknown }).window !== "undefined"
	);
}

// Error handling utilities
export function safeParseJSON<T>(json: string): T | null {
	try {
		return JSON.parse(json) as T;
	} catch {
		return null;
	}
}

export function createError(
	message: string,
	code?: string,
	details?: Record<string, unknown>,
): Error & { code?: string; details?: Record<string, unknown> } {
	const error = new Error(message) as Error & {
		code?: string;
		details?: Record<string, unknown>;
	};
	if (code) error.code = code;
	if (details) error.details = details;
	return error;
}
