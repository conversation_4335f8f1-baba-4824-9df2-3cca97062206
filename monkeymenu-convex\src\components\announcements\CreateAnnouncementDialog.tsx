import React, { useState, useEffect } from 'react';
import { useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Checkbox } from '../ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Eye, Edit, InfoIcon } from 'lucide-react';

interface CreateAnnouncementDialogProps {
  isOpen: boolean;
  onClose: () => void;
  editingAnnouncement?: any;
}

export function CreateAnnouncementDialog({ 
  isOpen, 
  onClose, 
  editingAnnouncement 
}: CreateAnnouncementDialogProps) {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [type, setType] = useState('info');
  const [isPinned, setIsPinned] = useState(false);
  const [expiresAt, setExpiresAt] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState<'edit' | 'preview'>('edit');

  useEffect(() => {
    if (editingAnnouncement) {
      setTitle(editingAnnouncement.title || '');
      setContent(editingAnnouncement.content || '');
      setType(editingAnnouncement.type || 'info');
      setIsPinned(editingAnnouncement.isPinned || false);
      setExpiresAt(
        editingAnnouncement.expiresAt 
          ? new Date(editingAnnouncement.expiresAt).toISOString().slice(0, 16) 
          : ''
      );
    } else {
      resetForm();
    }
  }, [editingAnnouncement]);

  const createAnnouncement = useMutation(api.announcements.createAnnouncement);
  const updateAnnouncement = useMutation(api.announcements.updateAnnouncement);

  const resetForm = () => {
    setTitle('');
    setContent('');
    setType('info');
    setIsPinned(false);
    setExpiresAt('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim() || !content.trim()) return;

    setIsSubmitting(true);
    try {
      const expiresAtTimestamp = expiresAt ? new Date(expiresAt).getTime() : undefined;
      
      if (editingAnnouncement) {
        await updateAnnouncement({
          id: editingAnnouncement._id,
          title: title.trim(),
          content: content.trim(),
          type,
          isPinned,
          expiresAt: expiresAtTimestamp,
        });
      } else {
        await createAnnouncement({
          title: title.trim(),
          content: content.trim(),
          type,
          isPinned,
          expiresAt: expiresAtTimestamp,
        });
      }
      
      onClose();
      resetForm();
    } catch (error) {
      console.error('Failed to save announcement:', error);
      alert('Failed to save announcement');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    onClose();
    if (!editingAnnouncement) {
      resetForm();
    }
  };

  const typeInfo = {
    info: { emoji: '📢', label: 'Info', color: 'blue' },
    warning: { emoji: '⚠️', label: 'Warning', color: 'yellow' },
    urgent: { emoji: '🚨', label: 'Urgent', color: 'red' },
    celebration: { emoji: '🎉', label: 'Celebration', color: 'green' }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {editingAnnouncement ? (
              <>
                <Edit className="h-5 w-5" />
                Edit Announcement
              </>
            ) : (
              <>
                <InfoIcon className="h-5 w-5" />
                Create New Announcement
              </>
            )}
          </DialogTitle>
          <DialogDescription>
            {editingAnnouncement 
              ? 'Update your announcement details below.'
              : 'Create a new announcement to share with your faction members.'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter announcement title"
              required
            />
          </div>

          {/* Content with Markdown Editor */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="content">Content *</Label>
              <Alert className="w-fit">
                <InfoIcon className="h-4 w-4" />
                <AlertDescription>
                  Supports Markdown formatting (bold, italic, links, lists, etc.)
                </AlertDescription>
              </Alert>
            </div>
            
            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'edit' | 'preview')}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="edit" className="flex items-center gap-2">
                  <Edit className="h-4 w-4" />
                  Edit
                </TabsTrigger>
                <TabsTrigger value="preview" className="flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  Preview
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="edit" className="mt-2">
                <Textarea
                  id="content"
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  rows={10}
                  placeholder="Enter announcement content... 

Examples:
**Bold text**
*Italic text*
[Link text](https://example.com)
- List item
- Another item"
                  className="font-mono"
                  required
                />
              </TabsContent>
              
              <TabsContent value="preview" className="mt-2">
                <div className="border rounded-md p-4 min-h-[240px] bg-muted/30">
                  {content.trim() ? (
                    <div className="prose prose-sm max-w-none dark:prose-invert">
                      <ReactMarkdown remarkPlugins={[remarkGfm]}>
                        {content}
                      </ReactMarkdown>
                    </div>
                  ) : (
                    <div className="text-muted-foreground italic text-center py-12">
                      Preview will appear here as you type...
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Settings Grid */}
          <div className="grid md:grid-cols-2 gap-4">
            {/* Type */}
            <div className="space-y-2">
              <Label htmlFor="type">Type</Label>
              <Select value={type} onValueChange={setType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(typeInfo).map(([key, info]) => (
                    <SelectItem key={key} value={key}>
                      <div className="flex items-center gap-2">
                        <span>{info.emoji}</span>
                        <span>{info.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Expires At */}
            <div className="space-y-2">
              <Label htmlFor="expiresAt">Expires At (Optional)</Label>
              <Input
                type="datetime-local"
                id="expiresAt"
                value={expiresAt}
                onChange={(e) => setExpiresAt(e.target.value)}
              />
            </div>
          </div>

          {/* Pin Checkbox */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="isPinned"
              checked={isPinned}
              onCheckedChange={(checked) => setIsPinned(checked === true)}
            />
            <Label htmlFor="isPinned" className="flex items-center gap-2">
              📌 Pin this announcement
            </Label>
          </div>

          {/* Preview Badge */}
          {title && type && (
            <div className="space-y-2">
              <Label>Preview</Label>
              <div className="border rounded-lg p-4 bg-muted/30">
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="secondary" className="text-sm">
                    {typeInfo[type as keyof typeof typeInfo].emoji} {typeInfo[type as keyof typeof typeInfo].label}
                  </Badge>
                  {isPinned && (
                    <Badge variant="outline" className="text-sm">
                      📌 Pinned
                    </Badge>
                  )}
                  {expiresAt && (
                    <Badge variant="outline" className="text-sm">
                      Expires: {new Date(expiresAt).toLocaleDateString()}
                    </Badge>
                  )}
                </div>
                <h3 className="font-semibold text-lg">{title}</h3>
              </div>
            </div>
          )}
        </form>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button 
            type="submit" 
            disabled={isSubmitting || !title.trim() || !content.trim()}
            onClick={handleSubmit}
          >
            {isSubmitting 
              ? (editingAnnouncement ? 'Updating...' : 'Creating...') 
              : (editingAnnouncement ? 'Update Announcement' : 'Create Announcement')
            }
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}