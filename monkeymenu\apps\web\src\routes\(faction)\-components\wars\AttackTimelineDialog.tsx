import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { trpc } from "@/lib/trpc-client";
import { useQuery } from "@tanstack/react-query";
import { Calendar, Clock, Flame } from "lucide-react";
import { useState } from "react";
import type { AttackTimelineDialogProps } from "./types";

export function AttackTimelineDialog({
	isOpen,
	onClose,
	warId,
}: AttackTimelineDialogProps) {
	const [timeResolution, setTimeResolution] = useState<"hour" | "day">("hour");

	const timelineQuery = useQuery({
		...trpc.wars.getAttackTimeline.queryOptions({ warId, timeResolution }),
		enabled: isOpen,
	});

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-h-[90vh] w-full max-w-6xl overflow-y-auto px-4 sm:px-6">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<Calendar className="h-5 w-5" />
						Attack Timeline Analysis
					</DialogTitle>
					<DialogDescription>
						Temporal breakdown of faction activity patterns and performance
					</DialogDescription>
				</DialogHeader>

				<div className="flex gap-2">
					<Button
						variant={timeResolution === "hour" ? "default" : "outline"}
						size="sm"
						onClick={() => setTimeResolution("hour")}
					>
						Hourly View
					</Button>
					<Button
						variant={timeResolution === "day" ? "default" : "outline"}
						size="sm"
						onClick={() => setTimeResolution("day")}
					>
						Daily View
					</Button>
				</div>

				{timelineQuery.isLoading && (
					<div className="space-y-4">
						<Skeleton className="h-32 w-full" />
						<div className="grid gap-4 md:grid-cols-2">
							<Skeleton className="h-48 w-full" />
							<Skeleton className="h-48 w-full" />
						</div>
					</div>
				)}

				{timelineQuery.error && (
					<div className="rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-950/20">
						<p className="text-red-800 dark:text-red-200">
							Failed to load timeline: {String(timelineQuery.error)}
						</p>
					</div>
				)}

				{timelineQuery.data && (
					<div className="space-y-6">
						{/* Activity Heatmap */}
						<Card>
							<CardHeader>
								<CardTitle className="flex items-center gap-2">
									<Flame className="h-5 w-5" />
									Activity Heatmap (24-Hour)
								</CardTitle>
							</CardHeader>
							<CardContent>
								{/* Desktop/Tablet View - 12 columns */}
								<div className="hidden sm:block">
									<div className="grid grid-cols-12 gap-1">
										{timelineQuery.data.heatmap.hourlyActivity.map((hour) => (
											<div
												key={hour.hour}
												className="flex flex-col items-center gap-1"
											>
												<div
													className="h-8 w-full rounded"
													style={{
														backgroundColor: `rgb(59, 130, 246, ${hour.intensity})`,
														border: "1px solid rgb(59, 130, 246, 0.3)",
													}}
												/>
												<span className="text-xs">{hour.hour}</span>
											</div>
										))}
									</div>
								</div>

								{/* Mobile View - 6 columns, 2 rows */}
								<div className="sm:hidden">
									<div className="space-y-2">
										<div className="grid grid-cols-6 gap-1">
											{timelineQuery.data.heatmap.hourlyActivity
												.slice(0, 12)
												.map((hour) => (
													<div
														key={hour.hour}
														className="flex flex-col items-center gap-1"
													>
														<div
															className="h-6 w-full rounded"
															style={{
																backgroundColor: `rgb(59, 130, 246, ${hour.intensity})`,
																border: "1px solid rgb(59, 130, 246, 0.3)",
															}}
														/>
														<span className="text-xs">{hour.hour}</span>
													</div>
												))}
										</div>
										<div className="grid grid-cols-6 gap-1">
											{timelineQuery.data.heatmap.hourlyActivity
												.slice(12, 24)
												.map((hour) => (
													<div
														key={hour.hour}
														className="flex flex-col items-center gap-1"
													>
														<div
															className="h-6 w-full rounded"
															style={{
																backgroundColor: `rgb(59, 130, 246, ${hour.intensity})`,
																border: "1px solid rgb(59, 130, 246, 0.3)",
															}}
														/>
														<span className="text-xs">{hour.hour}</span>
													</div>
												))}
										</div>
									</div>
								</div>

								<div className="mt-2 flex justify-between text-muted-foreground text-xs">
									<span>Low Activity</span>
									<span>High Activity</span>
								</div>
							</CardContent>
						</Card>

						{/* Timeline Breakdown */}
						<Card>
							<CardHeader>
								<CardTitle className="flex items-center gap-2">
									<Clock className="h-5 w-5" />
									{timeResolution === "hour" ? "Hourly" : "Daily"} Timeline
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="max-h-96 space-y-3 overflow-y-auto">
									{timelineQuery.data.timeline.map((period, index) => (
										<div
											key={`timeline-${period.timestamp}-${index}`}
											className="rounded-lg border p-4"
										>
											<div className="flex items-center justify-between">
												<div>
													<h4 className="font-medium">{period.period}</h4>
													<p className="text-muted-foreground text-sm">
														{period.stats.totalAttacks} attacks •{" "}
														{period.stats.uniqueAttackers} attackers
													</p>
												</div>
												<div className="text-right">
													<div className="font-bold text-lg">
														{period.stats.respectEarned.toFixed(1)}
													</div>
													<div className="text-muted-foreground text-xs">
														respect
													</div>
												</div>
											</div>

											<div className="mt-3 grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-5">
												<div className="text-center">
													<div className="font-semibold text-red-600">
														{period.stats.insideHits}
													</div>
													<div className="text-muted-foreground text-xs">
														War Hits
													</div>
												</div>
												<div className="text-center">
													<div className="font-semibold text-blue-600">
														{period.stats.outsideHits}
													</div>
													<div className="text-muted-foreground text-xs">
														Outside Hits
													</div>
												</div>
												<div className="text-center">
													<div className="font-semibold text-green-600">
														{period.stats.assists}
													</div>
													<div className="text-muted-foreground text-xs">
														Assists
													</div>
												</div>
												<div className="text-center">
													<div className="font-semibold text-purple-600">
														{period.stats.chainAttacks}
													</div>
													<div className="text-muted-foreground text-xs">
														Chain Hits
													</div>
												</div>
												<div className="text-center">
													<div className="font-semibold text-orange-600">
														{period.stats.nonChainAttacks}
													</div>
													<div className="text-muted-foreground text-xs">
														Individual Hits
													</div>
												</div>
											</div>

											{period.topPerformers.length > 0 && (
												<div className="mt-3">
													<h5 className="mb-2 font-medium text-sm">
														Top Performers
													</h5>
													<div className="flex flex-wrap gap-2">
														{period.topPerformers
															.slice(0, 3)
															.map((performer) => (
																<Badge
																	key={performer.playerId}
																	variant="outline"
																	className="text-xs"
																>
																	{performer.playerName ||
																		`#${performer.playerId}`}
																	: {performer.respect.toFixed(1)}
																</Badge>
															))}
													</div>
												</div>
											)}
										</div>
									))}
								</div>
							</CardContent>
						</Card>
					</div>
				)}
			</DialogContent>
		</Dialog>
	);
}
