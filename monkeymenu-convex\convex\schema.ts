import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Users table - stores user profiles and metadata
  users: defineTable({
    tornId: v.number(),
    clerkId: v.string(),
    username: v.string(),
    email: v.optional(v.string()),
    avatar: v.optional(v.string()),
    faction: v.optional(v.string()),
    factionId: v.optional(v.number()),
    factionName: v.optional(v.string()),
    level: v.optional(v.number()),
    role: v.optional(v.string()), // User's faction role
    roleLevel: v.optional(v.number()), // Numeric role level for hierarchy
    tornApiKey: v.optional(v.string()), // Store encrypted Torn API key
    tornApiKeyVerified: v.optional(v.boolean()), // Whether API key has been verified
    tornApiKeyStatus: v.optional(v.string()), // 'verified', 'invalid', 'pending'
    lastTargetFinderFetch: v.optional(v.number()), // Last time user fetched target data (cooldown)
    lastOverdoseEventTimestamp: v.optional(v.number()), // Last overdose event timestamp for monitoring
    lastSeen: v.number(),
    permissions: v.array(v.string()),
    isActive: v.boolean(),
    // Suspension system
    isSuspended: v.optional(v.boolean()),
    accessSuspended: v.optional(v.boolean()), // For compatibility with old monitoring code
    suspendedAt: v.optional(v.number()),
    suspendedBy: v.optional(v.id("users")),
    suspensionReason: v.optional(v.string()),
    suspensionType: v.optional(v.string()), // 'manual', 'automatic', 'api-key'
    // Additional fields for compatibility
    name: v.optional(v.string()), // Alternative to username
    tornUserId: v.optional(v.string()), // String version of tornId for compatibility
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_torn_id", ["tornId"])
    .index("by_clerk_id", ["clerkId"])
    .index("by_username", ["username"])
    .index("by_faction", ["faction"]),

  // Banking system tables
  accounts: defineTable({
    userId: v.id("users"),
    balance: v.number(),
    currency: v.string(), // "cash", "points", etc.
    isActive: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_currency", ["currency"])
    .index("by_user_currency", ["userId", "currency"]),

  withdrawalRequests: defineTable({
    userId: v.id("users"),
    amount: v.number(),
    status: v.string(), // "PENDING", "ACCEPTED", "DECLINED", "COMPLETED", "CANCELLED"
    discordMessageId: v.optional(v.string()),
    transactionId: v.optional(v.string()),
    processedById: v.optional(v.id("users")),
    processedByBotDiscordId: v.optional(v.string()),
    processedByBotDiscordTag: v.optional(v.string()),
    initiatedByDiscordId: v.optional(v.string()),
    initiatedByDiscordTag: v.optional(v.string()),
    processedAt: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_status", ["status"])
    .index("by_processed_by", ["processedById"])
    .index("by_created_at", ["createdAt"]),

  transactions: defineTable({
    fromAccountId: v.optional(v.id("accounts")),
    toAccountId: v.optional(v.id("accounts")),
    amount: v.number(),
    currency: v.string(),
    type: v.string(), // "deposit", "withdrawal", "transfer", "fee"
    status: v.string(), // "pending", "completed", "failed", "cancelled"
    reference: v.optional(v.string()),
    withdrawalRequestId: v.optional(v.id("withdrawalRequests")),
    metadata: v.optional(v.object({})),
    processedAt: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_from_account", ["fromAccountId"])
    .index("by_to_account", ["toAccountId"])
    .index("by_status", ["status"])
    .index("by_type", ["type"])
    .index("by_withdrawal_request", ["withdrawalRequestId"])
    .index("by_created_at", ["createdAt"]),

  // Announcements system
  announcements: defineTable({
    title: v.string(),
    content: v.string(),
    type: v.string(), // "info", "warning", "urgent", "celebration"
    authorId: v.id("users"),
    isActive: v.boolean(),
    isPinned: v.boolean(),
    expiresAt: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_active", ["isActive"])
    .index("by_pinned", ["isPinned"])
    .index("by_author", ["authorId"])
    .index("by_created_at", ["createdAt"]),

  // Target finder system - Target Lists
  targetLists: defineTable({
    name: v.string(),
    userId: v.optional(v.id("users")), // null/undefined for shared lists, userId for custom lists
    isShared: v.boolean(), // true for shared lists, false for custom lists
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_name", ["name"])
    .index("by_user", ["userId"])
    .index("by_shared", ["isShared"]),

  // Target finder system - Individual Targets
  targets: defineTable({
    listId: v.id("targetLists"),
    tornId: v.number(),
    username: v.string(),
    level: v.optional(v.number()),
    faction: v.optional(v.string()),
    status: v.string(), // Real-time status: "Okay", "Hospitalized (5m 32s)", "Error (12)", etc.
    profilePicture: v.optional(v.string()),
    respect: v.optional(v.number()),
    lastUpdated: v.number(),
    lastStatusFetch: v.optional(v.number()), // Last time status was fetched from Torn API
    fairFight: v.optional(v.number()),
    battleStats: v.optional(v.object({
      strength: v.number(),
      defense: v.number(),
      speed: v.number(),
      dexterity: v.number(),
    })),
    isActive: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_list", ["listId"])
    .index("by_torn_id", ["tornId"])
    .index("by_level", ["level"])
    .index("by_faction", ["faction"])
    .index("by_status", ["status"])
    .index("by_respect", ["respect"])
    .index("by_fair_fight", ["fairFight"])
    .index("by_last_updated", ["lastUpdated"]),

  // Wars system
  wars: defineTable({
    tornWarId: v.optional(v.number()), // Torn war ID for tracking
    factionId: v.number(),
    factionName: v.string(),
    enemyFactionId: v.number(),
    enemyFactionName: v.string(),
    startTime: v.number(),
    endTime: v.optional(v.number()),
    status: v.string(), // "active", "ended", "pending"
    ourScore: v.number(),
    enemyScore: v.number(),
    isActive: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_faction", ["factionId"])
    .index("by_enemy_faction", ["enemyFactionId"])
    .index("by_status", ["status"])
    .index("by_start_time", ["startTime"])
    .index("by_torn_war_id", ["tornWarId"]),

  warAttacks: defineTable({
    warId: v.id("wars"),
    attackerId: v.number(),
    attackerName: v.string(),
    defenderId: v.number(),
    defenderName: v.string(),
    result: v.string(), // "win", "loss", "timeout", "escape"
    respect: v.number(),
    chain: v.optional(v.number()),
    timestamp: v.number(),
    createdAt: v.number(),
  })
    .index("by_war", ["warId"])
    .index("by_attacker", ["attackerId"])
    .index("by_defender", ["defenderId"])
    .index("by_timestamp", ["timestamp"]),

  // War caching system for performance optimization
  cachedWarReports: defineTable({
    warId: v.number(),
    reportData: v.string(), // JSON string of the full report
    factionIds: v.string(), // Comma-separated faction IDs involved
    startTime: v.number(),
    endTime: v.optional(v.number()),
    winner: v.optional(v.number()),
    isCompleted: v.boolean(),
    cachedAt: v.number(),
    lastUpdated: v.number(),
  })
    .index("by_war_id", ["warId"])
    .index("by_start_time", ["startTime"])
    .index("by_end_time", ["endTime"])
    .index("by_is_completed", ["isCompleted"])
    .index("by_faction_ids", ["factionIds"]),

  cachedChainReports: defineTable({
    chainId: v.number(),
    warId: v.number(),
    reportData: v.string(), // JSON string of the full chain report
    startTime: v.number(),
    endTime: v.number(),
    chainLength: v.number(),
    totalRespect: v.number(),
    attackerCount: v.number(),
    cachedAt: v.number(),
  })
    .index("by_chain_id", ["chainId"])
    .index("by_war_id", ["warId"])
    .index("by_start_time", ["startTime"])
    .index("by_end_time", ["endTime"]),

  cachedWarAttacks: defineTable({
    attackId: v.string(), // Use attack ID from API
    warId: v.number(),
    attackData: v.string(), // JSON string of the attack
    attackerId: v.number(),
    defenderId: v.number(),
    attackerFactionId: v.optional(v.number()),
    defenderFactionId: v.optional(v.number()),
    timestamp: v.number(),
    chainId: v.optional(v.number()),
    result: v.string(),
    respect: v.number(),
    cachedAt: v.number(),
  })
    .index("by_war_id", ["warId"])
    .index("by_timestamp", ["timestamp"])
    .index("by_attacker_id", ["attackerId"])
    .index("by_defender_id", ["defenderId"])
    .index("by_chain_id", ["chainId"])
    .index("by_result", ["result"])
    .index("by_war_chain", ["warId", "chainId"])
    .index("by_war_attacker", ["warId", "attackerId"])
    .index("by_war_timestamp", ["warId", "timestamp"]),

  cachedWarStats: defineTable({
    id: v.string(), // Composite key like "war_53100_1234" for war stats for faction
    warId: v.number(),
    factionId: v.number(),
    statsType: v.string(), // "overall", "chains", "attacks", "timeline"
    timeframe: v.optional(v.string()), // "hourly", "daily", or null for overall
    statsData: v.string(), // JSON string of computed stats
    cachedAt: v.number(),
  })
    .index("by_composite_id", ["id"])
    .index("by_war_id", ["warId"])
    .index("by_faction_id", ["factionId"])
    .index("by_stats_type", ["statsType"])
    .index("by_war_faction_stats", ["warId", "factionId", "statsType", "timeframe"]),

  // Permissions system
  permissions: defineTable({
    name: v.string(),
    description: v.string(),
    category: v.string(), // "admin", "banking", "wars", "announcements", etc.
    isActive: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_name", ["name"])
    .index("by_category", ["category"]),

  userPermissions: defineTable({
    userId: v.id("users"),
    permissionId: v.id("permissions"),
    grantedBy: v.id("users"),
    grantedAt: v.number(),
    expiresAt: v.optional(v.number()),
    isActive: v.boolean(),
  })
    .index("by_user", ["userId"])
    .index("by_permission", ["permissionId"])
    .index("by_granted_by", ["grantedBy"]),

  // Discord integration
  discordUsers: defineTable({
    userId: v.id("users"),
    discordId: v.string(),
    username: v.string(),
    discriminator: v.string(),
    avatar: v.optional(v.string()),
    isActive: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_discord_id", ["discordId"]),

  // Guides system
  guides: defineTable({
    title: v.string(),
    content: v.string(),
    category: v.string(),
    authorId: v.id("users"),
    isPublished: v.boolean(),
    viewCount: v.number(),
    tags: v.array(v.string()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_category", ["category"])
    .index("by_author", ["authorId"])
    .index("by_published", ["isPublished"])
    .index("by_view_count", ["viewCount"]),

  // Session management
  sessions: defineTable({
    userId: v.id("users"),
    token: v.string(),
    expiresAt: v.number(),
    isActive: v.boolean(),
    metadata: v.optional(v.object({})),
    createdAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_token", ["token"])
    .index("by_expires_at", ["expiresAt"]),
});
