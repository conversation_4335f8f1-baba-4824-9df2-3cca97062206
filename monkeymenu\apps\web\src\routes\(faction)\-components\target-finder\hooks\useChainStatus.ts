import { useEffect, useRef, useState } from "react";
import type { ChainInfo, TornChain } from "../types";
import { playCriticalSound } from "../utils";

export function useChainStatus(chainInfo: ChainInfo | null | undefined) {
	const [chainRemaining, setChainRemaining] = useState<number | null>(null);
	const lastCategoryRef = useRef<string | null>(null);

	// Recompute remaining whenever new chainInfo polled
	useEffect(() => {
		if (!chainInfo?.chain) return;

		const now = Math.floor(Date.now() / 1000);
		const chain = chainInfo.chain as TornChain;
		let remaining = 0;

		if (chain.timeout && chain.timeout > 0) {
			remaining = chain.timeout;
		} else if (chain.cooldown && chain.cooldown > 0) {
			remaining = chain.cooldown;
		} else if (chain.end && chain.end > now) {
			remaining = chain.end - now;
		}

		setChainRemaining(remaining > 0 ? remaining : 0);
	}, [chainInfo]);

	// Live countdown tick each second
	useEffect(() => {
		const id = setInterval(() => {
			setChainRemaining((prev) =>
				prev !== null && prev > 0 ? prev - 1 : prev,
			);
		}, 1000);
		return () => clearInterval(id);
	}, []);

	// Handle body class and sound effects based on chain status
	useEffect(() => {
		const categories = [
			"chain-good",
			"chain-warning",
			"chain-critical",
		] as const;

		// Remove existing classes
		for (const c of categories) {
			document.body.classList.remove(c);
		}

		let category: string | null = null;

		if (
			chainRemaining !== null &&
			chainRemaining > 0 &&
			chainRemaining <= 300
		) {
			if (chainRemaining <= 60) category = "chain-critical";
			else if (chainRemaining <= 120) category = "chain-warning";
			else category = "chain-good";
		}

		if (category) {
			document.body.classList.add(category);
		}

		// Play sound every 15 seconds in critical zone
		if (category === "chain-critical" && chainRemaining !== null) {
			const shouldBeep = chainRemaining % 15 === 0;
			if (shouldBeep) {
				playCriticalSound();
			}
		}

		lastCategoryRef.current = category;

		return () => {
			for (const c of categories) {
				document.body.classList.remove(c);
			}
		};
	}, [chainRemaining]);

	return { chainRemaining };
}
