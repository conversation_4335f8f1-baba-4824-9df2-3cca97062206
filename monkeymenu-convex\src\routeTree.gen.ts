/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as WarsRouteImport } from './routes/wars'
import { Route as TargetsRouteImport } from './routes/targets'
import { Route as SignInRouteImport } from './routes/sign-in'
import { Route as OnboardingRouteImport } from './routes/onboarding'
import { Route as GuidesRouteImport } from './routes/guides'
import { Route as DashboardRouteImport } from './routes/dashboard'
import { Route as BankingRouteImport } from './routes/banking'
import { Route as AnnouncementsRouteImport } from './routes/announcements'
import { Route as AnalyticsRouteImport } from './routes/analytics'
import { Route as AdminPermissionsRouteImport } from './routes/admin-permissions'
import { Route as AdminRouteImport } from './routes/admin'
import { Route as IndexRouteImport } from './routes/index'

const WarsRoute = WarsRouteImport.update({
  id: '/wars',
  path: '/wars',
  getParentRoute: () => rootRouteImport,
} as any)
const TargetsRoute = TargetsRouteImport.update({
  id: '/targets',
  path: '/targets',
  getParentRoute: () => rootRouteImport,
} as any)
const SignInRoute = SignInRouteImport.update({
  id: '/sign-in',
  path: '/sign-in',
  getParentRoute: () => rootRouteImport,
} as any)
const OnboardingRoute = OnboardingRouteImport.update({
  id: '/onboarding',
  path: '/onboarding',
  getParentRoute: () => rootRouteImport,
} as any)
const GuidesRoute = GuidesRouteImport.update({
  id: '/guides',
  path: '/guides',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardRoute = DashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRouteImport,
} as any)
const BankingRoute = BankingRouteImport.update({
  id: '/banking',
  path: '/banking',
  getParentRoute: () => rootRouteImport,
} as any)
const AnnouncementsRoute = AnnouncementsRouteImport.update({
  id: '/announcements',
  path: '/announcements',
  getParentRoute: () => rootRouteImport,
} as any)
const AnalyticsRoute = AnalyticsRouteImport.update({
  id: '/analytics',
  path: '/analytics',
  getParentRoute: () => rootRouteImport,
} as any)
const AdminPermissionsRoute = AdminPermissionsRouteImport.update({
  id: '/admin-permissions',
  path: '/admin-permissions',
  getParentRoute: () => rootRouteImport,
} as any)
const AdminRoute = AdminRouteImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/admin': typeof AdminRoute
  '/admin-permissions': typeof AdminPermissionsRoute
  '/analytics': typeof AnalyticsRoute
  '/announcements': typeof AnnouncementsRoute
  '/banking': typeof BankingRoute
  '/dashboard': typeof DashboardRoute
  '/guides': typeof GuidesRoute
  '/onboarding': typeof OnboardingRoute
  '/sign-in': typeof SignInRoute
  '/targets': typeof TargetsRoute
  '/wars': typeof WarsRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/admin': typeof AdminRoute
  '/admin-permissions': typeof AdminPermissionsRoute
  '/analytics': typeof AnalyticsRoute
  '/announcements': typeof AnnouncementsRoute
  '/banking': typeof BankingRoute
  '/dashboard': typeof DashboardRoute
  '/guides': typeof GuidesRoute
  '/onboarding': typeof OnboardingRoute
  '/sign-in': typeof SignInRoute
  '/targets': typeof TargetsRoute
  '/wars': typeof WarsRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/admin': typeof AdminRoute
  '/admin-permissions': typeof AdminPermissionsRoute
  '/analytics': typeof AnalyticsRoute
  '/announcements': typeof AnnouncementsRoute
  '/banking': typeof BankingRoute
  '/dashboard': typeof DashboardRoute
  '/guides': typeof GuidesRoute
  '/onboarding': typeof OnboardingRoute
  '/sign-in': typeof SignInRoute
  '/targets': typeof TargetsRoute
  '/wars': typeof WarsRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/admin'
    | '/admin-permissions'
    | '/analytics'
    | '/announcements'
    | '/banking'
    | '/dashboard'
    | '/guides'
    | '/onboarding'
    | '/sign-in'
    | '/targets'
    | '/wars'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/admin'
    | '/admin-permissions'
    | '/analytics'
    | '/announcements'
    | '/banking'
    | '/dashboard'
    | '/guides'
    | '/onboarding'
    | '/sign-in'
    | '/targets'
    | '/wars'
  id:
    | '__root__'
    | '/'
    | '/admin'
    | '/admin-permissions'
    | '/analytics'
    | '/announcements'
    | '/banking'
    | '/dashboard'
    | '/guides'
    | '/onboarding'
    | '/sign-in'
    | '/targets'
    | '/wars'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AdminRoute: typeof AdminRoute
  AdminPermissionsRoute: typeof AdminPermissionsRoute
  AnalyticsRoute: typeof AnalyticsRoute
  AnnouncementsRoute: typeof AnnouncementsRoute
  BankingRoute: typeof BankingRoute
  DashboardRoute: typeof DashboardRoute
  GuidesRoute: typeof GuidesRoute
  OnboardingRoute: typeof OnboardingRoute
  SignInRoute: typeof SignInRoute
  TargetsRoute: typeof TargetsRoute
  WarsRoute: typeof WarsRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/wars': {
      id: '/wars'
      path: '/wars'
      fullPath: '/wars'
      preLoaderRoute: typeof WarsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/targets': {
      id: '/targets'
      path: '/targets'
      fullPath: '/targets'
      preLoaderRoute: typeof TargetsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/sign-in': {
      id: '/sign-in'
      path: '/sign-in'
      fullPath: '/sign-in'
      preLoaderRoute: typeof SignInRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/onboarding': {
      id: '/onboarding'
      path: '/onboarding'
      fullPath: '/onboarding'
      preLoaderRoute: typeof OnboardingRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/guides': {
      id: '/guides'
      path: '/guides'
      fullPath: '/guides'
      preLoaderRoute: typeof GuidesRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/banking': {
      id: '/banking'
      path: '/banking'
      fullPath: '/banking'
      preLoaderRoute: typeof BankingRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/announcements': {
      id: '/announcements'
      path: '/announcements'
      fullPath: '/announcements'
      preLoaderRoute: typeof AnnouncementsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/analytics': {
      id: '/analytics'
      path: '/analytics'
      fullPath: '/analytics'
      preLoaderRoute: typeof AnalyticsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/admin-permissions': {
      id: '/admin-permissions'
      path: '/admin-permissions'
      fullPath: '/admin-permissions'
      preLoaderRoute: typeof AdminPermissionsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/admin': {
      id: '/admin'
      path: '/admin'
      fullPath: '/admin'
      preLoaderRoute: typeof AdminRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AdminRoute: AdminRoute,
  AdminPermissionsRoute: AdminPermissionsRoute,
  AnalyticsRoute: AnalyticsRoute,
  AnnouncementsRoute: AnnouncementsRoute,
  BankingRoute: BankingRoute,
  DashboardRoute: DashboardRoute,
  GuidesRoute: GuidesRoute,
  OnboardingRoute: OnboardingRoute,
  SignInRoute: SignInRoute,
  TargetsRoute: TargetsRoute,
  WarsRoute: WarsRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
