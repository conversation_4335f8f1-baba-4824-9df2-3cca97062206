# Discord Worker

This directory contains the Discord bot integration for MonkeyMenu using [slash-create](https://github.com/Snazzah/slash-create) and Cloudflare Workers.

## Features

- **Slash Commands**: `/balance`, `/withdraw`, `/targets`, `/war`, `/help`, `/link`
- **Serverless**: Runs on Cloudflare Workers alongside the main app
- **Convex Integration**: Direct integration with Convex backend functions
- **No Separate Hosting**: Deployed as part of the main infrastructure

## Architecture

```
Discord → Cloudflare Worker → Convex Backend
```

The Discord Worker:
1. Receives Discord slash command interactions
2. Processes them using slash-create
3. Calls Convex backend functions via HTTP
4. Returns responses to Discord

## Setup

### 1. Environment Variables

Set these environment variables in your Cloudflare Worker:

```bash
DISCORD_APPLICATION_ID=your_discord_application_id
DISCORD_PUBLIC_KEY=your_discord_public_key
DISCORD_BOT_TOKEN=your_discord_bot_token
CONVEX_URL=your_convex_deployment_url
```

### 2. Discord Application Setup

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create a new application or use existing one
3. Go to "Bot" section and create a bot
4. Copy the Bot Token for `DISCORD_BOT_TOKEN`
5. Go to "General Information" and copy:
   - Application ID for `DISCORD_APPLICATION_ID`
   - Public Key for `DISCORD_PUBLIC_KEY`

### 3. Bot Permissions

Required bot permissions:
- Send Messages
- Use Slash Commands
- Embed Links
- Read Message History

### 4. Interaction Endpoint URL

Set the Interaction Endpoint URL in Discord Developer Portal to:
- Development: `https://discord-bot-dev.monkeymenu.com/discord/interactions`
- Staging: `https://discord-bot-staging.monkeymenu.com/discord/interactions`
- Production: `https://discord-bot.monkeymenu.com/discord/interactions`

## Development

### Local Development

```bash
# Start the Discord Worker locally
npm run discord:worker:dev
```

### Sync Commands

```bash
# Sync slash commands with Discord
npm run discord:sync
```

### Deploy

```bash
# Deploy to staging
npm run discord:worker:deploy:staging

# Deploy to production
npm run discord:worker:deploy:prod
```

## Available Commands

- `/balance` - Check your account balances
- `/withdraw <amount>` - Request a withdrawal from the faction bank
- `/targets [filter]` - Show available targets with optional filters
- `/war [subcommand]` - Show war information (active, stats)
- `/help` - Show available commands and help information
- `/link` - Check your account linking status

## Migration from Standalone Bot

This replaces the standalone Discord bot (`src/discord/bot.ts`) with:
- ✅ Cloudflare Worker integration (no separate hosting)
- ✅ Slash commands instead of message commands
- ✅ Direct Convex integration
- ✅ Better scalability and reliability

## Troubleshooting

### Commands not appearing in Discord
1. Make sure you've run `npm run discord:sync`
2. Check that environment variables are set correctly
3. Verify the Interaction Endpoint URL is configured in Discord

### Worker not responding
1. Check Cloudflare Worker logs
2. Verify the worker is deployed and running
3. Test the health endpoint: `https://your-worker-url/health`

### Convex integration issues
1. Verify `CONVEX_URL` is set correctly
2. Check that Convex functions are deployed
3. Review Convex function logs for errors
