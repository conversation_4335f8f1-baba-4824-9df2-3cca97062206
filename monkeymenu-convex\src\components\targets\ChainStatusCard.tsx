import React from 'react';

interface ChainStatusCardProps {
  chainInfo: {
    chain: {
      current: number;
      maximum: number;
      timeout: number;
      modifier: number;
      cooldown: number;
      start: number;
      end: number;
    };
  };
  chainRemaining: number;
  chainStatus?: 'good' | 'warning' | 'critical' | 'cooldown';
}

export function ChainStatusCard({ chainInfo, chainRemaining, chainStatus = 'good' }: ChainStatusCardProps) {
  const { chain } = chainInfo;
  
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const progressPercentage = (chain.current / chain.maximum) * 100;
  const timeoutPercentage = ((chain.timeout - Date.now() / 1000) / (chain.timeout - chain.start)) * 100;
  
  // Status-based styling
  const getStatusColors = () => {
    switch (chainStatus) {
      case 'critical':
        return {
          gradient: 'from-red-600 to-red-800',
          progressBg: 'bg-red-900',
          timeoutBar: 'bg-red-300',
          border: 'border-red-400'
        };
      case 'warning':
        return {
          gradient: 'from-yellow-500 to-orange-600',
          progressBg: 'bg-orange-800',
          timeoutBar: 'bg-yellow-300',
          border: 'border-orange-400'
        };
      case 'cooldown':
        return {
          gradient: 'from-gray-500 to-gray-700',
          progressBg: 'bg-gray-800',
          timeoutBar: 'bg-gray-300',
          border: 'border-gray-400'
        };
      default:
        return {
          gradient: 'from-orange-500 to-red-600',
          progressBg: 'bg-orange-800',
          timeoutBar: 'bg-yellow-300',
          border: 'border-orange-400'
        };
    }
  };
  
  const colors = getStatusColors();

  return (
    <div className={`bg-gradient-to-r ${colors.gradient} text-white rounded-lg p-6 shadow-lg ${chainStatus === 'critical' ? 'animate-pulse' : ''}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="text-3xl">⛓️</div>
          <div>
            <h3 className="text-xl font-bold">Active Chain</h3>
            <p className="text-orange-100">Chain in progress - target enemies now!</p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold">{chain.current}</div>
          <div className="text-sm text-orange-100">/ {chain.maximum}</div>
        </div>
      </div>

      <div className="space-y-3">
        {/* Chain Progress Bar */}
        <div>
          <div className="flex justify-between text-sm mb-1">
            <span>Chain Progress</span>
            <span>{Math.round(progressPercentage)}%</span>
          </div>
          <div className={`w-full ${colors.progressBg} rounded-full h-2`}>
            <div
              className="bg-white h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>

        {/* Timeout Progress Bar */}
        <div>
          <div className="flex justify-between text-sm mb-1">
            <span>Time Remaining</span>
            <span>{formatTime(chainRemaining)}</span>
          </div>
          <div className="w-full bg-red-800 rounded-full h-2">
            <div
              className={`${colors.timeoutBar} h-2 rounded-full transition-all duration-1000`}
              style={{ width: `${Math.max(0, timeoutPercentage)}%` }}
            />
          </div>
        </div>

        {/* Chain Stats */}
        <div className={`grid grid-cols-2 gap-4 pt-2 border-t ${colors.border}`}>
          <div className="text-center">
            <div className="text-lg font-bold">{chain.modifier.toFixed(1)}x</div>
            <div className="text-xs text-orange-100">Multiplier</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold">{Math.round(chain.cooldown / 60)}m</div>
            <div className="text-xs text-orange-100">Cooldown</div>
          </div>
        </div>
      </div>
    </div>
  );
}