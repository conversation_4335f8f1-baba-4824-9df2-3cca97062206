import { makeWorker } from "@livestore/adapter-web/worker";
import { makeCfSync } from "@livestore/sync-cf";
import { schema } from "./schema";

// Construct WebSocket URL with proper validation and protocol handling
function buildWebSocketUrl(): string {
	const serverUrl = import.meta.env.VITE_SERVER_URL;

	// Use localhost as fallback if no server URL is provided
	if (!serverUrl) {
		return "ws://localhost:3000/api/livestore";
	}

	// Validate that the server URL starts with http or https
	if (!serverUrl.startsWith("http://") && !serverUrl.startsWith("https://")) {
		console.error(
			"Invalid VITE_SERVER_URL: must start with http:// or https://",
			serverUrl,
		);
		return "ws://localhost:3000/api/livestore";
	}

	// Determine the appropriate WebSocket protocol
	let wsProtocol: string;
	let baseUrl: string;

	if (serverUrl.startsWith("https://")) {
		wsProtocol = "wss:";
		baseUrl = serverUrl.substring(8); // Remove "https://"
	} else {
		wsProtocol = "ws:";
		baseUrl = serverUrl.substring(7); // Remove "http://"
	}

	// Construct the final WebSocket URL
	const websocketUrl = `${wsProtocol}//${baseUrl}/api/livestore`;

	// Validate the constructed URL is well-formed
	try {
		new URL(websocketUrl);
		return websocketUrl;
	} catch (error) {
		console.error(
			"Failed to construct valid WebSocket URL:",
			websocketUrl,
			error,
		);
		return "ws://localhost:3000/api/livestore";
	}
}

makeWorker({
	schema,
	sync: {
		backend: makeCfSync({
			url: buildWebSocketUrl(),
		}),
		// Perform a blocking initial sync so we always pull the remote event log
		// before the client starts pushing its own events.
		initialSyncOptions: { _tag: "Blocking", timeout: 15000 },
	},
});
