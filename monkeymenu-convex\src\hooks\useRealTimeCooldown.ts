import { useState, useEffect } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';

interface UseRealTimeCooldownProps {
  feature: 'targetFinder' | 'banking' | 'wars';
}

export function useRealTimeCooldown({ feature }: UseRealTimeCooldownProps) {
  const [remainingTime, setRemainingTime] = useState<number>(0);
  const [isOnCooldown, setIsOnCooldown] = useState<boolean>(false);

  // Get cooldown data from Convex
  const cooldownData = useQuery(
    feature === 'targetFinder' ? api.targets.getTargetFinderCooldown :
    feature === 'banking' ? api.banking.getBankingCooldown :
    api.wars.getWarsCooldown
  );

  // Real-time countdown
  useEffect(() => {
    const interval = setInterval(() => {
      if (cooldownData?.cooldownUntil) {
        const now = Date.now();
        const remaining = Math.max(0, cooldownData.cooldownUntil - now);
        
        setRemainingTime(Math.ceil(remaining / 1000));
        setIsOnCooldown(remaining > 0);
      } else {
        setRemainingTime(0);
        setIsOnCooldown(false);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [cooldownData]);

  // Format remaining time as MM:SS
  const formatRemainingTime = (): string => {
    if (remainingTime <= 0) return '0:00';
    
    const minutes = Math.floor(remainingTime / 60);
    const seconds = remainingTime % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return {
    remainingTime,
    isOnCooldown,
    formatRemainingTime,
    cooldownData,
  };
}