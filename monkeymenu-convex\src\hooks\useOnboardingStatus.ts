import { useQuery } from "convex/react";
import { useUser } from "@clerk/clerk-react";
import { api } from "../../convex/_generated/api";

export function useOnboardingStatus() {
  const { user, isLoaded } = useUser();
  const tornInfo = useQuery(api.torn.getUserTornInfo);

  // Check if user needs onboarding
  const needsOnboarding = isLoaded && user && (!tornInfo || !tornInfo.tornId);
  
  // Check if Discord is linked
  const isDiscordLinked = user?.externalAccounts?.some(
    account => account.provider === 'discord'
  ) ?? false;

  return {
    needsOnboarding,
    isDiscordLinked,
    tornInfo,
    isLoading: !isLoaded || (user && tornInfo === undefined),
  };
}