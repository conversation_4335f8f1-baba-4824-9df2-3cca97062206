import { authClient } from "@/lib/auth-client";
import { CombinedThemeProvider } from "@/lib/combined-theme-provider";
import { MonkeyMenuLiveStoreProvider } from "@/lib/livestore-provider";
import { queryClient, trpc } from "@/lib/trpc-client";
import type { GetSessionResponse } from "@/types/auth";
import { QueryClientProvider } from "@tanstack/react-query";
import { RouterProvider, createRouter } from "@tanstack/react-router";
import React from "react";
import ReactDOM from "react-dom/client";
import { Loader } from "./components/navbar/loader";
import { routeTree } from "./routeTree.gen";

// Create router with wrapped query client
const router = createRouter({
	routeTree,
	defaultPreload: "intent",
	defaultPendingComponent: () => <Loader />,
	context: { trpc, queryClient },
	Wrap: function WrapComponent({ children }) {
		return (
			<QueryClientProvider client={queryClient}>
				<CombinedThemeProvider>
					<LiveStoreWrapper>{children}</LiveStoreWrapper>
				</CombinedThemeProvider>
			</QueryClientProvider>
		);
	},
});

// Register router for typesafety
declare module "@tanstack/react-router" {
	interface Register {
		router: typeof router;
	}
}

// Render the app
const rootElement = document.getElementById("root");
if (!rootElement) throw new Error("Root element not found");

if (!rootElement.innerHTML) {
	const root = ReactDOM.createRoot(rootElement);
	root.render(<RouterProvider router={router} />);
}

// Separate component so we can wait for the auth session *inside* the QueryClientProvider context
function LiveStoreWrapper({ children }: { children: React.ReactNode }) {
	const { data: session, isPending } = authClient.useSession();

	// Local state for LiveStore auth token
	const [authToken, setAuthToken] = React.useState<string | null>(null);

	React.useEffect(() => {
		let cancelled = false; // guard for race-conditions

		const fetchToken = async () => {
			// While we are still resolving the session just bail out – handled below.
			if (isPending) return;

			// Anonymous visitors use the public token immediately.
			if (!session?.user?.id) {
				if (!cancelled) setAuthToken("anonymous_token");
				return;
			}

			try {
				const result = await authClient.getSession();
				if (cancelled) return; // ignore out-of-date response

				// Narrow the type by checking the expected shape at runtime.
				const maybeToken = (result as Partial<GetSessionResponse>)?.data
					?.session?.token;
				const token =
					typeof maybeToken === "string" ? maybeToken : "anonymous_token";
				setAuthToken(token);
			} catch (err) {
				if (cancelled) return;
				console.error("[LiveStore] Failed to retrieve session token", err);
				setAuthToken("anonymous_token");
			}
		};

		fetchToken();

		return () => {
			// mark the effect as obsolete so we don't update state on unmounted or old user sessions
			cancelled = true;
		};
	}, [isPending, session?.user?.id]);

	// Show the loader while we resolve the auth session or auth token.
	if (isPending || authToken === null) {
		return <Loader />;
	}

	return (
		<MonkeyMenuLiveStoreProvider
			authToken={authToken}
			userId={session?.user?.id}
		>
			{children}
		</MonkeyMenuLiveStoreProvider>
	);
}
