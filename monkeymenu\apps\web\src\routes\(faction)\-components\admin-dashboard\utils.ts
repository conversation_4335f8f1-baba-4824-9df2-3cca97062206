import type { UserRoleAssignment } from "./types";

// Convert technical permission names to human-readable format
export function getReadablePermissionName(permissionName: string): string {
	const permissionMap: Record<string, string> = {
		"admin.view": "Admin Panel Access",
		"admin.user_management": "User Management",
		"announcements.view": "View Announcements",
		"announcements.create": "Create Announcements",
		"announcements.edit": "Edit Announcements",
		"announcements.delete": "Delete Announcements",
		"banking.view": "View Banking",
		"banking.withdraw": "Make Withdrawals",
		"banking.manage": "Manage Banking",
		"guides.view": "View Guides",
		"guides.create": "Create Guides",
		"guides.edit": "Edit Guides",
		"guides.delete": "Delete Guides",
		"target_finder.view": "View Target Finder",
		"target_finder.use": "Use Target Finder",
		"target.finder.view": "View Target Finder",
		"target.finder.manage.shared_lists": "Manage Shared Target Lists",
		"wars.view": "View War Reports",
		"dashboard.view": "View Dashboard",
		"permissions.view": "View Permissions",
		"permissions.manage": "Manage Permissions",
	};

	return (
		permissionMap[permissionName] ||
		permissionName
			.replace(/[._]/g, " ")
			.replace(/\b\w/g, (l) => l.toUpperCase())
	);
}

// Check if user is suspended
export function isUserSuspended(assignment: UserRoleAssignment): boolean {
	return !!assignment.suspensionStatus?.accessSuspended;
}

// Get role color based on hierarchy level
export function getRoleColor(hierarchyLevel: number): {
	background: string;
	text: string;
} {
	if (hierarchyLevel >= 9) {
		return {
			background: "bg-destructive/10",
			text: "text-destructive",
		};
	}
	if (hierarchyLevel >= 7) {
		return {
			background: "bg-primary/10",
			text: "text-primary",
		};
	}
	if (hierarchyLevel >= 5) {
		return {
			background: "bg-secondary/30",
			text: "text-secondary-foreground",
		};
	}
	return {
		background: "bg-muted",
		text: "text-muted-foreground",
	};
}
