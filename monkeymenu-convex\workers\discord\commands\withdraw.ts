import { CommandContext, CommandOptionType, BaseSlashCreator } from "slash-create";
import { BaseCommand } from "./base";

export class WithdrawCommand extends BaseCommand {
  constructor(creator: BaseSlashCreator, convexUrl: string) {
    super(creator, convexUrl, {
      name: "withdraw",
      description: "Request a withdrawal from the faction bank",
      options: [
        {
          type: CommandOptionType.INTEGER,
          name: "amount",
          description: "The amount you want to withdraw",
          required: true,
          min_value: 1,
        },
      ],
    });
  }

  async run(ctx: CommandContext) {
    const discordId = ctx.user.id;
    const amount = ctx.options.amount as number;

    try {
      // Handle the withdraw command through Convex
      const result = await this.handleBotCommand(discordId, "withdraw", [amount.toString()]) as any;

      if (result?.success) {
        if (result.embed) {
          return this.createEmbedResponse(result.embed);
        } else {
          return this.createSuccessResponse(result.message || "Withdrawal request processed successfully");
        }
      } else {
        return this.createErrorResponse(result?.message || "❌ Failed to process withdrawal request");
      }
    } catch (error) {
      console.error("Withdraw command error:", error);
      return this.createErrorResponse("❌ An error occurred while processing your withdrawal request.");
    }
  }
}
