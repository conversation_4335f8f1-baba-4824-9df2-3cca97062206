import { ThemeProvider } from "./theme-provider";
import { ThemeStyleProvider } from "./theme-style-provider";

type CombinedThemeProviderProps = {
	children: React.ReactNode;
};

export function CombinedThemeProvider({
	children,
}: CombinedThemeProviderProps) {
	return (
		<ThemeProvider>
			<ThemeStyleProvider defaultThemeStyle="default">
				{children}
			</ThemeStyleProvider>
		</ThemeProvider>
	);
}

// Re-export hooks for convenience
export { useTheme } from "./theme-provider";
export {
	useThemeStyle,
	THEME_STYLES,
	type ThemeStyle,
} from "./theme-style-provider";
