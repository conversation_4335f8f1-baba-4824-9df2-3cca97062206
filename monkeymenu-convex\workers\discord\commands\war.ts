import { CommandContext, CommandOptionType, BaseSlashCreator } from "slash-create";
import { BaseCommand } from "./base";

export class WarCommand extends BaseCommand {
  constructor(creator: BaseSlashCreator, convexUrl: string) {
    super(creator, convexUrl, {
      name: "war",
      description: "Show war information",
      options: [
        {
          type: CommandOptionType.STRING,
          name: "subcommand",
          description: "War subcommand (active, stats)",
          required: false,
          choices: [
            { name: "active", value: "active" },
            { name: "stats", value: "stats" },
          ],
        },
      ],
    });
  }

  async run(ctx: CommandContext) {
    const discordId = ctx.user.id;
    const subcommand = ctx.options.subcommand as string || "active";

    try {
      // Handle the war command through Convex
      const result = await this.handleBotCommand(discordId, "war", [subcommand]) as any;

      if (result?.success) {
        if (result.embed) {
          return this.createEmbedResponse(result.embed);
        } else {
          return this.createSuccessResponse(result.message || "War information retrieved successfully");
        }
      } else {
        return this.createErrorResponse(result?.message || "❌ Failed to retrieve war information");
      }
    } catch (error) {
      console.error("War command error:", error);
      return this.createErrorResponse("❌ An error occurred while retrieving war information.");
    }
  }
}
