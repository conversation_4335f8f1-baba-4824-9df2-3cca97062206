export interface FactionBalance {
	id: string;
	money: number;
	points: number;
	lastUpdated: Date;
}

export interface WithdrawalRequest {
	id: string;
	amount: number;
	status: "PENDING" | "ACCEPTED" | "DECLINED";
	requestedById: string;
	requestedByName: string;
	requestedByTornId?: number;
	processedById?: string;
	processedByName?: string;
	createdAt: Date;
	updatedAt: Date;
	processedAt?: Date;
	transactionId?: string;
	discordMessageId?: string;
}

export interface BankingUIState {
	id: string;
	value: {
		withdrawalFormAmount: string;
		withdrawalFormErrors: Record<string, string>;
		activeTab: "request" | "history" | "admin";
		statusFilter: "all" | "PENDING" | "ACCEPTED" | "DECLINED";
		showWithdrawalForm: boolean;
	};
}

export interface Announcement {
	id: string;
	title: string;
	content: string;
	category: string;
	authorId: string;
	authorName: string;
	createdAt: Date;
	updatedAt: Date;
}

export interface AnnouncementsUIState {
	id: string;
	value: {
		searchQuery: string;
		selectedCategory: string;
		viewMode: "grid" | "list";
		showCreateForm: boolean;
	};
}
