import { trpc } from "@/lib/trpc-client";
import { CACHE_CONFIG } from "@monkeymenu/shared";
import { useQuery } from "@tanstack/react-query";

/**
 * Hook to get the current user's permission context
 */
export function usePermissions() {
	return useQuery({
		...trpc.permissions.getMyPermissions.queryOptions(),
		staleTime: CACHE_CONFIG.PERMISSIONS_CACHE_TIME,
		refetchOnWindowFocus: true,
	});
}

/**
 * Hook to check if user has a specific permission
 */
export function useHasPermission(permissionName: string) {
	const { data: permissions, isLoading, isFetched } = usePermissions();

	return {
		hasPermission: permissions?.permissions.includes(permissionName) ?? false,
		isLoading,
		isFetched,
	};
}

/**
 * Hook to check if user has minimum role level
 */
export function useHasMinimumRole(minimumLevel: number) {
	const { data: permissions, isLoading, isFetched } = usePermissions();

	return {
		hasMinimumRole: (permissions?.roleLevel ?? 0) >= minimumLevel,
		isLoading,
		isFetched,
	};
}

/**
 * Hook to check if user can perform an action with ownership context
 */
export function useCanPerformAction(
	ownPermission: string,
	anyPermission: string,
	isOwner: boolean,
) {
	const { data: permissions, isLoading, isFetched } = usePermissions();

	const canPerform = permissions
		? permissions.permissions.includes(anyPermission) ||
			(isOwner && permissions.permissions.includes(ownPermission))
		: false;

	return {
		canPerform,
		isLoading,
		isFetched,
	};
}

/**
 * Hook to get all roles with their permissions (admin only)
 */
export function useRoles() {
	return useQuery({
		...trpc.permissions.getRoles.queryOptions(),
		staleTime: CACHE_CONFIG.ROLES_CACHE_TIME,
		refetchOnWindowFocus: true,
		// Add periodic polling every 5 minutes for admin role data
		refetchInterval: 5 * 60 * 1000, // 5 minutes
		// Only poll when window is visible to save resources
		refetchIntervalInBackground: false,
	});
}

/**
 * Hook to get all permissions (admin only)
 */
export function useAllPermissions() {
	return useQuery({
		...trpc.permissions.getPermissions.queryOptions(),
		staleTime: CACHE_CONFIG.ROLES_CACHE_TIME,
		refetchOnWindowFocus: true,
		// Add periodic polling every 5 minutes for admin permission data
		refetchInterval: 5 * 60 * 1000, // 5 minutes
		// Only poll when window is visible to save resources
		refetchIntervalInBackground: false,
	});
}

/**
 * Hook to get all user roles (admin only)
 */
export function useUserRoles() {
	return useQuery({
		...trpc.permissions.getUserRoles.queryOptions(),
		staleTime: CACHE_CONFIG.USER_ROLES_CACHE_TIME,
		refetchOnWindowFocus: true,
		// Add periodic polling every 5 minutes for admin user role data
		refetchInterval: 5 * 60 * 1000, // 5 minutes
		// Only poll when window is visible to save resources
		refetchIntervalInBackground: false,
	});
}

/**
 * Hook to get all users for admin management
 */
export function useAllUsers(options?: {
	search?: string;
	limit?: number;
	offset?: number;
}) {
	return useQuery({
		...trpc.admin.getAllUsers.queryOptions({
			search: options?.search,
			limit: options?.limit || 50,
			offset: options?.offset || 0,
		}),
		staleTime: CACHE_CONFIG.USER_ROLES_CACHE_TIME,
		refetchOnWindowFocus: true,
		// Add periodic polling every 5 minutes for admin user data
		refetchInterval: 5 * 60 * 1000, // 5 minutes
		// Only poll when window is visible to save resources
		refetchIntervalInBackground: false,
	});
}
