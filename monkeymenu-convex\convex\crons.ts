import { cronJobs } from "convex/server";
import { api } from "./_generated/api";

const crons = cronJobs();

// Transaction verification - runs every 10 minutes (same as old app)
crons.cron(
  "transaction-verification",
  "*/10 * * * *", // Every 10 minutes
  api.banking.runTransactionVerificationCron
);

// Withdrawal expiration - runs every hour at minute 0 (same as old app)
crons.cron(
  "withdrawal-expiration", 
  "0 * * * *", // Every hour at minute 0
  api.banking.runWithdrawalExpirationCron
);

// Overdose detection - runs every hour at minute 0 (same as old app)
crons.cron(
  "overdose-detection",
  "0 * * * *", // Every hour at minute 0
  api.monitoring.runOverdoseDetectionCron
);

// User verification and role updates - runs every hour at minute 0 (same as old app)
crons.cron(
  "user-verification",
  "0 * * * *", // Every hour at minute 0
  api.monitoring.runUserVerificationCron
);

// Discord role audit - runs every hour at minute 5 (5 minutes after user verification)
crons.cron(
  "discord-role-audit",
  "5 * * * *", // Every hour at minute 5
  api.monitoring.runDiscordRoleAuditCron
);

export default crons;
