import React, { memo } from 'react';

interface TargetCardProps {
  target: {
    _id?: string;
    tornId: number;
    username: string;
    level?: number;
    faction?: string;
    status: string; // Real-time status like "Okay", "Hospitalized (5m 32s)", "Error (12)"
    respect?: number;
    fairFight?: number;
    profilePicture?: string;
    battleStats?: {
      strength: number;
      defense: number;
      speed: number;
      dexterity: number;
    };
    lastUpdated?: number;
    createdAt?: number;
    // Enemy faction member fields
    lastAction?: any;
    position?: string;
    isRevivable?: boolean;
    isOnWall?: boolean;
    isInOc?: boolean;
    hasEarlyDischarge?: boolean;
  };
  onRemove?: () => void;
  canRemove?: boolean;
  viewMode?: 'grid' | 'list';
  countdown?: string | null;
  isReady?: boolean;
}

export const TargetCard = memo(function TargetCard({ 
  target, 
  onRemove, 
  canRemove = false,
  viewMode = 'grid',
  countdown,
  isReady = false
}: TargetCardProps) {
  const formatLastUpdated = (timestamp?: number) => {
    if (!timestamp) return 'Unknown';
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const getStatusStyle = (status: string) => {
    // Override status styling for ready targets
    if (isReady && status.includes('Hospitalized')) {
      return 'bg-green-100 text-green-800 border-green-200 animate-pulse';
    }
    
    if (status === 'Okay') {
      return 'bg-green-100 text-green-800 border-green-200';
    } else if (status.includes('Hospitalized')) {
      return 'bg-orange-100 text-orange-800 border-orange-200';
    } else if (status.includes('Error') || status === 'Fetch Error') {
      return 'bg-red-100 text-red-800 border-red-200';
    } else {
      return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    // Override icon for ready targets
    if (isReady && status.includes('Hospitalized')) return '🎯';
    
    if (status === 'Okay') return '✅';
    if (status.includes('Hospitalized')) return '🏥';
    if (status.includes('Error') || status === 'Fetch Error') return '❌';
    return '❓';
  };

  const getTotalBattleStats = () => {
    if (!target.battleStats) return null;
    return target.battleStats.strength + target.battleStats.defense + 
           target.battleStats.speed + target.battleStats.dexterity;
  };

  const handleAttackClick = () => {
    window.open(`https://www.torn.com/loader.php?sid=attack&user2ID=${target.tornId}`, '_blank');
  };

  const handleProfileClick = () => {
    window.open(`https://www.torn.com/profiles.php?XID=${target.tornId}`, '_blank');
  };

  const totalStats = getTotalBattleStats();
  const isHospitalized = target.status.includes('Hospitalized');
  const isError = target.status.includes('Error') || target.status === 'Fetch Error';
  
  // Display status with countdown if available
  const displayStatus = countdown && isHospitalized && !isReady 
    ? `Hospitalized (${countdown})` 
    : isReady && isHospitalized 
    ? 'Ready!' 
    : target.status;

  if (viewMode === 'list') {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between">
          {/* Target Info */}
          <div className="flex items-center space-x-4">
            {target.profilePicture && (
              <img
                src={target.profilePicture}
                alt={target.username}
                className="w-12 h-12 rounded-full"
              />
            )}
            <div>
              <div className="flex items-center space-x-2">
                <h3 className="text-lg font-semibold text-gray-900">
                  {target.username}
                </h3>
                <span className="text-sm text-gray-500">#{target.tornId}</span>
                {target.level && (
                  <>
                    <span className="text-gray-400">•</span>
                    <span className="text-sm text-gray-600">Level {target.level}</span>
                  </>
                )}
              </div>
              {target.faction && (
                <div className="text-sm text-blue-600">{target.faction}</div>
              )}
            </div>
          </div>

          {/* Status and Actions */}
          <div className="flex items-center space-x-4">
            {/* Status */}
            <div className={`px-3 py-1 rounded-full border text-sm font-medium ${getStatusStyle(target.status)}`}>
              <span className="mr-1">{getStatusIcon(target.status)}</span>
              {displayStatus}
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-2">
              <button
                onClick={handleAttackClick}
                disabled={(isHospitalized && !isReady) || isError}
                className={`px-3 py-1 text-sm rounded transition-colors ${
                  isReady && isHospitalized 
                    ? 'bg-green-600 text-white hover:bg-green-700 animate-pulse' 
                    : 'bg-red-600 text-white hover:bg-red-700'
                } disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                {isReady && isHospitalized ? '🎯 Attack Now!' : 'Attack'}
              </button>
              <button
                onClick={handleProfileClick}
                className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                Profile
              </button>
              {canRemove && onRemove && (
                <button
                  onClick={onRemove}
                  className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700"
                >
                  Remove
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Grid view (default)
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-3">
          {target.profilePicture && (
            <img
              src={target.profilePicture}
              alt={target.username}
              className="w-10 h-10 rounded-full"
            />
          )}
          <div className="flex flex-col">
            <div className="flex items-center space-x-2">
              <h3 className="text-lg font-semibold text-gray-900">
                {target.username}
              </h3>
              <span className="text-sm text-gray-500">#{target.tornId}</span>
            </div>
            <div className="flex items-center space-x-2 mt-1">
              {target.level && (
                <span className="text-sm text-gray-600">Level {target.level}</span>
              )}
              {target.faction && (
                <>
                  <span className="text-gray-400">•</span>
                  <span className="text-sm text-blue-600">{target.faction}</span>
                </>
              )}
            </div>
          </div>
        </div>
        
        {canRemove && onRemove && (
          <button
            onClick={onRemove}
            className="text-gray-400 hover:text-red-600 text-sm"
            title="Remove target"
          >
            ✕
          </button>
        )}
      </div>

      {/* Status */}
      <div className="mb-3">
        <div className={`inline-flex items-center px-3 py-1 rounded-full border text-sm font-medium ${getStatusStyle(target.status)}`}>
          <span className="mr-1">{getStatusIcon(target.status)}</span>
          {displayStatus}
          {countdown && isHospitalized && !isReady && (
            <div className="ml-2 text-xs bg-black bg-opacity-20 px-2 py-1 rounded font-mono">
              ⏱️ {countdown}
            </div>
          )}
        </div>
      </div>

      {/* Stats */}
      <div className="space-y-2 mb-3">
        {target.respect !== undefined && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Respect:</span>
            <span className="font-semibold text-green-600">
              {target.respect.toLocaleString()}
            </span>
          </div>
        )}
        
        {target.fairFight !== undefined && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Fair Fight:</span>
            <span className="font-semibold text-blue-600">
              {target.fairFight.toFixed(2)}
            </span>
          </div>
        )}
        
        {totalStats && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Total Stats:</span>
            <span className="font-semibold text-purple-600">
              {totalStats.toLocaleString()}
            </span>
          </div>
        )}

        {/* Enemy faction specific info */}
        {target.position && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Position:</span>
            <span className="font-semibold">{target.position}</span>
          </div>
        )}
      </div>

      {/* Battle Stats */}
      {target.battleStats && (
        <div className="mb-3">
          <div className="text-xs text-gray-600 mb-1">Battle Stats:</div>
          <div className="grid grid-cols-4 gap-2 text-xs">
            <div className="text-center">
              <div className="text-red-600 font-semibold">{target.battleStats.strength.toLocaleString()}</div>
              <div className="text-gray-500">STR</div>
            </div>
            <div className="text-center">
              <div className="text-blue-600 font-semibold">{target.battleStats.defense.toLocaleString()}</div>
              <div className="text-gray-500">DEF</div>
            </div>
            <div className="text-center">
              <div className="text-green-600 font-semibold">{target.battleStats.speed.toLocaleString()}</div>
              <div className="text-gray-500">SPD</div>
            </div>
            <div className="text-center">
              <div className="text-purple-600 font-semibold">{target.battleStats.dexterity.toLocaleString()}</div>
              <div className="text-gray-500">DEX</div>
            </div>
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="flex items-center justify-between pt-3 border-t border-gray-100">
        <div className="text-xs text-gray-500">
          {target.lastUpdated ? `Updated ${formatLastUpdated(target.lastUpdated)}` : 'Live data'}
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={handleAttackClick}
            disabled={(isHospitalized && !isReady) || isError}
            className={`px-3 py-1 text-xs rounded transition-colors ${
              isReady && isHospitalized 
                ? 'bg-green-600 text-white hover:bg-green-700 animate-pulse' 
                : 'bg-red-600 text-white hover:bg-red-700'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            {isReady && isHospitalized ? '🎯 Attack Now!' : '⚔️ Attack'}
          </button>
          <button
            onClick={handleProfileClick}
            className="px-3 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            👤 Profile
          </button>
        </div>
      </div>
    </div>
  );
});