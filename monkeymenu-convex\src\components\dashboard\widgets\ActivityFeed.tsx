import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "../../ui/card";
import { ScrollArea } from "../../ui/scroll-area";
import { Skeleton } from "../../ui/skeleton";
import {
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	Banknote,
	BookOpen,
	Check,
	Megaphone,
	X,
} from "lucide-react";
import { useMemo } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../../convex/_generated/api";

type ActivityItem = {
	type: "announcement" | "withdrawal" | "guide";
	icon: React.ReactNode;
	title: string;
	description: string;
	timestamp: Date;
	status?: string;
};

export function ActivityFeed() {
	// Fetch real data from Convex
	const announcementsQuery = useQuery(api.announcements.getRecentAnnouncements, {});
	const guidesQuery = useQuery(api.guides.getRecentGuides, {});
	const withdrawalsQuery = useQuery(api.banking.getRecentWithdrawals, {});

	const isLoading =
		announcementsQuery === undefined ||
		guidesQuery === undefined ||
		withdrawalsQuery === undefined;

	const activities = useMemo(() => {
		const combined: ActivityItem[] = [];

		// Process announcements
		if (announcementsQuery) {
			for (const announcement of announcementsQuery) {
				combined.push({
					type: "announcement",
					icon: <Megaphone className="h-4 w-4" />,
					title: "New Announcement",
					description: `"${announcement.title}" was published.`,
					timestamp: new Date(announcement.createdAt),
				});
			}
		}

		// Process guides
		if (guidesQuery) {
			for (const guide of guidesQuery) {
				combined.push({
					type: "guide",
					icon: <BookOpen className="h-4 w-4" />,
					title: "New Guide",
					description: `"${guide.title}" was published.`,
					timestamp: new Date(guide.createdAt),
				});
			}
		}

		// Process withdrawals
		if (withdrawalsQuery) {
			for (const withdrawal of withdrawalsQuery) {
				let icon = <Banknote className="h-4 w-4" />;
				let title = "Withdrawal Request";
				let description = `Request for $${withdrawal.amount.toLocaleString()} submitted.`;

				switch (withdrawal.status) {
					case "ACCEPTED":
						icon = <Check className="h-4 w-4 text-green-500" />;
						title = "Withdrawal Approved";
						description = `Request for $${withdrawal.amount.toLocaleString()} was approved.`;
						break;
					case "DECLINED":
						icon = <X className="h-4 w-4 text-red-500" />;
						title = "Withdrawal Declined";
						description = `Request for $${withdrawal.amount.toLocaleString()} was declined.`;
						break;
					case "PENDING":
						icon = <AlertTriangle className="h-4 w-4 text-yellow-500" />;
						title = "Withdrawal Pending";
						break;
				}

				combined.push({
					type: "withdrawal",
					icon,
					title,
					description,
					timestamp: new Date(withdrawal.createdAt),
					status: withdrawal.status,
				});
			}
		}

		// Filter to last 7 days and sort by most recent
		const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
		return combined
			.filter((activity) => activity.timestamp > sevenDaysAgo)
			.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
	}, [announcementsQuery, guidesQuery, withdrawalsQuery]);

	const timeAgo = (date: Date) => {
		const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000);
		let interval = seconds / 31536000;
		if (interval > 1) return `${Math.floor(interval)}y ago`;
		interval = seconds / 2592000;
		if (interval > 1) return `${Math.floor(interval)}mo ago`;
		interval = seconds / 86400;
		if (interval > 1) return `${Math.floor(interval)}d ago`;
		interval = seconds / 3600;
		if (interval > 1) return `${Math.floor(interval)}h ago`;
		interval = seconds / 60;
		if (interval > 1) return `${Math.floor(interval)}m ago`;
		return `${Math.floor(seconds)}s ago`;
	};

	return (
		<Card className="flex flex-col">
			<CardHeader data-card-header>
				<CardTitle>Recent Activity</CardTitle>
				<CardDescription>Latest actions within the faction.</CardDescription>
			</CardHeader>
			<CardContent data-card-content className="flex flex-1 flex-col">
				{isLoading ? (
					<div className="space-y-4">
						{["1", "2", "3", "4", "5"].map((k) => (
							<Skeleton key={`sk-${k}`} className="h-12 w-full" />
						))}
					</div>
				) : activities.length > 0 ? (
					<ScrollArea className="max-h-96 flex-1">
						<ul className="space-y-4 pr-4">
							{activities.map((activity, index) => (
								<li
									key={`${activity.type}-${activity.timestamp.getTime()}-${index}`}
									className="flex items-start gap-3"
								>
									<div className="mt-1 flex h-8 w-8 items-center justify-center rounded-full bg-secondary">
										{activity.icon}
									</div>
									<div className="flex-1">
										<p className="font-medium text-sm">{activity.title}</p>
										<p className="text-muted-foreground text-sm">
											{activity.description}
										</p>
									</div>
									<div className="text-right text-muted-foreground text-xs">
										{timeAgo(activity.timestamp)}
									</div>
								</li>
							))}
						</ul>
					</ScrollArea>
				) : (
					<div className="flex flex-1 items-center justify-center">
						<p className="text-muted-foreground text-sm">
							No recent activity to display.
						</p>
					</div>
				)}
			</CardContent>
		</Card>
	);
}