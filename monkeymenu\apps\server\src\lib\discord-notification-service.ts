import { and, eq } from "drizzle-orm";
import { initDb } from "../db";
import { account as accountTable } from "../db/schema/auth";
import type { AppBindings } from "./types";

// Types for Discord API
interface DiscordEmbed {
	title?: string;
	description?: string;
	color?: number;
	fields?: Array<{
		name: string;
		value: string;
		inline?: boolean;
	}>;
	footer?: {
		text: string;
	};
	timestamp?: string;
}

interface DiscordComponent {
	type: number;
	components: Array<{
		type: number;
		style: number;
		label: string;
		url: string;
		emoji?: {
			name: string;
		};
	}>;
}

interface DiscordMessage {
	content?: string;
	embeds: DiscordEmbed[];
	components?: DiscordComponent[];
}

export interface BankingNotificationPayload {
	userName: string;
	amount: number;
	requestId: string;
	requestingUserId: string;
	requesterTornId?: string;
}

export interface EmbedUpdateNotificationPayload {
	discordMessageId: string;
	newStatus: "ACCEPTED" | "DECLINED" | "EXPIRED" | "CANCELLED" | "COMPLETED";
	requestId: string;
	amount: number;
	processedBy: string;
	requesterTornId?: string;
}

export interface AnnouncementNotificationPayload {
	title: string;
	content: string;
	authorName: string;
	announcementId: number;
	isUrgent?: boolean;
}

export interface OverdoseNotificationPayload {
	userName: string;
	userTornId: string;
	timestamp: number;
	drugName?: string;
	eventText: string;
}

export interface DMNotificationPayload {
	userId: string; // MonkeyMenu user ID - will be converted to Discord ID internally
	withdrawalId: string;
	amount: number;
	status: "ACCEPTED" | "DECLINED" | "EXPIRED" | "CANCELLED";
	processedBy?: string;
	userName?: string;
}

export class DiscordNotificationService {
	private env: AppBindings["Bindings"];

	constructor(env: AppBindings["Bindings"]) {
		this.env = env;
	}

	/**
	 * Send a banking withdrawal request notification to Discord
	 */
	async sendBankingRequestNotification(
		payload: BankingNotificationPayload,
	): Promise<{ success: boolean; discordMessageId?: string; error?: string }> {
		const { userName, amount, requestId, requesterTornId } = payload;

		// Validate required environment variables
		if (
			!this.env.DISCORD_BOT_TOKEN ||
			!this.env.BANKING_NOTIFICATION_CHANNEL_ID
		) {
			console.warn(
				"Discord bot token or banking notification channel ID not configured",
			);
			return {
				success: false,
				error: "Discord not configured",
			};
		}

		if (!this.env.APP_URL) {
			console.error("APP_URL not configured for Discord button links");
			return {
				success: false,
				error: "APP_URL not configured",
			};
		}

		try {
			// Create the embed
			const embed: DiscordEmbed = {
				color: 0x58b6ff, // Blue for pending
				title: `🏦 Withdrawal Request: ${userName || "Unknown User"}`,
				fields: [
					{ name: "User", value: userName || "Unknown User", inline: true },
					{
						name: "Amount",
						value: `$${amount.toLocaleString()}`,
						inline: true,
					},
					{ name: "Status", value: "⏳ Pending Action", inline: true },
				],
				footer: {
					text: `MonkeyMenu Banking | Request ID: ${requestId}`,
				},
				timestamp: new Date().toISOString(),
			};

			// Create the action buttons
			const approveLink = `${this.env.APP_URL}/api/bot/approve-withdrawal?requestId=${requestId}&requesterTornId=${requesterTornId || "UNKNOWN"}&amount=${amount}`;
			const rejectLink = `${this.env.APP_URL}/api/bot/reject-withdrawal?requestId=${requestId}`;

			const components: DiscordComponent[] = [
				{
					type: 1, // ACTION_ROW
					components: [
						{
							type: 2, // BUTTON
							style: 5, // LINK
							label: "Approve",
							url: approveLink,
							emoji: {
								name: "✅",
							},
						},
						{
							type: 2, // BUTTON
							style: 5, // LINK
							label: "Reject",
							url: rejectLink,
							emoji: {
								name: "❌",
							},
						},
					],
				},
			];

			const message: DiscordMessage = {
				embeds: [embed],
				components,
			};

			// Send the message to Discord
			const response = await fetch(
				`https://discord.com/api/v10/channels/${this.env.BANKING_NOTIFICATION_CHANNEL_ID}/messages`,
				{
					method: "POST",
					headers: {
						Authorization: `Bot ${this.env.DISCORD_BOT_TOKEN}`,
						"Content-Type": "application/json",
					},
					body: JSON.stringify(message),
				},
			);

			if (!response.ok) {
				const errorText = await response.text();
				console.error(
					`Failed to send Discord notification: ${response.status} ${errorText}`,
				);
				return {
					success: false,
					error: `Discord API error: ${response.status}`,
				};
			}

			const result = (await response.json()) as { id: string };
			console.log(
				`Successfully sent banking request notification to Discord for request ${requestId}. Message ID: ${result.id}`,
			);

			return {
				success: true,
				discordMessageId: result.id,
			};
		} catch (error) {
			console.error("Error sending Discord notification:", error);
			return {
				success: false,
				error: error instanceof Error ? error.message : "Unknown error",
			};
		}
	}

	/**
	 * Update an existing Discord embed when withdrawal status changes
	 */
	async updateWithdrawalEmbed(
		payload: EmbedUpdateNotificationPayload,
	): Promise<{ success: boolean; error?: string }> {
		const { discordMessageId, newStatus, processedBy, requestId } = payload;

		// Validate required environment variables
		if (
			!this.env.DISCORD_BOT_TOKEN ||
			!this.env.BANKING_NOTIFICATION_CHANNEL_ID
		) {
			console.warn(
				"Discord bot token or banking notification channel ID not configured",
			);
			return {
				success: false,
				error: "Discord not configured",
			};
		}

		try {
			// First, fetch the existing message
			const fetchResponse = await fetch(
				`https://discord.com/api/v10/channels/${this.env.BANKING_NOTIFICATION_CHANNEL_ID}/messages/${discordMessageId}`,
				{
					headers: {
						Authorization: `Bot ${this.env.DISCORD_BOT_TOKEN}`,
					},
				},
			);

			if (!fetchResponse.ok) {
				console.error(
					`Failed to fetch Discord message ${discordMessageId}: ${fetchResponse.status}`,
				);
				return {
					success: false,
					error: `Failed to fetch message: ${fetchResponse.status}`,
				};
			}

			const existingMessage = (await fetchResponse.json()) as {
				embeds: DiscordEmbed[];
			};
			const originalEmbed = existingMessage.embeds[0];

			if (!originalEmbed) {
				console.error(`No embed found on Discord message ${discordMessageId}`);
				return {
					success: false,
					error: "No embed found on message",
				};
			}

			// Determine status text and color based on newStatus
			let statusText = "";
			let embedColor = originalEmbed.color;

			switch (newStatus) {
				case "ACCEPTED":
					statusText = `✅ Accepted by ${processedBy}`;
					embedColor = 0x00ff00; // Green
					break;
				case "DECLINED":
					statusText = `🚫 Declined by ${processedBy}`;
					embedColor = 0xff0000; // Red
					break;
				case "EXPIRED":
					statusText = "⏳ Expired";
					embedColor = 0xffa500; // Orange
					break;
				case "CANCELLED":
					statusText = `⏰ Cancelled by ${processedBy}`;
					embedColor = 0xffcc00; // Yellow/Orange
					break;
				case "COMPLETED":
					statusText = `✅ Completed by ${processedBy}`;
					embedColor = 0x00ff00; // Green
					break;
				default:
					console.warn(
						`Unhandled status: ${newStatus} for request ${requestId}`,
					);
					statusText = `Status: ${newStatus} (Processed by ${processedBy})`;
					break;
			}

			// Update the embed
			const updatedEmbed: DiscordEmbed = {
				...originalEmbed,
				color: embedColor,
				fields: originalEmbed.fields ? [...originalEmbed.fields] : [],
			};

			// Update or add the status field
			const statusFieldIndex = updatedEmbed.fields?.findIndex((field) =>
				field.name.toLowerCase().includes("status"),
			);

			const newStatusField = {
				name: "Status Update",
				value: statusText,
				inline: false,
			};

			if (statusFieldIndex !== undefined && statusFieldIndex !== -1) {
				if (updatedEmbed.fields) {
					updatedEmbed.fields[statusFieldIndex] = newStatusField;
				}
			} else {
				if (updatedEmbed.fields) {
					updatedEmbed.fields.push(newStatusField);
				} else {
					updatedEmbed.fields = [newStatusField];
				}
			}

			const updateMessage: DiscordMessage = {
				embeds: [updatedEmbed],
				components: [], // Remove buttons for final statuses
			};

			// Update the message
			const updateResponse = await fetch(
				`https://discord.com/api/v10/channels/${this.env.BANKING_NOTIFICATION_CHANNEL_ID}/messages/${discordMessageId}`,
				{
					method: "PATCH",
					headers: {
						Authorization: `Bot ${this.env.DISCORD_BOT_TOKEN}`,
						"Content-Type": "application/json",
					},
					body: JSON.stringify(updateMessage),
				},
			);

			if (!updateResponse.ok) {
				const errorText = await updateResponse.text();
				console.error(
					`Failed to update Discord message ${discordMessageId}: ${updateResponse.status} ${errorText}`,
				);
				return {
					success: false,
					error: `Failed to update message: ${updateResponse.status}`,
				};
			}

			console.log(
				`Successfully updated Discord embed ${discordMessageId} to status ${newStatus}`,
			);

			return {
				success: true,
			};
		} catch (error) {
			console.error("Error updating Discord embed:", error);
			return {
				success: false,
				error: error instanceof Error ? error.message : "Unknown error",
			};
		}
	}

	/**
	 * Send an announcement notification to Discord
	 */
	async sendAnnouncementNotification(
		payload: AnnouncementNotificationPayload,
	): Promise<{ success: boolean; error?: string }> {
		const { title, content, authorName, announcementId, isUrgent } = payload;

		// Validate required environment variables
		if (!this.env.DISCORD_BOT_TOKEN || !this.env.ANNOUNCEMENTS_CHANNEL_ID) {
			console.warn(
				"Discord bot token or announcements channel ID not configured",
			);
			return {
				success: false,
				error: "Discord not configured",
			};
		}

		try {
			// Truncate content if too long for Discord embed
			const maxContentLength = 1000;
			const truncatedContent =
				content.length > maxContentLength
					? `${content.substring(0, maxContentLength)}...`
					: content;

			// Create the embed
			const embed: DiscordEmbed = {
				color: isUrgent ? 0xff0000 : 0x5865f2, // Red for urgent, Discord blurple for normal
				title: `${isUrgent ? "🚨 URGENT: " : "📢 "}${title}`,
				description: truncatedContent,
				fields: [
					{ name: "Author", value: authorName, inline: true },
					{ name: "Posted", value: new Date().toLocaleString(), inline: true },
				],
				footer: {
					text: `MonkeyMenu Announcements | ID: ${announcementId}`,
				},
				timestamp: new Date().toISOString(),
			};

			// Add link to full announcement if content was truncated
			if (content.length > maxContentLength && this.env.APP_URL) {
				embed.fields?.push({
					name: "Full Announcement",
					value: `[View on MonkeyMenu](${this.env.APP_URL}/announcements)`,
					inline: false,
				});
			}

			// Prepare message content with optional role ping for urgent announcements
			let messageContent = "";
			if (isUrgent && this.env.FACTION_ROLE_ID) {
				messageContent = `🚨 <@&${this.env.FACTION_ROLE_ID}> **URGENT ANNOUNCEMENT** 🚨`;
			}

			const message: DiscordMessage = {
				content: messageContent || undefined,
				embeds: [embed],
			};

			// Send the message to Discord
			const response = await fetch(
				`https://discord.com/api/v10/channels/${this.env.ANNOUNCEMENTS_CHANNEL_ID}/messages`,
				{
					method: "POST",
					headers: {
						Authorization: `Bot ${this.env.DISCORD_BOT_TOKEN}`,
						"Content-Type": "application/json",
					},
					body: JSON.stringify(message),
				},
			);

			if (!response.ok) {
				const errorText = await response.text();
				console.error(
					`Failed to send Discord announcement: ${response.status} ${errorText}`,
				);
				return {
					success: false,
					error: `Discord API error: ${response.status}`,
				};
			}

			console.log(
				`Successfully sent ${isUrgent ? "urgent " : ""}announcement notification to Discord for announcement ${announcementId}`,
			);

			return {
				success: true,
			};
		} catch (error) {
			console.error("Error sending Discord announcement:", error);
			return {
				success: false,
				error: error instanceof Error ? error.message : "Unknown error",
			};
		}
	}

	/**
	 * Send an overdose notification to Discord
	 */
	async sendOverdoseNotification(
		payload: OverdoseNotificationPayload,
	): Promise<{ success: boolean; error?: string }> {
		const { userName, userTornId, timestamp, drugName, eventText } = payload;

		// Validate required environment variables
		if (
			!this.env.DISCORD_BOT_TOKEN ||
			!this.env.OVERDOSE_NOTIFICATION_CHANNEL_ID
		) {
			console.warn(
				"Discord bot token or overdose notification channel ID not configured",
			);
			return {
				success: false,
				error: "Discord not configured",
			};
		}

		try {
			// Extract drug name from event text if not provided
			const extractedDrugName = drugName || this.extractDrugName(eventText);

			// Create the embed
			const embed: DiscordEmbed = {
				color: 0xff4444, // Red for overdose alert
				title: `💊 Overdose Alert: ${userName}`,
				description: eventText,
				fields: [
					{ name: "User", value: userName, inline: true },
					{ name: "Torn ID", value: userTornId, inline: true },
					{ name: "Drug", value: extractedDrugName || "Unknown", inline: true },
					{ name: "Time", value: `<t:${timestamp}:F>`, inline: false },
				],
				footer: {
					text: "MonkeyMenu Overdose Detection",
				},
				timestamp: new Date(timestamp * 1000).toISOString(),
			};

			// Add profile link if APP_URL is configured
			if (this.env.APP_URL) {
				embed.fields?.push({
					name: "Profile",
					value: `[View on Torn](https://www.torn.com/profiles.php?XID=${userTornId})`,
					inline: true,
				});
			}

			const message: DiscordMessage = {
				embeds: [embed],
			};

			// Send the message to Discord
			const response = await fetch(
				`https://discord.com/api/v10/channels/${this.env.OVERDOSE_NOTIFICATION_CHANNEL_ID}/messages`,
				{
					method: "POST",
					headers: {
						Authorization: `Bot ${this.env.DISCORD_BOT_TOKEN}`,
						"Content-Type": "application/json",
					},
					body: JSON.stringify(message),
				},
			);

			if (!response.ok) {
				const errorText = await response.text();
				console.error(
					`Failed to send Discord overdose notification: ${response.status} ${errorText}`,
				);
				return {
					success: false,
					error: `Discord API error: ${response.status}`,
				};
			}

			console.log(
				`Successfully sent overdose notification to Discord for user ${userName} (ID: ${userTornId})`,
			);

			return {
				success: true,
			};
		} catch (error) {
			console.error("Error sending Discord overdose notification:", error);
			return {
				success: false,
				error: error instanceof Error ? error.message : "Unknown error",
			};
		}
	}

	/**
	 * Send a DM notification to a user about their withdrawal status
	 */
	async sendWithdrawalDMNotification(
		payload: DMNotificationPayload,
	): Promise<{ success: boolean; error?: string }> {
		const { userId, withdrawalId, amount, status, processedBy } = payload;

		// Validate required environment variables
		if (!this.env.DISCORD_BOT_TOKEN) {
			console.warn("Discord bot token not configured");
			return {
				success: false,
				error: "Discord not configured",
			};
		}

		try {
			// Convert MonkeyMenu user ID to Discord ID
			const discordUserId = await this.getDiscordIdFromUserId(userId);
			if (!discordUserId) {
				console.warn(`No Discord account found for user ${userId}`);
				return {
					success: false,
					error: "User has no linked Discord account",
				};
			}

			// Create DM channel with user
			const dmChannelResponse = await fetch(
				"https://discord.com/api/v10/users/@me/channels",
				{
					method: "POST",
					headers: {
						Authorization: `Bot ${this.env.DISCORD_BOT_TOKEN}`,
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						recipient_id: discordUserId,
					}),
				},
			);

			if (!dmChannelResponse.ok) {
				const errorText = await dmChannelResponse.text();
				console.error(
					`Failed to create DM channel with user ${discordUserId}: ${dmChannelResponse.status} ${errorText}`,
				);
				return {
					success: false,
					error: `Failed to create DM channel: ${dmChannelResponse.status}`,
				};
			}

			const dmChannel = (await dmChannelResponse.json()) as { id: string };

			// Determine status text and color based on status
			let statusText = "";
			let embedColor = 0x58b6ff; // Default blue
			let title = "";

			switch (status) {
				case "ACCEPTED":
					statusText = `✅ **Approved** by ${processedBy || "Admin"}`;
					embedColor = 0x00ff00; // Green
					title = "💰 Withdrawal Request Approved";
					break;
				case "DECLINED":
					statusText = `🚫 **Declined** by ${processedBy || "Admin"}`;
					embedColor = 0xff0000; // Red
					title = "❌ Withdrawal Request Declined";
					break;
				case "EXPIRED":
					statusText = "⏳ **Expired** (No action taken)";
					embedColor = 0xffa500; // Orange
					title = "⏰ Withdrawal Request Expired";
					break;
				case "CANCELLED":
					statusText = "⏰ **Cancelled** - Manual review required";
					embedColor = 0xffcc00; // Yellow/Orange
					title = "⏰ Withdrawal Request Cancelled";
					break;
				default:
					statusText = `Status: ${status}`;
					title = "📋 Withdrawal Update";
					break;
			}

			// Create the embed
			const embed: DiscordEmbed = {
				color: embedColor,
				title: title,
				description: statusText,
				fields: [
					{
						name: "💰 Amount",
						value: `$${amount.toLocaleString()}`,
						inline: true,
					},
					{ name: "🆔 Request ID", value: `\`${withdrawalId}\``, inline: true },
					{
						name: "📅 Updated",
						value: `<t:${Math.floor(Date.now() / 1000)}:R>`,
						inline: true,
					},
				],
				footer: {
					text: "MonkeyMenu Banking System",
				},
				timestamp: new Date().toISOString(),
			};

			// Add additional context based on status
			if (status === "ACCEPTED") {
				embed.fields?.push({
					name: "💡 What's Next?",
					value:
						"Your money will be transferred to your Torn account shortly. Please allow up to a few minutes for processing.",
					inline: false,
				});
			} else if (status === "DECLINED") {
				embed.fields?.push({
					name: "💡 Need Help?",
					value:
						"If you have questions about this decision, please contact a faction administrator.",
					inline: false,
				});
			} else if (status === "EXPIRED") {
				embed.fields?.push({
					name: "💡 Want to Try Again?",
					value:
						"You can submit a new withdrawal request anytime using the `/withdraw` command or the MonkeyMenu website.",
					inline: false,
				});
			}

			// Add link to banking dashboard
			if (this.env.APP_URL) {
				embed.fields?.push({
					name: "🔗 Banking Dashboard",
					value: `[View All Requests](${this.env.APP_URL}/dashboard/banking)`,
					inline: false,
				});
			}

			const message: DiscordMessage = {
				embeds: [embed],
			};

			// Send the DM
			const dmResponse = await fetch(
				`https://discord.com/api/v10/channels/${dmChannel.id}/messages`,
				{
					method: "POST",
					headers: {
						Authorization: `Bot ${this.env.DISCORD_BOT_TOKEN}`,
						"Content-Type": "application/json",
					},
					body: JSON.stringify(message),
				},
			);

			if (!dmResponse.ok) {
				const errorText = await dmResponse.text();
				console.error(
					`Failed to send DM to user ${discordUserId}: ${dmResponse.status} ${errorText}`,
				);
				return {
					success: false,
					error: `Failed to send DM: ${dmResponse.status}`,
				};
			}

			console.log(
				`Successfully sent DM notification to user ${discordUserId} for withdrawal ${withdrawalId} (status: ${status})`,
			);

			return {
				success: true,
			};
		} catch (error) {
			console.error("Error sending DM notification:", error);
			return {
				success: false,
				error: error instanceof Error ? error.message : "Unknown error",
			};
		}
	}

	/**
	 * Extract drug name from overdose event text
	 */
	private extractDrugName(eventText: string): string {
		const drugPatterns = [
			/overdosed on ([^.]+) and/i,
			/overdosed on ([^.]+)\./i,
			/overdosed on ([^.]+)$/i,
		];

		for (const pattern of drugPatterns) {
			const match = eventText.match(pattern);
			if (match?.[1]) {
				return match[1].trim();
			}
		}

		return "Unknown";
	}

	/**
	 * Get Discord ID from MonkeyMenu user ID
	 */
	private async getDiscordIdFromUserId(userId: string): Promise<string | null> {
		try {
			const db = initDb(this.env);
			const discordAccount = await db
				.select({
					accountId: accountTable.accountId,
				})
				.from(accountTable)
				.where(
					and(
						eq(accountTable.userId, userId),
						eq(accountTable.providerId, "discord"),
					),
				)
				.get();

			return discordAccount?.accountId || null;
		} catch (error) {
			console.error("Error fetching Discord ID:", error);
			return null;
		}
	}
}
