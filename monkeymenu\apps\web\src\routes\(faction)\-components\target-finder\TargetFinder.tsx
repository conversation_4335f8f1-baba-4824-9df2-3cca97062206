import { HasPermission } from "@/components/permissions/PermissionGuards";
import { TooltipProvider } from "@/components/ui/tooltip";
import { useTargetFinderLiveStoreSync } from "@/lib/target-finder-livestore-bridge";
import { trpc } from "@/lib/trpc-client";
import { PERMISSION_NAMES } from "@monkeymenu/shared";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { toast } from "sonner";
import { ChainStatusCard } from "./ChainStatusCard";
import { TargetContent } from "./TargetContent";
import { TargetFilters } from "./TargetFilters";
import { TargetHeader } from "./TargetHeader";
import { useChainStatus } from "./hooks/useChainStatus";
import { useTargetStatuses } from "./hooks/useTargetStatuses";
import type {
	ChainInfo,
	SortBy,
	SortOrder,
	StatusFilter,
	TargetStats,
	TargetWithStatus,
} from "./types";
import { getHospitalTimeInSeconds, getStatusPriority } from "./utils";

export function TargetFinder() {
	return (
		<HasPermission permission={PERMISSION_NAMES.TARGET_FINDER_VIEW}>
			<TargetFinderInternal />
		</HasPermission>
	);
}

function TargetFinderInternal() {
	const [selectedList, setSelectedList] = useState<string>("");
	const [searchTerm, setSearchTerm] = useState("");
	const [statusFilter, setStatusFilter] = useState<StatusFilter>("all");
	const [sortBy] = useState<SortBy>("smart");
	const [sortOrder] = useState<SortOrder>("asc");
	const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
	const [lastRefreshTime, setLastRefreshTime] = useState<Date | undefined>();

	const queryClient = useQueryClient();

	// LiveStore integration for real-time sync
	const { syncTargetRemoval } = useTargetFinderLiveStoreSync();

	// Track recent WebSocket notifications to prevent duplicates
	const recentNotificationsRef = useRef<Set<string>>(new Set());
	const timeoutsRef = useRef<Set<NodeJS.Timeout>>(new Set());

	// LiveStore now provides real-time updates; WebSocket no longer used

	// Automatically add target to local state when WebSocket notification received
	const handleAutoAddTarget = useCallback(
		async (tornId: string, listName: string) => {
			console.log(`Auto-adding target ${tornId} to ${listName}`);

			// Only auto-add if this is the currently selected list
			if (selectedList !== listName) {
				console.log(
					`Skipping auto-add - ${listName} is not the current list (${selectedList})`,
				);
				return;
			}

			try {
				// Refetch the specific list to get the new target
				// This is much more efficient than a full refresh
				const updatedTargets = await queryClient.fetchQuery(
					trpc.targetFinder.getTargets.queryOptions({ listName }),
				);

				// Update the cache with the new data
				queryClient.setQueryData(
					trpc.targetFinder.getTargets.queryKey({ listName }),
					updatedTargets,
				);

				console.log(`Successfully auto-added target to ${listName}`);
			} catch (error) {
				console.error(`Failed to auto-add target to ${listName}:`, error);
				// Don't show error to user - they can still manually refresh
			}
		},
		[queryClient, selectedList],
	);

	// Automatically remove target from local state when WebSocket notification received
	const handleAutoRemoveTarget = useCallback(
		(tornId: string, listName: string) => {
			console.log(`Auto-removing target ${tornId} from ${listName}`);

			// Update React Query cache to remove the target
			queryClient.setQueryData(
				trpc.targetFinder.getTargets.queryKey({ listName }),
				(oldData: TargetWithStatus[] | undefined) => {
					if (!oldData) return oldData;
					const filteredTargets = oldData.filter(
						(target) => target.tornId !== tornId,
					);
					console.log(
						`Removed target from cache. ${oldData.length} -> ${filteredTargets.length} targets`,
					);
					return filteredTargets;
				},
			);

			// Also remove from Custom List cache if it's the selected list
			if (listName === "Custom List") {
				queryClient.setQueryData(
					trpc.targetFinder.getTargets.queryKey({ listName: "Custom List" }),
					(oldData: TargetWithStatus[] | undefined) => {
						if (!oldData) return oldData;
						return oldData.filter((target) => target.tornId !== tornId);
					},
				);
			}

			// Sync removal to LiveStore - get target data from cache
			const cachedTargets = queryClient.getQueryData(
				trpc.targetFinder.getTargets.queryKey({ listName }),
			) as TargetWithStatus[] | undefined;

			const targetToRemove = cachedTargets?.find((t) => t.tornId === tornId);
			if (targetToRemove) {
				syncTargetRemoval({
					id: targetToRemove.id,
					listId: targetToRemove.listId,
					tornId: tornId,
				});
			}
		},
		[queryClient, syncTargetRemoval],
	);

	// Listen for WebSocket notifications with deduplication
	useEffect(() => {
		const handleTargetFinderNotification = (event: CustomEvent) => {
			const { type, listName, targetName, timestamp, tornId } = event.detail;

			console.log("WebSocket notification received:", {
				type,
				listName,
				targetName,
				timestamp,
				tornId,
			});

			// Create simpler unique key without timestamp (since server might send duplicates)
			const notificationKey = `${type}:${listName}:${targetName || tornId}`;

			console.log("Generated notification key:", notificationKey);
			console.log(
				"Current tracked notifications:",
				Array.from(recentNotificationsRef.current),
			);

			// Skip if we've already shown this notification recently
			if (recentNotificationsRef.current.has(notificationKey)) {
				console.log(
					`Skipping duplicate WebSocket notification: ${notificationKey}`,
				);
				return;
			}

			// Track this notification and clear it after 30 seconds (longer time to prevent duplicates)
			recentNotificationsRef.current.add(notificationKey);
			console.log("Added to tracked notifications:", notificationKey);

			// Clear the notification tracking after 30 seconds
			const timeoutId = setTimeout(() => {
				recentNotificationsRef.current.delete(notificationKey);
				timeoutsRef.current.delete(timeoutId);
				console.log("Removed from tracked notifications:", notificationKey);
			}, 30000); // 30 seconds

			// Track timeout for cleanup
			timeoutsRef.current.add(timeoutId);

			// Show toast notification and handle automatic updates
			console.log("Showing notification for:", notificationKey);
			if (type === "target_added") {
				toast.success(`${targetName} was automatically added to ${listName}`);
				// Automatically add the target to local state
				handleAutoAddTarget(tornId, listName);
			} else if (type === "target_removed") {
				toast.info(`${targetName} was automatically removed from ${listName}`);
				// Automatically remove the target from local state
				handleAutoRemoveTarget(tornId, listName);
			}
		};

		window.addEventListener(
			"targetFinderNotification",
			handleTargetFinderNotification as EventListener,
		);

		return () => {
			window.removeEventListener(
				"targetFinderNotification",
				handleTargetFinderNotification as EventListener,
			);

			// Clean up any pending timeouts
			for (const timeoutId of timeoutsRef.current) {
				clearTimeout(timeoutId);
			}
			timeoutsRef.current.clear();
		};
	}, [handleAutoAddTarget, handleAutoRemoveTarget]);

	// Enhanced target addition handler - more efficient than full refresh
	useEffect(() => {
		const handleTargetAddition = (event: Event) => {
			const customEvent = event as CustomEvent;
			const { tornId, listName } = customEvent.detail;

			// Only handle if it's for the currently selected list
			if (listName !== selectedList) return;

			try {
				console.log(
					`[Efficient] Triggering targeted refresh for new target: ${tornId}`,
				);

				// More efficient: only invalidate the specific list query
				// This preserves war info, chain status, and other list caches
				queryClient.invalidateQueries({
					queryKey: ["targetFinder", "getTargets", { listName: selectedList }],
				});

				console.log(
					`[Efficient] Targeted refresh triggered for: ${selectedList}`,
				);
			} catch (error) {
				console.error("Failed to trigger target refresh:", error);
			}
		};

		// Listen for target additions
		const listener = (event: Event) => {
			const customEvent = event as CustomEvent;
			if (customEvent.detail?.type === "target_added") {
				handleTargetAddition(event);
			}
		};

		window.addEventListener("targetFinderNotification", listener);

		return () => {
			window.removeEventListener("targetFinderNotification", listener);
		};
	}, [selectedList, queryClient]);

	// Fetch lists - these change infrequently, so longer cache is fine
	const { data: lists, isLoading: listsLoading } = useQuery({
		...trpc.targetFinder.getLists.queryOptions(),
		refetchOnWindowFocus: false, // Prevent auto-refetch on tab switch
		staleTime: 15 * 60 * 1000, // Consider lists fresh for 15 minutes (they rarely change)
		gcTime: 30 * 60 * 1000, // Keep in cache for 30 minutes
	});

	// Fetch war information - changes occasionally, moderate cache
	const { data: warInfo } = useQuery({
		...trpc.targetFinder.getWarInfo.queryOptions(),
		refetchOnWindowFocus: false, // Prevent auto-refetch on tab switch
		staleTime: 10 * 60 * 1000, // Consider war info fresh for 10 minutes
		gcTime: 20 * 60 * 1000, // Keep in cache for 20 minutes
	});

	// Fetch chain information - changes rapidly during active chains
	const { data: chainInfo } = useQuery({
		...trpc.targetFinder.getChainInfo.queryOptions(),
		refetchInterval: 15000, // Refresh every 15 seconds for active chain monitoring
		staleTime: 10 * 1000, // Consider stale after 10 seconds
		gcTime: 60 * 1000, // Keep in cache for 1 minute
	});

	// Extract enemy faction ID from war information
	const enemyFactionId = useMemo(() => {
		if (!warInfo?.wars?.ranked) return null;
		const ourFactionId = 53100;
		const enemyFaction = warInfo.wars.ranked.factions.find(
			(faction) => faction.id !== ourFactionId,
		);
		return enemyFaction ? String(enemyFaction.id) : null;
	}, [warInfo]);

	// Fetch enemy faction members when we have an enemy faction
	const {
		data: enemyMembersData,
		isLoading: enemyMembersLoading,
		refetch: refetchEnemyMembers,
		isFetching: enemyMembersIsFetching,
	} = useQuery({
		...trpc.targetFinder.getEnemyFactionMembers.queryOptions({
			factionId: enemyFactionId || "",
		}),
		enabled: !!enemyFactionId,
		refetchOnWindowFocus: false, // Don't auto-refetch - user controls when they want fresh data
		staleTime: 0, // Never use stale data - every query fetches fresh
		gcTime: 5 * 60 * 1000, // Keep in memory for 5 minutes for immediate access
		refetchInterval: false, // No background polling - rely on user actions + WebSocket
		retry: (failureCount, error) => {
			// Don't retry cooldown errors - respect server protection
			if (error?.message?.includes("Please wait before fetching again")) {
				return false;
			}
			// Single retry for network errors
			return failureCount < 1;
		},
	});

	// Determine if current list is an enemy faction list
	const isEnemyFactionList = selectedList.startsWith("Enemy Faction:");

	// Track successful enemy member fetches
	useEffect(() => {
		if (enemyMembersData && !enemyMembersLoading && isEnemyFactionList) {
			setLastRefreshTime(new Date());
		}
	}, [enemyMembersData, enemyMembersLoading, isEnemyFactionList]);

	// Fetch targets for selected list (regular lists)
	// FRESH-FIRST STRATEGY: No caching, always fresh data + WebSocket sharing
	const {
		data: targetsData,
		isLoading: targetsLoading,
		refetch: refetchTargets,
		error: targetsError,
		isFetching: targetsIsFetching,
	} = useQuery({
		...trpc.targetFinder.getTargets.queryOptions({
			listName: selectedList,
		}),
		enabled: selectedList !== "" && !isEnemyFactionList,
		refetchOnWindowFocus: false, // Don't auto-refetch - user controls when they want fresh data
		staleTime: 0, // Never use stale data - every query fetches fresh
		gcTime: 5 * 60 * 1000, // Keep in memory for 5 minutes for immediate access
		refetchInterval: false, // No background polling - rely on user actions + WebSocket
		retry: (failureCount, error) => {
			// Don't retry cooldown errors - respect server protection
			if (error?.message?.includes("Please wait before fetching again")) {
				return false;
			}
			// Single retry for network errors
			return failureCount < 1;
		},
	});

	// Track successful data fetches to update last refresh time
	useEffect(() => {
		if (targetsData && !targetsLoading && !targetsError) {
			setLastRefreshTime(new Date());
		}
	}, [targetsData, targetsLoading, targetsError]);

	// Use appropriate data source based on list type
	const targets = useMemo(() => {
		if (isEnemyFactionList) {
			return (enemyMembersData as TargetWithStatus[]) || [];
		}
		return (targetsData as TargetWithStatus[]) || [];
	}, [isEnemyFactionList, enemyMembersData, targetsData]);

	// Get cooldown info - changes every second, minimal cache
	const { data: cooldownData, refetch: refetchCooldown } = useQuery({
		...trpc.targetFinder.getTargetFinderCooldown.queryOptions(),
		refetchOnWindowFocus: false, // Prevent auto-refetch on tab switch
		staleTime: 5 * 1000, // Consider cooldown fresh for only 5 seconds
		gcTime: 30 * 1000, // Keep in cache for 30 seconds
		refetchInterval: 10 * 1000, // Refresh every 10 seconds for accurate cooldown
	});

	// Use custom hooks for chain status and target statuses
	const { chainRemaining } = useChainStatus(
		chainInfo as ChainInfo | null | undefined,
	);
	const { localStatuses, localCooldown } = useTargetStatuses(
		targets,
		cooldownData,
	);

	// Filtered and sorted targets
	const filteredTargets = useMemo(() => {
		let filtered = targets;

		// Apply search filter
		if (searchTerm) {
			filtered = filtered.filter(
				(target) =>
					target.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
					target.tornId.includes(searchTerm),
			);
		}

		// Apply status filter
		if (statusFilter !== "all") {
			filtered = filtered.filter((target) => {
				const status = localStatuses[target.tornId] ?? target.status;
				switch (statusFilter) {
					case "okay":
						return status === "Okay";
					case "hospitalized":
						return status.includes("Hospitalized");
					case "error":
						return status.includes("Error") || status === "Fetch Error";
					default:
						return true;
				}
			});
		}

		// Apply sorting
		filtered.sort((a, b) => {
			const aStatus = localStatuses[a.tornId] ?? a.status;
			const bStatus = localStatuses[b.tornId] ?? b.status;

			if (sortBy === "smart") {
				const aPriority = getStatusPriority(aStatus);
				const bPriority = getStatusPriority(bStatus);

				if (aPriority !== bPriority) {
					return aPriority - bPriority;
				}

				if (aPriority === 1) {
					return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
				}
				if (aPriority === 2) {
					const aTime = getHospitalTimeInSeconds(aStatus);
					const bTime = getHospitalTimeInSeconds(bStatus);
					if (aTime !== bTime) {
						return aTime - bTime;
					}
					return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
				}
				return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
			}

			let aValue: string | number;
			let bValue: string | number;

			switch (sortBy) {
				case "name":
					aValue = a.name.toLowerCase();
					bValue = b.name.toLowerCase();
					break;
				case "tornId":
					aValue = Number.parseInt(a.tornId);
					bValue = Number.parseInt(b.tornId);
					break;
				case "status":
					aValue = aStatus.toLowerCase();
					bValue = bStatus.toLowerCase();
					break;
				case "added":
					aValue = new Date(a.createdAt).getTime();
					bValue = new Date(b.createdAt).getTime();
					break;
				default:
					return 0;
			}

			if (aValue < bValue) return sortOrder === "asc" ? -1 : 1;
			if (aValue > bValue) return sortOrder === "asc" ? 1 : -1;
			return 0;
		});

		return filtered;
	}, [targets, searchTerm, statusFilter, sortBy, sortOrder, localStatuses]);

	// Group targets by status for filter buttons
	const targetStats: TargetStats = useMemo(() => {
		if (!targets.length) return { all: 0, okay: 0, hospitalized: 0, error: 0 };

		const stats = { all: targets.length, okay: 0, hospitalized: 0, error: 0 };
		stats.okay = targets.filter(
			(t) => (localStatuses[t.tornId] ?? t.status) === "Okay",
		).length;
		stats.hospitalized = targets.filter((t) =>
			(localStatuses[t.tornId] ?? t.status).includes("Hospitalized"),
		).length;
		stats.error = targets.filter((t) => {
			const status = localStatuses[t.tornId] ?? t.status;
			return status.includes("Error") || status === "Fetch Error";
		}).length;

		return stats;
	}, [targets, localStatuses]);

	// Remove target mutations
	const removeTargetFromCustomList = useMutation(
		trpc.targetFinder.removeTargetFromCustomList.mutationOptions({
			onSuccess: (_, variables) => {
				// Sync removal to LiveStore - notification will come via WebSocket
				const targetToRemove = targets.find(
					(t) => t.tornId === variables.tornId,
				);

				if (targetToRemove) {
					syncTargetRemoval({
						id: targetToRemove.id,
						listId: targetToRemove.listId,
						tornId: variables.tornId,
					});
				}
			},
			onError: (error) => {
				toast.error("Failed to remove target", {
					description: error.message,
				});
			},
		}),
	);

	const removeTargetFromList = useMutation(
		trpc.targetFinder.removeTargetFromList.mutationOptions({
			onSuccess: (_, variables) => {
				// Sync removal to LiveStore - notification will come via WebSocket
				const targetToRemove = targets.find(
					(t) => t.tornId === variables.tornId,
				);

				if (targetToRemove) {
					syncTargetRemoval({
						id: targetToRemove.id,
						listId: targetToRemove.listId,
						tornId: variables.tornId,
					});
				}
			},
			onError: (error) => {
				toast.error("Failed to remove target", {
					description: error.message,
				});
			},
		}),
	);

	const handleRefresh = useCallback(() => {
		if (selectedList !== "") {
			if (isEnemyFactionList) {
				refetchEnemyMembers();
			} else {
				refetchTargets();
			}
			refetchCooldown();
			toast.success("Refreshed target data");
		}
	}, [
		selectedList,
		isEnemyFactionList,
		refetchTargets,
		refetchEnemyMembers,
		refetchCooldown,
	]);

	// Force refresh function that bypasses cache completely for immediate fresh data
	const handleForceRefresh = useCallback(async () => {
		if (selectedList === "") return;

		try {
			// Force refetch regardless of staleness - this bypasses cache completely
			if (isEnemyFactionList) {
				await refetchEnemyMembers();
			} else {
				await refetchTargets();
			}
			toast.success("⚡ Fresh combat data loaded - all statuses current");
		} catch (error) {
			if (
				error instanceof Error &&
				error.message.includes("Please wait before fetching again")
			) {
				const remaining = error.message.match(/(\d+)/)?.[1];
				toast.error(
					`⏱️ Cooldown active - wait ${remaining || "30"}s before refreshing`,
				);
				// Note: Cache data remains available during cooldown period
			} else {
				toast.error("Failed to refresh targets");
			}
		}
	}, [selectedList, isEnemyFactionList, refetchEnemyMembers, refetchTargets]);

	// Determine if currently fetching/refreshing
	const isRefreshing = isEnemyFactionList
		? enemyMembersIsFetching
		: targetsIsFetching;

	const handleRemoveTarget = async (tornId: string) => {
		if (selectedList === "") return;

		if (selectedList === "Custom List") {
			await removeTargetFromCustomList.mutateAsync({ tornId });
		} else {
			await removeTargetFromList.mutateAsync({
				tornId,
				listName: selectedList,
			});
		}
	};

	const handleAddTarget = () => {
		// Don't auto-refresh to avoid disrupting timers
		// AddTargetDialog will handle showing success notification
		// Note: Target addition sync to LiveStore will be handled
		// when the tRPC mutation succeeds and the query data updates
	};

	const isRemoving =
		removeTargetFromCustomList.isPending || removeTargetFromList.isPending;

	return (
		<TooltipProvider>
			<div className="space-y-6">
				{/* Header Section */}
				<TargetHeader
					selectedList={selectedList}
					onAddTarget={handleAddTarget}
					onRefresh={handleForceRefresh}
					isRefreshing={isRefreshing}
					lastRefreshTime={lastRefreshTime}
				/>

				{/* Search and Filter Section */}
				<TargetFilters
					selectedList={selectedList}
					setSelectedList={setSelectedList}
					searchTerm={searchTerm}
					setSearchTerm={setSearchTerm}
					statusFilter={statusFilter}
					setStatusFilter={setStatusFilter}
					viewMode={viewMode}
					setViewMode={setViewMode}
					lists={lists}
					listsLoading={listsLoading}
					warInfo={warInfo}
					enemyFactionId={enemyFactionId}
					localCooldown={localCooldown}
					onRefresh={handleRefresh}
					isRefreshing={targetsLoading || enemyMembersLoading}
					targetStats={targetStats}
				/>

				{/* Targets List */}
				<div className="space-y-4">
					{/* Chain Status Card - Always visible regardless of list selection */}
					{chainInfo && chainRemaining !== null && chainRemaining > 0 && (
						<ChainStatusCard
							chainInfo={chainInfo as ChainInfo}
							chainRemaining={chainRemaining}
						/>
					)}

					{/* Target Content */}
					<TargetContent
						selectedList={selectedList}
						isLoading={targetsLoading || enemyMembersLoading}
						hasError={!!targetsError}
						errorMessage={targetsError?.message}
						filteredTargets={filteredTargets}
						allTargets={targets}
						searchTerm={searchTerm}
						statusFilter={statusFilter}
						viewMode={viewMode}
						localStatuses={localStatuses}
						onRemoveTarget={handleRemoveTarget}
						isRemoving={isRemoving}
						onRefresh={handleRefresh}
						onClearFilters={() => {
							setSearchTerm("");
							setStatusFilter("all");
						}}
					/>
				</div>
			</div>
		</TooltipProvider>
	);
}
