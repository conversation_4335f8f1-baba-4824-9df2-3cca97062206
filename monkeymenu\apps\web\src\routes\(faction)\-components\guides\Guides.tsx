import { useState } from "react";
import { CreateGuideDialog } from "./CreateGuideDialog";
import { GuideFilters } from "./GuideFilters";
import { GuideHeader } from "./GuideHeader";
import { GuideList } from "./GuideList";
import { GuideViewer } from "./GuideViewer";
import { useGuideFilters, useGuideMutations, useGuides } from "./hooks";
import type { GuideFilters as FilterType, ViewMode } from "./types";

interface GuideData {
	guide: {
		id: number;
		title: string;
		content: string;
		category: string;
		createdAt: string;
		updatedAt: string;
	};
	author?: {
		name: string;
	};
}

export function Guides() {
	const [showCreateForm, setShowCreateForm] = useState(false);
	const [editingGuide, setEditingGuide] = useState<GuideData | null>(null);
	const [selectedGuide, setSelectedGuide] = useState<GuideData | null>(null);
	const [viewMode, setViewMode] = useState<ViewMode>("grid");
	const [filters, setFilters] = useState<FilterType>({
		searchQuery: "",
		selectedCategory: "all",
	});

	// Fetch guides and mutations
	const guides = useGuides();
	const { createMutation, updateMutation, deleteMutation } =
		useGuideMutations();

	// Filter guides
	const { filteredGuides, guideStats } = useGuideFilters(guides.data, filters);

	// Handle form submission
	const handleSubmit = (data: {
		title: string;
		content: string;
		category: string;
	}) => {
		if (editingGuide) {
			updateMutation.mutate(
				{
					id: editingGuide.guide.id,
					...data,
				},
				{
					onSuccess: () => {
						setShowCreateForm(false);
						setEditingGuide(null);
					},
				},
			);
		} else {
			createMutation.mutate(data, {
				onSuccess: () => {
					setShowCreateForm(false);
				},
			});
		}
	};

	// Handle editing
	const handleEdit = (guide: GuideData) => {
		setEditingGuide(guide);
		setShowCreateForm(true);
	};

	// Handle delete
	const handleDelete = (id: number) => {
		deleteMutation.mutate({ id });
	};

	// Handle dialog close
	const handleDialogClose = () => {
		setShowCreateForm(false);
		setEditingGuide(null);
	};

	return (
		<div className="space-y-6">
			<GuideHeader onCreateClick={() => setShowCreateForm(true)} />

			<GuideFilters
				filters={filters}
				onFiltersChange={setFilters}
				viewMode={viewMode}
				onViewModeChange={setViewMode}
				guideStats={guideStats}
			/>

			<CreateGuideDialog
				isOpen={showCreateForm}
				onClose={handleDialogClose}
				editingGuide={editingGuide}
				onSubmit={handleSubmit}
				isSubmitting={createMutation.isPending || updateMutation.isPending}
			/>

			<GuideViewer
				guide={selectedGuide}
				onClose={() => setSelectedGuide(null)}
			/>

			<GuideList
				guides={filteredGuides}
				viewMode={viewMode}
				isLoading={guides.isLoading}
				error={guides.error}
				searchQuery={filters.searchQuery}
				selectedCategory={filters.selectedCategory}
				onView={setSelectedGuide}
				onEdit={handleEdit}
				onDelete={handleDelete}
				onCreateFirst={() => setShowCreateForm(true)}
			/>
		</div>
	);
}
