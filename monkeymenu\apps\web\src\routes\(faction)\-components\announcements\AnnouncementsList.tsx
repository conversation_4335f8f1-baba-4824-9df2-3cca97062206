import { HasPermission } from "@/components/permissions/PermissionGuards";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { PERMISSION_NAMES } from "@monkeymenu/shared";
import { Megaphone, Plus } from "lucide-react";
import { AnnouncementCard } from "./AnnouncementCard";
import type { AnnouncementData, ViewMode } from "./types";

interface AnnouncementsListProps {
	announcements: AnnouncementData[];
	viewMode: ViewMode;
	isLoading: boolean;
	error: { message: string } | null;
	searchQuery: string;
	onView: (announcement: AnnouncementData) => void;
	onEdit: (announcement: AnnouncementData) => void;
	onDelete: (announcement: AnnouncementData) => void;
	onCreateNew: () => void;
}

export function AnnouncementsList({
	announcements,
	viewMode,
	isLoading,
	error,
	searchQuery,
	onView,
	onEdit,
	onDelete,
	onCreateNew,
}: AnnouncementsListProps) {
	if (isLoading) {
		return (
			<div className="flex items-center justify-center py-12">
				<div className="text-center">
					<div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-primary border-b-2" />
					<p className="text-muted-foreground">Loading announcements...</p>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<Card>
				<CardContent className="py-12 text-center">
					<p className="mb-2 text-red-500">Error loading announcements</p>
					<p className="text-muted-foreground text-sm">{error.message}</p>
				</CardContent>
			</Card>
		);
	}

	if (announcements.length === 0) {
		return (
			<Card>
				<CardContent className="py-12 text-center">
					<Megaphone className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
					<h3 className="mb-2 font-semibold text-lg">
						{searchQuery ? "No announcements found" : "No announcements yet"}
					</h3>
					<p className="mb-4 text-muted-foreground">
						{searchQuery
							? "Try adjusting your search criteria"
							: "Be the first to create an announcement for the faction"}
					</p>
					{!searchQuery && (
						<HasPermission permission={PERMISSION_NAMES.ANNOUNCEMENTS_MANAGE}>
							<Button onClick={onCreateNew} className="gap-2">
								<Plus className="h-4 w-4" />
								Create First Announcement
							</Button>
						</HasPermission>
					)}
				</CardContent>
			</Card>
		);
	}

	return (
		<div
			className={
				viewMode === "grid"
					? "grid gap-6 sm:grid-cols-2 lg:grid-cols-3"
					: "space-y-4"
			}
		>
			{announcements.map((announcement) => (
				<AnnouncementCard
					key={announcement.announcement.id}
					announcement={announcement}
					viewMode={viewMode}
					onView={onView}
					onEdit={onEdit}
					onDelete={onDelete}
				/>
			))}
		</div>
	);
}
