import { useStore } from "@livestore/react";
import { useEffect, useState } from "react";

/**
 * Inspects the internal `syncState` to see if the client finished the
 * initial pull+merge. We consider the store "ready" when
 *   1. `pending` is empty (no un-acknowledged local events) **and**
 *   2. `localHead` equals `upstreamHead` (we're caught up with the server).
 *
 * The implementation is defensive – if the private internals change we
 * simply return `false` and keep polling.
 */
function isStoreSynced(store: unknown): boolean {
	try {
		// biome-ignore lint/suspicious/noExplicitAny: LiveStore internals lack public type definitions
		const sp: any = (store as any)?.syncProcessor;
		if (!sp?.syncState) return false;

		// Handle different LiveStore version patterns for accessing sync state
		// biome-ignore lint/suspicious/noExplicitAny: LiveStore internals lack public type definitions
		let state: any;

		// Try different access patterns in order of preference
		if (typeof sp.syncState.get === "function") {
			// For newer versions that return an Effect, try to get the raw result
			const result = sp.syncState.get();
			// If it's a simple value, use it directly. If it has runSync, skip Effect for now
			if (result && typeof result === "object" && !result.runSync) {
				state = result;
			} else {
				// Fall back to direct access
				state = sp.syncState;
			}
		} else if (typeof sp.syncState === "function") {
			// Try calling as function for older versions
			const result = sp.syncState();
			state = result && typeof result === "object" ? result : sp.syncState;
		} else {
			// Direct object access for oldest versions
			state = sp.syncState;
		}

		if (!state) return false;

		// biome-ignore lint/suspicious/noExplicitAny: LiveStore internals lack public type definitions
		const pending: any[] = Array.isArray(state.pending) ? state.pending : [];
		const upHead = state.upstreamHead;
		const locHead = state.localHead;

		// If we haven't received the upstream head yet treat as not synced.
		if (!upHead || !locHead) return false;

		const headsEqual =
			upHead.global === locHead.global && upHead.client === locHead.client;

		return pending.length === 0 && headsEqual;
	} catch {
		return false;
	}
}

export function useLiveStoreReady(): boolean {
	const { store } = useStore();
	const [ready, setReady] = useState(() => isStoreSynced(store));

	useEffect(() => {
		if (ready) return;
		const id = setInterval(() => {
			if (isStoreSynced(store)) {
				setReady(true);
				clearInterval(id);
			}
		}, 300);
		return () => clearInterval(id);
	}, [ready, store]);

	return ready;
}
