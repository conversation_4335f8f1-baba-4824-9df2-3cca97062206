import { PERMISSIONS } from "@monkeymenu/shared";
import { and, eq } from "drizzle-orm";
import {
	type CommandContext,
	SlashCommand,
	type SlashCreator,
} from "slash-create/web";
import { initDb } from "../../../db";
import { user as userTable } from "../../../db/schema/auth";
import { account as accountTable } from "../../../db/schema/auth";
import { tornUser } from "../../../db/schema/torn";
import { decrypt, initCrypto } from "../../../lib/crypto";
import {
	checkDiscordUserPermission,
	createAccountNotLinkedEmbed,
	createPermissionErrorEmbed,
} from "../../../lib/discord-permissions";
import { createTornAPIWithSuspension } from "../../../lib/tornApi";
import type { AppBindings } from "../../../lib/types";

export default class BalanceCommand extends SlashCommand {
	constructor(creator: <PERSON><PERSON><PERSON><PERSON>) {
		super(creator, {
			name: "balance",
			description: "Check the current faction bank balance",
		});
	}

	async run(ctx: CommandContext) {
		const discordUserId = ctx.user.id;
		const discordUserTag = `${ctx.user.username}#${ctx.user.discriminator}`;

		// Initial processing embed
		const processingEmbed = {
			color: 0xffa500, // Orange
			title: "🔄 Checking Balance",
			description: "Please wait while we fetch the current faction balance...",
			fields: [
				{
					name: "👤 Requested by",
					value: discordUserTag,
					inline: true,
				},
				{
					name: "⏰ Status",
					value: "🔄 Processing...",
					inline: true,
				},
			],
			footer: {
				text: "MonkeyMenu Banking System",
			},
			timestamp: new Date().toISOString(),
		};

		// Send initial response immediately
		await ctx.send({
			embeds: [processingEmbed],
			ephemeral: true,
		});

		try {
			// Get environment from creator options
			const env = (this.creator.options as { env?: AppBindings["Bindings"] })
				.env;
			if (!env) {
				throw new Error("Environment not available in creator options");
			}
			const db = initDb(env);

			// Check permissions first - this includes account linking check
			const permissionCheck = await checkDiscordUserPermission(
				db,
				discordUserId,
				PERMISSIONS.BANKING_VIEW.name,
			);

			if (!permissionCheck.isLinked) {
				return await ctx.editOriginal({
					embeds: [createAccountNotLinkedEmbed(discordUserTag)],
				});
			}

			if (!permissionCheck.hasPermission) {
				return await ctx.editOriginal({
					embeds: [
						createPermissionErrorEmbed(
							discordUserTag,
							PERMISSIONS.BANKING_VIEW.name,
							"Recruit or higher",
						),
					],
				});
			}

			// Get user's torn API key to fetch balance
			const userAccount = await db
				.select({
					userId: userTable.id,
					tornApiKey: tornUser.tornApiKey,
					tornApiKeyVerified: tornUser.tornApiKeyVerified,
				})
				.from(accountTable)
				.innerJoin(userTable, eq(accountTable.userId, userTable.id))
				.leftJoin(tornUser, eq(userTable.id, tornUser.id))
				.where(
					and(
						eq(accountTable.providerId, "discord"),
						eq(accountTable.accountId, discordUserId),
					),
				)
				.get();

			if (!userAccount?.tornApiKey || !userAccount.tornApiKeyVerified) {
				const apiKeyEmbed = {
					color: 0xff8c00, // Dark orange
					title: "⚠️ API Key Required",
					description:
						"A verified Torn API key is required to check faction balance.",
					fields: [
						{
							name: "⚠️ Status",
							value: "API Key Not Verified",
							inline: true,
						},
						{
							name: "📋 Next Steps",
							value:
								"Visit [MonkeyMenu Settings](https://monkeymenu.app/settings) to verify your API key",
							inline: false,
						},
					],
					footer: {
						text: "MonkeyMenu Banking System",
					},
					timestamp: new Date().toISOString(),
				};

				return await ctx.editOriginal({
					embeds: [apiKeyEmbed],
				});
			}

			// Initialize crypto and decrypt API key
			initCrypto(env.TORN_API_KEY_ENCRYPTION_SECRET);
			let apiKey: string;
			try {
				apiKey = decrypt(userAccount.tornApiKey);
			} catch (error) {
				throw new Error("Failed to decrypt API key");
			}

			// Fetch faction balance using TornAPI utility
			const tornApi = createTornAPIWithSuspension(
				apiKey,
				db,
				userAccount.userId,
				env,
			);
			const balance = await tornApi.getFactionBalance();

			// Success embed
			const successEmbed = {
				color: 0x00ff00, // Green
				title: "💰 Faction Bank Balance",
				description: "Current balance information for our faction bank:",
				fields: [
					{
						name: "💵 Money Balance",
						value: `$${balance.money.toLocaleString()}`,
						inline: true,
					},
					{
						name: "🏆 Points Balance",
						value: balance.points.toLocaleString(),
						inline: true,
					},
					{
						name: "👤 Requested by",
						value: discordUserTag,
						inline: true,
					},
					{
						name: "📊 Additional Info",
						value:
							"Visit [MonkeyMenu Banking Dashboard](https://monkeymenu.app/dashboard/banking) for more details",
						inline: false,
					},
				],
				footer: {
					text: "MonkeyMenu Banking System • Data from Torn API",
				},
				timestamp: new Date().toISOString(),
			};

			return await ctx.editOriginal({
				embeds: [successEmbed],
			});
		} catch (error) {
			console.error("Error in balance command:", error);

			const errorEmbed = {
				color: 0xff0000, // Red
				title: "❌ Balance Check Failed",
				description: "An unexpected error occurred while checking the balance.",
				fields: [
					{
						name: "👤 Requested by",
						value: discordUserTag,
						inline: true,
					},
					{
						name: "❌ Error",
						value: error instanceof Error ? error.message : "Unknown error",
						inline: false,
					},
					{
						name: "💡 Possible Solutions",
						value:
							"• Check if your API key is still valid\n" +
							"• Ensure you have faction access permissions\n" +
							"• Try again in a few moments",
						inline: false,
					},
				],
				footer: {
					text: "MonkeyMenu Banking System",
				},
				timestamp: new Date().toISOString(),
			};

			return await ctx.editOriginal({
				embeds: [errorEmbed],
			});
		}
	}
}
