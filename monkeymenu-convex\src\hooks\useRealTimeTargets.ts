import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useAction } from 'convex/react';
import { api } from '../../convex/_generated/api';

interface Target {
  tornId: number;
  username: string;
  status: 'okay' | 'hospitalized' | 'error' | 'unknown';
  statusText: string;
  hospitalCountdown?: number;
  lastUpdated: number;
  addedAt: number;
  addedBy: string;
}

interface UseRealTimeTargetsProps {
  listId?: string;
  refreshInterval?: number;
}

export function useRealTimeTargets({ listId, refreshInterval = 30000 }: UseRealTimeTargetsProps = {}) {
  const [targets, setTargets] = useState<Target[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null);
  const [targetCountdowns, setTargetCountdowns] = useState<Map<number, number>>(new Map());

  // Convex queries for real-time updates
  const targetsList = useQuery(
    api.targets.getTargets,
    listId ? { listId } : "skip"
  );

  // Real-time status updates action
  const getTargetStatuses = useAction(api.targets.getTargetStatuses);
  const updateTargetStatus = useMutation(api.targets.updateTargetStatus);

  // Real-time countdown for hospitalized targets
  useEffect(() => {
    const interval = setInterval(() => {
      setTargetCountdowns(currentCountdowns => {
        const newCountdowns = new Map(currentCountdowns);
        let hasChanges = false;
        
        for (const [tornId, countdown] of currentCountdowns) {
          if (countdown > 0) {
            const newCountdown = Math.max(0, countdown - 1);
            newCountdowns.set(tornId, newCountdown);
            hasChanges = true;
            
            // Auto-transition to "okay" when countdown reaches 0
            if (newCountdown === 0 && countdown > 0) {
              try {
                updateTargetStatus({
                  tornId,
                  status: 'okay',
                  statusText: 'Okay',
                  hospitalCountdown: 0
                });
              } catch (error) {
                console.error('Failed to update target status:', error);
              }
            }
          }
        }
        
        return hasChanges ? newCountdowns : currentCountdowns;
      });
    }, 1000); // Update every second

    return () => clearInterval(interval);
  }, [updateTargetStatus]);
  
  // Function to extract countdown from status text
  const extractCountdownFromStatus = useCallback((status: string): number => {
    // Match patterns like "Hospitalized (5m 32s)" or "Hospital (2m 15s)"
    const match = status.match(/(\d+)m\s*(\d+)s/);
    if (match) {
      const minutes = parseInt(match[1], 10);
      const seconds = parseInt(match[2], 10);
      return minutes * 60 + seconds;
    }
    return 0;
  }, []);
  
  // Initialize countdowns when targets change
  useEffect(() => {
    if (targets.length > 0) {
      setTargetCountdowns(currentCountdowns => {
        const newCountdowns = new Map(currentCountdowns);
        targets.forEach(target => {
          // Check if target has explicit countdown
          if (target.hospitalCountdown && target.hospitalCountdown > 0) {
            newCountdowns.set(target.tornId, target.hospitalCountdown);
          } else if (target.statusText?.includes('Hospitalized') || target.status?.includes('Hospitalized')) {
            // Extract countdown from status text
            const statusText = target.statusText || target.status || '';
            const countdown = extractCountdownFromStatus(statusText);
            if (countdown > 0) {
              newCountdowns.set(target.tornId, countdown);
            }
          }
        });
        return newCountdowns;
      });
    }
  }, [targets, extractCountdownFromStatus]);

  // Sync with Convex real-time data
  useEffect(() => {
    if (targetsList) {
      setTargets(targetsList.map(target => ({
        ...target,
        hospitalCountdown: target.hospitalCountdown || 0
      })));
    }
  }, [targetsList]);

  // Refresh target statuses from Torn API
  const refreshStatuses = useCallback(async (force: boolean = false) => {
    if (!listId || loading) return;

    setLoading(true);
    setError(null);

    try {
      const updatedTargets = await getTargetStatuses({ 
        listId,
        forceRefresh: force 
      });
      
      if (updatedTargets) {
        setLastRefreshTime(new Date());
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh target statuses');
    } finally {
      setLoading(false);
    }
  }, [listId, loading, getTargetStatuses]);

  // Auto-refresh on interval
  useEffect(() => {
    if (!listId || refreshInterval <= 0) return;

    const interval = setInterval(() => {
      refreshStatuses(false);
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [listId, refreshInterval, refreshStatuses]);

  // Initial load
  useEffect(() => {
    if (listId) {
      refreshStatuses(false);
    }
  }, [listId, refreshStatuses]);

  // Function to update target statuses with real-time data
  const updateTargetStatuses = useCallback((targetList: any[]) => {
    if (!targetList || targetList.length === 0) return targetList;
    
    return targetList.map(target => {
      // Initialize countdown for new hospitalized targets
      if ((target.status?.includes('Hospitalized') || target.status?.includes('Hospital')) && !targetCountdowns.has(target.tornId)) {
        const countdown = extractCountdownFromStatus(target.status);
        if (countdown > 0) {
          setTargetCountdowns(prev => new Map(prev).set(target.tornId, countdown));
        }
      }
      
      const countdown = targetCountdowns.get(target.tornId);
      
      // If target is hospitalized and we have a countdown
      if ((target.status?.includes('Hospitalized') || target.status?.includes('Hospital')) && countdown !== undefined) {
        if (countdown <= 0) {
          // Target is ready
          return {
            ...target,
            status: 'Ready!'
          };
        } else {
          // Update with live countdown
          return {
            ...target,
            status: `Hospitalized (${formatTime(countdown)})`
          };
        }
      }
      
      return target;
    });
  }, [targetCountdowns, extractCountdownFromStatus]);
  
  // Function to get countdown for a specific target
  const getTargetCountdown = useCallback((target: any) => {
    const countdown = targetCountdowns.get(target.tornId);
    if (countdown && countdown > 0) {
      return formatTime(countdown);
    }
    return null;
  }, [targetCountdowns]);
  
  // Function to check if target is ready
  const isTargetReady = useCallback((target: any) => {
    if (!target.status?.includes('Hospitalized') && !target.status?.includes('Hospital')) return false;
    const countdown = targetCountdowns.get(target.tornId);
    return countdown !== undefined && countdown <= 0;
  }, [targetCountdowns]);

  return {
    targets,
    loading,
    error,
    lastRefreshTime,
    refreshStatuses,
    updateTargetStatuses,
    getTargetCountdown,
    isTargetReady,
    targetCountdowns,
  };
}

// Helper function to format time in MM:SS format
function formatTime(seconds: number): string {
  if (seconds <= 0) return '0:00';
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}