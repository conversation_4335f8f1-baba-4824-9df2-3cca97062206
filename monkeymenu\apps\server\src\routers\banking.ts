import { PERMISSIONS } from "@monkeymenu/shared";
import { and, asc, count, desc, eq, gte, inArray } from "drizzle-orm";
import { z } from "zod";
import type { DBInstance } from "../db/index";
import { user } from "../db/schema/auth";
import { withdrawalRequest } from "../db/schema/banking";
import { tornUser } from "../db/schema/torn";
import { decrypt, initCrypto } from "../lib/crypto";
import { DiscordNotificationService } from "../lib/discord-notification-service";
import { pushLiveStoreEvents } from "../lib/livestore-events";
import { createTornAPIWithSuspension } from "../lib/tornApi";
import {
	factionPermissionProcedure,
	protectedProcedure,
	requirePermission,
	router,
} from "../lib/trpc";
import { WebSocketService } from "../lib/websocket-service";

// Constants for withdrawal handling
export const WITHDRAWAL_RATE_LIMIT_MINUTES = 1;
export const MAX_WITHDRAWALS_PER_USER = 50;
export const FACTION_POSITIONS_WITH_APPROVAL_PERMS = [
	"Leader",
	"Co-leader",
	"Monkey Mentor",
	"Gorilla",
	"Primate Liaison",
	"Baboon",
];

/**
 * Handles rate limiting for withdrawal requests and cleans up old requests.
 */
export async function handleWithdrawalRateLimitingAndCleanup(
	db: DBInstance,
	userId: string,
): Promise<void> {
	// 1. Rate Limiting
	const recentWithdrawalResult = await db
		.select({ id: withdrawalRequest.id })
		.from(withdrawalRequest)
		.where(
			and(
				eq(withdrawalRequest.requestedById, userId),
				gte(
					withdrawalRequest.createdAt,
					new Date(Date.now() - WITHDRAWAL_RATE_LIMIT_MINUTES * 60 * 1000),
				),
			),
		)
		.limit(1);

	if (recentWithdrawalResult.length > 0) {
		throw new Error(
			`You have made a withdrawal request too recently. Please wait ${WITHDRAWAL_RATE_LIMIT_MINUTES} minute(s).`,
		);
	}

	// 2. Clean up old requests if current count is at or exceeds max
	const countResult = await db
		.select({ value: count() })
		.from(withdrawalRequest)
		.where(eq(withdrawalRequest.requestedById, userId));

	const userWithdrawalsCount = countResult[0]?.value ?? 0;

	if (userWithdrawalsCount >= MAX_WITHDRAWALS_PER_USER) {
		const numToDelete = userWithdrawalsCount - MAX_WITHDRAWALS_PER_USER + 1;
		const oldestWithdrawals = await db
			.select({ id: withdrawalRequest.id })
			.from(withdrawalRequest)
			.where(eq(withdrawalRequest.requestedById, userId))
			.orderBy(asc(withdrawalRequest.createdAt))
			.limit(numToDelete);

		if (oldestWithdrawals.length > 0) {
			await db.delete(withdrawalRequest).where(
				inArray(
					withdrawalRequest.id,
					oldestWithdrawals.map((w: { id: string }) => w.id),
				),
			);
		}
	}
}

const factionBalanceSchema = z.object({
	factionBalance: z.object({
		money: z.number(),
		points: z.number(),
	}),
});

export const bankingRouter = router({
	getFactionBalance: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.BANKING_VIEW.name))
		.output(factionBalanceSchema)
		.query(async ({ ctx }) => {
			const userId = ctx.session.userId;

			// Get user's torn API key
			const userTornData = await ctx.db
				.select({
					tornApiKey: tornUser.tornApiKey,
					verified: tornUser.tornApiKeyVerified,
				})
				.from(tornUser)
				.where(eq(tornUser.id, userId))
				.get();

			if (!userTornData?.tornApiKey || !userTornData.verified) {
				throw new Error(
					"User API key is not set or not verified. Please verify your API key in settings.",
				);
			}

			// Initialize crypto to decrypt API key
			initCrypto(ctx.env.TORN_API_KEY_ENCRYPTION_SECRET);

			// Decrypt the Torn API key
			let apiKey: string;
			try {
				apiKey = decrypt(userTornData.tornApiKey);
			} catch (error) {
				throw new Error("Failed to decrypt API key");
			}

			try {
				const tornApi = createTornAPIWithSuspension(
					apiKey,
					ctx.db,
					userId,
					ctx.env,
				);
				const balance = await tornApi.getFactionBalance();
				return { factionBalance: balance };
			} catch (error) {
				console.error("Failed to fetch faction balance:", error);
				throw new Error(
					`Failed to fetch faction balance: ${error instanceof Error ? error.message : "Unknown error"}`,
				);
			}
		}),

	createWithdrawalRequest: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.BANKING_REQUEST.name))
		.input(
			z.object({
				amount: z.number().min(1).max(9_999_999_999_999),
			}),
		)
		.mutation(async ({ ctx, input }) => {
			const userId = ctx.session.userId;

			// Handle rate limiting and cleanup
			await handleWithdrawalRateLimitingAndCleanup(ctx.db, userId);

			// Get user name and torn ID for broadcasting and Discord notifications
			const userData = await ctx.db
				.select({
					name: user.name,
					tornUserId: tornUser.tornUserId,
				})
				.from(user)
				.leftJoin(tornUser, eq(user.id, tornUser.id))
				.where(eq(user.id, userId))
				.get();

			// Create withdrawal request
			const now = new Date();
			const withdrawalId = `wr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

			const newWithdrawal = await ctx.db
				.insert(withdrawalRequest)
				.values({
					id: withdrawalId,
					amount: input.amount,
					status: "PENDING",
					createdAt: now,
					updatedAt: now,
					requestedById: userId,
				})
				.returning()
				.get();

			// LiveStore event for withdrawal created
			try {
				await pushLiveStoreEvents(ctx.env, [
					{
						name: "v1.WithdrawalCreated",
						data: {
							id: newWithdrawal.id,
							amount: newWithdrawal.amount,
							requestedById: newWithdrawal.requestedById,
							requestedByName: userData?.name || "Unknown User",
							requestedByTornId: userData?.tornUserId
								? Number(userData.tornUserId)
								: undefined,
							createdAt: newWithdrawal.createdAt,
						},
					},
				]);
			} catch (error) {
				console.error("Failed to push LiveStore withdrawal created:", error);
			}

			// Legacy WebSocket broadcast (to be removed later)
			try {
				const wsService = new WebSocketService();
				await wsService.broadcastWithdrawalCreated({
					id: newWithdrawal.id,
					amount: newWithdrawal.amount,
					requestedByName: userData?.name || "Unknown User",
				});
			} catch (error) {
				console.error("Failed to broadcast withdrawal creation:", error);
				// Don't fail the request if WebSocket broadcast fails
			}

			// Send Discord notification to banking channel
			try {
				const discordService = new DiscordNotificationService(ctx.env);
				const discordResult =
					await discordService.sendBankingRequestNotification({
						userName: userData?.name || "Unknown User",
						amount: newWithdrawal.amount,
						requestId: newWithdrawal.id,
						requestingUserId: userId,
						requesterTornId: userData?.tornUserId?.toString(),
					});

				// If Discord notification was successful, save the message ID for future updates
				if (discordResult.success && discordResult.discordMessageId) {
					await ctx.db
						.update(withdrawalRequest)
						.set({ discordMessageId: discordResult.discordMessageId })
						.where(eq(withdrawalRequest.id, newWithdrawal.id));

					console.log(
						`Successfully saved Discord message ID ${discordResult.discordMessageId} for withdrawal ${newWithdrawal.id}`,
					);
				} else if (!discordResult.success) {
					console.warn(
						`Discord notification failed for withdrawal ${newWithdrawal.id}: ${discordResult.error}`,
					);
				}
			} catch (error) {
				console.error("Failed to send Discord notification:", error);
				// Don't fail the request if Discord notification fails
			}

			return newWithdrawal;
		}),

	getMyWithdrawals: protectedProcedure.query(async ({ ctx }) => {
		const userId = ctx.session.userId;

		const withdrawals = await ctx.db
			.select({
				id: withdrawalRequest.id,
				amount: withdrawalRequest.amount,
				status: withdrawalRequest.status,
				createdAt: withdrawalRequest.createdAt,
				updatedAt: withdrawalRequest.updatedAt,
				processedAt: withdrawalRequest.processedAt,
				transactionId: withdrawalRequest.transactionId,
				requestedById: withdrawalRequest.requestedById,
				processedById: withdrawalRequest.processedById,
			})
			.from(withdrawalRequest)
			.where(eq(withdrawalRequest.requestedById, userId))
			.orderBy(desc(withdrawalRequest.createdAt))
			.limit(50);

		return withdrawals;
	}),

	getAllWithdrawals: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.BANKING_MANAGE_REQUESTS.name))
		.input(
			z.object({
				status: z.string().optional(),
			}),
		)
		.query(async ({ ctx, input }) => {
			const baseQuery = ctx.db
				.select({
					id: withdrawalRequest.id,
					amount: withdrawalRequest.amount,
					status: withdrawalRequest.status,
					createdAt: withdrawalRequest.createdAt,
					updatedAt: withdrawalRequest.updatedAt,
					processedAt: withdrawalRequest.processedAt,
					transactionId: withdrawalRequest.transactionId,
					requestedById: withdrawalRequest.requestedById,
					processedById: withdrawalRequest.processedById,
					requestedByName: user.name,
					requestedByTornId: tornUser.tornUserId,
				})
				.from(withdrawalRequest)
				.leftJoin(user, eq(withdrawalRequest.requestedById, user.id))
				.leftJoin(tornUser, eq(withdrawalRequest.requestedById, tornUser.id));

			const requests = input.status
				? await baseQuery
						.where(eq(withdrawalRequest.status, input.status))
						.orderBy(desc(withdrawalRequest.createdAt))
						.limit(100)
				: await baseQuery.orderBy(desc(withdrawalRequest.createdAt)).limit(100);

			return { requests };
		}),

	getBankingManagementPermissions: factionPermissionProcedure.query(
		async ({ ctx }) => {
			// Check if user has banking management permissions using the permission context
			const canApprove =
				ctx.permissions?.permissions.includes(
					PERMISSIONS.BANKING_MANAGE_REQUESTS.name,
				) ?? false;

			const canViewAll =
				ctx.permissions?.permissions.includes(
					PERMISSIONS.BANKING_MANAGE_REQUESTS.name,
				) ?? false;

			return {
				canManageWithdrawals: canApprove,
				isAdmin: canApprove,
				canViewAll: canViewAll,
				canApprove: canApprove,
			};
		},
	),

	updateWithdrawalStatus: factionPermissionProcedure
		.input(
			z.object({
				withdrawalId: z.string(),
				status: z.enum(["ACCEPTED", "DECLINED"]),
			}),
		)
		.use(requirePermission(PERMISSIONS.BANKING_MANAGE_REQUESTS.name))
		.mutation(async ({ ctx, input }) => {
			const userId = ctx.session.userId;
			const { withdrawalId, status } = input;

			// Get the current withdrawal request with user info and Discord message ID
			const currentWithdrawal = await ctx.db
				.select({
					id: withdrawalRequest.id,
					amount: withdrawalRequest.amount,
					status: withdrawalRequest.status,
					discordMessageId: withdrawalRequest.discordMessageId,
					requestedById: withdrawalRequest.requestedById,
					requestedByName: user.name,
					requestedByTornId: tornUser.tornUserId,
				})
				.from(withdrawalRequest)
				.leftJoin(user, eq(withdrawalRequest.requestedById, user.id))
				.leftJoin(tornUser, eq(withdrawalRequest.requestedById, tornUser.id))
				.where(eq(withdrawalRequest.id, withdrawalId))
				.get();

			if (!currentWithdrawal) {
				throw new Error("Withdrawal request not found");
			}

			if (currentWithdrawal.status !== "PENDING") {
				throw new Error(
					`Withdrawal request is already ${currentWithdrawal.status}`,
				);
			}

			// Update the withdrawal status
			const now = new Date();
			const updatedWithdrawal = await ctx.db
				.update(withdrawalRequest)
				.set({
					status,
					updatedAt: now,
					processedAt: now,
					processedById: userId,
				})
				.where(eq(withdrawalRequest.id, withdrawalId))
				.returning()
				.get();

			// LiveStore event for withdrawal status updated
			try {
				await pushLiveStoreEvents(ctx.env, [
					{
						name: "v1.WithdrawalStatusUpdated",
						data: {
							id: updatedWithdrawal.id,
							status: updatedWithdrawal.status,
							processedById: updatedWithdrawal.processedById || undefined,
							processedByName: currentWithdrawal.requestedByName || "Admin",
							processedAt: updatedWithdrawal.processedAt || undefined,
							transactionId: updatedWithdrawal.transactionId || undefined,
						},
					},
				]);
			} catch (error) {
				console.error("Failed to push LiveStore withdrawal update:", error);
			}

			// Update Discord embed if there's a Discord message ID
			if (currentWithdrawal.discordMessageId) {
				try {
					// Get the user who processed this withdrawal
					const processingUser = await ctx.db
						.select({ name: user.name })
						.from(user)
						.where(eq(user.id, userId))
						.get();

					const discordService = new DiscordNotificationService(ctx.env);
					const discordResult = await discordService.updateWithdrawalEmbed({
						discordMessageId: currentWithdrawal.discordMessageId,
						newStatus: status,
						requestId: updatedWithdrawal.id,
						amount: updatedWithdrawal.amount,
						processedBy: processingUser?.name || "Unknown Admin",
						requesterTornId: currentWithdrawal.requestedByTornId?.toString(),
					});

					if (!discordResult.success) {
						console.warn(
							`Failed to update Discord embed for withdrawal ${updatedWithdrawal.id}: ${discordResult.error}`,
						);
					}
				} catch (error) {
					console.error("Failed to update Discord embed:", error);
					// Don't fail the request if Discord update fails
				}
			}

			// Send DM notification to the user
			try {
				// Get the user who processed this withdrawal
				const processingUser = await ctx.db
					.select({ name: user.name })
					.from(user)
					.where(eq(user.id, userId))
					.get();

				const discordService = new DiscordNotificationService(ctx.env);
				const dmResult = await discordService.sendWithdrawalDMNotification({
					userId: currentWithdrawal.requestedById,
					withdrawalId: updatedWithdrawal.id,
					amount: updatedWithdrawal.amount,
					status: status,
					processedBy: processingUser?.name || "Unknown Admin",
					userName: currentWithdrawal.requestedByName || "Unknown User",
				});

				if (!dmResult.success) {
					console.warn(
						`Failed to send DM notification for withdrawal ${updatedWithdrawal.id}: ${dmResult.error}`,
					);
				} else {
					console.log(
						`Successfully sent DM notification to user ${currentWithdrawal.requestedById} for withdrawal ${updatedWithdrawal.id}`,
					);
				}
			} catch (error) {
				console.error("Failed to send DM notification:", error);
				// Don't fail the request if DM notification fails
			}

			// Legacy WebSocket broadcast (to be removed later)
			try {
				const wsService = new WebSocketService();
				await wsService.broadcastWithdrawalUpdated({
					id: updatedWithdrawal.id,
					amount: updatedWithdrawal.amount,
					status: updatedWithdrawal.status,
					requestedByName: currentWithdrawal.requestedByName || "Unknown User",
				});
			} catch (error) {
				console.error("Failed to broadcast withdrawal update:", error);
			}

			return {
				...updatedWithdrawal,
				requestedByTornId: currentWithdrawal.requestedByTornId,
				requestedByName: currentWithdrawal.requestedByName,
			};
		}),
});
