import { createFileRoute } from '@tanstack/react-router';
import { ProtectedRoute } from '../components/auth/ProtectedRoute';
import { PermissionGuard } from '../components/auth/PermissionGuard';
import { BankingDashboard } from '../components/banking/BankingDashboard';
import { PERMISSION_NAMES } from '../lib/permissions';

export const Route = createFileRoute('/banking')({
  component: BankingPage,
});

function BankingPage() {
  return (
    <ProtectedRoute>
      <PermissionGuard permission={PERMISSION_NAMES.BANKING_VIEW}>
        <BankingDashboard />
      </PermissionGuard>
    </ProtectedRoute>
  );
}