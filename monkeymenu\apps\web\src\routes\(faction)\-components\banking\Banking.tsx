import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useBankingLiveStoreSync } from "@/lib/banking-livestore-bridge";
import { trpc } from "@/lib/trpc-client";
import { useStore } from "@livestore/react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { toast } from "sonner";
import {
	factionBalance$,
	myWithdrawals$,
	pendingWithdrawals$,
} from "../../../../livestore/queries";
import type {
	FactionBalance,
	WithdrawalRequest,
} from "../../../../livestore/types";
import { AdminTab } from "./AdminTab";
import { DashboardTab } from "./DashboardTab";
import { HistoryTab } from "./HistoryTab";
import { StatisticsCards } from "./StatisticsCards";
import { WithdrawalDetailsModal } from "./WithdrawalDetailsModal";
import type { WithdrawalWithUser } from "./types";
import { calculateWithdrawalStats } from "./utils";

export function Banking() {
	console.log("🏦 Banking component rendering");

	const queryClient = useQueryClient();
	const { store } = useStore();

	// Set up LiveStore sync (this will sync tRPC data to LiveStore automatically)
	const { syncWithdrawalStatusUpdate, permissions: liveStorePermissions } =
		useBankingLiveStoreSync();

	// Track if this is the initial mount to prevent URL update conflicts
	const isInitialMount = useRef(true);

	// UI State
	const [activeTab, setActiveTab] = useState(() => {
		console.log("🏦 Initializing activeTab state");
		const params = new URLSearchParams(window.location.search);
		const tabParam = params.get("tab");
		return tabParam ? tabParam : "dashboard";
	});
	const [selectedWithdrawal, setSelectedWithdrawal] =
		useState<WithdrawalWithUser | null>(null);
	const [liveStoreError, setLiveStoreError] = useState<string | null>(null);

	console.log("🏦 About to start data queries");

	// Get permissions (fallback to tRPC if LiveStore hasn't synced yet)
	const { data: tRPCPermissions } = useQuery({
		...trpc.banking.getBankingManagementPermissions.queryOptions(),
	});
	const permissions = liveStorePermissions || tRPCPermissions;

	// Get real-time data from LiveStore
	let liveStoreBalance: FactionBalance | null = null;
	let liveStoreMyWithdrawals: WithdrawalRequest[] = [];
	let liveStorePendingWithdrawals: WithdrawalRequest[] = [];

	try {
		const balanceResults = store.useQuery(factionBalance$);
		liveStoreBalance =
			Array.isArray(balanceResults) && balanceResults.length > 0
				? (balanceResults[0] as FactionBalance)
				: null;

		const myWithdrawalsResults = store.useQuery(myWithdrawals$);
		liveStoreMyWithdrawals = Array.isArray(myWithdrawalsResults)
			? (myWithdrawalsResults as WithdrawalRequest[])
			: [];

		const pendingWithdrawalsResults = store.useQuery(pendingWithdrawals$);
		liveStorePendingWithdrawals = Array.isArray(pendingWithdrawalsResults)
			? (pendingWithdrawalsResults as WithdrawalRequest[])
			: [];

		// Clear any previous error state on successful query
		if (liveStoreError) {
			setLiveStoreError(null);
		}
	} catch (error) {
		console.error("❌ Error in LiveStore queries:", error);
		const errorMessage =
			error instanceof Error ? error.message : "Unknown error occurred";
		const fullErrorMessage = `LiveStore connection issue: ${errorMessage}. Falling back to server data.`;

		// Set error state for UI feedback
		setLiveStoreError(fullErrorMessage);

		// Show user-facing notification
		toast.warning("Real-time data temporarily unavailable", {
			description:
				"Using cached data instead. Real-time updates may be delayed.",
			duration: 5000,
		});
	}

	// Fallback to tRPC data if LiveStore is not available
	const { data: tRPCBalance, isLoading: isLoadingBalance } = useQuery({
		...trpc.banking.getFactionBalance.queryOptions(),
		retry: false,
		staleTime: 0,
		refetchOnWindowFocus: true,
	});

	const { data: tRPCAllWithdrawals, isLoading: isLoadingAllWithdrawals } =
		useQuery({
			...trpc.banking.getAllWithdrawals.queryOptions({ status: "PENDING" }),
			enabled: permissions?.canManageWithdrawals ?? false,
		});

	const { data: tRPCMyWithdrawals, isLoading: isLoadingMyWithdrawals } =
		useQuery({
			...trpc.banking.getMyWithdrawals.queryOptions(),
		});

	console.log("🏦 Permissions query result:", permissions);
	console.log("🏦 Balance query result:", {
		liveStoreBalance,
		tRPCBalance,
		isLoadingBalance,
	});
	console.log("🏦 All withdrawals query result:", {
		tRPCAllWithdrawals,
		isLoadingAllWithdrawals,
	});
	console.log("🏦 My withdrawals query result:", {
		liveStoreMyWithdrawals,
		tRPCMyWithdrawals,
		isLoadingMyWithdrawals,
	});

	// Use LiveStore data when available, fallback to tRPC
	const balance = liveStoreBalance
		? {
				factionBalance: {
					money: liveStoreBalance.money,
					points: liveStoreBalance.points,
				},
			}
		: tRPCBalance;

	const myWithdrawals =
		liveStoreMyWithdrawals.length > 0
			? liveStoreMyWithdrawals
			: tRPCMyWithdrawals;
	const allWithdrawals = tRPCAllWithdrawals; // Keep using tRPC for admin data for now

	const isAdmin = permissions?.canManageWithdrawals ?? false;

	console.log("🏦 Admin status:", isAdmin);

	// Transformed data for the current user's withdrawals
	const myHistoryData = useMemo(() => {
		return (myWithdrawals || []).map((request) => ({
			withdrawal: {
				id: request.id,
				amount: request.amount,
				status: request.status,
				createdAt:
					typeof request.createdAt === "string"
						? request.createdAt
						: request.createdAt.toISOString(),
				updatedAt:
					typeof request.updatedAt === "string"
						? request.updatedAt
						: request.updatedAt.toISOString(),
				processedAt: request.processedAt
					? typeof request.processedAt === "string"
						? request.processedAt
						: request.processedAt.toISOString()
					: null,
				transactionId: request.transactionId ?? null,
				requestedById: request.requestedById,
				processedById: request.processedById ?? null,
			},
			user: {
				// For 'my' history, user is 'You'
				tornUsername: "You",
				tornUserId: "", // User's torn ID could be added if needed
			},
		}));
	}, [myWithdrawals]);

	// Data for Admin Panel: All pending withdrawals from any user
	// Note: Not using useMemo here because LiveStore data is reactive and changes frequently
	// LiveStore will automatically trigger re-renders when data changes
	const getPendingAdminWithdrawals = () => {
		// Use LiveStore data if available, otherwise fallback to tRPC
		const pendingSource =
			liveStorePendingWithdrawals.length > 0
				? liveStorePendingWithdrawals.map((w) => ({
						id: w.id,
						amount: w.amount,
						status: w.status,
						createdAt:
							typeof w.createdAt === "string"
								? w.createdAt
								: w.createdAt.toISOString(),
						updatedAt:
							typeof w.updatedAt === "string"
								? w.updatedAt
								: w.updatedAt.toISOString(),
						processedAt: w.processedAt
							? typeof w.processedAt === "string"
								? w.processedAt
								: w.processedAt.toISOString()
							: null,
						transactionId: w.transactionId ?? null,
						requestedById: w.requestedById,
						processedById: w.processedById ?? null,
						requestedByName: w.requestedByName,
						requestedByTornId: w.requestedByTornId,
					}))
				: allWithdrawals?.requests || [];

		if (isAdmin) {
			return pendingSource
				.filter((request) => request.status === "PENDING")
				.map((request) => ({
					withdrawal: {
						id: request.id,
						amount: request.amount,
						status: request.status,
						createdAt: request.createdAt,
						updatedAt: request.updatedAt,
						processedAt: request.processedAt,
						transactionId: request.transactionId ?? null,
						requestedById: request.requestedById,
						processedById: request.processedById ?? null,
					},
					user: {
						tornUsername: request.requestedByName || "Unknown User",
						tornUserId: request.requestedByTornId?.toString() || "",
					},
				}));
		}
		return [];
	};

	const pendingAdminWithdrawals = getPendingAdminWithdrawals();

	// Statistics (calculated from user's own withdrawals)
	const withdrawalStats = useMemo(() => {
		return calculateWithdrawalStats(myHistoryData);
	}, [myHistoryData]);

	// Enhanced mutations that sync with LiveStore
	const updateWithdrawalStatus = useMutation(
		trpc.banking.updateWithdrawalStatus.mutationOptions({
			onSuccess: (data, variables) => {
				const action =
					variables.status === "ACCEPTED" ? "approved" : "declined";
				toast.success(`Withdrawal request ${action} successfully!`);

				// Sync the status update to LiveStore immediately
				syncWithdrawalStatusUpdate({
					id: variables.withdrawalId,
					status: variables.status,
					processedAt: new Date(),
					transactionId:
						variables.status === "ACCEPTED"
							? `tx_${crypto.randomUUID()}`
							: undefined,
				});

				if (
					variables.status === "ACCEPTED" &&
					data.requestedByTornId &&
					data.amount
				) {
					const tornUrl = `https://www.torn.com/factions.php?step=your#/tab=controls&option=give-to-user&giveMoneyTo=${data.requestedByTornId}&money=${data.amount}`;

					// Always redirect the current window for consistency
					setTimeout(() => {
						window.location.href = tornUrl;
					}, 1500);
				}

				// Invalidate queries
				queryClient.invalidateQueries({
					predicate: (query) => {
						const queryKey = query.queryKey;
						return (
							Array.isArray(queryKey) &&
							Array.isArray(queryKey[0]) &&
							queryKey[0][0] === "banking"
						);
					},
				});
			},
			onError: (error) => {
				toast.error(`Failed to process withdrawal: ${error.message}`);
			},
		}),
	);

	// Use ref to avoid adding mutate as dependency (which would cause re-renders)
	const mutateFunctionRef = useRef(updateWithdrawalStatus.mutate);
	mutateFunctionRef.current = updateWithdrawalStatus.mutate;

	// Event handlers
	const handleApproval = (
		withdrawalId: string,
		status: "ACCEPTED" | "DECLINED",
	) => {
		updateWithdrawalStatus.mutate({ withdrawalId, status });
	};

	const handleFormSubmitSuccess = () => {
		// Could switch to history tab or trigger refresh
		// setActiveTab("history");
	};

	// Stabilize the mutation function with useCallback
	const processUrlAction = useCallback(
		(action: string, requestId: string) => {
			if (action === "approve" && permissions?.canManageWithdrawals) {
				mutateFunctionRef.current({
					withdrawalId: requestId,
					status: "ACCEPTED",
				});
			} else if (action === "reject" && permissions?.canManageWithdrawals) {
				mutateFunctionRef.current({
					withdrawalId: requestId,
					status: "DECLINED",
				});
			}
			// Clean up URL parameters after processing
			window.history.replaceState({}, "", "/banking");
		},
		[permissions?.canManageWithdrawals], // Only depend on permissions
	);

	// URL parameter handling - fixed to prevent infinite re-renders
	useEffect(() => {
		const urlParams = new URLSearchParams(window.location.search);
		const action = urlParams.get("action");
		const requestId = urlParams.get("requestId");

		if (action && requestId && permissions?.canManageWithdrawals) {
			processUrlAction(action, requestId);
		}
	}, [permissions?.canManageWithdrawals, processUrlAction]);

	// after state initialization, add effect to update URL param when activeTab changes
	useEffect(() => {
		// Skip URL update on initial mount to prevent conflicts with state initialization
		if (isInitialMount.current) {
			isInitialMount.current = false;
			return;
		}

		const params = new URLSearchParams(window.location.search);
		params.set("tab", activeTab);
		const newUrl = `${window.location.pathname}?${params.toString()}`;
		window.history.replaceState({}, "", newUrl);
	}, [activeTab]);

	console.log("🏦 About to render Banking component");

	return (
		<div className="space-y-6">
			{/* Header Section */}
			<div>
				<h1 className="font-bold text-3xl text-foreground">
					💰 Faction Banking
				</h1>
				<p className="text-muted-foreground">
					Manage faction withdrawals and view your transaction history
				</p>
			</div>

			{/* LiveStore Error Alert */}
			{liveStoreError && (
				<Alert variant="destructive">
					<AlertDescription>⚠️ {liveStoreError}</AlertDescription>
				</Alert>
			)}

			{/* Statistics Cards */}
			<StatisticsCards
				balance={balance}
				isLoadingBalance={isLoadingBalance}
				stats={withdrawalStats}
			/>

			{/* Main Tabs Interface */}
			<Tabs
				value={activeTab}
				onValueChange={setActiveTab}
				className="space-y-6"
			>
				<TabsList className="grid w-full grid-cols-3">
					<TabsTrigger value="dashboard">Dashboard</TabsTrigger>
					<TabsTrigger value="history">History</TabsTrigger>
					{isAdmin && <TabsTrigger value="admin">Admin Panel</TabsTrigger>}
				</TabsList>

				{/* Dashboard Tab */}
				<TabsContent value="dashboard" className="space-y-6">
					<DashboardTab
						balance={balance}
						isLoadingBalance={isLoadingBalance}
						onSubmitSuccess={handleFormSubmitSuccess}
					/>
				</TabsContent>

				{/* History Tab */}
				<TabsContent value="history" className="space-y-6">
					<HistoryTab
						withdrawals={myHistoryData}
						isLoading={isLoadingMyWithdrawals}
						onWithdrawalSelect={setSelectedWithdrawal}
					/>
				</TabsContent>

				{/* Admin Panel Tab */}
				{isAdmin && (
					<TabsContent value="admin" className="space-y-6">
						<AdminTab
							withdrawals={pendingAdminWithdrawals}
							isLoading={isLoadingAllWithdrawals}
							onWithdrawalSelect={setSelectedWithdrawal}
							onApprove={(id) => handleApproval(id, "ACCEPTED")}
							onDecline={(id) => handleApproval(id, "DECLINED")}
							isProcessing={updateWithdrawalStatus.isPending}
						/>
					</TabsContent>
				)}
			</Tabs>

			{/* Withdrawal Detail Modal */}
			<WithdrawalDetailsModal
				withdrawal={selectedWithdrawal}
				isOpen={!!selectedWithdrawal}
				onClose={() => setSelectedWithdrawal(null)}
			/>
		</div>
	);
}
