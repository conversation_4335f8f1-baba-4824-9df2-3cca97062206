// Torn API related constants and types

// Torn API Base Configuration
export const TORN_API = {
	BASE_URL: "https://api.torn.com/v2",
	V1_BASE_URL: "https://api.torn.com", // For legacy endpoints that still require v1
	TIMEOUT_MS: 10000,
	MAX_RETRIES: 3,
	RETRY_DELAY_MS: 500,
} as const;

// Torn API Error Codes
export enum TORN_API_ERRORS {
	UNKNOWN_ERROR = 0,
	KEY_EMPTY = 1,
	INCORRECT_KEY = 2,
	WRONG_TYPE = 3,
	WRONG_FIELDS = 4,
	TOO_MANY_REQUESTS = 5,
	INCORRECT_ID = 6,
	INCORRECT_ID_ENTITY_RELATION = 7,
	IP_BLOCKED = 8,
	API_DISABLED = 9,
	KEY_OWNER_IN_FEDERAL_JAIL = 10,
	KEY_CHANGE_ERROR = 11,
	KEY_READ_ERROR = 12,
	KEY_DISABLED_INACTIVITY = 13,
	DAILY_READ_LIMIT_REACHED = 14,
	TEMPORARY_ERROR = 15,
	ACCESS_LEVEL_INSUFFICIENT = 16,
	BACKEND_ERROR = 17,
	KEY_PAUSED_BY_OWNER = 18,
	MUST_MIGRATE_CRIMES = 19,
	RACE_NOT_FINISHED = 20,
	INCORRECT_CATEGORY = 21,
	SELECTION_API_V1_ONLY = 22,
	SELECTION_API_V2_ONLY = 23,
	CLOSED_TEMPORARILY = 24,
}

// Torn Position to Role Mapping
export const TORN_POSITION_MAPPING = {
	Leader: "LEADER",
	"Co-leader": "CO_LEADER",
	"Monkey Mentor": "MONKEY_MENTOR",
	Gorilla: "GORILLA",
	"Primate Liaison": "PRIMATE_LIAISON",
	Baboon: "BABOON",
	Orangutan: "ORANGUTAN",
	Chimpanzee: "CHIMPANZEE",
	Recruit: "RECRUIT",
	// Handle common variations/fallbacks
	Member: "RECRUIT",
	"New Recruit": "RECRUIT",
} as const;

// Torn API Response Types
export interface TornAPIError {
	code: number;
	error: string;
}

export interface TornAPIResponse {
	player_id?: number;
	faction?: {
		faction_id?: number;
		position?: string;
	};
	error?: TornAPIError;
}

export interface TornPlayerProfile {
	player_id: number;
	name: string;
	faction?: {
		faction_id: number;
		position: string;
		faction_name: string;
	};
}

// Utility Functions
export function getTornPositionRole(
	tornPosition: string | null | undefined,
): string {
	if (!tornPosition) return "RECRUIT";

	const position =
		TORN_POSITION_MAPPING[tornPosition as keyof typeof TORN_POSITION_MAPPING];
	return position || "RECRUIT";
}

export function isTornAPIError(
	response: TornAPIResponse,
): response is TornAPIResponse & { error: TornAPIError } {
	return !!response.error;
}

// Overload for exhaustiveness checking with enum values
export function getTornAPIErrorMessage(code: TORN_API_ERRORS): string;
// Overload for legacy number usage
export function getTornAPIErrorMessage(code: number): string;
// Implementation
export function getTornAPIErrorMessage(code: TORN_API_ERRORS | number): string {
	switch (code) {
		case TORN_API_ERRORS.UNKNOWN_ERROR:
			return "Unknown error";
		case TORN_API_ERRORS.KEY_EMPTY:
			return "API key is empty";
		case TORN_API_ERRORS.INCORRECT_KEY:
			return "Incorrect API key";
		case TORN_API_ERRORS.WRONG_TYPE:
			return "Wrong request type";
		case TORN_API_ERRORS.WRONG_FIELDS:
			return "Wrong selection fields";
		case TORN_API_ERRORS.TOO_MANY_REQUESTS:
			return "Too many requests (max 100 per minute)";
		case TORN_API_ERRORS.INCORRECT_ID:
			return "Incorrect ID value";
		case TORN_API_ERRORS.INCORRECT_ID_ENTITY_RELATION:
			return "Requesting private data of another user/faction";
		case TORN_API_ERRORS.IP_BLOCKED:
			return "IP address is temporarily banned due to abuse";
		case TORN_API_ERRORS.API_DISABLED:
			return "API system is currently disabled";
		case TORN_API_ERRORS.KEY_OWNER_IN_FEDERAL_JAIL:
			return "API key owner is in federal jail";
		case TORN_API_ERRORS.KEY_CHANGE_ERROR:
			return "You can only change your API key once every 60 seconds";
		case TORN_API_ERRORS.KEY_READ_ERROR:
			return "Error reading key from database";
		case TORN_API_ERRORS.KEY_DISABLED_INACTIVITY:
			return "API key is disabled due to owner inactivity (7+ days)";
		case TORN_API_ERRORS.DAILY_READ_LIMIT_REACHED:
			return "Daily read limit reached";
		case TORN_API_ERRORS.TEMPORARY_ERROR:
			return "Temporary error";
		case TORN_API_ERRORS.ACCESS_LEVEL_INSUFFICIENT:
			return "Access level of this key is not high enough";
		case TORN_API_ERRORS.BACKEND_ERROR:
			return "Backend error occurred, please try again";
		case TORN_API_ERRORS.KEY_PAUSED_BY_OWNER:
			return "API key has been paused by the owner";
		case TORN_API_ERRORS.MUST_MIGRATE_CRIMES:
			return "Must be migrated to crimes 2.0";
		case TORN_API_ERRORS.RACE_NOT_FINISHED:
			return "Race not yet finished";
		case TORN_API_ERRORS.INCORRECT_CATEGORY:
			return "Incorrect category";
		case TORN_API_ERRORS.SELECTION_API_V1_ONLY:
			return "This selection is only available in API v1";
		case TORN_API_ERRORS.SELECTION_API_V2_ONLY:
			return "This selection is only available in API v2";
		case TORN_API_ERRORS.CLOSED_TEMPORARILY:
			return "Closed temporarily";
		default:
			// For unknown enum values or numbers, return a generic error message
			return "Unknown API error";
	}
}

// Add Torn chain list & chain report types for faction chains feature
export interface TornChainInfo {
	id: number;
	chain: number;
	respect: number;
	start: number; // unix timestamp (seconds)
	end: number; // unix timestamp (seconds)
}

export interface TornChainDetails {
	chain: number;
	respect: number;
	members: number;
	targets: number;
	war: number;
	best: number;
	leave: number;
	mug: number;
	hospitalize: number;
	assists: number;
	retaliations: number;
	overseas: number;
	draws: number;
	escapes: number;
	losses: number;
}

export interface TornChainBonus {
	attacker_id: number;
	defender_id: number;
	chain: number;
	respect: number;
}

export interface TornChainAttackerAttacksBreakdown {
	total: number;
	leave: number;
	mug: number;
	hospitalize: number;
	assists: number;
	retaliations: number;
	overseas: number;
	draws: number;
	escapes: number;
	losses: number;
	war: number;
	bonuses: number;
}

export interface TornChainAttacker {
	id: number;
	respect: {
		total: number;
		average: number;
		best: number;
	};
	attacks: TornChainAttackerAttacksBreakdown;
}

export interface TornChainReport {
	id: number;
	faction_id: number;
	start: number;
	end: number;
	details: TornChainDetails;
	bonuses: TornChainBonus[];
	attackers: TornChainAttacker[];
	non_attackers: number[];
}

export interface TornAttackParticipantFaction {
	id: number;
	name: string;
}

export interface TornAttackParticipant {
	id: number;
	name: string;
	level: number;
	faction: TornAttackParticipantFaction | null;
}

export interface TornAttack {
	id: number;
	code: string;
	started: number;
	ended: number;
	attacker: TornAttackParticipant | null;
	defender: TornAttackParticipant;
	result: string;
	respect_gain: number;
	respect_loss: number;
	chain: number;
	is_interrupted: boolean;
	is_stealthed: boolean;
	is_raid: boolean;
	is_ranked_war: boolean;
	modifiers: Record<string, number>;
	finishing_hit_effects: unknown[];
}
