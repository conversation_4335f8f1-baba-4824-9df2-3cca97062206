---
description: 
globs: 
alwaysApply: true
---
# MonkeyMenu Turbo Cloud - Project Overview

This is a full-stack monorepo project called "MonkeyMenu" built with modern technologies for a scalable cloud-based application.

## Architecture Overview

The project follows a monorepo structure with:
- **Server**: Cloudflare Workers-based API ([apps/server](mdc:apps/server))
- **Web**: React frontend application ([apps/web](mdc:apps/web))  
- **Shared**: Common utilities and types ([packages/shared](mdc:packages/shared))

## Key Technologies

### Backend (Server)
- **Hono**: Lightweight web framework for Cloudflare Workers
- **tRPC**: End-to-end typesafe APIs
- **Drizzle ORM**: SQL ORM with Cloudflare D1 database
- **better-auth**: Authentication system
- **Discord Integration**: Bot commands and interactions

### Frontend (Web)
- **React 19**: Latest React with modern patterns
- **TanStack Router**: File-based routing
- **TanStack Query**: Server state management
- **Tailwind CSS**: Utility-first styling
- **Radix UI**: Accessible component primitives
- **Vite**: Build tool and dev server

### Development Tools
- **Turbo**: Monorepo build system
- **Biome**: Linting and formatting
- **TypeScript**: Type safety across the stack
- **pnpm**: Package manager

## Project Structure

```
monkeymenu/
├── apps/
│   ├── server/          # Cloudflare Workers API
│   └── web/             # React frontend
├── packages/
│   └── shared/          # Shared utilities
├── package.json         # Root workspace config
├── turbo.json          # Turbo monorepo config
└── pnpm-workspace.yaml # pnpm workspace config
```

## Development Workflow

- Use `pnpm dev` to start all services
- Use `pnpm dev:server` or `pnpm dev:web` for individual apps
- Database operations are handled through Drizzle ORM
- Environment variables are managed via Doppler
- Code quality is enforced through Biome and pre-commit hooks
