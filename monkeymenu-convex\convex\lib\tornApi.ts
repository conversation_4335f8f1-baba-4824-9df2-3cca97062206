// Torn API integration for Convex
// Based on the original implementation but adapted for Convex environment

const MY_FACTION_ID = 53100; // Menacing Monkeys

export interface TornAttack {
  id: number;
  attacker?: {
    id: number;
    name: string;
    faction?: {
      id: number;
      name: string;
    };
  };
  defender?: {
    id: number;
    name: string;
    faction?: {
      id: number;
      name: string;
    };
  };
  result: string;
  started: number;
  chain?: number;
  modifiers?: {
    fair_fight?: number;
  };
}

export interface TornChainInfo {
  id: number;
  start: number;
  end: number;
}

export interface TornChainReport {
  id: number;
  start: number;
  end: number;
  details: {
    chain: number;
    respect: number;
    war: number;
  };
  attackers?: Array<{
    id: number;
    name?: string;
    attacks: {
      total: number;
      war: number;
      assists: number;
    };
    respect: {
      total: number;
      average: number;
      best: number;
    };
  }>;
}

export interface TornRankedWar {
  id: number;
  start: number;
  end?: number;
  winner?: number;
  forfeit: boolean;
  factions: Array<{
    id: number;
    name: string;
    score: number;
    attacks: number;
    members: Array<{
      id: number;
      name: string;
      level: number;
      score: number;
      attacks: number;
    }>;
  }>;
}

export class TornAPIError extends Error {
  constructor(
    message: string,
    public code?: number,
    public originalError?: any
  ) {
    super(message);
    this.name = 'TornAPIError';
  }
}

export class TornAPI {
  private apiKey: string;
  private baseUrl = 'https://api.torn.com';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  private async makeRequest<T>(endpoint: string, params: Record<string, any> = {}): Promise<T> {
    const url = new URL(`${this.baseUrl}/${endpoint}`);
    url.searchParams.set('key', this.apiKey);
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.set(key, String(value));
      }
    });

    try {
      const response = await fetch(url.toString());
      
      if (!response.ok) {
        throw new TornAPIError(
          `API request failed: ${response.status} ${response.statusText}`,
          response.status
        );
      }

      const data = await response.json();
      
      if (data.error) {
        throw new TornAPIError(
          `Torn API Error: ${data.error.error}`,
          data.error.code
        );
      }

      return data;
    } catch (error) {
      if (error instanceof TornAPIError) {
        throw error;
      }
      
      throw new TornAPIError(
        `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        undefined,
        error
      );
    }
  }

  async getFactionRankedWars(factionId: number): Promise<TornRankedWar[]> {
    const data = await this.makeRequest<{ rankedwars: Record<string, any> }>(
      `faction/${factionId}`,
      { selections: 'rankedwars' }
    );
    
    // Transform the API response to match our interface
    const wars: TornRankedWar[] = Object.entries(data.rankedwars || {}).map(([warId, warData]) => {
      // Extract factions array from the nested structure
      const factions = Object.entries(warData.factions || {}).map(([factionId, factionData]: [string, any]) => ({
        id: parseInt(factionId),
        name: factionData.name,
        score: factionData.score,
        attacks: factionData.score, // Use score as attacks for now
        members: [] // Empty for now, would need separate API call
      }));
      
      return {
        id: parseInt(warId),
        start: warData.war?.start || 0,
        end: warData.war?.end,
        winner: warData.war?.winner,
        forfeit: false, // Not in API response
        factions: factions
      };
    });
    
    console.log('Transformed wars:', wars.map(w => ({ id: w.id, start: w.start, end: w.end })));
    
    return wars;
  }

  async getRankedWarReport(warId: number): Promise<TornRankedWar> {
    // Get war from faction's ranked wars list (like original implementation)
    try {
      const rankedWars = await this.getFactionRankedWars(MY_FACTION_ID);
      console.log(`Available wars from API:`, rankedWars.map(w => ({ id: w.id, start: w.start })));
      
      const warReport = rankedWars.find(war => war.id === warId);
      
      if (!warReport) {
        console.log(`War ${warId} not found. Available war IDs: ${rankedWars.map(w => w.id).join(', ')}`);
        
        // Try alternative approach for historical wars
        try {
          console.log(`Attempting direct war access for war ${warId}...`);
          const directData = await this.makeRequest<{ rankedwarreport: TornRankedWar }>(
            `faction/${warId}/rankedwarreport`
          );
          console.log(`Direct access successful for war ${warId}`);
          return directData.rankedwarreport;
        } catch (directError) {
          console.log(`Direct access failed for war ${warId}:`, directError);
          throw new TornAPIError(
            `War ${warId} not found in faction's ranked wars (${rankedWars.length} wars available) and direct access failed`, 
            404
          );
        }
      }
      
      return warReport;
    } catch (error) {
      if (error instanceof TornAPIError) {
        throw error;
      }
      throw new TornAPIError(
        `Failed to fetch war report for war ${warId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  }

  async getFactionChains(
    limit: number = 100,
    sort: 'ASC' | 'DESC' = 'DESC',
    to?: number,
    from?: number
  ): Promise<TornChainInfo[]> {
    // Try the v2 API endpoint first (from original implementation analysis)
    const baseUrlV2 = 'https://api.torn.com/v2';
    let endpoint = `/faction/chains`;
    const params = new URLSearchParams();
    params.set('limit', limit.toString());
    params.set('sort', sort);
    
    if (to) params.set('to', to.toString());
    if (from) params.set('from', from.toString());
    
    const url = new URL(`${baseUrlV2}${endpoint}`);
    url.searchParams.set('key', this.apiKey);
    
    // Add our custom params
    for (const [key, value] of params) {
      url.searchParams.set(key, value);
    }

    console.log('Chains API URL (v2):', url.toString());

    try {
      const response = await fetch(url.toString());
      
      if (!response.ok) {
        throw new TornAPIError(
          `API request failed: ${response.status} ${response.statusText}`,
          response.status
        );
      }

      const data = await response.json();
      
      console.log('Raw chains API response (v2):', JSON.stringify(data, null, 2));
      
      if (data.error) {
        throw new TornAPIError(
          `Torn API Error: ${data.error.error}`,
          data.error.code
        );
      }

      // Based on original implementation, the response should be an array of chains
      const chains: TornChainInfo[] = Array.isArray(data.chains) 
        ? data.chains.map((chainData: any) => ({
            id: chainData.id,
            start: chainData.start,
            end: chainData.end
          }))
        : [];
      
      console.log('Transformed chains (v2):', chains.map(c => ({ id: c.id, start: c.start, end: c.end })));
      
      return chains;
    } catch (error) {
      console.log('v2 API failed, falling back to v1 with selections...');
      
      // Fallback to v1 API with selections parameter
      try {
        const params: Record<string, any> = {
          selections: 'chains',
          limit,
          sort
        };
        
        if (to) params.to = to;
        if (from) params.from = from;

        const data = await this.makeRequest<{ chains: Record<string, any> }>(
          `faction/${MY_FACTION_ID}`,
          params
        );
        
        console.log('Raw chains API response (v1 fallback):', JSON.stringify(data, null, 2));
        
        // Transform the v1 API response (object with chain IDs as keys)
        const chains: TornChainInfo[] = Object.entries(data.chains || {}).map(([chainId, chainData]) => ({
          id: parseInt(chainId),
          start: chainData.start,
          end: chainData.end
        }));
        
        console.log('Transformed chains (v1):', chains.map(c => ({ id: c.id, start: c.start, end: c.end })));
        
        return chains;
      } catch (fallbackError) {
        if (fallbackError instanceof TornAPIError) {
          throw fallbackError;
        }
        
        throw new TornAPIError(
          `Both v2 and v1 API calls failed: ${fallbackError instanceof Error ? fallbackError.message : 'Unknown error'}`,
          undefined,
          fallbackError
        );
      }
    }
  }

  async getAllChainsInTimeRange(warStart: number, warEnd: number): Promise<TornChainInfo[]> {
    console.log(`Fetching chains for timeframe: ${warStart} to ${warEnd}`);
    
    const allChains: TornChainInfo[] = [];
    let cursor: number | undefined = warEnd + 300; // 5 minute buffer
    
    while (true) {
      const batch = await this.getFactionChains(100, "DESC", cursor, warStart);
      console.log(`Chain batch received:`, batch.map(c => ({ id: c.id, start: c.start, end: c.end })));
      
      if (batch.length === 0) break;
      
      // Filter chains that overlap with war timeframe and have valid IDs
      const relevantChains = batch.filter(
        chain => chain.id && chain.start && chain.end && 
                 chain.start <= warEnd && chain.end >= warStart
      );
      
      console.log(`Relevant chains after filtering:`, relevantChains.map(c => ({ id: c.id, start: c.start, end: c.end })));
      
      allChains.push(...relevantChains);
      
      if (batch.length < 100) break;
      
      const oldest = batch[batch.length - 1];
      if (!oldest.start || oldest.start <= warStart) break;
      
      cursor = oldest.start - 1;
    }
    
    console.log(`Total chains found in timeframe:`, allChains.length);
    return allChains;
  }

  async getChainReport(chainId: number): Promise<TornChainReport> {
    const maxRetries = 3;
    const baseDelay = 500; // 500ms base delay like original app
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Fetching chain report for chain ${chainId} (attempt ${attempt}/${maxRetries})...`);
        
        // Try v2 API first (based on original implementation)
        const baseUrlV2 = 'https://api.torn.com/v2';
        const url = new URL(`${baseUrlV2}/faction/${chainId}/chainreport`);
        url.searchParams.set('key', this.apiKey);
        
        console.log('Chain report API URL (v2):', url.toString());
        
        try {
          const response = await fetch(url.toString());
          
          if (!response.ok) {
            throw new TornAPIError(
              `API request failed: ${response.status} ${response.statusText}`,
              response.status
            );
          }

          const data = await response.json();
          
          console.log(`Chain report response (v2) for ${chainId}:`, data);
          
          if (data.error) {
            // Check for rate limiting error
            if (data.error.code === 5) { // Too many requests
              if (attempt < maxRetries) {
                const delay = baseDelay * attempt; // Exponential backoff
                console.log(`Rate limited, waiting ${delay}ms before retry...`);
                await new Promise(resolve => setTimeout(resolve, delay));
                continue; // Retry
              }
            }
            
            throw new TornAPIError(
              `Torn API Error: ${data.error.error}`,
              data.error.code
            );
          }

          return data;
        } catch (error) {
          if (attempt < maxRetries && error instanceof TornAPIError && error.code === 5) {
            const delay = baseDelay * attempt;
            console.log(`v2 rate limited, waiting ${delay}ms before trying v1 fallback...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
          
          console.log(`v2 chain report failed for ${chainId}, trying v1 fallback...`);
          
          // Fallback to v1 API
          const data = await this.makeRequest<{ chainreport: TornChainReport }>(
            `faction/${MY_FACTION_ID}`,
            { selections: 'chainreport', id: chainId }
          );
          
          console.log(`Chain report response (v1 fallback) for ${chainId}:`, data);
          return data.chainreport;
        }
      } catch (error) {
        if (attempt < maxRetries && error instanceof TornAPIError && error.code === 5) {
          const delay = baseDelay * attempt; // Exponential backoff
          console.log(`Attempt ${attempt} failed with rate limit, waiting ${delay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
        
        console.error(`Chain report API call failed for chain ${chainId} (attempt ${attempt}):`, error);
        
        if (attempt === maxRetries) {
          throw error; // Final attempt failed
        }
      }
    }
    
    throw new TornAPIError(`Failed to fetch chain report ${chainId} after ${maxRetries} attempts`);
  }

  async getFactionAttacks(
    limit: number = 100,
    sort: 'ASC' | 'DESC' = 'DESC',
    from?: number,
    to?: number
  ): Promise<TornAttack[]> {
    // Try v2 API first
    const baseUrlV2 = 'https://api.torn.com/v2';
    let endpoint = `/faction/attacks`;
    const params = new URLSearchParams();
    params.set('limit', limit.toString());
    params.set('sort', sort);
    
    if (from) params.set('from', from.toString());
    if (to) params.set('to', to.toString());
    
    const url = new URL(`${baseUrlV2}${endpoint}`);
    url.searchParams.set('key', this.apiKey);
    
    // Add our custom params
    for (const [key, value] of params) {
      url.searchParams.set(key, value);
    }

    console.log('Attacks API URL (v2):', url.toString());

    try {
      const response = await fetch(url.toString());
      
      if (!response.ok) {
        throw new TornAPIError(
          `API request failed: ${response.status} ${response.statusText}`,
          response.status
        );
      }

      const data = await response.json();
      
      console.log('Raw attacks API response (v2):', data ? 'Got response' : 'No response');
      
      if (data.error) {
        throw new TornAPIError(
          `Torn API Error: ${data.error.error}`,
          data.error.code
        );
      }

      // Based on original implementation, the response should be an array of attacks
      const attacks: TornAttack[] = Array.isArray(data.attacks) 
        ? data.attacks
        : [];
      
      console.log(`Transformed attacks (v2): ${attacks.length} attacks`);
      
      return attacks;
    } catch (error) {
      console.log('v2 attacks API failed, falling back to v1 with selections...');
      
      // Fallback to v1 API with selections parameter
      try {
        const params: Record<string, any> = {
          selections: 'attacks',
          limit,
          sort
        };
        
        if (from) params.from = from;
        if (to) params.to = to;

        const data = await this.makeRequest<{ attacks: Record<string, TornAttack> }>(
          `faction/${MY_FACTION_ID}`,
          params
        );
        
        console.log('Raw attacks API response (v1 fallback):', data ? 'Got response' : 'No response');
        
        const attacks = Object.values(data.attacks || {});
        console.log(`Transformed attacks (v1): ${attacks.length} attacks`);
        
        return attacks;
      } catch (fallbackError) {
        if (fallbackError instanceof TornAPIError) {
          throw fallbackError;
        }
        
        throw new TornAPIError(
          `Both v2 and v1 attacks API calls failed: ${fallbackError instanceof Error ? fallbackError.message : 'Unknown error'}`,
          undefined,
          fallbackError
        );
      }
    }
  }

  async validateApiKey(): Promise<boolean> {
    try {
      await this.makeRequest('user', { selections: 'profile' });
      return true;
    } catch (error) {
      return false;
    }
  }

  async getUserInfo(): Promise<{ id: number; name: string; faction?: { id: number; name: string } }> {
    const data = await this.makeRequest<{
      player_id: number;
      name: string;
      faction?: {
        faction_id: number;
        faction_name: string;
      };
    }>('user', { selections: 'profile' });
    
    return {
      id: data.player_id,
      name: data.name,
      faction: data.faction ? {
        id: data.faction.faction_id,
        name: data.faction.faction_name
      } : undefined
    };
  }
}

// Helper function to create TornAPI instance with error handling
export function createTornAPI(apiKey?: string): TornAPI | null {
  if (!apiKey) {
    return null;
  }
  
  return new TornAPI(apiKey);
}

// Utility functions from original implementation
export function getAllChainsInTimeRange(
  tornApi: TornAPI,
  warStart: number,
  warEnd: number
): Promise<TornChainInfo[]> {
  return tornApi.getFactionChains(100, 'DESC', warEnd, warStart);
}