import { useState, useEffect, useCallback } from 'react';
import { useAction } from 'convex/react';
import { api } from '../../convex/_generated/api';

interface ChainInfo {
  current: number;
  max: number;
  timeout: number;
  modifier: number;
  cooldown: number;
  start: number;
  end: number;
}

interface UseRealTimeChainProps {
  refreshInterval?: number;
  enableAudioAlerts?: boolean;
}

export function useRealTimeChain({ 
  refreshInterval = 15000, 
  enableAudioAlerts = false 
}: UseRealTimeChainProps = {}) {
  const [chainInfo, setChainInfo] = useState<ChainInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null);
  const [remainingTime, setRemainingTime] = useState<number>(0);
  const [cooldownRemaining, setCooldownRemaining] = useState<number>(0);
  const [chainStatus, setChainStatus] = useState<'good' | 'warning' | 'critical' | 'cooldown'>('good');

  // Get chain info action
  const getChainInfo = useAction(api.targets.getChainInfo);

  // Real-time countdown
  useEffect(() => {
    const interval = setInterval(() => {
      if (chainInfo) {
        const now = Date.now() / 1000;
        const timeoutRemaining = Math.max(0, chainInfo.timeout - now);
        const cooldownTime = Math.max(0, chainInfo.cooldown - now);
        
        setRemainingTime(timeoutRemaining);
        setCooldownRemaining(cooldownTime);

        // Determine chain status
        if (cooldownTime > 0) {
          setChainStatus('cooldown');
        } else if (timeoutRemaining <= 60) {
          setChainStatus('critical');
        } else if (timeoutRemaining <= 300) { // 5 minutes
          setChainStatus('warning');
        } else {
          setChainStatus('good');
        }

        // Audio alerts for critical chain status
        if (enableAudioAlerts && chainStatus === 'critical' && timeoutRemaining > 0 && timeoutRemaining <= 60) {
          try {
            // Use a simple beep sound encoded as data URL
            const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvGodCEOayOzGeSsFJH/N8tWLOAkaaLru555NEAxTqeD1r2oTC0mlz+fAeCoFKIDN8tiJOAkZarPs4Z9OEw1UqeH2tGsTA0CIx/PfmkMKF12n2dPIaikFLIHO8tiJNwgZarPs4Z9OEw1UqeL2tGsTA0CKzfPgmkMKF12n2dPIaikFLIHO8tiJNwgZarPs4Z9OEw1UqeL2tGsTA0CLz/PgmkMKF12n2dPIaikFLIHO8tiJNwgZarPs4Z9OEw1UqeL2tGsTC0am0ujAeisFKYDN8tuIOAkZarPt4Z9OEw1UqeL2tGsTC0an1ujAeysFKYDN8tuIOAkZarTs459OEAxUqOL2s2sTC0am0+nAeyQFKYDN8tiIOAkYarTs459OEAxUqOH1s2sUC0am0efAeyQFKYDN8tiIOwkYarTs45tNEAhUqOH1s2oUC0al0efAeCcEKYDN8tiIOwoYarTs45xNEAhUqOD1s2oUC0al0efAeCcEKYDN8tiIOwoYarTr45xNEAhUqOD1s2oUC0al0efAeCcEKYDO8tiIOQkYarTs45xNEAhUqOD1s2oUC0ak0efAeCcEKoAA');
            audio.volume = 0.3;
            audio.play().catch(() => {
              // Silent fail - user may not have interacted with page yet
            });
          } catch (error) {
            // Silent fail - audio not supported
          }
        }
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [chainInfo, enableAudioAlerts, chainStatus]);

  // Refresh chain info
  const refreshChainInfo = useCallback(async (showLoading: boolean = true) => {
    if (showLoading) setLoading(true);
    setError(null);

    try {
      const info = await getChainInfo();
      
      if (info?.chain) {
        setChainInfo(info.chain);
        setLastRefreshTime(new Date());
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch chain info');
    } finally {
      if (showLoading) setLoading(false);
    }
  }, [getChainInfo]);

  // Auto-refresh on interval
  useEffect(() => {
    if (refreshInterval <= 0) return;

    const interval = setInterval(() => {
      refreshChainInfo(false);
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [refreshInterval, refreshChainInfo]);

  // Initial load
  useEffect(() => {
    refreshChainInfo(true);
  }, [refreshChainInfo]);

  // Format remaining time
  const formatRemainingTime = (seconds: number): string => {
    if (seconds <= 0) return '0:00';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    
    if (minutes >= 60) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return `${hours}:${remainingMinutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return {
    chainInfo,
    loading,
    error,
    lastRefreshTime,
    chainRemaining: remainingTime,
    cooldownRemaining,
    chainStatus,
    refreshChainInfo,
    formatRemainingTime: () => formatRemainingTime(remainingTime),
    // Additional status helpers
    isCritical: chainStatus === 'critical',
    isWarning: chainStatus === 'warning',
    isGood: chainStatus === 'good',
    isOnCooldown: chainStatus === 'cooldown',
  };
}