import React, { useState } from 'react';
import { useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';

interface AddTargetDialogProps {
  selectedList: string;
  onSuccess: () => void;
}

export function AddTargetDialog({ selectedList, onSuccess }: AddTargetDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [tornId, setTornId] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const addTargetToList = useMutation(api.targets.addTargetToList);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!tornId || !selectedList) return;

    setIsLoading(true);
    try {
      await addTargetToList({
        tornId: parseInt(tornId),
        listName: selectedList,
      });
      setTornId('');
      setIsOpen(false);
      onSuccess();
    } catch (error: any) {
      alert(`Failed to add target: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        disabled={!selectedList}
        className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Add Target
      </button>

      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold mb-4">Add Target to {selectedList}</h2>
            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label htmlFor="tornId" className="block text-sm font-medium text-gray-700 mb-2">
                  Torn ID
                </label>
                <input
                  type="number"
                  id="tornId"
                  value={tornId}
                  onChange={(e) => setTornId(e.target.value)}
                  placeholder="Enter Torn ID..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={isLoading}
                  required
                />
              </div>
              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => setIsOpen(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800"
                  disabled={isLoading}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isLoading || !tornId}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Adding...' : 'Add Target'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  );
}