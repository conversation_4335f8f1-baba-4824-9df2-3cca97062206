"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericServer = void 0;
const server_1 = require("../server");
/**
 * A generic server that uses Requests and Responses.
 */
class GenericServer extends server_1.Server {
    constructor() {
        super({ alreadyListening: true });
        /**
         * The endpoint handler for the server. This will return the response to send.
         * @example
         * export const server = new GenericServer();
         * creator.withServer(server);
         * return await server.endpoint(request);
         */
        this.endpoint = async (request) => {
            if (!this._handler)
                return new Response('Server has no handler.', { status: 503 });
            if (request.method !== 'POST')
                return new Response('Server only supports POST requests.', { status: 405 });
            const body = await request.text();
            return new Promise((resolve, reject) => {
                this._handler({
                    // @ts-ignore
                    headers: Object.fromEntries(request.headers.entries()),
                    body: body ? JSON.parse(body) : body,
                    request,
                    response: null,
                    rawBody: body
                }, async (response) => {
                    if (response.files) {
                        const data = new FormData();
                        for (const file of response.files)
                            data.append(file.name, file.file, file.name);
                        data.append('payload_json', JSON.stringify(response.body));
                        resolve(new Response(data, {
                            status: response.status || 200,
                            headers: (response.headers || {})
                        }));
                    }
                    else
                        resolve(new Response(response.body ? JSON.stringify(response.body) : null, {
                            status: response.status || 200,
                            headers: {
                                ...(response.headers || {}),
                                'content-type': 'application/json'
                            }
                        }));
                }).catch(reject);
            });
        };
    }
    /** @private */
    createEndpoint(path, handler) {
        this._handler = handler;
    }
}
exports.GenericServer = GenericServer;
