import { Badge } from "../../ui/badge";
import { But<PERSON> } from "../../ui/button";
import { <PERSON> } from "@tanstack/react-router";
import { ExternalLink } from "lucide-react";
import type { FactionTool } from "../types";

interface FactionToolsProps {
	factionTools: FactionTool[];
}

export function FactionTools({ factionTools }: FactionToolsProps) {
	const availableTools = factionTools.filter((tool) => tool.available);
	const lockedTools = factionTools.filter(
		(tool) => !tool.available && tool.permission,
	);

	return (
		<div className="space-y-4">
			{/* Mobile and Tablet – single column list */}
			<div className="grid grid-cols-1 gap-3 lg:hidden">
				{/* Available Tools */}
				{availableTools.map((tool) => (
					<Button
						asChild
						key={tool.title}
						variant="outline"
						size="sm"
						className="h-auto w-full min-w-0 justify-start p-3"
					>
						<Link
							to={tool.href}
							className="flex w-full min-w-0 items-center gap-3"
						>
							<div className="flex-shrink-0 rounded-md bg-background/10 p-1.5">
								<tool.icon className="h-4 w-4" />
							</div>
							<div className="min-w-0 flex-1 text-left">
								<div className="flex flex-wrap items-center gap-2 font-medium text-sm leading-tight">
									{tool.title}
									{tool.adminOnly && (
										<Badge variant="destructive" className="text-xs">
											Admin
										</Badge>
									)}
								</div>
								<div
									className="overflow-hidden break-words text-xs opacity-70"
									style={{
										display: "-webkit-box",
										WebkitLineClamp: 2,
										WebkitBoxOrient: "vertical",
									}}
								>
									{tool.description}
								</div>
							</div>
							<ExternalLink className="h-4 w-4 flex-shrink-0 opacity-50" />
						</Link>
					</Button>
				))}

				{/* Locked Tools */}
				{lockedTools.map((tool) => (
					<Button
						key={tool.title}
						disabled
						variant="outline"
						size="sm"
						className="h-auto w-full min-w-0 justify-start p-3 opacity-60"
					>
						<div className="flex w-full min-w-0 items-center gap-3">
							<div className="flex-shrink-0 rounded-md bg-background/10 p-1.5">
								<tool.icon className="h-4 w-4 text-muted-foreground" />
							</div>
							<div className="min-w-0 flex-1 text-left">
								<div className="flex flex-wrap items-center gap-2 font-medium text-muted-foreground text-sm leading-tight">
									{tool.title}
									<Badge variant="outline" className="text-xs">
										Locked
									</Badge>
								</div>
								<div
									className="overflow-hidden break-words text-xs opacity-70"
									style={{
										display: "-webkit-box",
										WebkitLineClamp: 2,
										WebkitBoxOrient: "vertical",
									}}
								>
									{tool.description}
								</div>
							</div>
						</div>
					</Button>
				))}
			</div>

			{/* Desktop only – two column list */}
			<div className="hidden grid-cols-2 gap-3 lg:grid">
				{/* Available Tools */}
				{availableTools.map((tool) => (
					<Button
						asChild
						key={tool.title}
						variant="outline"
						size="sm"
						className="h-auto w-full min-w-0 justify-start p-3"
					>
						<Link
							to={tool.href}
							className="flex w-full min-w-0 items-center gap-3"
						>
							<div className="flex-shrink-0 rounded-md bg-background/10 p-1.5">
								<tool.icon className="h-4 w-4" />
							</div>
							<div className="min-w-0 flex-1 text-left">
								<div className="flex flex-wrap items-center gap-2 font-medium text-sm leading-tight">
									{tool.title}
									{tool.adminOnly && (
										<Badge variant="destructive" className="text-xs">
											Admin
										</Badge>
									)}
								</div>
								<div
									className="mt-0.5 overflow-hidden break-words text-xs opacity-70"
									style={{
										display: "-webkit-box",
										WebkitLineClamp: 2,
										WebkitBoxOrient: "vertical",
									}}
								>
									{tool.description}
								</div>
							</div>
							<ExternalLink className="h-4 w-4 flex-shrink-0 opacity-50" />
						</Link>
					</Button>
				))}

				{/* Locked Tools */}
				{lockedTools.map((tool) => (
					<Button
						key={tool.title}
						disabled
						variant="outline"
						size="sm"
						className="h-auto w-full min-w-0 justify-start p-3 opacity-60"
					>
						<div className="flex w-full min-w-0 items-center gap-3">
							<div className="flex-shrink-0 rounded-md bg-background/10 p-1.5">
								<tool.icon className="h-4 w-4 text-muted-foreground" />
							</div>
							<div className="min-w-0 flex-1 text-left">
								<div className="flex flex-wrap items-center gap-2 font-medium text-muted-foreground text-sm leading-tight">
									{tool.title}
									<Badge variant="outline" className="text-xs">
										Locked
									</Badge>
								</div>
								<div
									className="mt-0.5 overflow-hidden break-words text-xs opacity-70"
									style={{
										display: "-webkit-box",
										WebkitLineClamp: 2,
										WebkitBoxOrient: "vertical",
									}}
								>
									{tool.description}
								</div>
							</div>
						</div>
					</Button>
				))}
			</div>
		</div>
	);
}