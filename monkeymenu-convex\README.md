# MonkeyMenu Convex Migration

A modern real-time application built with Convex, React, and TypeScript. This is a migration of the original MonkeyMenu application to leverage Convex's real-time database and serverless functions.

## Features

- **Real-time Database**: Powered by Convex for instant data synchronization
- **Authentication**: Clerk integration for secure user management
- **Discord Integration**: Bot commands and notifications
- **Banking System**: Secure transaction handling
- **Target Finder**: Advanced search and filtering
- **War Management**: Real-time war tracking and updates
- **Announcements**: Admin broadcast system
- **Permissions**: Role-based access control

## Tech Stack

- **Frontend**: React 18, TypeScript, Vite, TanStack Router
- **Backend**: Convex (serverless functions + real-time database)
- **Authentication**: Clerk
- **UI**: Tailwind CSS, Radix UI
- **Validation**: Zod

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Set up Convex:
   ```bash
   npx convex dev
   ```

3. Configure environment variables:
   ```bash
   cp .env.example .env.local
   ```

4. Start development server:
   ```bash
   npm run dev
   ```

## Project Structure

```
├── convex/                 # Convex backend functions and schema
│   ├── _generated/         # Generated Convex files
│   ├── functions/          # Serverless functions
│   ├── schema.ts           # Database schema
│   └── convex.config.js    # Convex configuration
├── src/                    # React frontend
│   ├── components/         # Reusable UI components
│   ├── routes/            # Application routes
│   ├── hooks/             # Custom React hooks
│   ├── lib/               # Utility functions
│   └── types/             # TypeScript type definitions
└── public/                # Static assets
```

## Migration Notes

This project is a migration from the original MonkeyMenu Cloudflare Workers + tRPC setup to Convex. Key changes:

- **Database**: Moved from external DB to Convex's built-in database
- **API**: Replaced tRPC with Convex functions
- **Real-time**: Native real-time capabilities via Convex
- **Authentication**: Migrated to Clerk for better integration
- **Deployment**: Simplified deployment with Convex

## Development

- `npm run dev` - Start both Convex and Vite dev servers
- `npm run dev:web` - Start only the web dev server
- `npm run dev:convex` - Start only Convex dev server
- `npm run build` - Build for production
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript checks
