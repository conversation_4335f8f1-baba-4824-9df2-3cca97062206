import { Card, CardContent } from "@/components/ui/card";
import { useState } from "react";
import { WithdrawalCard } from "./WithdrawalCard";
import { WithdrawalFilters } from "./WithdrawalFilters";
import type { HistoryTabProps } from "./types";
import { filterWithdrawals } from "./utils";

export function HistoryTab({
	withdrawals,
	isLoading,
	onWithdrawalSelect,
}: HistoryTabProps) {
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedStatus, setSelectedStatus] = useState<string>("all");
	const [selectedAmountRange, setSelectedAmountRange] = useState<string>("all");
	const [viewMode, setViewMode] = useState<"grid" | "list">("list");

	const filteredWithdrawals = filterWithdrawals(
		withdrawals,
		searchQuery,
		selectedStatus,
		selectedAmountRange,
	);

	if (isLoading) {
		return <div className="py-8 text-center">Loading withdrawals...</div>;
	}

	return (
		<div className="space-y-6">
			<WithdrawalFilters
				searchQuery={searchQuery}
				onSearchChange={setSearchQuery}
				selectedStatus={selectedStatus}
				onStatusChange={setSelectedStatus}
				selectedAmountRange={selectedAmountRange}
				onAmountRangeChange={setSelectedAmountRange}
				viewMode={viewMode}
				onViewModeChange={setViewMode}
			/>

			{filteredWithdrawals.length === 0 ? (
				<Card>
					<CardContent className="py-8 text-center">
						<p className="text-muted-foreground">
							No withdrawals found in your history
						</p>
					</CardContent>
				</Card>
			) : (
				<div
					className={
						viewMode === "grid"
							? "grid gap-4 md:grid-cols-2 lg:grid-cols-3"
							: "space-y-4"
					}
				>
					{filteredWithdrawals.map((withdrawal) => (
						<WithdrawalCard
							key={withdrawal.withdrawal.id}
							withdrawal={withdrawal}
							onViewDetails={onWithdrawalSelect}
						/>
					))}
				</div>
			)}
		</div>
	);
}
