import { and, eq } from "drizzle-orm";
import type { DrizzleD1Database } from "drizzle-orm/d1";
import { user } from "../db/schema/auth";
import { tornUser } from "../db/schema/torn";
import { decrypt } from "../lib/crypto";
import { DiscordNotificationService } from "../lib/discord-notification-service";
import { createTornAPIWithSuspension } from "../lib/tornApi";
import type { AppBindings } from "../lib/types";

interface UserWithApiKey {
	id: string; // Internal user ID
	tornApiKey: string;
	tornUserId: string; // Torn User ID
	userName: string; // User name from auth table
	lastOverdoseEventTimestamp?: Date | null;
}

async function getUsersWithVerifiedApiKeys(
	db: DrizzleD1Database,
): Promise<UserWithApiKey[]> {
	try {
		const users = await db
			.select({
				id: tornUser.id,
				tornApiKey: tornUser.tornApiKey,
				tornUserId: tornUser.tornUserId,
				userName: user.name,
				lastOverdoseEventTimestamp: tornUser.lastOverdoseEventTimestamp,
			})
			.from(tornUser)
			.innerJoin(user, eq(tornUser.id, user.id))
			.where(
				and(
					eq(tornUser.tornApiKeyVerified, true),
					eq(tornUser.accessSuspended, false),
				),
			);

		return users
			.filter(
				(user) => !!user.tornApiKey && !!user.tornUserId && !!user.userName,
			)
			.map((user) => ({
				id: user.id,
				tornApiKey: user.tornApiKey as string,
				tornUserId: user.tornUserId as string,
				userName: user.userName,
				lastOverdoseEventTimestamp: user.lastOverdoseEventTimestamp,
			}));
	} catch (error) {
		console.error("Error fetching users with verified API keys:", error);
		return [];
	}
}

async function updateLastOverdoseEventTimestamp(
	db: DrizzleD1Database,
	userId: string,
	timestamp: Date,
): Promise<void> {
	try {
		await db
			.update(tornUser)
			.set({ lastOverdoseEventTimestamp: timestamp })
			.where(eq(tornUser.id, userId));
	} catch (error) {
		console.error(
			`Error updating last overdose event timestamp for user ${userId}:`,
			error,
		);
	}
}

export async function runOverdoseDetection(
	db: DrizzleD1Database,
	env: AppBindings["Bindings"],
): Promise<void> {
	console.log("Starting overdose detection task...");

	const users = await getUsersWithVerifiedApiKeys(db);
	if (users.length === 0) {
		console.log(
			"No users found with verified API keys for overdose detection.",
		);
		return;
	}

	console.log(`Processing overdose detection for ${users.length} users`);
	const discordService = new DiscordNotificationService(env);

	for (const currentUser of users) {
		console.log(
			`Processing user: ${currentUser.userName} (Torn ID: ${currentUser.tornUserId})`,
		);

		// Decrypt the API key before using it
		const decryptedApiKey = decrypt(currentUser.tornApiKey);
		const tornApi = createTornAPIWithSuspension(
			decryptedApiKey,
			db,
			currentUser.id,
			env,
		);

		try {
			// Fetch events since the last processed event for this user
			const lastTimestamp = currentUser.lastOverdoseEventTimestamp;
			// Convert Date to Unix timestamp for API call, or use 0 if no previous timestamp
			const lastUnixTimestamp = lastTimestamp
				? Math.floor(lastTimestamp.getTime() / 1000)
				: 0;
			const fetchFromTimestamp =
				lastUnixTimestamp > 0 ? lastUnixTimestamp + 1 : undefined;

			const events = await tornApi.getUserEvents(fetchFromTimestamp);

			if (!events || Object.keys(events).length === 0) {
				console.log(
					`No new events found for user ${currentUser.userName} since timestamp ${fetchFromTimestamp || "beginning"}`,
				);
				continue;
			}

			let latestEventTimestampForUser = lastUnixTimestamp;

			// Sort event IDs by timestamp to process them in order
			const sortedEventKeys = Object.keys(events).sort(
				(a, b) => events[a].timestamp - events[b].timestamp,
			);

			for (const eventId of sortedEventKeys) {
				const event = events[eventId];
				if (!event) continue;

				// Update the latest timestamp encountered for this user in this batch
				if (event.timestamp > latestEventTimestampForUser) {
					latestEventTimestampForUser = event.timestamp;
				}

				// Check if the event is an overdose event
				const eventText = event.event.toLowerCase();
				if (eventText.includes("overdosed")) {
					// Skip if this is an old event
					if (fetchFromTimestamp && event.timestamp < fetchFromTimestamp) {
						console.log(
							`Skipping already processed event for ${currentUser.userName} at ${event.timestamp}`,
						);
						continue;
					}

					console.log(
						`Overdose event identified for ${currentUser.userName}: "${event.event}" at ${event.timestamp}`,
					);

					// Send notification
					const notificationResult =
						await discordService.sendOverdoseNotification({
							userName: currentUser.userName,
							userTornId: currentUser.tornUserId,
							timestamp: event.timestamp,
							eventText: event.event,
						});

					if (notificationResult.success) {
						console.log(
							`Overdose notification sent for ${currentUser.userName}.`,
						);
					} else {
						console.error(
							`Failed to send overdose notification for ${currentUser.userName}: ${notificationResult.error}`,
						);
					}
				}
			}

			// Update the last processed timestamp in the database
			if (latestEventTimestampForUser > lastUnixTimestamp) {
				// Convert Unix timestamp back to Date for database storage
				const newTimestamp = new Date(latestEventTimestampForUser * 1000);
				await updateLastOverdoseEventTimestamp(
					db,
					currentUser.id,
					newTimestamp,
				);
				console.log(
					`Updated last processed timestamp for ${currentUser.userName} to ${latestEventTimestampForUser}`,
				);
			}
		} catch (error) {
			console.error(
				`Error processing overdose detection for user ${currentUser.userName} (Torn ID: ${currentUser.tornUserId}):`,
				error,
			);

			// Handle specific API errors
			if (error instanceof Error && error.message.includes("API key invalid")) {
				console.warn(
					`API key for user ${currentUser.userName} might be invalid.`,
				);
			}
		}
	}

	console.log("Overdose detection task finished.");
}
