import { and, eq, inArray, lt } from "drizzle-orm";
import type { DBInstance } from "../db/index";
import { user } from "../db/schema/auth";
import { withdrawalRequest } from "../db/schema/banking";
import { DiscordNotificationService } from "./discord-notification-service";
import type { AppBindings } from "./types";
import { WebSocketService } from "./websocket-service";

// How old a PENDING request must be to be expired (in hours)
const EXPIRY_HOURS = 24;

interface WithdrawalToExpire {
	id: string;
	amount: number;
	requestedByName: string;
	requestedById: string;
}

/**
 * Main function to expire old pending withdrawal requests
 */
export async function runWithdrawalExpiration(
	db: DBInstance,
	env: AppBindings["Bindings"],
): Promise<{
	total: number;
	expired: number;
	errors: string[];
}> {
	console.log("[WithdrawalExpiration] Starting withdrawal expiration...");

	const results = {
		total: 0,
		expired: 0,
		errors: [] as string[],
	};

	try {
		// Find old PENDING withdrawal requests
		const oldWithdrawals = await getOldPendingWithdrawals(db);
		results.total = oldWithdrawals.length;

		if (oldWithdrawals.length === 0) {
			console.log("[WithdrawalExpiration] No pending withdrawals to expire.");
			return results;
		}

		console.log(
			`[WithdrawalExpiration] Found ${oldWithdrawals.length} old withdrawals to expire`,
		);

		// Extract IDs for batch update
		const idsToExpire = oldWithdrawals.map((w) => w.id);

		// Update all old withdrawals to EXPIRED status
		await db
			.update(withdrawalRequest)
			.set({
				status: "EXPIRED",
				updatedAt: new Date(),
			})
			.where(inArray(withdrawalRequest.id, idsToExpire));

		results.expired = oldWithdrawals.length;

		// Broadcast WebSocket updates and send DMs for each expired withdrawal
		const wsService = new WebSocketService();
		const discordService = new DiscordNotificationService(env);

		for (const withdrawal of oldWithdrawals) {
			try {
				await wsService.broadcastWithdrawalUpdated({
					id: withdrawal.id,
					amount: withdrawal.amount,
					status: "EXPIRED",
					requestedByName: withdrawal.requestedByName,
				});
			} catch (error) {
				console.error(
					`[WithdrawalExpiration] Failed to broadcast expiration for ${withdrawal.id}:`,
					error,
				);
			}

			// Send DM notification
			try {
				const dmResult = await discordService.sendWithdrawalDMNotification({
					userId: withdrawal.requestedById,
					withdrawalId: withdrawal.id,
					amount: withdrawal.amount,
					status: "EXPIRED",
					userName: withdrawal.requestedByName,
				});

				if (!dmResult.success) {
					console.warn(
						`[WithdrawalExpiration] Failed to send DM notification for expired withdrawal ${withdrawal.id}: ${dmResult.error}`,
					);
				} else {
					console.log(
						`[WithdrawalExpiration] Successfully sent DM notification for expired withdrawal ${withdrawal.id}`,
					);
				}
			} catch (error) {
				console.error(
					`[WithdrawalExpiration] Failed to send DM notification for ${withdrawal.id}:`,
					error,
				);
			}
		}

		console.log(
			`[WithdrawalExpiration] Successfully expired ${results.expired} withdrawal request(s). IDs: ${idsToExpire.join(", ")}`,
		);
	} catch (error) {
		const errorMessage = `Fatal error in withdrawal expiration: ${
			error instanceof Error ? error.message : "Unknown error"
		}`;
		results.errors.push(errorMessage);
		console.error(`[WithdrawalExpiration] ${errorMessage}`);
	}

	return results;
}

/**
 * Get all PENDING withdrawals that are older than EXPIRY_HOURS
 */
async function getOldPendingWithdrawals(
	db: DBInstance,
): Promise<WithdrawalToExpire[]> {
	const expiryDate = new Date(Date.now() - EXPIRY_HOURS * 60 * 60 * 1000);

	const withdrawals = await db
		.select({
			id: withdrawalRequest.id,
			amount: withdrawalRequest.amount,
			requestedByName: user.name,
			requestedById: withdrawalRequest.requestedById,
		})
		.from(withdrawalRequest)
		.leftJoin(user, eq(withdrawalRequest.requestedById, user.id))
		.where(
			and(
				eq(withdrawalRequest.status, "PENDING"),
				lt(withdrawalRequest.createdAt, expiryDate),
			),
		);

	return withdrawals.map((w) => ({
		id: w.id,
		amount: w.amount,
		requestedByName: w.requestedByName || "Unknown User",
		requestedById: w.requestedById,
	}));
}
