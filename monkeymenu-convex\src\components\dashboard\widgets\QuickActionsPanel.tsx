import { <PERSON><PERSON> } from "../../ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "../../ui/card";
import { usePermissions } from "../../../hooks/usePermissions";
import { <PERSON> } from "@tanstack/react-router";
import { Banknote, CreditCard, Crosshair, Plus, Target } from "lucide-react";
import { WithdrawalDialogTrigger } from "./WithdrawalDialog";
import { useQuery } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { useMemo } from "react";

export function QuickActionsPanel() {
	const permissions = usePermissions();

	// Check permissions for quick actions
	const canAccessBanking = permissions.canAccessBanking();
	const canViewTargets = permissions.canViewTargets();
	const canManageBanking = permissions.canManageBanking();

	// Fetch war information to determine if there's an ongoing war
	const warData = useQuery(api.wars.listWars, {});

	// Determine if there's an ongoing war (adapted from original logic)
	const currentWarInfo = useMemo(() => {
		if (!warData || warData.length === 0) {
			return null;
		}

		const now = Math.floor(Date.now() / 1000);

		// Find active war
		const currentWar = warData.find(
			(war: any) => war.status === "active" && war.endTime && war.endTime > now
		);

		if (!currentWar) return null;

		// Return enemy faction info
		return {
			enemyName: currentWar.enemyFactionName,
			enemyId: currentWar.enemyFactionId,
		};
	}, [warData]);

	const hasOngoingWar = currentWarInfo !== null;

	// Get pending withdrawal requests count only if user can manage banking
	const pendingRequests = useQuery(
		api.banking.getPendingWithdrawals, 
		canManageBanking ? {} : "skip"
	);
	const pendingCount = pendingRequests?.length ?? 0;

	return (
		<Card className="flex flex-col">
			<CardHeader data-card-header>
				<CardTitle className="flex items-center gap-2">
					<Plus className="h-5 w-5" />
					Quick Actions
				</CardTitle>
				<CardDescription>Essential shortcuts</CardDescription>
			</CardHeader>
			<CardContent data-card-content className="flex flex-1 flex-col">
				<div className="grid gap-3 overflow-y-auto">
					{/* Withdrawal Dialog - Always available if user has banking permissions */}
					{canAccessBanking && (
						<WithdrawalDialogTrigger>
							<Button
								variant="default"
								size="sm"
								className="h-auto w-full justify-start p-3"
							>
								<div className="flex items-center gap-3">
									<div className="rounded-md bg-background/10 p-1.5">
										<CreditCard className="h-4 w-4" />
									</div>
									<div className="flex-1 text-left">
										<div className="font-medium text-sm">New Withdrawal</div>
										<div className="text-xs opacity-70">
											Request faction funds
										</div>
									</div>
								</div>
							</Button>
						</WithdrawalDialogTrigger>
					)}

					{/* War-specific actions - Only show when there's an ongoing war */}
					{hasOngoingWar && canViewTargets && (
						<>
							<Button
								variant="secondary"
								size="sm"
								asChild
								className="h-auto justify-start p-3"
							>
								<Link to="/targets" className="flex items-center gap-3">
									<div className="rounded-md bg-background/10 p-1.5">
										<Crosshair className="h-4 w-4" />
									</div>
									<div className="flex-1 text-left">
										<div className="font-medium text-sm">Find Inside Hit</div>
										<div className="text-xs opacity-70">
											Hunt {currentWarInfo?.enemyName} members
										</div>
									</div>
								</Link>
							</Button>

							<Button
								variant="secondary"
								size="sm"
								asChild
								className="h-auto justify-start p-3"
							>
								<Link to="/targets" className="flex items-center gap-3">
									<div className="rounded-md bg-background/10 p-1.5">
										<Target className="h-4 w-4" />
									</div>
									<div className="flex-1 text-left">
										<div className="font-medium text-sm">Find Outside Hit</div>
										<div className="text-xs opacity-70">
											Target anyone online
										</div>
									</div>
								</Link>
							</Button>
						</>
					)}

					{/* Admin actions - Pending withdrawal requests */}
					{canManageBanking && pendingCount > 0 && (
						<Button
							variant="destructive"
							size="sm"
							asChild
							className="h-auto animate-pulse justify-start p-3"
						>
							<Link to="/banking" className="flex items-center gap-3">
								<div className="rounded-md bg-background/10 p-1.5">
									<Banknote className="h-4 w-4" />
								</div>
								<div className="flex-1 text-left">
									<div className="font-medium text-sm">Pending Requests</div>
									<div className="text-xs opacity-70">
										Review {pendingCount} request{pendingCount > 1 ? "s" : ""}
									</div>
								</div>
							</Link>
						</Button>
					)}
				</div>

				{!canAccessBanking &&
					!(hasOngoingWar && canViewTargets) &&
					!(canManageBanking && pendingCount > 0) && (
						<div className="flex flex-1 items-center justify-center text-muted-foreground text-sm">
							No quick actions available.
						</div>
					)}
			</CardContent>
		</Card>
	);
}