import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import { BankingDashboard } from './BankingDashboard'
import { renderWithProviders, mockUser } from '../../test/utils'

// Mock the child components
vi.mock('./WithdrawalRequest', () => ({
  WithdrawalRequest: () => <div data-testid="withdrawal-request">Withdrawal Request</div>,
}))

vi.mock('./TransactionHistory', () => ({
  TransactionHistory: ({ userId, limit }: { userId: string; limit?: number }) => (
    <div data-testid="transaction-history">
      Transaction History - User: {userId} - Limit: {limit || 'unlimited'}
    </div>
  ),
}))

vi.mock('./BankingStats', () => ({
  BankingStats: ({ userId }: { userId: string }) => (
    <div data-testid="banking-stats">Banking Stats - User: {userId}</div>
  ),
}))

vi.mock('./AccountBalance', () => ({
  AccountBalance: ({ balance }: { balance: number }) => (
    <div data-testid="account-balance">Balance: ${balance.toLocaleString()}</div>
  ),
}))

// Mock hooks directly with simple implementations
vi.mock('../../hooks/usePermissions', () => ({
  usePermissions: () => ({
    canAccessBanking: true,
    canManageBanking: false,
  }),
}))

describe('BankingDashboard', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock convex queries to return our test data
    const { useQuery } = require('convex/react')
    useQuery.mockImplementation((queryName: string) => {
      if (queryName.includes('getCurrentUser')) {
        return mockUser
      }
      if (queryName.includes('getUserCashBalance')) {
        return 5000000
      }
      return null
    })
  })

  it('renders banking dashboard with correct title and description', () => {
    renderWithProviders(<BankingDashboard />)

    expect(screen.getByText('Banking Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Manage your cash and view transaction history')).toBeInTheDocument()
  })

  it('displays account balance correctly', () => {
    renderWithProviders(<BankingDashboard />)

    expect(screen.getByTestId('account-balance')).toBeInTheDocument()
    expect(screen.getByText('Balance: $5,000,000')).toBeInTheDocument()
  })

  it('renders all navigation tabs correctly', () => {
    renderWithProviders(<BankingDashboard />)

    expect(screen.getByText(/📊.*Overview/)).toBeInTheDocument()
    expect(screen.getByText(/💸.*Withdraw/)).toBeInTheDocument()
    expect(screen.getByText(/📜.*History/)).toBeInTheDocument()
  })

  it('switches tabs correctly', () => {
    renderWithProviders(<BankingDashboard />)

    // Initially shows banking stats
    expect(screen.getByTestId('banking-stats')).toBeInTheDocument()

    // Click withdraw tab
    fireEvent.click(screen.getByText(/💸.*Withdraw/))
    expect(screen.getByTestId('withdrawal-request')).toBeInTheDocument()

    // Click history tab
    fireEvent.click(screen.getByText(/📜.*History/))
    expect(screen.getByTestId('transaction-history')).toBeInTheDocument()
  })

  it('renders overview tab content correctly', () => {
    renderWithProviders(<BankingDashboard />)

    expect(screen.getByTestId('banking-stats')).toBeInTheDocument()
    expect(screen.getByTestId('transaction-history')).toBeInTheDocument()
  })
})