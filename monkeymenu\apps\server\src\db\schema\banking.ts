import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core";

export const withdrawalRequest = sqliteTable("withdrawal_request", {
	id: text("id").primaryKey(),
	amount: integer("amount").notNull(),
	status: text("status").notNull().default("PENDING"),
	discordMessageId: text("discord_message_id"),
	createdAt: integer("created_at", { mode: "timestamp" }).notNull(),
	updatedAt: integer("updated_at", { mode: "timestamp" }).notNull(),
	processedAt: integer("processed_at", { mode: "timestamp" }),
	transactionId: text("transaction_id"),
	processedByBotDiscordId: text("processed_by_bot_discord_id"),
	processedByBotDiscordTag: text("processed_by_bot_discord_tag"),
	initiatedByDiscordId: text("initiated_by_discord_id"),
	initiatedByDiscordTag: text("initiated_by_discord_tag"),
	requestedById: text("requested_by_id").notNull(),
	processedById: text("processed_by_id"),
});
