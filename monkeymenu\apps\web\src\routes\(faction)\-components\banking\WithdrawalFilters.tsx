import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Grid, List, Search, SlidersHorizontal } from "lucide-react";
import type { WithdrawalFiltersProps } from "./types";
import { AMOUNT_RANGES, WITHDRAWAL_STATUSES } from "./utils";

export function WithdrawalFilters({
	searchQuery,
	onSearchChange,
	selectedStatus,
	onStatusChange,
	selectedAmountRange,
	onAmountRangeChange,
	viewMode,
	onViewModeChange,
}: WithdrawalFiltersProps) {
	// Get active filter count
	const activeFilters = [
		selectedStatus !== "all" ? 1 : 0,
		selectedAmountRange !== "all" ? 1 : 0,
	].reduce((sum, count) => sum + count, 0);

	return (
		<div className="flex flex-row items-center justify-between gap-2">
			{/* Search Bar */}
			<div className="relative min-w-0 flex-1">
				<Search className="absolute top-2.5 left-2 h-4 w-4 text-muted-foreground" />
				<Input
					placeholder="Search by user or amount..."
					value={searchQuery}
					onChange={(e) => onSearchChange(e.target.value)}
					className="pl-8"
				/>
			</div>

			{/* Filter Button and View Toggle */}
			<div className="flex items-center gap-2">
				<Popover>
					<PopoverTrigger asChild>
						<Button variant="outline" size="sm" className="relative">
							<SlidersHorizontal className="mr-2 h-4 w-4" />
							Filters
							{activeFilters > 0 && (
								<span className="-top-1 -right-1 absolute flex h-5 w-5 items-center justify-center rounded-full bg-blue-500 text-white text-xs">
									{activeFilters}
								</span>
							)}
						</Button>
					</PopoverTrigger>
					<PopoverContent className="w-80" align="end">
						<div className="space-y-4">
							{/* Status Filter */}
							<div>
								<label
									htmlFor="status-filter"
									className="mb-2 block font-medium text-sm"
								>
									Status
								</label>
								<Select value={selectedStatus} onValueChange={onStatusChange}>
									<SelectTrigger id="status-filter">
										<SelectValue placeholder="Filter by status" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="all">All Statuses</SelectItem>
										{WITHDRAWAL_STATUSES.map((status) => (
											<SelectItem key={status.id} value={status.id}>
												{status.emoji} {status.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>

							{/* Amount Filter */}
							<div>
								<label
									htmlFor="amount-filter"
									className="mb-2 block font-medium text-sm"
								>
									Amount Range
								</label>
								<Select
									value={selectedAmountRange}
									onValueChange={onAmountRangeChange}
								>
									<SelectTrigger id="amount-filter">
										<SelectValue placeholder="Filter by amount" />
									</SelectTrigger>
									<SelectContent>
										{AMOUNT_RANGES.map((range) => (
											<SelectItem key={range.id} value={range.id}>
												{range.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>

							{/* View Mode */}
							<div>
								<div className="mb-2 font-medium text-sm">View Mode</div>
								<div className="flex gap-2">
									<Button
										variant={viewMode === "list" ? "default" : "outline"}
										size="sm"
										onClick={() => onViewModeChange("list")}
										className="flex-1"
									>
										<List className="mr-2 h-4 w-4" />
										List
									</Button>
									<Button
										variant={viewMode === "grid" ? "default" : "outline"}
										size="sm"
										onClick={() => onViewModeChange("grid")}
										className="flex-1"
									>
										<Grid className="mr-2 h-4 w-4" />
										Grid
									</Button>
								</div>
							</div>
						</div>
					</PopoverContent>
				</Popover>
			</div>
		</div>
	);
}
