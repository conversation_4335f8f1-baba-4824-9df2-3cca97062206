import { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { StatsCard } from './StatsCard';
import { TrendChart } from './TrendChart';
import { Leaderboard } from './Leaderboard';
import { ErrorBoundary } from '../banking/ErrorBoundary';

type TimeRange = '7d' | '30d' | '90d' | '1y';
type AnalyticsTab = 'personal' | 'faction' | 'trends' | 'leaderboards';

export function AnalyticsDashboard() {
  const [activeTab, setActiveTab] = useState<AnalyticsTab>('personal');
  const [timeRange, setTimeRange] = useState<TimeRange>('30d');

  const userStats = useQuery(api.analytics.getUserStats, { timeRange });
  const factionStats = useQuery(api.analytics.getFactionStats, { timeRange });

  const tabs = [
    { id: 'personal' as const, label: 'Personal Stats', icon: '👤' },
    { id: 'faction' as const, label: 'Faction Stats', icon: '🏛️' },
    { id: 'trends' as const, label: 'Trends', icon: '📈' },
    { id: 'leaderboards' as const, label: 'Leaderboards', icon: '🏆' },
  ];

  const timeRanges = [
    { value: '7d' as const, label: '7 Days' },
    { value: '30d' as const, label: '30 Days' },
    { value: '90d' as const, label: '90 Days' },
    { value: '1y' as const, label: '1 Year' },
  ];

  const renderPersonalStats = () => {
    if (userStats === undefined) {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
              <div className="h-6 bg-gray-200 rounded mb-2"></div>
              <div className="h-8 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {/* Banking Stats */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">💰 Banking Activity</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatsCard
              title="Current Balance"
              value={`$${userStats.banking.currentBalance.toLocaleString()}`}
              icon="💵"
              color="green"
            />
            <StatsCard
              title="Total Deposits"
              value={`$${userStats.banking.totalDeposits.toLocaleString()}`}
              icon="📈"
              color="blue"
            />
            <StatsCard
              title="Total Withdrawals"
              value={`$${userStats.banking.totalWithdrawals.toLocaleString()}`}
              icon="📉"
              color="red"
            />
            <StatsCard
              title="Transactions"
              value={userStats.banking.totalTransactions.toString()}
              icon="🔄"
              color="purple"
            />
          </div>
        </div>

        {/* War Stats */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">⚔️ War Performance</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatsCard
              title="Total Attacks"
              value={userStats.wars.totalAttacks.toString()}
              icon="🎯"
              color="orange"
            />
            <StatsCard
              title="Win Rate"
              value={`${userStats.wars.winRate}%`}
              icon="🏆"
              color="green"
            />
            <StatsCard
              title="Total Respect"
              value={userStats.wars.totalRespect.toLocaleString()}
              icon="⭐"
              color="yellow"
            />
            <StatsCard
              title="Wins/Losses"
              value={`${userStats.wars.wins}/${userStats.wars.losses}`}
              icon="📊"
              color="blue"
            />
          </div>
        </div>

        {/* Guide Stats */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">📚 Guide Contributions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatsCard
              title="Total Guides"
              value={userStats.guides.totalGuides.toString()}
              icon="📝"
              color="indigo"
            />
            <StatsCard
              title="Published"
              value={userStats.guides.publishedGuides.toString()}
              icon="📖"
              color="green"
            />
            <StatsCard
              title="Total Views"
              value={userStats.guides.totalViews.toLocaleString()}
              icon="👀"
              color="purple"
            />
            <StatsCard
              title="Avg Views"
              value={userStats.guides.averageViews.toString()}
              icon="📈"
              color="blue"
            />
          </div>
        </div>
      </div>
    );
  };

  const renderFactionStats = () => {
    if (factionStats === undefined) {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
              <div className="h-6 bg-gray-200 rounded mb-2"></div>
              <div className="h-8 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {/* Member Stats */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">👥 Membership</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <StatsCard
              title="Total Members"
              value={factionStats.members.total.toString()}
              icon="👤"
              color="blue"
            />
            <StatsCard
              title="Active Members"
              value={factionStats.members.active.toString()}
              icon="🟢"
              color="green"
            />
            <StatsCard
              title="New Members"
              value={factionStats.members.newMembers.toString()}
              icon="🆕"
              color="purple"
            />
          </div>
        </div>

        {/* Banking Stats */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">💰 Banking Activity</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <StatsCard
              title="Total Transactions"
              value={factionStats.banking.totalTransactions.toString()}
              icon="🔄"
              color="green"
            />
            <StatsCard
              title="Total Volume"
              value={`$${factionStats.banking.totalVolume.toLocaleString()}`}
              icon="💵"
              color="blue"
            />
            <StatsCard
              title="Active Users"
              value={factionStats.banking.activeUsers.toString()}
              icon="👥"
              color="purple"
            />
          </div>
        </div>

        {/* War Stats */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">⚔️ War Performance</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <StatsCard
              title="Active Wars"
              value={factionStats.wars.activeWars.toString()}
              icon="🏛️"
              color="red"
            />
            <StatsCard
              title="Total Attacks"
              value={factionStats.wars.totalAttacks.toString()}
              icon="⚔️"
              color="orange"
            />
            <StatsCard
              title="Win Rate"
              value={`${factionStats.wars.winRate}%`}
              icon="🏆"
              color="green"
            />
            <StatsCard
              title="Total Respect"
              value={factionStats.wars.totalRespect.toLocaleString()}
              icon="⭐"
              color="yellow"
            />
          </div>
        </div>
      </div>
    );
  };

  const renderTrends = () => (
    <ErrorBoundary fallback={<div className="text-center py-8 text-red-600">Error loading trends. Please try again.</div>}>
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <TrendChart metric="users" title="User Registrations" />
          <TrendChart metric="transactions" title="Banking Transactions" />
          <TrendChart metric="wars" title="War Activity" />
          <TrendChart metric="guides" title="Guide Creation" />
        </div>
      </div>
    </ErrorBoundary>
  );

  const renderLeaderboards = () => (
    <ErrorBoundary fallback={<div className="text-center py-8 text-red-600">Error loading leaderboards. Please try again.</div>}>
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Leaderboard category="respect" title="Top Respect Earners" />
          <Leaderboard category="attacks" title="Most Active Warriors" />
          <Leaderboard category="banking" title="Highest Balances" />
          <Leaderboard category="guides" title="Top Guide Authors" />
        </div>
      </div>
    </ErrorBoundary>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'personal':
        return renderPersonalStats();
      case 'faction':
        return renderFactionStats();
      case 'trends':
        return renderTrends();
      case 'leaderboards':
        return renderLeaderboards();
      default:
        return renderPersonalStats();
    }
  };

  return (
    <ErrorBoundary fallback={<div className="text-center py-8 text-red-600">Error loading analytics dashboard. Please try again.</div>}>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
          <p className="text-gray-600 mt-1">
            Track performance and analyze trends across all activities
          </p>
        </div>
        
        {/* Time Range Selector */}
        {(activeTab === 'personal' || activeTab === 'faction') && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600 mr-2">Time Range:</span>
            {timeRanges.map((range) => (
              <button
                key={range.value}
                onClick={() => setTimeRange(range.value)}
                className={`px-3 py-1 rounded text-sm transition-colors ${
                  timeRange === range.value
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {range.label}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span>{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        {renderTabContent()}
      </div>
      </div>
    </ErrorBoundary>
  );
}