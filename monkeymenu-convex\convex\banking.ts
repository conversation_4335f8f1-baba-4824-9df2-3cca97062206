import { query, mutation, action, internalQuery, internalMutation } from "./_generated/server";
import { v } from "convex/values";
import { ConvexError } from "convex/values";
import {
  getCurrentUser,
  requireBankingPermission,
  requireBankingViewPermission,
  canAccessBanking
} from "./lib/permissions";
import {
  validateAmount,
  validateDiscordId,
  validateDiscordTag
} from "./lib/validation";

// Get banking cooldown status
export const getBankingCooldown = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) throw new Error("User not found");

    // Check for recent banking operations within last 30 seconds
    const thirtySecondsAgo = Date.now() - 30000;
    const recentOperation = await ctx.db
      .query("withdrawalRequests")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.gte(q.field("createdAt"), thirtySecondsAgo))
      .first();

    if (recentOperation) {
      const cooldownUntil = (recentOperation.createdAt ?? 0) + 30000;
      return {
        cooldownUntil,
        cooldownDuration: 30000,
        remaining: Math.max(0, Math.ceil((cooldownUntil - Date.now()) / 1000))
      };
    }

    return {
      cooldownUntil: null,
      cooldownDuration: 30000,
      remaining: 0
    };
  },
});

// Get user account by currency
export const getUserAccount = query({
  args: { 
    userId: v.id("users"),
    currency: v.string() 
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("accounts")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("currency"), args.currency))
      .first();
  },
});

// Get all user accounts
export const getUserAccounts = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("accounts")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();
  },
});

// Create account
export const createAccount = mutation({
  args: {
    userId: v.id("users"),
    currency: v.string(),
    initialBalance: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    return await ctx.db.insert("accounts", {
      userId: args.userId,
      balance: args.initialBalance ?? 0,
      currency: args.currency,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Update account balance
export const updateAccountBalance = mutation({
  args: {
    accountId: v.id("accounts"),
    newBalance: v.number(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.patch(args.accountId, {
      balance: args.newBalance,
      updatedAt: Date.now(),
    });
  },
});

// Create transaction
export const createTransaction = mutation({
  args: {
    fromAccountId: v.optional(v.id("accounts")),
    toAccountId: v.optional(v.id("accounts")),
    amount: v.number(),
    currency: v.string(),
    type: v.string(),
    reference: v.optional(v.string()),
    metadata: v.optional(v.object({})),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    // Create the transaction
    const transactionId = await ctx.db.insert("transactions", {
      fromAccountId: args.fromAccountId,
      toAccountId: args.toAccountId,
      amount: args.amount,
      currency: args.currency,
      type: args.type,
      status: "pending",
      reference: args.reference,
      metadata: args.metadata,
      createdAt: now,
      updatedAt: now,
    });

    return transactionId;
  },
});

// Get detailed transaction history with pagination
export const getTransactionHistory = query({
  args: {
    userId: v.optional(v.id("users")),
    currency: v.optional(v.string()),
    type: v.optional(v.string()),
    status: v.optional(v.string()),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await requireBankingViewPermission(ctx);
    
    let query = ctx.db.query("transactions");
    
    // Get user's accounts if userId is specified
    let accountIds: string[] = [];
    if (args.userId) {
      const accounts = await ctx.db
        .query("accounts")
        .withIndex("by_user", (q: any) => q.eq("userId", args.userId!))
        .collect();
      accountIds = accounts.map(acc => acc._id);
    }

    // Build query based on filters
    const transactions = await query.order("desc").collect();
    
    let filteredTransactions = transactions;

    // Filter by user accounts if specified
    if (args.userId && accountIds.length > 0) {
      filteredTransactions = filteredTransactions.filter(tx => 
        (tx.fromAccountId && accountIds.includes(tx.fromAccountId)) ||
        (tx.toAccountId && accountIds.includes(tx.toAccountId))
      );
    }

    // Apply other filters
    if (args.currency) {
      filteredTransactions = filteredTransactions.filter(tx => tx.currency === args.currency);
    }
    if (args.type) {
      filteredTransactions = filteredTransactions.filter(tx => tx.type === args.type);
    }
    if (args.status) {
      filteredTransactions = filteredTransactions.filter(tx => tx.status === args.status);
    }

    // Apply pagination
    const offset = args.offset ?? 0;
    const limit = args.limit ?? 20;
    const paginatedTransactions = filteredTransactions.slice(offset, offset + limit);

    // Enrich with account and user data
    const enrichedTransactions = await Promise.all(
      paginatedTransactions.map(async (tx) => {
        let fromAccount = null;
        let toAccount = null;
        let fromUser = null;
        let toUser = null;

        if (tx.fromAccountId) {
          fromAccount = await ctx.db.get(tx.fromAccountId);
          if (fromAccount) {
            fromUser = await ctx.db.get(fromAccount.userId);
          }
        }

        if (tx.toAccountId) {
          toAccount = await ctx.db.get(tx.toAccountId);
          if (toAccount) {
            toUser = await ctx.db.get(toAccount.userId);
          }
        }

        return {
          ...tx,
          fromAccount,
          toAccount,
          fromUser,
          toUser,
        };
      })
    );

    return {
      transactions: enrichedTransactions,
      total: filteredTransactions.length,
      hasMore: offset + limit < filteredTransactions.length,
    };
  },
});

// Get banking statistics for dashboard
export const getBankingStats = query({
  args: {
    userId: v.optional(v.id("users")),
    days: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await requireBankingViewPermission(ctx);
    
    const days = args.days ?? 30;
    const startTime = Date.now() - (days * 24 * 60 * 60 * 1000);

    let accountIds: string[] = [];
    if (args.userId) {
      const accounts = await ctx.db
        .query("accounts")
        .withIndex("by_user", (q: any) => q.eq("userId", args.userId!))
        .collect();
      accountIds = accounts.map(acc => acc._id);
    }

    // Get transactions in time range
    let transactions = await ctx.db
      .query("transactions")
      .filter((q) => q.gte(q.field("createdAt"), startTime))
      .collect();

    // Filter by user if specified
    if (args.userId && accountIds.length > 0) {
      transactions = transactions.filter(tx => 
        (tx.fromAccountId && accountIds.includes(tx.fromAccountId)) ||
        (tx.toAccountId && accountIds.includes(tx.toAccountId))
      );
    }

    // Calculate statistics
    const stats = {
      totalTransactions: transactions.length,
      totalDeposits: 0,
      totalWithdrawals: 0,
      totalTransfers: 0,
      depositAmount: 0,
      withdrawalAmount: 0,
      transferAmount: 0,
      pendingWithdrawals: 0,
      completedWithdrawals: 0,
      cancelledWithdrawals: 0,
    };

    transactions.forEach(tx => {
      switch (tx.type) {
        case 'deposit':
          stats.totalDeposits++;
          stats.depositAmount += tx.amount;
          break;
        case 'withdrawal':
          // Only count completed withdrawals in transaction stats
          if (tx.status === 'completed') {
            stats.totalWithdrawals++;
            stats.withdrawalAmount += tx.amount;
            stats.completedWithdrawals++;
          }
          break;
        case 'transfer':
          stats.totalTransfers++;
          stats.transferAmount += tx.amount;
          break;
      }
    });

    // Get withdrawal request stats
    let withdrawalRequests = await ctx.db
      .query("withdrawalRequests")
      .filter((q) => q.gte(q.field("createdAt"), startTime))
      .collect();

    if (args.userId) {
      withdrawalRequests = withdrawalRequests.filter(req => req.userId === args.userId);
    }

    // Count ACCEPTED and COMPLETED withdrawals in statistics (committed money)
    withdrawalRequests.forEach(req => {
      switch (req.status) {
        case 'PENDING':
          stats.pendingWithdrawals++;
          break;
        case 'ACCEPTED':
        case 'COMPLETED':
          // Count both ACCEPTED and COMPLETED as "committed" withdrawals
          stats.totalWithdrawals++;
          stats.withdrawalAmount += req.amount;
          if (req.status === 'COMPLETED') stats.completedWithdrawals++;
          break;
        case 'CANCELLED':
          stats.cancelledWithdrawals++;
          break;
      }
    });

    return stats;
  },
});

// Get user transactions
export const getUserTransactions = query({
  args: { 
    userId: v.id("users"),
    limit: v.optional(v.number()) 
  },
  handler: async (ctx, args) => {
    // Get user accounts first
    const accounts = await ctx.db
      .query("accounts")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();
    
    const accountIds = accounts.map(acc => acc._id);
    
    // Get transactions involving these accounts
    const transactions = await ctx.db
      .query("transactions")
      .order("desc")
      .collect();
    
    // Filter transactions that involve user's accounts
    const userTransactions = transactions.filter(tx => 
      (tx.fromAccountId && accountIds.includes(tx.fromAccountId)) ||
      (tx.toAccountId && accountIds.includes(tx.toAccountId))
    );

    return userTransactions.slice(0, args.limit ?? 50);
  },
});

// Create or get user's cash account
export const getOrCreateCashAccount = mutation({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    // Check if account already exists
    const existingAccount = await ctx.db
      .query("accounts")
      .withIndex("by_user_currency", (q) => 
        q.eq("userId", args.userId).eq("currency", "cash")
      )
      .first();

    if (existingAccount) {
      return existingAccount;
    }

    // Create new cash account
    const now = Date.now();
    const accountId = await ctx.db.insert("accounts", {
      userId: args.userId,
      balance: 0,
      currency: "cash",
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });

    return await ctx.db.get(accountId);
  },
});

// Create withdrawal request
export const createWithdrawalRequest = mutation({
  args: {
    amount: v.number(),
    discordMessageId: v.optional(v.string()),
    initiatedByDiscordId: v.optional(v.string()),
    initiatedByDiscordTag: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    
    if (!(await canAccessBanking(ctx))) {
      throw new ConvexError("Banking access required");
    }

    // Validate input
    validateAmount(args.amount);
    validateDiscordId(args.initiatedByDiscordId);
    validateDiscordTag(args.initiatedByDiscordTag);
    
    // Rate limiting: Only enforce a 1-minute cooldown between requests
    const oneMinuteAgo = Date.now() - 60 * 1000;
    const recentRequests = await ctx.db
      .query("withdrawalRequests")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.gte(q.field("createdAt"), oneMinuteAgo))
      .collect();

    if (recentRequests.length > 0) {
      throw new ConvexError("Rate limit: Please wait before making another withdrawal request");
    }

    // Check total withdrawal count (max 50 per user)
    const totalRequests = await ctx.db
      .query("withdrawalRequests")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .collect();

    if (totalRequests.length >= 50) {
      throw new ConvexError("Maximum withdrawal limit reached (50 requests per user)");
    }

    // Validate amount
    if (args.amount <= 0) {
      throw new ConvexError("Amount must be greater than 0");
    }

    const now = Date.now();
    return await ctx.db.insert("withdrawalRequests", {
      userId: user._id,
      amount: args.amount,
      status: "PENDING",
      discordMessageId: args.discordMessageId,
      initiatedByDiscordId: args.initiatedByDiscordId,
      initiatedByDiscordTag: args.initiatedByDiscordTag,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Get user's withdrawal requests
export const getUserWithdrawalRequests = query({
  args: {
    userId: v.id("users"),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("withdrawalRequests")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .take(args.limit ?? 20);
  },
});

// Internal version for actions/crons
export const getUserWithdrawalRequestsInternal = internalQuery({
  args: {
    userId: v.id("users"),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("withdrawalRequests")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .take(args.limit ?? 20);
  },
});



// Get all pending withdrawal requests (admin)
export const getPendingWithdrawalRequests = query({
  args: {},
  handler: async (ctx) => {
    await requireBankingPermission(ctx);
    const requests = await ctx.db
      .query("withdrawalRequests")
      .withIndex("by_status", (q) => q.eq("status", "PENDING"))
      .order("desc")
      .collect();

    // Join with user data
    const requestsWithUsers = await Promise.all(
      requests.map(async (request) => {
        const user = await ctx.db.get(request.userId);
        return { ...request, user };
      })
    );

    return requestsWithUsers;
  },
});

// Update withdrawal request status (admin)
export const updateWithdrawalRequestStatus = mutation({
  args: {
    requestId: v.id("withdrawalRequests"),
    status: v.string(),
    processedByBotDiscordId: v.optional(v.string()),
    processedByBotDiscordTag: v.optional(v.string()),
    transactionId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await requireBankingPermission(ctx);
    const user = await getCurrentUser(ctx);

    // Get the withdrawal request first to get user info
    const withdrawalRequest = await ctx.db.get(args.requestId);
    if (!withdrawalRequest) {
      throw new ConvexError("Withdrawal request not found");
    }

    // Get the requesting user's Torn ID
    const requestingUser = await ctx.db.get(withdrawalRequest.userId);

    const now = Date.now();
    const updateData: any = {
      status: args.status,
      updatedAt: now,
    };

    updateData.processedById = user._id;
    updateData.processedAt = now;

    if (args.processedByBotDiscordId) {
      updateData.processedByBotDiscordId = args.processedByBotDiscordId;
    }

    if (args.processedByBotDiscordTag) {
      updateData.processedByBotDiscordTag = args.processedByBotDiscordTag;
    }

    if (args.transactionId) {
      updateData.transactionId = args.transactionId;
    }

    await ctx.db.patch(args.requestId, updateData);

    // Return data needed for Torn URL generation
    return {
      success: true,
      amount: withdrawalRequest.amount,
      requestedByTornId: requestingUser?.tornId,
      status: args.status,
    };
  },
});

// Internal version for cron jobs (no auth required)
export const updateWithdrawalRequestStatusInternal = internalMutation({
  args: {
    requestId: v.id("withdrawalRequests"),
    status: v.string(),
    processedByBotDiscordId: v.optional(v.string()),
    processedByBotDiscordTag: v.optional(v.string()),
    transactionId: v.optional(v.string()),
    processedById: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    const updateData: any = {
      status: args.status,
      updatedAt: now,
      processedAt: now,
    };

    if (args.processedById) {
      updateData.processedById = args.processedById;
    }

    if (args.processedByBotDiscordId) {
      updateData.processedByBotDiscordId = args.processedByBotDiscordId;
    }

    if (args.processedByBotDiscordTag) {
      updateData.processedByBotDiscordTag = args.processedByBotDiscordTag;
    }

    if (args.transactionId) {
      updateData.transactionId = args.transactionId;
    }

    return await ctx.db.patch(args.requestId, updateData);
  },
});

// Get user's cash balance
export const getUserCashBalance = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const account = await ctx.db
      .query("accounts")
      .withIndex("by_user_currency", (q) => 
        q.eq("userId", args.userId).eq("currency", "cash")
      )
      .first();

    return account?.balance ?? 0;
  },
});

// Update user's cash balance (from Torn API sync)
export const updateUserCashBalance = mutation({
  args: {
    userId: v.id("users"),
    newBalance: v.number(),
  },
  handler: async (ctx, args) => {
    // Validate input
    validateAmount(args.newBalance);
    
    const account = await ctx.db
      .query("accounts")
      .withIndex("by_user_currency", (q) => 
        q.eq("userId", args.userId).eq("currency", "cash")
      )
      .first();

    if (!account) {
      // Create new account if it doesn't exist
      const now = Date.now();
      return await ctx.db.insert("accounts", {
        userId: args.userId,
        balance: args.newBalance,
        currency: "cash",
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
    }

    return await ctx.db.patch(account._id, {
      balance: args.newBalance,
      updatedAt: Date.now(),
    });
  },
});

// Process withdrawal transaction (complete the withdrawal flow)
// Internal version for server-side verification
export const processWithdrawalTransactionInternal = internalMutation({
  args: {
    withdrawalRequestId: v.id("withdrawalRequests"),
    tornTransactionId: v.string(),
    processedById: v.id("users"),
  },
  handler: async (ctx, args) => {
    // No requireBankingPermission for internal
    // Get the withdrawal request
    const withdrawalRequest = await ctx.db.get(args.withdrawalRequestId);
    if (!withdrawalRequest) {
      throw new ConvexError("Withdrawal request not found");
    }

    if (withdrawalRequest.status !== "ACCEPTED") {
      throw new ConvexError("Withdrawal request must be ACCEPTED to process transaction");
    }

    // Get user's cash account
    const account = await ctx.db
      .query("accounts")
      .withIndex("by_user_currency", (q) =>
        q.eq("userId", withdrawalRequest.userId).eq("currency", "cash")
      )
      .first();

    if (!account) {
      throw new ConvexError("User cash account not found");
    }

    // Create transaction record
    const now = Date.now();
    const transactionId = await ctx.db.insert("transactions", {
      fromAccountId: account._id,
      amount: withdrawalRequest.amount,
      currency: "cash",
      type: "withdrawal",
      status: "completed",
      reference: args.tornTransactionId,
      withdrawalRequestId: args.withdrawalRequestId,
      metadata: {
        processedById: args.processedById,
        tornTransactionId: args.tornTransactionId,
      },
      processedAt: now,
      createdAt: now,
      updatedAt: now,
    });

    // Update withdrawal request status
    await ctx.db.patch(args.withdrawalRequestId, {
      status: "COMPLETED",
      transactionId: args.tornTransactionId,
      processedById: args.processedById,
      processedAt: now,
      updatedAt: now,
    });

    // Update account balance
    await ctx.db.patch(account._id, {
      balance: account.balance - withdrawalRequest.amount,
      updatedAt: now,
    });

    return transactionId;
  },
});
export const processWithdrawalTransaction = mutation({
  args: {
    withdrawalRequestId: v.id("withdrawalRequests"),
    tornTransactionId: v.string(),
    processedById: v.id("users"),
  },
  handler: async (ctx, args) => {
    await requireBankingPermission(ctx);
    
    // Get the withdrawal request
    const withdrawalRequest = await ctx.db.get(args.withdrawalRequestId);
    if (!withdrawalRequest) {
      throw new ConvexError("Withdrawal request not found");
    }

    if (withdrawalRequest.status !== "ACCEPTED") {
      throw new ConvexError("Withdrawal request must be ACCEPTED to process transaction");
    }

    // Get user's cash account
    const account = await ctx.db
      .query("accounts")
      .withIndex("by_user_currency", (q) => 
        q.eq("userId", withdrawalRequest.userId).eq("currency", "cash")
      )
      .first();

    if (!account) {
      throw new ConvexError("User cash account not found");
    }

    // Create transaction record
    const now = Date.now();
    const transactionId = await ctx.db.insert("transactions", {
      fromAccountId: account._id,
      amount: withdrawalRequest.amount,
      currency: "cash",
      type: "withdrawal",
      status: "completed",
      reference: args.tornTransactionId,
      withdrawalRequestId: args.withdrawalRequestId,
      metadata: {
        processedById: args.processedById,
        tornTransactionId: args.tornTransactionId,
      },
      processedAt: now,
      createdAt: now,
      updatedAt: now,
    });

    // Update withdrawal request status
    await ctx.db.patch(args.withdrawalRequestId, {
      status: "COMPLETED",
      transactionId: args.tornTransactionId,
      processedById: args.processedById,
      processedAt: now,
      updatedAt: now,
    });

    // Update account balance
    await ctx.db.patch(account._id, {
      balance: account.balance - withdrawalRequest.amount,
      updatedAt: now,
    });

    return transactionId;
  },
});

// Cancel withdrawal request
export const cancelWithdrawalRequest = mutation({
  args: {
    withdrawalRequestId: v.id("withdrawalRequests"),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const withdrawalRequest = await ctx.db.get(args.withdrawalRequestId);
    if (!withdrawalRequest) {
      throw new ConvexError("Withdrawal request not found");
    }

    if (withdrawalRequest.status === "COMPLETED" || withdrawalRequest.status === "CANCELLED") {
      throw new ConvexError("Cannot cancel a completed or already cancelled request");
    }

    const now = Date.now();
    
    // Update withdrawal request
    await ctx.db.patch(args.withdrawalRequestId, {
      status: "CANCELLED",
      updatedAt: now,
      processedAt: now,
    });

    // Create transaction record for cancellation
    if (withdrawalRequest.status === "ACCEPTED") {
      await ctx.db.insert("transactions", {
        amount: withdrawalRequest.amount,
        currency: "cash",
        type: "withdrawal_cancelled",
        status: "completed",
        withdrawalRequestId: args.withdrawalRequestId,
        metadata: {
          reason: args.reason || "Request cancelled",
          originalStatus: withdrawalRequest.status,
        },
        processedAt: now,
        createdAt: now,
        updatedAt: now,
      });
    }

    return true;
  },
});

// Add funds to user account (admin function)
export const addFundsToAccount = mutation({
  args: {
    userId: v.id("users"),
    amount: v.number(),
    currency: v.string(),
    reference: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await requireBankingPermission(ctx);
    const adminUser = await getCurrentUser(ctx);
    if (args.amount <= 0) {
      throw new ConvexError("Amount must be greater than 0");
    }

    // Get or create account
    let account = await ctx.db
      .query("accounts")
      .withIndex("by_user_currency", (q) => 
        q.eq("userId", args.userId).eq("currency", args.currency)
      )
      .first();

    const now = Date.now();

    if (!account) {
      // Create new account
      const accountId = await ctx.db.insert("accounts", {
        userId: args.userId,
        balance: args.amount,
        currency: args.currency,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
      account = await ctx.db.get(accountId);
    } else {
      // Update existing account
      await ctx.db.patch(account._id, {
        balance: account.balance + args.amount,
        updatedAt: now,
      });
    }

    // Create transaction record
    const transactionId = await ctx.db.insert("transactions", {
      toAccountId: account!._id,
      amount: args.amount,
      currency: args.currency,
      type: "deposit",
      status: "completed",
      reference: args.reference,
      metadata: {
        addedById: adminUser._id,
        reason: "Admin added funds",
      },
      processedAt: now,
      createdAt: now,
      updatedAt: now,
    });

    return transactionId;
  },
});

// Transfer funds between accounts
export const transferFunds = mutation({
  args: {
    fromUserId: v.id("users"),
    toUserId: v.id("users"),
    amount: v.number(),
    currency: v.string(),
    reference: v.optional(v.string()),
    initiatedById: v.id("users"),
  },
  handler: async (ctx, args) => {
    await requireBankingPermission(ctx);
    
    if (args.amount <= 0) {
      throw new ConvexError("Amount must be greater than 0");
    }

    // Get source account
    const fromAccount = await ctx.db
      .query("accounts")
      .withIndex("by_user_currency", (q) => 
        q.eq("userId", args.fromUserId).eq("currency", args.currency)
      )
      .first();

    if (!fromAccount) {
      throw new ConvexError("Source account not found");
    }

    if (fromAccount.balance < args.amount) {
      throw new ConvexError("Insufficient funds");
    }

    // Get or create destination account
    let toAccount = await ctx.db
      .query("accounts")
      .withIndex("by_user_currency", (q) => 
        q.eq("userId", args.toUserId).eq("currency", args.currency)
      )
      .first();

    const now = Date.now();

    if (!toAccount) {
      // Create destination account
      const accountId = await ctx.db.insert("accounts", {
        userId: args.toUserId,
        balance: 0,
        currency: args.currency,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
      toAccount = await ctx.db.get(accountId);
    }

    // Update balances
    await ctx.db.patch(fromAccount._id, {
      balance: fromAccount.balance - args.amount,
      updatedAt: now,
    });

    await ctx.db.patch(toAccount!._id, {
      balance: toAccount!.balance + args.amount,
      updatedAt: now,
    });

    // Create transaction record
    const transactionId = await ctx.db.insert("transactions", {
      fromAccountId: fromAccount._id,
      toAccountId: toAccount!._id,
      amount: args.amount,
      currency: args.currency,
      type: "transfer",
      status: "completed",
      reference: args.reference,
      metadata: {
        initiatedById: args.initiatedById,
      },
      processedAt: now,
      createdAt: now,
      updatedAt: now,
    });

    return transactionId;
  },
});

// Get pending withdrawals for dashboard
export const getPendingWithdrawals = query({
  args: {},
  handler: async (ctx) => {
    await requireBankingPermission(ctx);
    
    return await ctx.db
      .query("withdrawalRequests")
      .withIndex("by_status", (q) => q.eq("status", "PENDING"))
      .order("desc")
      .collect();
  },
});

// Get recent withdrawals for activity feed
export const getRecentWithdrawals = query({
  args: {},
  handler: async (ctx) => {
    const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
    
    return await ctx.db
      .query("withdrawalRequests")
      .filter((q) => q.gte(q.field("createdAt"), sevenDaysAgo))
      .order("desc")
      .take(20);
  },
});

// Action to fetch faction balance from Torn API
export const getFactionBalance = action({
  args: {},
  handler: async (ctx: any): Promise<any> => {
    // Get current user to retrieve their API key
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("Not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q: any) => q.eq("clerkId", identity.subject))
      .first();


    if (!user) {
      throw new ConvexError("User not found");
    }

    if (!user.tornApiKey) {
      throw new ConvexError("User API key not found. Please add your Torn API key in your profile settings.");
    }

    try {
      // Fetch faction data from Torn API
      const response = await fetch(
        `https://api.torn.com/faction/?selections=basic,donations&key=${user.tornApiKey}`
      );

      if (!response.ok) {
        throw new ConvexError("Failed to fetch faction data");
      }

      const data = await response.json();
      console.log("Torn API Response:", data);

      if (data.error) {
        throw new ConvexError(data.error.error || "Torn API error");
      }

      // Get the current user's Torn ID to find their personal balance
      const currentUser = user;
      
      if (!currentUser?.tornId) {
        throw new ConvexError("User Torn ID not found");
      }
      
      // Calculate totals and find user's personal balance
      let totalMoney = 0;
      let totalPoints = 0;
      let userBalance = 0;
      let userPoints = 0;
      
      if (data.donations) {
        Object.entries(data.donations).forEach(([userId, member]: [string, any]) => {
          const memberBalance = member.money_balance || 0;
          const memberPoints = member.points_balance || 0;
          
          totalMoney += memberBalance;
          totalPoints += memberPoints;
          
          // Check if this is the current user's donation
          if (parseInt(userId) === currentUser.tornId) {
            userBalance = memberBalance;
            userPoints = memberPoints;
          }
        });
      }
      
      console.log("Money calculation:", {
        totalFactionMoney: totalMoney,
        totalFactionPoints: totalPoints,
        userPersonalBalance: userBalance,
        userPersonalPoints: userPoints,
        userTornId: currentUser.tornId,
        memberCount: Object.keys(data.donations || {}).length
      });

      // Return the user's personal balance, not the total faction balance
      const result = {
        factionId: data.ID,
        factionName: data.name,
        totalMoney: totalMoney, // Total faction balance (for reference)
        availableFunds: userBalance, // User's personal balance (what they can withdraw)
        userBalance: userBalance, // Explicit user balance field
        members: Object.keys(data.members || {}).length,
        lastUpdated: Date.now(),
      };
      
      console.log("Final balance result:", result);
      
      // Mark API key as verified since we successfully used it
      if (!user.tornApiKeyVerified) {
        await ctx.db.patch(user._id, {
          tornApiKeyVerified: true,
          updatedAt: Date.now(),
        });
      }
      
      return result;
    } catch (error) {
      console.error("Faction balance fetch error:", error);
      throw new ConvexError(
        error instanceof Error ? error.message : "Failed to fetch faction balance"
      );
    }
  },
});

// Internal query to get user by Clerk ID (for actions)
export const getUserByClerkId = query({
  args: { clerkId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", args.clerkId))
      .first();
  },
});

// Debug query to check current user data
export const getCurrentUserDebug = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return { error: "Not authenticated" };
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    return {
      identity: {
        subject: identity.subject,
        name: identity.name,
        email: identity.email
      },
      user: user ? {
        id: user._id,
        username: user.username,
        clerkId: user.clerkId,
        hasTornApiKey: !!user.tornApiKey,
        tornApiKeyLength: user.tornApiKey?.length,
        permissions: user.permissions
      } : null
    };
  },
});

// Sync completed withdrawals that don't have transaction records
export const syncCompletedWithdrawals = mutation({
  args: {},
  handler: async (ctx) => {
    await requireBankingPermission(ctx);

    // Get all completed withdrawal requests that don't have corresponding transactions
    const completedWithdrawals = await ctx.db
      .query("withdrawalRequests")
      .withIndex("by_status", (q) => q.eq("status", "COMPLETED"))
      .collect();

    let syncedCount = 0;
    const errors: string[] = [];

    for (const withdrawal of completedWithdrawals) {
      try {
        // Check if transaction already exists for this withdrawal
        const existingTransaction = await ctx.db
          .query("transactions")
          .withIndex("by_withdrawal_request", (q) => q.eq("withdrawalRequestId", withdrawal._id))
          .first();

        if (existingTransaction) {
          continue; // Skip if transaction already exists
        }

        // Get user's cash account
        const account = await ctx.db
          .query("accounts")
          .withIndex("by_user_currency", (q) =>
            q.eq("userId", withdrawal.userId).eq("currency", "cash")
          )
          .first();

        if (!account) {
          errors.push(`No cash account found for withdrawal ${withdrawal._id}`);
          continue;
        }

        // Create transaction record
        const now = Date.now();
        await ctx.db.insert("transactions", {
          fromAccountId: account._id,
          amount: withdrawal.amount,
          currency: "cash",
          type: "withdrawal",
          status: "completed",
          reference: withdrawal.transactionId || `sync-${withdrawal._id}`,
          withdrawalRequestId: withdrawal._id,
          metadata: {
            syncedFromCompleted: true,
            originalProcessedAt: withdrawal.processedAt,
          },
          processedAt: withdrawal.processedAt || now,
          createdAt: withdrawal.createdAt,
          updatedAt: now,
        });

        syncedCount++;
      } catch (error) {
        errors.push(`Error syncing withdrawal ${withdrawal._id}: ${error}`);
      }
    }

    return {
      syncedCount,
      totalCompleted: completedWithdrawals.length,
      errors,
    };
  },
});

// Create a test completed transaction for debugging
export const createTestCompletedTransaction = mutation({
  args: {
    userId: v.id("users"),
    amount: v.number(),
  },
  handler: async (ctx, args) => {
    // Get or create user's cash account
    const account = await ctx.db
      .query("accounts")
      .withIndex("by_user_currency", (q) =>
        q.eq("userId", args.userId).eq("currency", "cash")
      )
      .first();

    if (!account) {
      throw new ConvexError("User cash account not found. Create one first.");
    }

    // Create completed transaction
    const now = Date.now();
    const transactionId = await ctx.db.insert("transactions", {
      fromAccountId: account._id,
      amount: args.amount,
      currency: "cash",
      type: "withdrawal",
      status: "completed",
      reference: `test-completed-${now}`,
      metadata: {
        isTest: true,
        createdForDebugging: true,
      },
      processedAt: now,
      createdAt: now,
      updatedAt: now,
    });

    return transactionId;
  },
});

// Convert accepted withdrawals to completed and create transaction records (TEMPORARY FIX)
export const convertAcceptedWithdrawalsToCompleted = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    await requireBankingPermission(ctx);

    // Get user's accepted withdrawal requests
    const acceptedWithdrawals = await ctx.db
      .query("withdrawalRequests")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("status"), "ACCEPTED"))
      .collect();

    if (acceptedWithdrawals.length === 0) {
      return {
        convertedCount: 0,
        totalAccepted: 0,
        errors: [],
      };
    }

    // Get user's cash account
    const account = await ctx.db
      .query("accounts")
      .withIndex("by_user_currency", (q) =>
        q.eq("userId", args.userId).eq("currency", "cash")
      )
      .first();

    if (!account) {
      throw new ConvexError("User cash account not found. Create one first.");
    }

    let convertedCount = 0;
    const errors: string[] = [];

    for (const withdrawal of acceptedWithdrawals) {
      try {
        // Check if transaction already exists for this withdrawal
        const existingTransaction = await ctx.db
          .query("transactions")
          .withIndex("by_withdrawal_request", (q) => q.eq("withdrawalRequestId", withdrawal._id))
          .first();

        if (existingTransaction) {
          continue; // Skip if transaction already exists
        }

        const now = Date.now();

        // Update withdrawal status to COMPLETED
        await ctx.db.patch(withdrawal._id, {
          status: "COMPLETED",
          updatedAt: now,
        });

        // Create transaction record
        await ctx.db.insert("transactions", {
          fromAccountId: account._id,
          amount: withdrawal.amount,
          currency: "cash",
          type: "withdrawal",
          status: "completed",
          reference: `manual-complete-${withdrawal._id}`,
          withdrawalRequestId: withdrawal._id,
          metadata: {
            convertedFromAccepted: true,
            originalProcessedAt: withdrawal.processedAt,
          },
          processedAt: withdrawal.processedAt || now,
          createdAt: withdrawal.createdAt,
          updatedAt: now,
        });

        convertedCount++;
      } catch (error) {
        errors.push(`Error converting withdrawal ${withdrawal._id}: ${error}`);
      }
    }

    return {
      convertedCount,
      totalAccepted: acceptedWithdrawals.length,
      errors,
    };
  },
});

// Helper function to fetch faction transactions from Torn API
async function fetchFactionTransactions(apiKey: string): Promise<Record<string, { news: string; timestamp: number }>> {
  const response = await fetch(
    `https://api.torn.com/faction/?selections=fundsnews&key=${apiKey}`
  );

  if (!response.ok) {
    throw new ConvexError(`Torn API request failed: ${response.status}`);
  }

  const data = await response.json();

  if (data.error) {
    throw new ConvexError(`Torn API Error: ${data.error.error}`);
  }

  const transactions = data.fundsnews || {};
  console.log(`Fetched ${Object.keys(transactions).length} faction transactions from Torn API`);

  // Log a few sample transactions for debugging
  const sampleIds = Object.keys(transactions).slice(0, 3);
  for (const id of sampleIds) {
    console.log(`Sample transaction ${id}: ${transactions[id].news}`);
  }

  return transactions;
}

// Helper function to get already used transaction IDs
async function getUsedTransactionIds(ctx: any): Promise<Set<string>> {
  const transactions = await ctx.db
    .query("transactions")
    .filter((q: any) => q.neq(q.field("reference"), null))
    .collect();

  return new Set(transactions.map((t: any) => t.reference).filter(Boolean));
}

// Helper function to find matching transaction
function findMatchingTransaction(
  transactions: Record<string, { news: string; timestamp: number }>,
  withdrawal: any,
  usedTransactionIds: Set<string>,
  userTornId: string
): { id: string; transaction: { news: string; timestamp: number } } | null {
  for (const [id, transaction] of Object.entries(transactions)) {
    // Skip if transaction ID already used
    if (usedTransactionIds.has(id)) continue;

    // Parse the transaction news
    const amountRegex = /\$([0-9,]+)/;
    const userIdRegex = /XID=(\d+)/;

    const amountMatch = amountRegex.exec(transaction.news);
    const userIdMatch = userIdRegex.exec(transaction.news);

    if (!amountMatch?.[1] || !userIdMatch?.[1]) continue;

    const amount = parseInt(amountMatch[1].replace(/,/g, ""), 10);
    const userId = userIdMatch[1];

    // Check if this transaction matches our withdrawal
    if (amount === withdrawal.amount && userId === userTornId) {
      return { id, transaction };
    }
  }

  return null;
}

// Verify withdrawal transactions against Torn API (PROPER PRODUCTION SOLUTION)
export const verifyWithdrawalTransactions = action({
  args: {
    userId: v.optional(v.id("users")),
  },
  handler: async (ctx: any, args: any): Promise<any> => {
    // Get current user to retrieve their API key
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("Not authenticated");
    }

    // First get user data to access API key
    const userData = await ctx.runMutation(getUserDataForVerification, {
      clerkId: identity.subject,
      targetUserId: args.userId,
    });

    if (!userData.user.tornApiKey) {
      throw new ConvexError("User API key not found. Please add your Torn API key in your profile settings.");
    }

    if (!userData.user.tornId) {
      throw new ConvexError("Torn user ID not found. Please complete your profile setup.");
    }

    // Fetch faction transactions from Torn API (only actions can do this)
    const factionTransactions = await fetchFactionTransactions(userData.user.tornApiKey);

    // Now process the withdrawals with the API data
    return await ctx.runMutation(processWithdrawalVerificationWithApiData, {
      clerkId: identity.subject,
      targetUserId: args.userId,
      factionTransactions,
    });
  },
});

// Internal mutation to get user data for verification
export const getUserDataForVerification = internalMutation({
  args: {
    clerkId: v.string(),
    targetUserId: v.optional(v.id("users"))
  },
  handler: async (ctx, args) => {
    // Get user by clerk ID
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", args.clerkId))
      .first();

    if (!user) {
      throw new ConvexError("User not found");
    }

    // Get accepted withdrawals to verify
    const targetUserId = args.targetUserId || user._id;

    const allWithdrawals = await ctx.db
      .query("withdrawalRequests")
      .withIndex("by_user", (q) => q.eq("userId", targetUserId))
      .order("desc")
      .take(50);

    const withdrawalsToVerify = allWithdrawals.filter((w: any) =>
      w.status === "ACCEPTED" &&
      // Only verify withdrawals that are at least 2 minutes old (to allow time for Torn to process)
      w.createdAt < Date.now() - 2 * 60 * 1000 &&
      // Only verify withdrawals less than 24 hours old
      w.createdAt > Date.now() - 24 * 60 * 60 * 1000
    );

    return {
      user,
      withdrawalsToVerify,
    };
  },
});

// Helper function to process withdrawal transaction (extracted to avoid circular references)
async function processWithdrawalTransactionHelper(
  ctx: any,
  args: {
    withdrawalRequestId: string;
    tornTransactionId: string;
    processedById: string;
  }
) {
  // Get the withdrawal request
  const withdrawalRequest = await ctx.db.get(args.withdrawalRequestId);
  if (!withdrawalRequest) {
    throw new ConvexError("Withdrawal request not found");
  }

  if (withdrawalRequest.status !== "ACCEPTED") {
    throw new ConvexError("Withdrawal request must be ACCEPTED to process transaction");
  }

  // Get user's cash account
  const account = await ctx.db
    .query("accounts")
    .withIndex("by_user_currency", (q: any) =>
      q.eq("userId", withdrawalRequest.userId).eq("currency", "cash")
    )
    .first();

  if (!account) {
    throw new ConvexError("User cash account not found");
  }

  // Create transaction record
  const now = Date.now();
  const transactionId = await ctx.db.insert("transactions", {
    fromAccountId: account._id,
    amount: withdrawalRequest.amount,
    currency: "cash",
    type: "withdrawal",
    status: "completed",
    reference: args.tornTransactionId,
    withdrawalRequestId: args.withdrawalRequestId,
    metadata: {
      processedById: args.processedById,
      tornTransactionId: args.tornTransactionId,
    },
    processedAt: now,
    createdAt: now,
    updatedAt: now,
  });

  // Update account balance
  await ctx.db.patch(account._id, {
    balance: account.balance - withdrawalRequest.amount,
    updatedAt: now,
  });

  return transactionId;
}

// Helper function to update withdrawal request status (extracted to avoid circular references)
async function updateWithdrawalRequestStatusHelper(
  ctx: any,
  args: {
    requestId: string;
    status: string;
    processedByBotDiscordId?: string;
    processedByBotDiscordTag?: string;
    transactionId?: string;
    processedById?: string;
  }
) {
  const now = Date.now();
  const updateData: any = {
    status: args.status,
    updatedAt: now,
    processedAt: now,
  };

  if (args.processedById) {
    updateData.processedById = args.processedById;
  }

  if (args.processedByBotDiscordId) {
    updateData.processedByBotDiscordId = args.processedByBotDiscordId;
  }

  if (args.processedByBotDiscordTag) {
    updateData.processedByBotDiscordTag = args.processedByBotDiscordTag;
  }

  if (args.transactionId) {
    updateData.transactionId = args.transactionId;
  }

  return await ctx.db.patch(args.requestId, updateData);
}

// Helper function to get users with accepted withdrawals (extracted to avoid circular references)
async function getUsersWithAcceptedWithdrawalsHelper(ctx: any) {
  // Get all accepted withdrawal requests
  const acceptedWithdrawals = await ctx.db
    .query("withdrawalRequests")
    .filter((q: any) => q.eq(q.field("status"), "ACCEPTED"))
    .collect();

  // Get unique user IDs
  const userIds = [...new Set(acceptedWithdrawals.map((w: any) => w.userId))];

  // Get users with API keys
  const users = [];
  for (const userId of userIds) {
    const user = await ctx.db.get(userId);
    if (user && user.tornApiKey && user.tornId) {
      users.push(user);
    }
  }

  return users;
}

// Helper function to get expired withdrawals (extracted to avoid circular references)
async function getExpiredWithdrawalsHelper(ctx: any, cutoffTime: number) {
  return await ctx.db
    .query("withdrawalRequests")
    .filter((q: any) =>
      q.and(
        q.or(
          q.eq(q.field("status"), "PENDING"),
          q.eq(q.field("status"), "ACCEPTED")
        ),
        q.lt(q.field("createdAt"), cutoffTime)
      )
    )
    .collect();
}

// Helper function to update user's Torn data (extracted to avoid circular references)
async function updateUserTornDataHelper(
  ctx: any,
  args: {
    userId: string;
    tornUserId: number;
    tornUsername: string;
    tornLevel: number;
    factionId?: number | null;
    factionName?: string | null;
  }
) {
  await ctx.db.patch(args.userId, {
    tornId: args.tornUserId,
    username: args.tornUsername,
    level: args.tornLevel,
    factionId: args.factionId,
    factionName: args.factionName,
    faction: args.factionName,
    tornApiKeyVerified: true,
    tornApiKeyStatus: 'verified',
    updatedAt: Date.now(),
  });
}

// Internal mutation to process verification with API data
export const processWithdrawalVerificationWithApiData = internalMutation({
  args: {
    clerkId: v.string(),
    targetUserId: v.optional(v.id("users")),
    factionTransactions: v.any(), // The API response data
  },
  handler: async (ctx, args) => {
    // Get user by clerk ID
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", args.clerkId))
      .first();

    if (!user) {
      throw new ConvexError("User not found");
    }

    const results = {
      total: 0,
      verified: 0,
      cancelled: 0,
      errors: [] as string[],
    };

    try {
      // Get accepted withdrawals to verify
      const targetUserId = args.targetUserId || user._id;

      const allWithdrawals = await ctx.db
        .query("withdrawalRequests")
        .withIndex("by_user", (q) => q.eq("userId", targetUserId))
        .order("desc")
        .take(50);

      const withdrawalsToVerify = allWithdrawals.filter((w: any) =>
        w.status === "ACCEPTED" &&
        // Only verify withdrawals that are at least 2 minutes old (to allow time for Torn to process)
        w.createdAt < Date.now() - 2 * 60 * 1000 &&
        // Only verify withdrawals less than 24 hours old
        w.createdAt > Date.now() - 24 * 60 * 60 * 1000
      );

      results.total = withdrawalsToVerify.length;

      if (withdrawalsToVerify.length === 0) {
        return results;
      }

      const usedTransactionIds = new Set<string>();

      // Process each withdrawal
      for (const withdrawal of withdrawalsToVerify) {
        try {
          console.log(`Processing withdrawal ${withdrawal._id}: $${withdrawal.amount} for user ${user.tornId}`);

          const matchingTransaction = findMatchingTransaction(
            args.factionTransactions,
            withdrawal,
            usedTransactionIds,
            user.tornId!.toString()
          );

          if (matchingTransaction) {
            // Mark as completed and create transaction record
            await processWithdrawalTransactionHelper(ctx, {
              withdrawalRequestId: withdrawal._id,
              tornTransactionId: `torn-tx-${matchingTransaction.id}`,
              processedById: user._id,
            });

            // Update withdrawal status to completed
            await updateWithdrawalRequestStatusHelper(ctx, {
              requestId: withdrawal._id,
              status: "COMPLETED",
              transactionId: matchingTransaction.id,
            });

            usedTransactionIds.add(matchingTransaction.id);
            results.verified++;
          } else {
            // No matching transaction found - mark as cancelled
            await updateWithdrawalRequestStatusHelper(ctx, {
              requestId: withdrawal._id,
              status: "CANCELLED",
            });

            results.cancelled++;
          }
        } catch (error: any) {
          console.error(`Error processing withdrawal ${withdrawal._id}:`, error);
          results.errors.push(`Withdrawal ${withdrawal._id}: ${error.message}`);
        }
      }

      return results;
    } catch (error: any) {
      console.error("Fatal error in transaction verification:", error);
      results.errors.push(`Fatal error: ${error.message}`);
      return results;
    }
  },
});

// Helper function to get users with accepted withdrawals
export const getUsersWithAcceptedWithdrawals = internalQuery({
  args: {},
  handler: async (ctx) => {
    // Get all accepted withdrawal requests
    const acceptedWithdrawals = await ctx.db
      .query("withdrawalRequests")
      .filter((q) => q.eq(q.field("status"), "ACCEPTED"))
      .collect();

    // Get unique user IDs
    const userIds = [...new Set(acceptedWithdrawals.map(w => w.userId))];

    // Get users with API keys
    const users = [];
    for (const userId of userIds) {
      const user = await ctx.db.get(userId);
      if (user && user.tornApiKey && user.tornId) {
        users.push(user);
      }
    }

    return users;
  },
});

// Helper function to get expired withdrawals
export const getExpiredWithdrawals = internalQuery({
  args: {
    cutoffTime: v.number(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("withdrawalRequests")
      .filter((q) =>
        q.and(
          q.or(
            q.eq(q.field("status"), "PENDING"),
            q.eq(q.field("status"), "ACCEPTED")
          ),
          q.lt(q.field("createdAt"), args.cutoffTime)
        )
      )
      .collect();
  },
});

// Helper function to verify withdrawals for a specific user
async function verifyWithdrawalsForUser(ctx: any, user: any) {
  const results = {
    total: 0,
    verified: 0,
    cancelled: 0,
    errors: [] as string[],
  };

  try {
    // Get user's accepted withdrawals directly from database
    const acceptedWithdrawals = await ctx.db
      .query("withdrawalRequests")
      .withIndex("by_user", (q: any) => q.eq("userId", user._id))
      .order("desc")
      .take(50);

    const withdrawalsToVerify = acceptedWithdrawals.filter((w: any) =>
      w.status === "ACCEPTED" &&
      // Only verify withdrawals that are at least 2 minutes old (to allow time for Torn to process)
      w.createdAt < Date.now() - 2 * 60 * 1000 &&
      // Only verify withdrawals less than 24 hours old
      w.createdAt > Date.now() - 24 * 60 * 60 * 1000
    );

    results.total = withdrawalsToVerify.length;

    if (withdrawalsToVerify.length === 0) {
      return results;
    }

    // Fetch faction transactions from Torn API
    const factionTransactions = await fetchFactionTransactions(user.tornApiKey);

    // Get already used transaction IDs to avoid duplicates
    const usedTransactionIds = await getUsedTransactionIds(ctx);

    // Process each withdrawal
    for (const withdrawal of withdrawalsToVerify) {
      try {
        const matchingTransaction = findMatchingTransaction(
          factionTransactions,
          withdrawal,
          usedTransactionIds,
          user.tornId.toString()
        );

        if (matchingTransaction) {
          // Mark as completed and create transaction record
          await ctx.runMutation(processWithdrawalTransaction, {
            withdrawalRequestId: withdrawal._id,
            tornTransactionId: matchingTransaction.id,
            processedById: user._id,
          });

          results.verified++;
          usedTransactionIds.add(matchingTransaction.id); // Prevent reuse in this batch
        } else {
          // Mark as cancelled if no matching transaction found
          await ctx.runMutation(updateWithdrawalRequestStatusInternal, {
            requestId: withdrawal._id,
            status: "CANCELLED",
          });

          results.cancelled++;
        }

        // Add delay to avoid API rate limits
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        const errorMessage = `Error processing withdrawal ${withdrawal._id}: ${error}`;
        results.errors.push(errorMessage);
        console.error(errorMessage);
      }
    }

    return results;
  } catch (error) {
    const errorMessage = `Error verifying withdrawals for user ${user._id}: ${error}`;
    results.errors.push(errorMessage);
    console.error(errorMessage);
    return results;
  }
}

// Cron job for automatic transaction verification (runs every 10 minutes)
export const runTransactionVerificationCron = action({
  args: {},
  handler: async (ctx) => {
    console.log("[TransactionVerificationCron] Starting automatic transaction verification...");

    const overallResults = {
      totalUsers: 0,
      totalWithdrawals: 0,
      totalVerified: 0,
      totalCancelled: 0,
      errors: [] as string[],
    };

    try {
      // Get all users with verified API keys who have accepted withdrawals
      const usersWithAcceptedWithdrawals = await getUsersWithAcceptedWithdrawalsHelper(ctx);

      overallResults.totalUsers = usersWithAcceptedWithdrawals.length;

      if (usersWithAcceptedWithdrawals.length === 0) {
        console.log("[TransactionVerificationCron] No users with accepted withdrawals found.");
        return overallResults;
      }

      console.log(`[TransactionVerificationCron] Processing ${usersWithAcceptedWithdrawals.length} users with accepted withdrawals`);

      // Process each user
      for (const user of usersWithAcceptedWithdrawals) {
        try {
          const userResults = await verifyWithdrawalsForUser(ctx, user);

          overallResults.totalWithdrawals += userResults.total;
          overallResults.totalVerified += userResults.verified;
          overallResults.totalCancelled += userResults.cancelled;
          overallResults.errors.push(...userResults.errors);

          // Add delay between users to avoid API rate limits
          await new Promise(resolve => setTimeout(resolve, 2000));
        } catch (error) {
          const errorMessage = `Error processing user ${user._id}: ${error}`;
          overallResults.errors.push(errorMessage);
          console.error(`[TransactionVerificationCron] ${errorMessage}`);
        }
      }

      console.log(`[TransactionVerificationCron] Completed: ${overallResults.totalVerified} verified, ${overallResults.totalCancelled} cancelled out of ${overallResults.totalWithdrawals} total withdrawals across ${overallResults.totalUsers} users`);

      return overallResults;
    } catch (error) {
      const errorMessage = `Fatal error in transaction verification cron: ${error}`;
      overallResults.errors.push(errorMessage);
      console.error(`[TransactionVerificationCron] ${errorMessage}`);
      return overallResults;
    }
  },
});

// Cron job for withdrawal expiration (runs every hour)
export const runWithdrawalExpirationCron = action({
  args: {},
  handler: async (ctx) => {
    console.log("[WithdrawalExpirationCron] Starting withdrawal expiration check...");

    const results = {
      total: 0,
      expired: 0,
      errors: [] as string[],
    };

    try {
      // Get all pending withdrawals older than 24 hours
      const cutoffTime = Date.now() - 24 * 60 * 60 * 1000; // 24 hours ago

      const expiredWithdrawals = await getExpiredWithdrawalsHelper(ctx, cutoffTime);

      results.total = expiredWithdrawals.length;

      if (expiredWithdrawals.length === 0) {
        console.log("[WithdrawalExpirationCron] No expired withdrawals found.");
        return results;
      }

      console.log(`[WithdrawalExpirationCron] Found ${expiredWithdrawals.length} expired withdrawals`);

      // Mark each as cancelled
      for (const withdrawal of expiredWithdrawals) {
        try {
          await updateWithdrawalRequestStatusHelper(ctx, {
            requestId: withdrawal._id,
            status: "CANCELLED",
          });

          results.expired++;
        } catch (error) {
          const errorMessage = `Error expiring withdrawal ${withdrawal._id}: ${error}`;
          results.errors.push(errorMessage);
          console.error(`[WithdrawalExpirationCron] ${errorMessage}`);
        }
      }

      console.log(`[WithdrawalExpirationCron] Completed: ${results.expired} withdrawals expired out of ${results.total} total`);

      return results;
    } catch (error) {
      const errorMessage = `Fatal error in withdrawal expiration cron: ${error}`;
      results.errors.push(errorMessage);
      console.error(`[WithdrawalExpirationCron] ${errorMessage}`);
      return results;
    }
  },
});

// Update user's Torn API key
export const updateUserApiKey = mutation({
  args: {
    apiKey: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("Not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new ConvexError("User not found");
    }

    // Basic API key validation (Torn API keys are typically 16 characters)
    if (!args.apiKey || args.apiKey.length !== 16) {
      throw new ConvexError("Invalid API key format. Torn API keys should be 16 characters long.");
    }

    // Update the user's API key
    await ctx.db.patch(user._id, {
      tornApiKey: args.apiKey,
      tornApiKeyVerified: false, // Will be verified on first successful API call
      tornApiKeyStatus: 'pending',
      updatedAt: Date.now(),
    });

    return { success: true, message: "API key updated successfully" };
  },
});

// Migration function to fix existing users with API key in wrong field
export const migrateApiKey = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("Not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new ConvexError("User not found");
    }

    // Check if user has API key in the wrong field (legacy 'apiKey' field)
    const userRecord = user as any;
    if (userRecord.apiKey && !user.tornApiKey) {
      console.log("Migrating API key from 'apiKey' to 'tornApiKey' field");
      
      // Move API key to correct field
      await ctx.db.patch(user._id, {
        tornApiKey: userRecord.apiKey,
        updatedAt: Date.now(),
      });
      
      return { success: true, message: "API key migrated successfully" };
    }

    return { success: false, message: "No migration needed" };
  },
});

// Internal mutation to mark API key as verified
export const markApiKeyAsVerified = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.userId, {
      tornApiKeyVerified: true,
      tornApiKeyStatus: 'verified',
      updatedAt: Date.now(),
    });
  },
});

// Action to refresh user permissions based on current faction role
export const refreshUserPermissions = action({
  args: {},
  handler: async (ctx: any): Promise<any> => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("Not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q: any) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new ConvexError("User not found");
    }

    // Re-validate with Torn API to get current faction position
    if (!user.tornApiKey) {
      throw new ConvexError("No API key found. Please complete onboarding first.");
    }

    try {
      console.log("Refreshing permissions for user:", user.username, "with API key length:", user.tornApiKey?.length);
      
      const response = await fetch(
        `https://api.torn.com/user/?selections=profile&key=${user.tornApiKey}`
      );
      
      if (!response.ok) {
        console.error("HTTP Error:", response.status, response.statusText);
        throw new ConvexError("Failed to validate with Torn API");
      }
      
      const data = await response.json();
      console.log("Torn API Response:", data);
      
      if (data.error) {
        console.error("Torn API Error:", data.error);
        throw new ConvexError("Torn API error: " + (data.error.error || JSON.stringify(data.error)));
      }

      // Define position mapping and permissions outside the function
      const TORN_POSITION_MAPPING = {
        "Leader": "LEADER",
        "Co-leader": "CO_LEADER", 
        "Monkey Mentor": "MONKEY_MENTOR",
        "Gorilla": "GORILLA",
        "Primate Liaison": "PRIMATE_LIAISON",
        "Baboon": "BABOON",
        "Orangutan": "ORANGUTAN", 
        "Chimpanzee": "CHIMPANZEE",
      } as const;

      const DEFAULT_ROLE_PERMISSIONS = {
        "leader": ["guides.view", "guides.manage", "dashboard.view", "announcements.view", "announcements.manage", "admin.view", "admin.users.suspend", "admin.users.recheck", "admin.users.delete", "banking.view", "banking.request", "banking.requests.manage", "target.finder.view", "target.finder.manage.shared_lists", "wars.view"],
        "co-leader": ["guides.view", "guides.manage", "dashboard.view", "announcements.view", "announcements.manage", "admin.view", "admin.users.suspend", "admin.users.recheck", "admin.users.delete", "banking.view", "banking.request", "banking.requests.manage", "target.finder.view", "target.finder.manage.shared_lists", "wars.view"],
        "monkey-mentor": ["guides.view", "guides.manage", "dashboard.view", "announcements.view", "announcements.manage", "admin.view", "admin.users.suspend", "admin.users.recheck", "admin.users.delete", "banking.view", "banking.request", "banking.requests.manage", "target.finder.view", "target.finder.manage.shared_lists", "wars.view"],
        "gorilla": ["guides.view", "dashboard.view", "announcements.view", "banking.view", "banking.request", "banking.requests.manage", "target.finder.view", "wars.view"],
        "primate-liaison": ["guides.view", "dashboard.view", "announcements.view", "banking.view", "banking.request", "banking.requests.manage", "target.finder.view", "wars.view"],
        "baboon": ["guides.view", "dashboard.view", "announcements.view", "banking.view", "banking.request", "target.finder.view", "wars.view"],
        "orangutan": ["guides.view", "dashboard.view", "announcements.view", "banking.view", "banking.request", "target.finder.view", "wars.view"],
        "chimpanzee": ["guides.view", "dashboard.view", "announcements.view", "banking.view", "banking.request", "target.finder.view", "wars.view"],
        "recruit": ["guides.view", "dashboard.view", "announcements.view", "banking.view", "banking.request", "target.finder.view", "wars.view"],
      } as const;

      // Use internal permission logic (copied from torn.ts)
      const getFactionPermissions = (factionId?: number, tornPosition?: string): string[] => {
        if (factionId !== 53100) {
          return ["banking.view", "target.finder.view", "announcements.view"];
        }
        
        const roleKey = TORN_POSITION_MAPPING[tornPosition as keyof typeof TORN_POSITION_MAPPING];
        const roleName = roleKey?.toLowerCase().replace('_', '-') || "recruit";
        
        return [...(DEFAULT_ROLE_PERMISSIONS[roleName as keyof typeof DEFAULT_ROLE_PERMISSIONS] || DEFAULT_ROLE_PERMISSIONS.recruit)];
      };

      // Calculate role level
      const getRoleLevel = (roleName: string): number => {
        const roleLevels: Record<string, number> = {
          'recruit': 1,
          'chimpanzee': 2,
          'orangutan': 3,
          'baboon': 4,
          'primate-liaison': 5,
          'gorilla': 6,
          'monkey-mentor': 7,
          'co-leader': 8,
          'leader': 9,
          'system-admin': 10,
        };
        
        return roleLevels[roleName.toLowerCase()] || 1;
      };

      const permissions = getFactionPermissions(data.faction?.faction_id, data.faction?.position);
      const roleKey = TORN_POSITION_MAPPING[data.faction?.position as keyof typeof TORN_POSITION_MAPPING];
      const roleName = roleKey?.toLowerCase().replace('_', '-') || "recruit";
      const roleLevel = getRoleLevel(roleName);

      // Call internal mutation to update user permissions
      await ctx.runMutation(updateUserPermissions, {
        userId: user._id,
        permissions,
        role: data.faction?.position || "recruit",
        roleLevel: roleLevel,
        factionId: data.faction?.faction_id,
        factionName: data.faction?.faction_name,
      });

      return { 
        success: true, 
        message: "Permissions refreshed successfully",
        newPermissions: permissions,
        role: data.faction?.position || "recruit"
      };
    } catch (error) {
      throw new ConvexError("Failed to refresh permissions: " + (error instanceof Error ? error.message : "Unknown error"));
    }
  },
});

// Internal mutation to update user permissions (called by action)
export const updateUserPermissions = mutation({
  args: {
    userId: v.id("users"),
    permissions: v.array(v.string()),
    role: v.string(),
    roleLevel: v.optional(v.number()),
    factionId: v.optional(v.number()),
    factionName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.userId, {
      permissions: args.permissions,
      role: args.role,
      roleLevel: args.roleLevel,
      factionId: args.factionId,
      factionName: args.factionName,
      updatedAt: Date.now(),
    });
  },
});

// Enhanced withdrawal request creation with rate limiting
export const createWithdrawalRequestEnhanced = mutation({
  args: {
    amount: v.number(),
    discordMessageId: v.optional(v.string()),
    initiatedByDiscordId: v.optional(v.string()),
    initiatedByDiscordTag: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    
    if (!(await canAccessBanking(ctx))) {
      throw new ConvexError("Banking access required");
    }

    // Validate input
    validateAmount(args.amount);
    
    // Enhanced rate limiting: Check if user has pending requests or made request within last minute
    const oneMinuteAgo = Date.now() - 60 * 1000;
    const recentRequests = await ctx.db
      .query("withdrawalRequests")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.gte(q.field("createdAt"), oneMinuteAgo))
      .collect();

    if (recentRequests.length > 0) {
      throw new ConvexError("Rate limit: Please wait 1 minute between withdrawal requests");
    }

    // Check for pending requests
    const pendingRequests = await ctx.db
      .query("withdrawalRequests")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("status"), "PENDING"))
      .collect();

    if (pendingRequests.length > 0) {
      throw new ConvexError("You already have a pending withdrawal request");
    }

    // Check total withdrawal count (max 50 per user)
    const totalRequests = await ctx.db
      .query("withdrawalRequests")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .collect();

    if (totalRequests.length >= 50) {
      throw new ConvexError("Maximum withdrawal limit reached (50 requests per user)");
    }

    // Validate amount constraints
    if (args.amount <= 0) {
      throw new ConvexError("Amount must be greater than 0");
    }

    if (args.amount > 100000000) { // 100M limit
      throw new ConvexError("Maximum withdrawal amount is $100,000,000");
    }

    const now = Date.now();
    return await ctx.db.insert("withdrawalRequests", {
      userId: user._id,
      amount: args.amount,
      status: "PENDING",
      discordMessageId: args.discordMessageId,
      initiatedByDiscordId: args.initiatedByDiscordId,
      initiatedByDiscordTag: args.initiatedByDiscordTag,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Action to setup user's Torn profile by fetching data from API
export const setupUserTornProfile = action({
  args: {},
  handler: async (ctx: any): Promise<any> => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("Not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q: any) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new ConvexError("User not found");
    }

    if (!user.tornApiKey) {
      throw new ConvexError("No Torn API key found. Please add your API key first.");
    }

    try {
      // Fetch user data from Torn API
      const response = await fetch(
        `https://api.torn.com/user/?selections=profile&key=${user.tornApiKey}`
      );

      if (!response.ok) {
        throw new ConvexError("Failed to fetch data from Torn API");
      }

      const data = await response.json();

      if (data.error) {
        throw new ConvexError(`Torn API Error: ${data.error.error}`);
      }

      // Update user with Torn data using internal mutation
      await updateUserTornDataHelper(ctx, {
        userId: user._id,
        tornUserId: data.player_id,
        tornUsername: data.name,
        tornLevel: data.level,
        factionId: data.faction?.faction_id || null,
        factionName: data.faction?.faction_name || null,
      });

      return {
        success: true,
        message: "Torn profile setup completed successfully!",
        userData: {
          tornId: data.player_id,
          username: data.name,
          level: data.level,
          faction: data.faction ? {
            id: data.faction.faction_id,
            name: data.faction.faction_name,
          } : null,
        },
      };
    } catch (error: any) {
      throw new ConvexError(`Failed to setup Torn profile: ${error.message}`);
    }
  },
});

// Internal mutation to update user's Torn data
export const updateUserTornData = internalMutation({
  args: {
    userId: v.id("users"),
    tornUserId: v.number(),
    tornUsername: v.string(),
    tornLevel: v.number(),
    factionId: v.optional(v.number()),
    factionName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.userId, {
      tornId: args.tornUserId,
      username: args.tornUsername,
      level: args.tornLevel,
      factionId: args.factionId,
      factionName: args.factionName,
      faction: args.factionName,
      tornApiKeyVerified: true,
      tornApiKeyStatus: 'verified',
      updatedAt: Date.now(),
    });
  },
});