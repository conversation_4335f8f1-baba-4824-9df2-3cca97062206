import crypto from "node:crypto";

const ALGORITHM = "aes-256-gcm";
const IV_LENGTH = 12; // For GCM, a 12-byte IV is recommended (NIST).

// Global variable to store the encryption secret - will be set by init function
let ENCRYPTION_KEY_HEX: string | null = null;

// Initialize the crypto module with the encryption secret from Cloudflare Worker env
export function initCrypto(encryptionSecret: string): void {
	if (
		!encryptionSecret ||
		encryptionSecret.length !== 64 ||
		!/^[0-9a-fA-F]{64}$/.test(encryptionSecret)
	) {
		throw new Error(
			"FATAL: TORN_API_KEY_ENCRYPTION_SECRET environment variable is not set, not a 64-character string, or contains non-hex characters. Application cannot start.",
		);
	}
	ENCRYPTION_KEY_HEX = encryptionSecret;
}

const getKey = (): Buffer => {
	if (!ENCRYPTION_KEY_HEX || ENCRYPTION_KEY_HEX.length !== 64) {
		throw new Error(
			"Crypto module not initialized. Call initCrypto() with TORN_API_KEY_ENCRYPTION_SECRET first.",
		);
	}
	return Buffer.from(ENCRYPTION_KEY_HEX, "hex");
};

const ENCRYPTION_PREFIX = "v1:";

export function encrypt(text: string): string {
	try {
		const key = getKey();
		const iv = crypto.randomBytes(IV_LENGTH);
		const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
		let encrypted = cipher.update(text, "utf8", "hex");
		encrypted += cipher.final("hex");
		const authTag = cipher.getAuthTag();
		// Prepend IV and authTag to the encrypted text for storage
		// IV (hex) + AuthTag (hex) + EncryptedData (hex)
		return `${ENCRYPTION_PREFIX}${iv.toString("hex")}:${authTag.toString("hex")}:${encrypted}`;
	} catch (_error) {
		// Do not log sensitive error details
		throw new Error("Failed to encrypt data.");
	}
}

export function decrypt(encryptedText: string): string {
	try {
		if (!encryptedText.startsWith(ENCRYPTION_PREFIX)) {
			throw new Error(
				"Missing encryption prefix. Data may not be encrypted or is using an unsupported version.",
			);
		}
		const raw = encryptedText.slice(ENCRYPTION_PREFIX.length);
		const key = getKey();
		const parts = raw.split(":");
		if (parts.length !== 3) {
			throw new Error(
				"Invalid encrypted text format. Expected iv:authTag:data.",
			);
		}
		const iv = Buffer.from(parts[0], "hex");
		const authTag = Buffer.from(parts[1], "hex");
		const encryptedData = parts[2];

		const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
		decipher.setAuthTag(authTag);
		let decrypted = decipher.update(encryptedData, "hex", "utf8");
		decrypted += decipher.final("utf8");
		return decrypted;
	} catch (_error) {
		// Do not log sensitive error details
		throw new Error(
			"Failed to decrypt data. The data may be corrupt or the key incorrect.",
		);
	}
}

export function isEncrypted(text: string): boolean {
	return typeof text === "string" && text.startsWith(ENCRYPTION_PREFIX);
}
