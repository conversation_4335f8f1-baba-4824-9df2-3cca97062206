import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { ExternalLink } from "lucide-react";
import type { AdminToolsProps } from "./types";

export function AdminTools({ tools }: AdminToolsProps) {
	return (
		<div>
			<h2 className="mb-4 font-semibold text-xl">Admin Tools</h2>
			<div className="grid gap-4 md:grid-cols-2">
				{tools.map((tool) => (
					<Card
						key={tool.title}
						className="group cursor-pointer transition-all hover:shadow-md"
						onClick={tool.onClick}
					>
						<CardHeader>
							<div className="flex items-center gap-3">
								<div className="rounded-lg bg-primary/10 p-2">
									<tool.icon className="h-6 w-6 text-primary" />
								</div>
								<div>
									<CardTitle className="text-lg">{tool.title}</CardTitle>
									<Badge variant="secondary" className="text-xs">
										{tool.category}
									</Badge>
								</div>
							</div>
							<CardDescription className="text-sm">
								{tool.description}
							</CardDescription>
						</CardHeader>
						<CardContent>
							<Button className="w-full gap-2" onClick={tool.onClick}>
								Open {tool.title}
								<ExternalLink className="h-4 w-4" />
							</Button>
						</CardContent>
					</Card>
				))}
			</div>
		</div>
	);
}
