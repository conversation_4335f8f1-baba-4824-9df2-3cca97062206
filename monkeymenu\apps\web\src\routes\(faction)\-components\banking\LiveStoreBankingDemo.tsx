import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useBankingLiveStoreSync } from "@/lib/banking-livestore-bridge";
import { useStore } from "@livestore/react";
import { useState } from "react";
import { toast } from "sonner";
import {
	bankingUIState$,
	factionBalance$,
	myWithdrawals$,
	pendingWithdrawals$,
} from "../../../../livestore/queries";
import { events } from "../../../../livestore/schema";
import type {
	BankingUIState,
	FactionBalance,
	WithdrawalRequest,
} from "../../../../livestore/types";

export function LiveStoreBankingDemo() {
	const { store } = useStore();
	const [withdrawalAmount, setWithdrawalAmount] = useState("");

	// Initialize LiveStore with default data
	useBankingLiveStoreSync();

	// Debug: Log store status
	console.log("🏪 LiveStore status:", { store });

	// Query real-time data from LiveStore with proper error handling
	let balance: FactionBalance | null = null;
	let myWithdrawals: WithdrawalRequest[] = [];
	let pendingWithdrawals: WithdrawalRequest[] = [];
	let uiState: BankingUIState | null = null;

	try {
		const balanceResults = store.useQuery(factionBalance$);
		console.log("💰 Balance query results:", balanceResults);
		balance =
			Array.isArray(balanceResults) && balanceResults.length > 0
				? (balanceResults[0] as FactionBalance)
				: null;

		const myWithdrawalsResults = store.useQuery(myWithdrawals$);
		console.log("📋 My withdrawals query results:", myWithdrawalsResults);
		myWithdrawals = Array.isArray(myWithdrawalsResults)
			? (myWithdrawalsResults as WithdrawalRequest[])
			: [];

		const pendingWithdrawalsResults = store.useQuery(pendingWithdrawals$);
		console.log(
			"⏳ Pending withdrawals query results:",
			pendingWithdrawalsResults,
		);
		pendingWithdrawals = Array.isArray(pendingWithdrawalsResults)
			? (pendingWithdrawalsResults as WithdrawalRequest[])
			: [];

		const uiStateResults = store.useQuery(bankingUIState$);
		console.log("🎨 UI state query results:", uiStateResults);
		uiState =
			Array.isArray(uiStateResults) && uiStateResults.length > 0
				? (uiStateResults[0] as BankingUIState)
				: null;
	} catch (error) {
		console.error("❌ Error in LiveStore queries:", error);
		return (
			<Card>
				<CardHeader>
					<CardTitle>LiveStore Error</CardTitle>
				</CardHeader>
				<CardContent>
					<p className="text-red-500">
						Error loading LiveStore data:{" "}
						{error instanceof Error ? error.message : String(error)}
					</p>
					<p className="mt-2 text-muted-foreground text-sm">
						Check the console for more details.
					</p>
				</CardContent>
			</Card>
		);
	}

	// Handle withdrawal creation
	const handleCreateWithdrawal = () => {
		const amount = Number.parseInt(withdrawalAmount);
		if (!amount || amount <= 0) {
			toast.error("Please enter a valid amount");
			return;
		}

		// Create withdrawal event in LiveStore
		store.commit(
			events.withdrawalCreated({
				id: `wr_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
				amount,
				requestedById: "current_user", // TODO: Get from auth context
				requestedByName: "Current User", // TODO: Get from auth context
				requestedByTornId: 12345, // TODO: Get from auth context
				createdAt: new Date(),
			}),
		);

		setWithdrawalAmount("");
		toast.success("Withdrawal request created!");
	};

	// Handle withdrawal status update (admin action)
	const handleUpdateStatus = (
		withdrawalId: string,
		status: "ACCEPTED" | "DECLINED",
	) => {
		store.commit(
			events.withdrawalStatusUpdated({
				id: withdrawalId,
				status,
				processedById: "admin_user", // TODO: Get from auth context
				processedByName: "Admin User", // TODO: Get from auth context
				processedAt: new Date(),
				transactionId: status === "ACCEPTED" ? `tx_${Date.now()}` : undefined,
			}),
		);

		toast.success(`Withdrawal ${status.toLowerCase()}!`);
	};

	// Update faction balance (simulate external update)
	const handleUpdateBalance = () => {
		const newMoney =
			(balance?.money || 0) + Math.floor(Math.random() * 1000000);
		const newPoints = (balance?.points || 0) + Math.floor(Math.random() * 1000);

		store.commit(
			events.factionBalanceUpdated({
				money: newMoney,
				points: newPoints,
				updatedAt: new Date(),
			}),
		);

		toast.success("Balance updated!");
	};

	return (
		<div className="space-y-6">
			<div>
				<h2 className="font-bold text-2xl">🚀 LiveStore Banking Demo</h2>
				<p className="text-muted-foreground">
					Real-time banking with local-first data sync
				</p>
			</div>

			{/* Balance Card */}
			<Card>
				<CardHeader>
					<CardTitle>Faction Balance</CardTitle>
					<CardDescription>Real-time faction funds</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="grid grid-cols-2 gap-4">
						<div>
							<Label>Money</Label>
							<div className="font-bold text-2xl text-green-600">
								${balance?.money?.toLocaleString() || "0"}
							</div>
						</div>
						<div>
							<Label>Points</Label>
							<div className="font-bold text-2xl text-blue-600">
								{balance?.points?.toLocaleString() || "0"}
							</div>
						</div>
					</div>
					<Button onClick={handleUpdateBalance} variant="outline" size="sm">
						Simulate Balance Update
					</Button>
				</CardContent>
			</Card>

			{/* Create Withdrawal */}
			<Card>
				<CardHeader>
					<CardTitle>Request Withdrawal</CardTitle>
					<CardDescription>Create a new withdrawal request</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="flex gap-2">
						<div className="flex-1">
							<Label htmlFor="amount">Amount</Label>
							<Input
								id="amount"
								type="number"
								placeholder="Enter amount..."
								value={withdrawalAmount}
								onChange={(e) => setWithdrawalAmount(e.target.value)}
							/>
						</div>
						<div className="flex items-end">
							<Button onClick={handleCreateWithdrawal}>Create Request</Button>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* My Withdrawals */}
			<Card>
				<CardHeader>
					<CardTitle>My Withdrawals ({myWithdrawals.length})</CardTitle>
					<CardDescription>Your withdrawal history</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="space-y-2">
						{myWithdrawals.length === 0 ? (
							<p className="text-muted-foreground">No withdrawals yet</p>
						) : (
							myWithdrawals.map((withdrawal) => (
								<div
									key={withdrawal.id}
									className="flex items-center justify-between rounded-lg border p-3"
								>
									<div>
										<div className="font-medium">
											${withdrawal.amount.toLocaleString()}
										</div>
										<div className="text-muted-foreground text-sm">
											{new Date(withdrawal.createdAt).toLocaleString()}
										</div>
									</div>
									<div className="flex items-center gap-2">
										<span
											className={`rounded px-2 py-1 font-medium text-xs ${
												withdrawal.status === "PENDING"
													? "bg-yellow-100 text-yellow-800"
													: withdrawal.status === "ACCEPTED"
														? "bg-green-100 text-green-800"
														: "bg-red-100 text-red-800"
											}`}
										>
											{withdrawal.status}
										</span>
									</div>
								</div>
							))
						)}
					</div>
				</CardContent>
			</Card>

			{/* Admin Panel - Pending Withdrawals */}
			<Card>
				<CardHeader>
					<CardTitle>
						Admin Panel - Pending Withdrawals ({pendingWithdrawals.length})
					</CardTitle>
					<CardDescription>Manage withdrawal requests</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="space-y-2">
						{pendingWithdrawals.length === 0 ? (
							<p className="text-muted-foreground">No pending withdrawals</p>
						) : (
							pendingWithdrawals.map((withdrawal) => (
								<div
									key={withdrawal.id}
									className="flex items-center justify-between rounded-lg border p-3"
								>
									<div>
										<div className="font-medium">
											${withdrawal.amount.toLocaleString()}
										</div>
										<div className="text-muted-foreground text-sm">
											by {withdrawal.requestedByName} •{" "}
											{new Date(withdrawal.createdAt).toLocaleString()}
										</div>
									</div>
									<div className="flex gap-2">
										<Button
											size="sm"
											variant="outline"
											className="border-green-600 text-green-600 hover:bg-green-50"
											onClick={() =>
												handleUpdateStatus(withdrawal.id, "ACCEPTED")
											}
										>
											Approve
										</Button>
										<Button
											size="sm"
											variant="outline"
											className="border-red-600 text-red-600 hover:bg-red-50"
											onClick={() =>
												handleUpdateStatus(withdrawal.id, "DECLINED")
											}
										>
											Decline
										</Button>
									</div>
								</div>
							))
						)}
					</div>
				</CardContent>
			</Card>

			{/* Debug Info */}
			<Card>
				<CardHeader>
					<CardTitle>Debug Info</CardTitle>
					<CardDescription>LiveStore state information</CardDescription>
				</CardHeader>
				<CardContent>
					<pre className="overflow-auto rounded bg-muted p-2 text-xs">
						{JSON.stringify(
							{
								balance,
								withdrawalCount: myWithdrawals.length,
								pendingCount: pendingWithdrawals.length,
								uiState,
							},
							null,
							2,
						)}
					</pre>
				</CardContent>
			</Card>
		</div>
	);
}
