import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	InputOTP,
	InputOTPGroup,
	InputOTPSlot,
} from "@/components/ui/input-otp";
import { useAppForm } from "@/components/ui/tanstack-form";
import { authClient } from "@/lib/auth-client";
import { Si<PERSON><PERSON>rd, SiGithub, SiGoogle } from "@icons-pack/react-simple-icons";
import { EmailSchema, OTPSchema } from "@monkeymenu/shared";
import { useMutation } from "@tanstack/react-query";
import { useNavigate, useRouterState } from "@tanstack/react-router";
import { ArrowLeft, Mail } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export function SignInForm() {
	const navigate = useNavigate();
	const routerState = useRouterState();
	const searchParams = new URLSearchParams(routerState.location.search);

	// Sanitize redirectTo to prevent open-redirect vulnerabilities
	const rawRedirectTo = searchParams.get("redirect") || "/";
	let redirectTo = "/";
	if (typeof window !== "undefined") {
		try {
			const currentOrigin = window.location.origin;
			const url = new URL(rawRedirectTo, currentOrigin);
			if (url.origin === currentOrigin && url.pathname.startsWith("/")) {
				redirectTo = url.pathname + url.search + url.hash;
			}
		} catch {
			// fallback to "/" if URL parsing fails
			redirectTo = "/";
		}
	} else {
		// SSR: window is undefined, use default
		redirectTo = "/";
	}

	const { isPending } = authClient.useSession();
	const [isOtpSent, setIsOtpSent] = useState(false);
	const [email, setEmail] = useState("");

	// Email OTP send mutation
	const sendOtpMutation = useMutation({
		mutationFn: async (email: string) => {
			return authClient.emailOtp.sendVerificationOtp({
				email,
				type: "sign-in",
			});
		},
		onSuccess: () => {
			setIsOtpSent(true);
			toast.success("Verification code sent to your email");
		},
		onError: (error: { error?: { message?: string } }) => {
			toast.error(error.error?.message || "Failed to send verification code");
		},
	});

	// Sign in with OTP mutation
	const verifyOtpMutation = useMutation({
		mutationFn: async ({ email, otp }: { email: string; otp: string }) => {
			return authClient.signIn.emailOtp({ email, otp });
		},
		onSuccess: async () => {
			toast.success("Sign in successful");
			// Wait for session to be available
			const session = await authClient.getSession();
			if (session) {
				navigate({
					to: redirectTo,
				});
			} else {
				toast.error("Failed to establish session");
			}
		},
		onError: (error: { error?: { message?: string } }) => {
			toast.error(error.error?.message || "Failed to verify code");
		},
	});

	// Social sign in mutations
	const socialSignInMutation = useMutation({
		mutationFn: async ({
			provider,
		}: { provider: "google" | "github" | "discord" }) => {
			// Use the redirectTo for the callbackURL to preserve destination after social auth
			const baseUrl = import.meta.env.VITE_FRONTEND_URL;
			// Use URL constructor for proper URL joining
			const callbackURL = new URL(redirectTo, baseUrl).toString();

			return authClient.signIn.social({
				provider,
				callbackURL,
			});
		},
		onSuccess: (_, variables) => {
			toast.success(`Successfully signed in with ${variables.provider}`);
		},
		onError: (error: { error?: { message?: string } }, variables) => {
			toast.error(
				error.error?.message || `Failed to sign in with ${variables.provider}`,
			);
			console.error(`${variables.provider} sign-in error:`, error);
		},
	});

	const emailForm = useAppForm({
		defaultValues: {
			email: "",
		},
		validators: {
			onChange: EmailSchema,
		},
		onSubmit: async ({ value }) => {
			setEmail(value.email);
			sendOtpMutation.mutate(value.email);
		},
	});

	const otpForm = useAppForm({
		defaultValues: {
			otp: "",
		},
		validators: {
			onChange: OTPSchema,
		},
		onSubmit: async ({ value }) => {
			verifyOtpMutation.mutate({ email, otp: value.otp });
		},
	});

	const handleGoogleLogin = () => {
		socialSignInMutation.mutate({ provider: "google" });
	};

	const handleGithubLogin = () => {
		socialSignInMutation.mutate({ provider: "github" });
	};

	const handleDiscordLogin = () => {
		socialSignInMutation.mutate({ provider: "discord" });
	};

	// Reset OTP form when verification fails
	if (verifyOtpMutation.isError) {
		otpForm.setFieldValue("otp", "");
		verifyOtpMutation.reset();
	}

	if (isPending) {
		return (
			<div className="mx-auto w-full max-w-md space-y-6 p-6">
				<div className="text-center">
					<h1 className="font-bold text-3xl">Welcome 🐒</h1>
					<p className="mt-2 text-muted-foreground">
						Choose a sign in method below
					</p>
				</div>
				{/* Render empty container while loading */}
			</div>
		);
	}

	return (
		<div className="mx-auto w-full max-w-md space-y-6 p-6">
			{/* Header */}
			<div className="text-center">
				<h1 className="font-bold text-3xl">Welcome 🐒</h1>
				<p className="mt-2 text-muted-foreground">
					{isOtpSent
						? "Enter your verification code"
						: "Choose a sign in method below"}
				</p>
			</div>

			{!isOtpSent && (
				<>
					{/* Email Form */}
					<emailForm.AppForm>
						<form
							onSubmit={(e) => {
								e.preventDefault();
								e.stopPropagation();
								void emailForm.handleSubmit();
							}}
							className="space-y-4"
						>
							<emailForm.AppField name="email">
								{(field) => (
									<field.FormItem>
										<field.FormLabel>Email Address</field.FormLabel>
										<field.FormControl>
											<div className="relative">
												<Mail className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-muted-foreground" />
												<Input
													type="email"
													value={field.state.value}
													onChange={(e) => field.handleChange(e.target.value)}
													onBlur={field.handleBlur}
													disabled={sendOtpMutation.isPending}
													placeholder="<EMAIL>"
													className="pl-10"
												/>
											</div>
										</field.FormControl>
										<field.FormMessage />
									</field.FormItem>
								)}
							</emailForm.AppField>

							<emailForm.Subscribe>
								{(state) => (
									<Button
										type="submit"
										className="w-full"
										disabled={
											!state.canSubmit ||
											state.isSubmitting ||
											sendOtpMutation.isPending
										}
									>
										{sendOtpMutation.isPending
											? "Sending code..."
											: "Send verification code"}
									</Button>
								)}
							</emailForm.Subscribe>
						</form>
					</emailForm.AppForm>

					{/* Divider */}
					<div className="relative">
						<div className="absolute inset-0 flex items-center">
							<div className="w-full border-muted border-t" />
						</div>
						<div className="relative flex justify-center text-sm">
							<span className="bg-background px-3 text-muted-foreground">
								Or continue with
							</span>
						</div>
					</div>

					{/* Social Login Buttons */}
					<div className="space-y-3">
						<Button
							type="button"
							variant="outline"
							className="w-full"
							onClick={handleGoogleLogin}
							disabled={socialSignInMutation.isPending}
						>
							<SiGoogle className="mr-2 h-4 w-4" />
							Sign in with Google
						</Button>

						<Button
							type="button"
							variant="outline"
							className="w-full"
							onClick={handleGithubLogin}
							disabled={socialSignInMutation.isPending}
						>
							<SiGithub className="mr-2 h-4 w-4" />
							Sign in with GitHub
						</Button>

						<Button
							type="button"
							variant="outline"
							className="w-full"
							onClick={handleDiscordLogin}
							disabled={socialSignInMutation.isPending}
						>
							<SiDiscord className="mr-2 h-4 w-4" />
							Sign in with Discord
						</Button>
					</div>
				</>
			)}

			{isOtpSent && (
				<otpForm.AppForm>
					<form
						onSubmit={(e) => {
							e.preventDefault();
							e.stopPropagation();
							void otpForm.handleSubmit();
						}}
						className="space-y-6"
					>
						<div className="text-center">
							<p className="text-muted-foreground text-sm">
								We've sent a verification code to{" "}
								<span className="font-medium text-foreground">{email}</span>
							</p>
						</div>

						<otpForm.AppField name="otp">
							{(field) => (
								<field.FormItem>
									<field.FormLabel className="text-center">
										Verification Code
									</field.FormLabel>
									<field.FormControl>
										<InputOTP
											value={field.state.value}
											onChange={(value) => field.handleChange(value)}
											onBlur={field.handleBlur}
											disabled={verifyOtpMutation.isPending}
											autoComplete="one-time-code"
											inputMode="numeric"
											pattern="[0-9]*"
											maxLength={6}
										>
											<InputOTPGroup className="justify-center">
												<InputOTPSlot
													key={0}
													index={0}
													className="h-12 w-12 text-xl"
												/>
												<InputOTPSlot
													key={1}
													index={1}
													className="h-12 w-12 text-xl"
												/>
												<InputOTPSlot
													key={2}
													index={2}
													className="h-12 w-12 text-xl"
												/>
												<InputOTPSlot
													key={3}
													index={3}
													className="h-12 w-12 text-xl"
												/>
												<InputOTPSlot
													key={4}
													index={4}
													className="h-12 w-12 text-xl"
												/>
												<InputOTPSlot
													key={5}
													index={5}
													className="h-12 w-12 text-xl"
												/>
											</InputOTPGroup>
										</InputOTP>
									</field.FormControl>
									<field.FormMessage />
								</field.FormItem>
							)}
						</otpForm.AppField>

						<div className="space-y-3">
							<otpForm.Subscribe>
								{(state) => (
									<Button
										type="submit"
										className="w-full"
										disabled={
											!state.canSubmit ||
											state.isSubmitting ||
											verifyOtpMutation.isPending
										}
									>
										{verifyOtpMutation.isPending
											? "Verifying..."
											: "Verify & Sign In"}
									</Button>
								)}
							</otpForm.Subscribe>

							<Button
								type="button"
								variant="ghost"
								className="w-full"
								onClick={() => {
									setIsOtpSent(false);
									otpForm.setFieldValue("otp", "");
								}}
								disabled={verifyOtpMutation.isPending}
							>
								<ArrowLeft className="mr-2 h-4 w-4" />
								Back to Email
							</Button>
						</div>
					</form>
				</otpForm.AppForm>
			)}
		</div>
	);
}
