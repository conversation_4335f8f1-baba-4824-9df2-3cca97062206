import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core";
import { user } from "./auth";

export const tornUser = sqliteTable("torn_user", {
	id: text("id")
		.primaryKey()
		.$defaultFn(() => crypto.randomUUID())
		.references(() => user.id),
	tornApiKey: text("torn_api_key"),
	tornApiKeyVerified: integer("torn_api_key_verified", {
		mode: "boolean",
	}).default(false),
	tornUserId: text("torn_user_id"),
	tornFactionId: text("torn_faction_id"),
	tornApiKeyLastCheckedAt: integer("torn_api_key_last_checked_at", {
		mode: "timestamp",
	}),
	// Access suspension tracking
	accessSuspended: integer("access_suspended", {
		mode: "boolean",
	}).default(false),
	accessSuspensionReason: text("access_suspension_reason"),
	accessSuspendedAt: integer("access_suspended_at", {
		mode: "timestamp",
	}),
	// Suspension type: "admin" for manual admin suspensions, "api_error" for automatic API error suspensions
	suspensionType: text("suspension_type", { enum: ["admin", "api_error"] }),
	lastTornApiError: integer("last_torn_api_error"),
	lastTornApiErrorAt: integer("last_torn_api_error_at", {
		mode: "timestamp",
	}),
	lastTargetFinderFetch: integer("last_target_finder_fetch", {
		mode: "timestamp",
	}),
	// Overdose detection tracking
	lastOverdoseEventTimestamp: integer("last_overdose_event_timestamp", {
		mode: "timestamp",
	}),
});
