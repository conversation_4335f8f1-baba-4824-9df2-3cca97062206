import React from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { usePermissions } from '../../hooks/usePermissions';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import {
  CheckCircle,
  XCircle,
  ExternalLink,
  AlertCircle
} from 'lucide-react';

export function AdminPanel() {
  const { canManageBanking } = usePermissions();

  // Only load admin data if user has proper permissions
  const hasAdminPermissions = canManageBanking();

  // Get pending withdrawal requests (only if admin)
  const pendingRequests = useQuery(
    api.banking.getPendingWithdrawalRequests,
    hasAdminPermissions ? {} : "skip"
  );

  // Mutation for updating withdrawal status
  const updateWithdrawalStatus = useMutation(api.banking.updateWithdrawalRequestStatus);

  const handleApproval = async (requestId: string, status: 'ACCEPTED' | 'DECLINED') => {
    try {
      const result = await updateWithdrawalStatus({
        requestId,
        status,
      });

      // If accepted and we have the user's Torn ID, redirect to pre-filled Torn page
      if (status === 'ACCEPTED' && result?.requestedByTornId && result?.amount) {
        const tornUrl = `https://www.torn.com/factions.php?step=your#/tab=controls&option=give-to-user&giveMoneyTo=${result.requestedByTornId}&money=${result.amount}`;

        // Open in new tab/window
        window.open(tornUrl, '_blank');
      }
    } catch (error) {
      console.error('Failed to update withdrawal status:', error);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace('$', '$');
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  // Show access denied if user doesn't have admin permissions
  if (!hasAdminPermissions) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <CardTitle className="text-red-800 mb-2">Access Denied</CardTitle>
          <CardDescription className="text-red-600">
            You don't have permission to access the admin panel.
          </CardDescription>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Pending Withdrawal Requests</span>
          {pendingRequests && pendingRequests.length > 0 && (
            <Badge variant="destructive" className="px-2 py-1">
              {pendingRequests.length} pending
            </Badge>
          )}
        </CardTitle>
        <CardDescription>Review and process withdrawal requests</CardDescription>
      </CardHeader>
      <CardContent>

        {pendingRequests && pendingRequests.length > 0 ? (
          <div className="space-y-4">
            {pendingRequests.map((request) => (
              <div key={request._id} className="border rounded-lg p-4 space-y-3">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="font-medium">{request.user?.username || 'Unknown User'}</div>
                    <div className="text-sm text-muted-foreground">
                      Requested {formatDate(request.createdAt)}
                    </div>
                  </div>
                  <Badge variant="secondary">
                    {formatCurrency(request.amount)}
                  </Badge>
                </div>

                <div className="flex gap-2">
                  <Button
                    onClick={() => handleApproval(request._id, 'ACCEPTED')}
                    size="sm"
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Approve
                  </Button>
                  <Button
                    onClick={() => handleApproval(request._id, 'DECLINED')}
                    size="sm"
                    variant="destructive"
                  >
                    <XCircle className="h-4 w-4 mr-1" />
                    Decline
                  </Button>
                  <Button
                    onClick={() => window.open(`https://www.torn.com/factions.php?step=your#/tab=controls&option=give-to-user`, '_blank')}
                    size="sm"
                    variant="outline"
                  >
                    <ExternalLink className="h-4 w-4 mr-1" />
                    Give Money
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            No pending withdrawal requests
          </div>
        )}
      </CardContent>
    </Card>
  );
}