import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useState, useEffect } from "react";
import { WelcomeStep } from "./WelcomeStep";
import { TornSetupStep } from "./TornSetupStep";
import { DiscordSetupStep } from "./DiscordSetupStep";
import { PermissionsSetupStep } from "./PermissionsSetupStep";
import { CompletionStep } from "./CompletionStep";
import { ONBOARDING_STEPS } from "./config";
import type { OnboardingStep } from "./types";
import { useUser, useClerk } from "@clerk/clerk-react";
import { useAction, useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";

export function Onboarding() {
  const { user } = useUser();
  const clerk = useClerk();
  const [currentStep, setCurrentStep] = useState<OnboardingStep>("welcome");
  const [isDiscordLinked, setIsDiscordLinked] = useState(false);
  const [permissions, setPermissions] = useState<string[]>([]);
  const [isProcessingPermissions, setIsProcessingPermissions] = useState(false);

  // Get current user from Convex
  const tornInfo = useQuery(api.torn.getUserTornInfo);
  
  // Action to validate and update user with Torn info
  const validateAndUpdateTornUser = useAction(api.torn.validateAndUpdateTornUser);

  // Check Discord linking status and handle redirects
  useEffect(() => {
    // Check if user has Discord linked
    const discordAccount = user?.externalAccounts?.find(
      account => account.provider === 'discord'
    );
    setIsDiscordLinked(!!discordAccount);

    // Handle redirect from Discord OAuth
    const urlParams = new URLSearchParams(window.location.search);
    const step = urlParams.get('step');
    const linkStatus = urlParams.get('linkStatus');

    if (step && linkStatus === 'success') {
      // Set current step based on redirect
      if (step === 'discord-setup') {
        setCurrentStep('discord-setup');
        setIsDiscordLinked(true);
      }
      // Clean up URL
      window.history.replaceState({}, '', window.location.pathname);
    }

    // Auto-advance if user already has torn info
    if (tornInfo && currentStep === 'welcome') {
      setCurrentStep('discord-setup');
      setPermissions(tornInfo.permissions || []);
    }
  }, [user, tornInfo, currentStep]);

  const currentStepIndex = ONBOARDING_STEPS.findIndex(
    (step) => step.id === currentStep,
  );
  const progress = ((currentStepIndex + 1) / ONBOARDING_STEPS.length) * 100;

  const handleNextStep = () => {
    const nextIndex = currentStepIndex + 1;
    if (nextIndex < ONBOARDING_STEPS.length) {
      setCurrentStep(ONBOARDING_STEPS[nextIndex].id);
    }
  };

  const handlePrevStep = () => {
    const prevIndex = currentStepIndex - 1;
    if (prevIndex >= 0) {
      setCurrentStep(ONBOARDING_STEPS[prevIndex].id);
    }
  };

  const handleApiKeySubmit = async (apiKey: string) => {
    if (!user) throw new Error("No user found");

    try {
      setIsProcessingPermissions(true);
      
      const result = await validateAndUpdateTornUser({
        apiKey: apiKey.trim(),
      });

      if (result.success) {
        setPermissions(result.user.permissions);
        setIsProcessingPermissions(false);
      } else {
        setIsProcessingPermissions(false);
        throw new Error("Failed to validate Torn account");
      }
    } catch (error) {
      setIsProcessingPermissions(false);
      console.error("Failed to validate Torn info:", error);
      throw error;
    }
  };

  const handleDiscordLink = async () => {
    try {
      // Check if Discord is already linked
      const existingDiscordAccount = user?.externalAccounts?.find(
        account => account.provider === 'discord'
      );
      
      if (existingDiscordAccount) {
        setIsDiscordLinked(true);
        return;
      }

      // Initiate Discord OAuth linking
      await clerk.user?.createExternalAccount({
        strategy: 'oauth_discord',
        redirectUrl: `${window.location.origin}/onboarding?step=discord-setup&linkStatus=success`,
      });
    } catch (error) {
      console.error("Failed to link Discord account:", error);
      throw new Error("Failed to link Discord account");
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case "welcome":
        return <WelcomeStep onNextStep={handleNextStep} onPrevStep={handlePrevStep} />;

      case "torn-setup":
        return (
          <TornSetupStep
            onNextStep={handleNextStep}
            onPrevStep={handlePrevStep}
            onApiKeySubmit={handleApiKeySubmit}
          />
        );

      case "discord-setup":
        return (
          <DiscordSetupStep
            onNextStep={handleNextStep}
            onPrevStep={handlePrevStep}
            isDiscordLinked={isDiscordLinked}
            onDiscordLink={handleDiscordLink}
          />
        );

      case "permissions-setup":
        return (
          <PermissionsSetupStep
            onNextStep={handleNextStep}
            onPrevStep={handlePrevStep}
            permissions={permissions}
            isProcessing={isProcessingPermissions}
          />
        );

      case "completion":
        return (
          <CompletionStep
            onNextStep={handleNextStep}
            onPrevStep={handlePrevStep}
            isDiscordLinked={isDiscordLinked}
          />
        );
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="font-bold text-3xl text-foreground">🚀 Account Setup</h1>
        <p className="text-muted-foreground">
          Let's get your MonkeyMenu account configured
        </p>
      </div>

      {/* Progress Bar */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">
                Step {currentStepIndex + 1} of {ONBOARDING_STEPS.length}
              </span>
              <span className="text-muted-foreground">
                {Math.round(progress)}%
              </span>
            </div>
            <Progress value={progress} className="h-2" />
            <div className="flex justify-between text-muted-foreground text-xs">
              {ONBOARDING_STEPS.map((step, index) => (
                <span
                  key={step.id}
                  className={index <= currentStepIndex ? "text-primary" : ""}
                >
                  {step.title}
                </span>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Step Content */}
      {renderStepContent()}
    </div>
  );
}