---
description: 
globs: apps/server/src/db/**
alwaysApply: false
---
# Database Development Patterns

The project uses **Drizzle ORM** with **Cloudflare D1** (SQLite) for data persistence.

## Database Configuration

### Configuration Files
- [apps/server/drizzle.config.ts](mdc:apps/server/drizzle.config.ts) - Drizzle ORM configuration
- [apps/server/wrangler.jsonc](mdc:apps/server/wrangler.jsonc) - D1 database bindings

### Schema Location
- [apps/server/src/db/schema](mdc:apps/server/src/db/schema) - Database schema definitions
- Each table is typically defined in its own file
- Export all schemas from a central index file

## Migration Workflow

### Generating Migrations
```bash
# After making schema changes
pnpm db:generate
```

This creates migration files in [apps/server/src/db/migrations](mdc:apps/server/src/db/migrations).

### Applying Migrations
```bash
# Local development
pnpm db:migrate

# Production
pnpm db:migrate:prod
```

### Database Studio
```bash
# Local database browser
pnpm db:studio

# Production database browser
pnpm db:studio:prod
```

## Schema Patterns

### Table Definition
```typescript
// Example table definition
export const users = sqliteTable('users', {
  id: text('id').primaryKey(),
  email: text('email').notNull().unique(),
  name: text('name'),
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});
```

### Relations
```typescript
// Define relations between tables
export const usersRelations = relations(users, ({ many }) => ({
  posts: many(posts),
}));
```

### Indexes
```typescript
// Add indexes for performance
export const userEmailIndex = index('user_email_idx').on(users.email);
```

## Query Patterns

### Database Connection
```typescript
// Get database instance
const db = drizzle(env.DB, { schema });
```

### Basic Queries
```typescript
// Select
const allUsers = await db.select().from(users);
const userById = await db.select().from(users).where(eq(users.id, userId));

// Insert
const newUser = await db.insert(users).values({
  email: '<EMAIL>',
  name: 'John Doe',
}).returning();

// Update
await db.update(users)
  .set({ name: 'Jane Doe' })
  .where(eq(users.id, userId));

// Delete
await db.delete(users).where(eq(users.id, userId));
```

### Complex Queries
```typescript
// Joins
const usersWithPosts = await db
  .select()
  .from(users)
  .leftJoin(posts, eq(users.id, posts.userId));

// Transactions
await db.transaction(async (tx) => {
  await tx.insert(users).values(userData);
  await tx.insert(posts).values(postData);
});
```

## Development Best Practices

### Schema Changes
1. Always generate migrations after schema changes
2. Review generated migrations before applying
3. Test migrations on local database first
4. Use descriptive names for tables and columns

### Query Optimization
- Use indexes for frequently queried columns
- Avoid N+1 queries by using joins or batch queries
- Use select only needed columns
- Consider pagination for large result sets

### Error Handling
```typescript
try {
  const result = await db.select().from(users);
  return result;
} catch (error) {
  console.error('Database error:', error);
  throw new Error('Failed to fetch users');
}
```

### Type Safety
- Use Drizzle's inferred types: `typeof users.$inferSelect`
- Define custom types for complex queries
- Use schema validation with Zod

## Seeding Data

### Seed Scripts
- [apps/server/src/db/seeds](mdc:apps/server/src/db/seeds) - Database seeding scripts
- Run seeds for local development data
- Keep production seeds separate from development seeds

### Seeding Pattern
```typescript
// Example seed script
export async function seedUsers(db: DrizzleD1Database) {
  await db.insert(users).values([
    { id: '1', email: '<EMAIL>', name: 'Admin User' },
    { id: '2', email: '<EMAIL>', name: 'Regular User' },
  ]);
}
```

## Environment Considerations

### Local Development
- Uses local D1 database
- Migrations applied with `--local` flag
- Database file stored locally

### Production
- Uses Cloudflare D1 database
- Migrations applied with `--remote` flag
- Database managed through Cloudflare dashboard
