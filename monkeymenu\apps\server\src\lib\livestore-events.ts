// Temporary stub – until official helper is exposed by @livestore/sync-cf.
// Sends a minimal PushReq over WebSocket to the LiveStore Durable Object – TODO implement.

interface LiveStoreEventPayload {
	batch: Array<{
		name: string;
		data: unknown;
		timestamp?: string;
	}>;
	payload?: unknown;
}

async function push(
	_stub: DurableObjectStub,
	_message: LiveStoreEventPayload,
): Promise<void> {
	// No-op for now. Clients rely on pull stream to catch up.
}

// Public event descriptor used by server callers.
interface LiveStoreEvent {
	name: string;
	data: unknown;
	timestamp?: string | Date;
}

/**
 * Pushes a batch of LiveStore events to the `LiveStoreWebSocketServer` Durable Object.
 * Currently this is a NO-OP until the upstream `@livestore/sync-cf` exposes
 * a stable programmatic API for server-side pushes.
 */
export async function pushLiveStoreEvents(
	env: { LIVESTORE_WEBSOCKET_SERVER: DurableObjectNamespace },
	events: LiveStoreEvent[],
): Promise<void> {
	if (events.length === 0) return;

	const id = env.LIVESTORE_WEBSOCKET_SERVER.idFromName("livestore-sync");
	const stub = env.LIVESTORE_WEBSOCKET_SERVER.get(id);

	await push(stub, {
		batch: events.map((e) => ({
			name: e.name,
			data: e.data,
			timestamp: (e.timestamp
				? new Date(e.timestamp)
				: new Date()
			).toISOString(),
		})),
		payload: { authToken: "internal" },
	});
}
