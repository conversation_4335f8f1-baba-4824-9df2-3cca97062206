import { useTheme } from "@/lib/combined-theme-provider";
import { Moon, Sun } from "lucide-react";
import { Button } from "../ui/button";

export function ThemeToggle() {
	const { theme, setTheme } = useTheme();

	const toggleTheme = () => {
		setTheme(theme === "light" ? "dark" : "light");
	};

	return (
		<Button
			variant="outline"
			size="sm"
			onClick={toggleTheme}
			className="relative h-9 w-9 p-0"
			aria-label={`Switch to ${theme === "light" ? "dark" : "light"} mode`}
		>
			<Sun
				className={`h-4 w-4 transition-all ${
					theme === "light" ? "rotate-0 scale-100" : "-rotate-90 scale-0"
				}`}
			/>
			<Moon
				className={`absolute h-4 w-4 transition-all ${
					theme === "dark" ? "rotate-0 scale-100" : "rotate-90 scale-0"
				}`}
			/>
		</Button>
	);
}
