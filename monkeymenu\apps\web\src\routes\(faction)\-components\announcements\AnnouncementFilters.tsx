import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Grid, List, Search, SlidersHorizontal } from "lucide-react";
import type { AnnouncementStats, ViewMode } from "./types";
import { ANNOUNCEMENT_CATEGORIES } from "./utils";

interface AnnouncementFiltersProps {
	searchQuery: string;
	onSearchChange: (query: string) => void;
	selectedCategory: string;
	onCategoryChange: (category: string) => void;
	viewMode: ViewMode;
	onViewModeChange: (mode: ViewMode) => void;
	announcementStats: AnnouncementStats;
}

export function AnnouncementFilters({
	searchQuery,
	onSearchChange,
	selectedCategory,
	onCategoryChange,
	viewMode,
	onViewModeChange,
	announcementStats,
}: AnnouncementFiltersProps) {
	// Get active filter count
	const activeFilters = selectedCategory !== "all" ? 1 : 0;

	return (
		<div className="flex flex-row items-center justify-between gap-2">
			{/* Search Bar */}
			<div className="relative min-w-0 flex-1">
				<Search className="absolute top-2.5 left-2 h-4 w-4 text-muted-foreground" />
				<Input
					placeholder="Search announcements..."
					value={searchQuery}
					onChange={(e) => onSearchChange(e.target.value)}
					className="pl-8"
				/>
			</div>

			{/* Filter Button */}
			<div className="flex items-center gap-2">
				<Popover>
					<PopoverTrigger asChild>
						<Button variant="outline" size="sm" className="relative">
							<SlidersHorizontal className="mr-2 h-4 w-4" />
							Filters
							{activeFilters > 0 && (
								<span className="-top-1 -right-1 absolute flex h-5 w-5 items-center justify-center rounded-full bg-blue-500 text-white text-xs">
									{activeFilters}
								</span>
							)}
						</Button>
					</PopoverTrigger>
					<PopoverContent className="w-80" align="end">
						<div className="space-y-4">
							{/* Category Filter */}
							<div>
								<label
									htmlFor="category-filter"
									className="mb-2 block font-medium text-sm"
								>
									Category
								</label>
								<Select
									value={selectedCategory}
									onValueChange={onCategoryChange}
								>
									<SelectTrigger id="category-filter">
										<SelectValue placeholder="Filter by category" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="all">
											All Categories ({announcementStats.all || 0})
										</SelectItem>
										{ANNOUNCEMENT_CATEGORIES.map((category) => {
											const count = announcementStats[category.id] || 0;
											return (
												<SelectItem key={category.id} value={category.id}>
													{category.emoji} {category.label} ({count})
												</SelectItem>
											);
										})}
									</SelectContent>
								</Select>
							</div>

							{/* View Mode */}
							<div>
								<div className="mb-2 font-medium text-sm">View Mode</div>
								<div className="flex gap-2">
									<Button
										variant={viewMode === "list" ? "default" : "outline"}
										size="sm"
										onClick={() => onViewModeChange("list")}
										className="flex-1"
									>
										<List className="mr-2 h-4 w-4" />
										List
									</Button>
									<Button
										variant={viewMode === "grid" ? "default" : "outline"}
										size="sm"
										onClick={() => onViewModeChange("grid")}
										className="flex-1"
									>
										<Grid className="mr-2 h-4 w-4" />
										Grid
									</Button>
								</div>
							</div>
						</div>
					</PopoverContent>
				</Popover>
			</div>
		</div>
	);
}
