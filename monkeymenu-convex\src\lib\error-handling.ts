// Comprehensive error handling and logging system
import React from 'react';

export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  CLIENT_ERROR = 'CLIENT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export interface ErrorContext {
  userId?: string;
  userAgent?: string;
  timestamp: number;
  url: string;
  component?: string;
  action?: string;
  additionalData?: Record<string, any>;
}

export interface AppError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  originalError?: Error;
  context: ErrorContext;
  stack?: string;
  handled: boolean;
}

// Error classification utility
export class ErrorClassifier {
  static classifyError(error: Error): { type: ErrorType; severity: ErrorSeverity } {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    // Network errors
    if (message.includes('network') || message.includes('fetch') || message.includes('timeout')) {
      return { type: ErrorType.NETWORK_ERROR, severity: ErrorSeverity.MEDIUM };
    }

    // Authentication errors
    if (message.includes('unauthorized') || message.includes('authentication')) {
      return { type: ErrorType.AUTHENTICATION_ERROR, severity: ErrorSeverity.HIGH };
    }

    // Authorization errors
    if (message.includes('forbidden') || message.includes('permission')) {
      return { type: ErrorType.AUTHORIZATION_ERROR, severity: ErrorSeverity.HIGH };
    }

    // Validation errors
    if (message.includes('validation') || message.includes('invalid')) {
      return { type: ErrorType.VALIDATION_ERROR, severity: ErrorSeverity.LOW };
    }

    // Not found errors
    if (message.includes('not found') || message.includes('404')) {
      return { type: ErrorType.NOT_FOUND_ERROR, severity: ErrorSeverity.LOW };
    }

    // Server errors
    if (message.includes('server') || message.includes('500') || message.includes('503')) {
      return { type: ErrorType.SERVER_ERROR, severity: ErrorSeverity.HIGH };
    }

    // Client errors
    if (stack.includes('at react') || stack.includes('component')) {
      return { type: ErrorType.CLIENT_ERROR, severity: ErrorSeverity.MEDIUM };
    }

    return { type: ErrorType.UNKNOWN_ERROR, severity: ErrorSeverity.MEDIUM };
  }
}

// Error logger
export class ErrorLogger {
  private static instance: ErrorLogger;
  private errors: AppError[] = [];
  private maxErrors = 1000;

  static getInstance(): ErrorLogger {
    if (!this.instance) {
      this.instance = new ErrorLogger();
    }
    return this.instance;
  }

  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getContext(component?: string, action?: string, additionalData?: Record<string, any>): ErrorContext {
    return {
      userId: this.getCurrentUserId(),
      userAgent: navigator.userAgent,
      timestamp: Date.now(),
      url: window.location.href,
      component,
      action,
      additionalData,
    };
  }

  private getCurrentUserId(): string | undefined {
    // This would integrate with your auth system
    try {
      // Placeholder - integrate with Clerk or your auth system
      return 'current-user-id';
    } catch {
      return undefined;
    }
  }

  // Log an error
  logError(
    error: Error,
    component?: string,
    action?: string,
    additionalData?: Record<string, any>
  ): AppError {
    const { type, severity } = ErrorClassifier.classifyError(error);
    const context = this.getContext(component, action, additionalData);

    const appError: AppError = {
      id: this.generateErrorId(),
      type,
      severity,
      message: error.message,
      originalError: error,
      context,
      stack: error.stack,
      handled: false,
    };

    this.errors.push(appError);

    // Keep only the last N errors
    if (this.errors.length > this.maxErrors) {
      this.errors.shift();
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      this.logToConsole(appError);
    }

    // Send to external logging service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToExternalService(appError);
    }

    return appError;
  }

  private logToConsole(error: AppError): void {
    const logLevel = this.getLogLevel(error.severity);
    const logMessage = `[${error.type}] ${error.message}`;
    
    console[logLevel](logMessage, {
      id: error.id,
      component: error.context.component,
      action: error.context.action,
      additionalData: error.context.additionalData,
      stack: error.stack,
    });
  }

  private getLogLevel(severity: ErrorSeverity): 'log' | 'warn' | 'error' {
    switch (severity) {
      case ErrorSeverity.LOW:
        return 'log';
      case ErrorSeverity.MEDIUM:
        return 'warn';
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        return 'error';
      default:
        return 'warn';
    }
  }

  private async sendToExternalService(error: AppError): Promise<void> {
    // This would send to services like Sentry, LogRocket, etc.
    try {
      // Placeholder for external logging service
      console.log('Would send to external logging service:', error.id);
    } catch (e) {
      console.error('Failed to send error to external service:', e);
    }
  }

  // Mark error as handled
  markAsHandled(errorId: string): void {
    const error = this.errors.find(e => e.id === errorId);
    if (error) {
      error.handled = true;
    }
  }

  // Get all errors
  getErrors(severity?: ErrorSeverity, handled?: boolean): AppError[] {
    return this.errors.filter(error => {
      if (severity && error.severity !== severity) return false;
      if (handled !== undefined && error.handled !== handled) return false;
      return true;
    });
  }

  // Get error statistics
  getErrorStats(): Record<string, number> {
    const stats: Record<string, number> = {};
    
    Object.values(ErrorType).forEach(type => {
      stats[type] = this.errors.filter(e => e.type === type).length;
    });
    
    return stats;
  }

  // Clear all errors
  clearErrors(): void {
    this.errors = [];
  }
}

// Error boundary for React components
export class ErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType<{ error: Error }> },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const logger = ErrorLogger.getInstance();
    logger.logError(error, 'ErrorBoundary', 'componentDidCatch', {
      errorInfo: errorInfo.componentStack,
    });
  }

  render() {
    if (this.state.hasError && this.state.error) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      return React.createElement(FallbackComponent, { error: this.state.error });
    }

    return this.props.children;
  }
}

// Default error fallback component
function DefaultErrorFallback({ error }: { error: Error }) {
  return React.createElement('div', 
    { className: "min-h-[400px] flex items-center justify-center" },
    React.createElement('div', 
      { className: "text-center" },
      React.createElement('div', { className: "text-red-500 text-6xl mb-4" }, '⚠️'),
      React.createElement('h2', { className: "text-2xl font-bold text-gray-900 mb-2" }, 'Something went wrong'),
      React.createElement('p', { className: "text-gray-600 mb-4" }, 
        'An unexpected error occurred. Please try refreshing the page.'
      ),
      React.createElement('details', { className: "text-left max-w-md mx-auto" },
        React.createElement('summary', { className: "cursor-pointer text-sm text-gray-500 hover:text-gray-700" },
          'Error details'
        ),
        React.createElement('pre', { className: "mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto" },
          error.message
        )
      ),
      React.createElement('button', {
        onClick: () => window.location.reload(),
        className: "mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
      }, 'Refresh Page')
    )
  );
}

// Hook for error handling
export function useErrorHandler() {
  const logger = ErrorLogger.getInstance();

  const handleError = React.useCallback(
    (error: Error, component?: string, action?: string, additionalData?: Record<string, any>) => {
      return logger.logError(error, component, action, additionalData);
    },
    [logger]
  );

  const handleAsyncError = React.useCallback(
    async <T>(
      asyncOperation: () => Promise<T>,
      component?: string,
      action?: string
    ): Promise<T | null> => {
      try {
        return await asyncOperation();
      } catch (error) {
        handleError(error as Error, component, action);
        return null;
      }
    },
    [handleError]
  );

  return {
    handleError,
    handleAsyncError,
    getErrors: logger.getErrors.bind(logger),
    getErrorStats: logger.getErrorStats.bind(logger),
  };
}

// Utility to wrap async functions with error handling
export function withErrorHandling<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  component?: string,
  action?: string
): T {
  return (async (...args: Parameters<T>) => {
    try {
      return await fn(...args);
    } catch (error) {
      const logger = ErrorLogger.getInstance();
      logger.logError(error as Error, component, action);
      throw error; // Re-throw to allow component-level handling
    }
  }) as T;
}