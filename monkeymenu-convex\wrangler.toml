# Wrangler configuration for Cloudflare Pages deployment
# Note: Discord Worker has its own wrangler.toml in workers/discord/

name = "monkeymenu"
compatibility_date = "2024-01-01"

# Pages configuration
[pages]
# Build configuration
build.command = "npm run build:web"
build.cwd = ""
build.destination_dir = "dist"

# Environment variables for different environments
[env.staging.vars]
# These will be set via GitHub Actions or Cloudflare dashboard
# VITE_CONVEX_URL = "https://staging-deployment.convex.cloud"
# VITE_CLERK_PUBLISHABLE_KEY = "pk_test_..."

[env.production.vars]
# These will be set via GitHub Actions or Cloudflare dashboard
# VITE_CONVEX_URL = "https://production-deployment.convex.cloud"
# VITE_CLERK_PUBLISHABLE_KEY = "pk_live_..."

# Custom domains (configure these in Cloudflare dashboard)
# staging: staging.monkeymenu.com
# production: monkeymenu.com