import { useEffect, useState } from 'react';
import { useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { useSession } from '../../hooks/useSession';

export function PermissionMigration() {
  const { convexUser } = useSession();
  const [migrationComplete, setMigrationComplete] = useState(false);
  const addMissingPermissions = useMutation(api.users.addMissingPermissions);

  useEffect(() => {
    if (convexUser && !migrationComplete) {
      const runMigration = async () => {
        try {
          const result = await addMissingPermissions({});
          console.log('🔄 Permission migration completed:', result);
          setMigrationComplete(true);
        } catch (error) {
          console.error('❌ Permission migration failed:', error);
          // Set as complete anyway to avoid infinite loop
          setMigrationComplete(true);
        }
      };

      // Check if user might need migration (missing basic permissions)
      const requiredPermissions = ['targets.view', 'wars.view', 'announcements.view'];
      const needsMigration = requiredPermissions.some(permission => 
        !convexUser.permissions?.includes(permission)
      );

      if (needsMigration) {
        runMigration();
      } else {
        setMigrationComplete(true);
      }
    }
  }, [convexUser, migrationComplete, addMissingPermissions]);

  // This component doesn't render anything
  return null;
}