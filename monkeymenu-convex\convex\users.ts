import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Internal helper function to get user by Clerk ID
export async function getUserByClerkIdInternal(ctx: any, clerkId: string) {
  return await ctx.db
    .query("users")
    .withIndex("by_clerk_id", (q: any) => q.eq("clerkId", clerkId))
    .first();
}

// Get user by Clerk ID (helper function)
export const getUserByClerkId = query({
  args: { clerkId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q: any) => q.eq("clerkId", args.clerkId))
      .first();
  },
});

// Get current user (without arguments, uses auth context)
export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }
    
    return await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();
  },
});

// Get user suspension status
export const getUserSuspensionStatus = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    if (!user) {
      return { isSuspended: false };
    }

    return {
      isSuspended: user.isSuspended || false,
      suspendedAt: user.suspendedAt || null,
      suspendedBy: user.suspendedBy || null,
      suspensionReason: user.suspensionReason || null,
      suspensionType: user.suspensionType || null,
      reason: user.suspensionReason || 'No reason provided',
    };
  },
});

// Suspend user (admin only)
export const suspendUser = mutation({
  args: {
    userId: v.id("users"),
    reason: v.string(),
    type: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const adminUser = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!adminUser) {
      throw new Error("Admin user not found");
    }

    // Check if admin has permission to suspend users
    const canSuspend = adminUser.permissions?.includes('admin.users.suspend') || 
                      adminUser.permissions?.includes('admin.all');
    
    if (!canSuspend) {
      throw new Error("Not authorized to suspend users");
    }

    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }

    // Prevent suspending users with higher role level
    if (user.roleLevel && adminUser.roleLevel && user.roleLevel >= adminUser.roleLevel) {
      throw new Error("Cannot suspend users with equal or higher role level");
    }

    await ctx.db.patch(args.userId, {
      isSuspended: true,
      suspendedAt: Date.now(),
      suspendedBy: adminUser._id,
      suspensionReason: args.reason,
      suspensionType: args.type || 'manual',
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Restore user (remove suspension)
export const restoreUser = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const adminUser = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!adminUser) {
      throw new Error("Admin user not found");
    }

    // Check if admin has permission to restore users
    const canRestore = adminUser.permissions?.includes('admin.users.suspend') || 
                      adminUser.permissions?.includes('admin.all');
    
    if (!canRestore) {
      throw new Error("Not authorized to restore users");
    }

    await ctx.db.patch(args.userId, {
      isSuspended: false,
      suspendedAt: undefined,
      suspendedBy: undefined,
      suspensionReason: undefined,
      suspensionType: undefined,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Create user with default permissions (mutation)
export const createUserWithDefaults = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("No authentication identity found");
    }

    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (existingUser) {
      return existingUser;
    }

    // Create new user with default permissions
    const now = Date.now();
    const userId = await ctx.db.insert("users", {
      clerkId: identity.subject,
      tornId: 0, // Will be updated when they link their Torn account
      username: identity.name || identity.email || "Unknown User",
      email: identity.email,
      avatar: identity.pictureUrl,
      faction: undefined,
      level: undefined,
      permissions: [
        "banking.view", 
        "targets.view", 
        "wars.view", 
        "announcements.view", 
        "guides.view", 
        "dashboard.view"
      ], // Default permissions for new users
      isActive: true,
      lastSeen: now,
      createdAt: now,
      updatedAt: now,
    });

    return await ctx.db.get(userId);
  },
});

// Create or update user
export const createOrUpdateUser = mutation({
  args: {
    clerkId: v.string(),
    tornId: v.number(),
    username: v.string(),
    email: v.optional(v.string()),
    avatar: v.optional(v.string()),
    faction: v.optional(v.string()),
    level: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q: any) => q.eq("clerkId", args.clerkId))
      .first();

    const now = Date.now();

    if (existingUser) {
      // Update existing user
      return await ctx.db.patch(existingUser._id, {
        ...args,
        lastSeen: now,
        updatedAt: now,
      });
    } else {
      // Create new user
      return await ctx.db.insert("users", {
        ...args,
        permissions: [],
        isActive: true,
        lastSeen: now,
        createdAt: now,
        updatedAt: now,
      });
    }
  },
});

// Get user by Torn ID
export const getUserByTornId = query({
  args: { tornId: v.number() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_torn_id", (q: any) => q.eq("tornId", args.tornId))
      .first();
  },
});

// Update user permissions
export const updateUserPermissions = mutation({
  args: {
    userId: v.id("users"),
    permissions: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.patch(args.userId, {
      permissions: args.permissions,
      updatedAt: Date.now(),
    });
  },
});

// Update last target finder fetch timestamp
export const updateLastTargetFinderFetch = mutation({
  args: { timestamp: v.number() },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) throw new Error("User not found");

    await ctx.db.patch(user._id, {
      lastTargetFinderFetch: args.timestamp,
      updatedAt: Date.now(),
    });
  },
});

// Update user's Torn API key
export const updateTornApiKey = mutation({
  args: {
    apiKey: v.string(),
    verified: v.boolean(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) throw new Error("User not found");

    await ctx.db.patch(user._id, {
      tornApiKey: args.apiKey, // In production, this should be encrypted
      tornApiKeyVerified: args.verified,
      updatedAt: Date.now(),
    });
  },
});

// Add missing permissions to existing users
export const addMissingPermissions = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) throw new Error("User not found");

    const requiredPermissions = [
      "banking.view", 
      "targets.view", 
      "wars.view", 
      "announcements.view", 
      "guides.view", 
      "dashboard.view"
    ];

    const currentPermissions = user.permissions || [];
    const missingPermissions = requiredPermissions.filter(p => !currentPermissions.includes(p));

    if (missingPermissions.length > 0) {
      const updatedPermissions = [...currentPermissions, ...missingPermissions];
      await ctx.db.patch(user._id, {
        permissions: updatedPermissions,
        updatedAt: Date.now(),
      });
    }

    return { added: missingPermissions };
  },
});
