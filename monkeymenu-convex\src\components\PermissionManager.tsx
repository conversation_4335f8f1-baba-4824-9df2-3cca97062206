import { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';

export function PermissionManager() {
  const [loading, setLoading] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  
  // Get current user debug info
  const userDebug = useQuery(api.adminHelpers.getCurrentUserDebug);
  
  // Mutations
  const grantFullPermissions = useMutation(api.adminHelpers.grantMeFullPermissions);
  
  const handleGrantFullPermissions = async () => {
    setLoading(true);
    try {
      const result = await grantFullPermissions();
      alert(`Success! Granted ${result.newPermissions.length} permissions. Refresh the page to see changes.`);
      window.location.reload();
    } catch (error) {
      alert(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  if (!userDebug) {
    return null; // Don't show anything while loading
  }

  if ('error' in userDebug) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <h3 className="text-lg font-semibold text-red-800 mb-2">Permission Error</h3>
        <p className="text-red-600 text-sm">{userDebug.error}</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">🔑 Permission Manager</h2>
          <p className="text-sm text-gray-600">
            Current permissions: {userDebug.user.permissions.length} 
            {userDebug.user.permissions.includes('admin.all') ? ' (Admin)' : ''}
          </p>
        </div>
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="text-sm text-blue-600 hover:text-blue-800"
        >
          {showDetails ? 'Hide Details' : 'Show Details'}
        </button>
      </div>

      {showDetails && (
        <div className="mb-4 p-4 bg-gray-50 rounded-lg">
          <h3 className="font-semibold text-gray-800 mb-2">Current Permissions:</h3>
          {userDebug.user.permissions.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {userDebug.user.permissions.map((permission) => (
                <span key={permission} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded font-mono">
                  {permission}
                </span>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-sm italic">No permissions assigned</p>
          )}
        </div>
      )}

      {/* Quick action if user doesn't have access to targets */}
      {!userDebug.user.permissions.includes('targets.view') && 
       !userDebug.user.permissions.includes('targets.manage') && 
       !userDebug.user.permissions.includes('admin.all') && (
        <div className="flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div>
            <h3 className="font-semibold text-yellow-800">Missing Target Access</h3>
            <p className="text-sm text-yellow-600">
              Click to grant full permissions and access all features including Target Finder
            </p>
          </div>
          <button
            onClick={handleGrantFullPermissions}
            disabled={loading}
            className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 disabled:opacity-50 transition-colors"
          >
            {loading ? 'Granting...' : 'Grant Full Access'}
          </button>
        </div>
      )}

      {/* Show success message if user has admin access */}
      {userDebug.user.permissions.includes('admin.all') && (
        <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
          <h3 className="font-semibold text-green-800">✅ Full Access Granted</h3>
          <p className="text-sm text-green-600">
            You have admin.all permission - all features including Target Finder are now accessible!
          </p>
        </div>
      )}
    </div>
  );
}