import { Loader } from "@/components/navbar/loader";
import { SuspensionAlert } from "@/components/ui/suspension-alert";
import { useAccessStatus } from "@/hooks/useAccessStatus";
import { authClient } from "@/lib/auth-client";
import { getRequiredFactionId } from "@/lib/required-faction";
import { type TRPCRouterOutput, trpc } from "@/lib/trpc-client";
import { useQuery } from "@tanstack/react-query";
import {
	Navigate,
	Outlet,
	createFileRoute,
	useRouter,
	useRouterState,
} from "@tanstack/react-router";
import { useEffect } from "react";
import { toast } from "sonner";

// Type definition for user profile response
type UserProfileResponse = TRPCRouterOutput["user"]["getProfile"];

// For a route group (faction), the layout route is typically associated with the group path itself.
export const Route = createFileRoute("/(faction)")({
	component: FactionLayoutComponent,
});

function FactionLayoutComponent() {
	const routerState = useRouterState();
	const router = useRouter();
	const { data: session, isPending: isSessionPending } =
		authClient.useSession();

	// Get required faction ID at runtime - will throw an error if not set
	const requiredFactionId = getRequiredFactionId();

	const {
		data: userProfile,
		isLoading: isProfileLoading,
		isError: isProfileError,
		error: profileError,
		refetch: refetchProfile,
	} = useQuery({
		...trpc.user.getProfile.queryOptions(),
		enabled: !!session?.user?.id,
	});

	// Monitor access suspension status
	const {
		accessStatus,
		isSuspended,
		isAccessStatusLoading,
		error: accessStatusError,
		refetch: refetchAccessStatus,
	} = useAccessStatus();

	// Consolidated navigation logic in a single useEffect to prevent conflicts
	useEffect(() => {
		// Only run when we have all the data we need
		if (
			!session?.user ||
			isProfileLoading ||
			isSessionPending ||
			isAccessStatusLoading
		) {
			return;
		}

		// Handle profile loading error
		if (isProfileError) {
			toast.error(
				`Failed to load user profile: ${profileError?.message || "Unknown error"}`,
			);
			router.navigate({ to: "/" });
			return;
		}

		// Handle access status error
		if (accessStatusError) {
			toast.error(
				`Failed to check access status: ${accessStatusError?.message || "Unknown error"}`,
			);
			// Don't redirect, but block access by not rendering protected content
			return;
		}

		// Handle missing profile data
		if (!userProfile) {
			return; // Will be handled by render-time checks
		}

		// Handle access suspension - show alert but don't redirect
		if (isSuspended) {
			return; // Show suspension alert in render
		}

		// Handle unverified API key - redirect to onboarding
		if (!userProfile.tornUser?.tornApiKeyVerified) {
			toast.info("Please complete onboarding to access faction features.");
			router.navigate({
				to: "/onboarding",
				search: { redirect: routerState.location.pathname },
			});
			return;
		}

		// Handle missing faction membership - redirect to onboarding
		if (!userProfile.tornUser?.tornFactionId) {
			toast.error(
				"Could not verify faction membership. Please try re-onboarding or contact support.",
			);
			router.navigate({
				to: "/onboarding",
				search: { redirect: routerState.location.pathname },
			});
			return;
		}

		// Handle wrong faction - redirect to access denied
		const userFactionId = String(userProfile.tornUser.tornFactionId);
		const factionIdToCheck = String(requiredFactionId);
		if (userFactionId !== factionIdToCheck) {
			toast.info(
				`You need to be a member of faction #${requiredFactionId} to access this section.`,
			);
			router.navigate({ to: "/access-denied" });
			return;
		}
	}, [
		session?.user,
		userProfile,
		isProfileLoading,
		isSessionPending,
		isProfileError,
		profileError,
		requiredFactionId,
		router,
		routerState.location.pathname,
		isSuspended,
		isAccessStatusLoading,
		accessStatusError,
	]);

	// Early returns for loading states
	if (isSessionPending) {
		return <Loader />;
	}

	if (!session?.user) {
		// Not authenticated, redirect to sign-in, preserving the intended destination
		return (
			<Navigate
				to="/sign-in"
				search={{ redirect: routerState.location.pathname }}
			/>
		);
	}

	if (isProfileLoading || isAccessStatusLoading) {
		return <Loader />;
	}

	if (isProfileError) {
		return null; // Navigation will be handled by useEffect
	}

	if (accessStatusError) {
		return <Loader />; // Block rendering while showing error toast
	}

	if (!userProfile) {
		return <Loader />; // Still loading or profile doesn't exist
	}

	// Final validation before rendering - these should match the useEffect conditions
	const profile = userProfile as UserProfileResponse;

	// Show suspension alert if access is suspended
	if (isSuspended && accessStatus) {
		return (
			<div className="container mx-auto p-4">
				<SuspensionAlert
					reason={accessStatus.reason}
					suspendedAt={
						accessStatus.suspendedAt ? new Date(accessStatus.suspendedAt) : null
					}
					suspensionType={accessStatus.suspensionType}
					onAccessRestored={() => {
						refetchProfile();
						refetchAccessStatus();
					}}
				/>
			</div>
		);
	}

	if (!profile?.tornUser?.tornApiKeyVerified) {
		return null; // Navigation will be handled by useEffect
	}

	if (!profile?.tornUser?.tornFactionId) {
		return null; // Navigation will be handled by useEffect
	}

	const userFactionId = String(profile.tornUser.tornFactionId);
	const factionIdToCheck = String(requiredFactionId);
	if (userFactionId !== factionIdToCheck) {
		return null; // Navigation will be handled by useEffect
	}

	// All checks passed: user is authenticated, onboarded, not suspended, and in the correct faction
	return <Outlet />;
}
