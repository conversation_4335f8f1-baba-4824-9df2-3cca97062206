// Performance configuration for production optimization

export const PERFORMANCE_CONFIG = {
  // API Configuration
  api: {
    // Query throttling (ms)
    queryThrottle: 100,
    // Maximum concurrent requests
    maxConcurrentRequests: 10,
    // Request timeout (ms)
    requestTimeout: 30000,
    // Retry attempts
    maxRetries: 3,
  },

  // Caching Configuration
  cache: {
    // Cache duration for different data types (ms)
    userProfile: 5 * 60 * 1000, // 5 minutes
    bankingData: 30 * 1000, // 30 seconds
    targets: 2 * 60 * 1000, // 2 minutes
    announcements: 1 * 60 * 1000, // 1 minute
    wars: 30 * 1000, // 30 seconds
    guides: 10 * 60 * 1000, // 10 minutes
    analytics: 5 * 60 * 1000, // 5 minutes
  },

  // Component Optimization
  components: {
    // Virtual scrolling thresholds
    virtualScrollThreshold: 100, // Items before virtual scrolling kicks in
    virtualScrollItemHeight: 80, // Default item height in pixels
    virtualScrollOverscan: 5, // Number of items to render outside viewport
    
    // Lazy loading
    lazyLoadThreshold: 0.1, // Intersection threshold for lazy loading
    
    // Debounce delays (ms)
    searchDebounce: 300,
    filterDebounce: 250,
    scrollDebounce: 16, // ~60fps
    
    // Throttle delays (ms)
    scrollThrottle: 16, // ~60fps
    resizeThrottle: 100,
  },

  // Memory Management
  memory: {
    // Maximum items to keep in memory
    maxCachedItems: 1000,
    // Cleanup interval (ms)
    cleanupInterval: 5 * 60 * 1000, // 5 minutes
    // Memory usage threshold for cleanup (bytes)
    memoryThreshold: 50 * 1024 * 1024, // 50MB
  },

  // Bundle Optimization
  bundle: {
    // Code splitting thresholds
    minChunkSize: 20000, // 20KB
    maxChunkSize: 500000, // 500KB
    
    // Prefetching
    enablePrefetch: true,
    prefetchDelay: 2000, // 2 seconds after load
  },

  // Development Settings
  development: {
    enablePerformanceLogging: true,
    enableMemoryMonitoring: true,
    enableBundleAnalysis: false, // Set to true for bundle analysis
    performanceLogLevel: 'warn' as 'log' | 'warn' | 'error',
  },

  // Production Settings
  production: {
    enablePerformanceLogging: false,
    enableMemoryMonitoring: false,
    enableBundleAnalysis: false,
    performanceLogLevel: 'error' as 'log' | 'warn' | 'error',
  },
} as const;

// Get environment-specific config
export const getPerformanceConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  
  return {
    ...PERFORMANCE_CONFIG,
    ...(isProduction ? PERFORMANCE_CONFIG.production : PERFORMANCE_CONFIG.development),
  };
};

// Performance budgets for monitoring
export const PERFORMANCE_BUDGETS = {
  // Time budgets (ms)
  firstContentfulPaint: 1500,
  largestContentfulPaint: 2500,
  firstInputDelay: 100,
  cumulativeLayoutShift: 0.1,
  
  // Size budgets (bytes)
  initialBundle: 250 * 1024, // 250KB
  totalBundle: 1 * 1024 * 1024, // 1MB
  images: 500 * 1024, // 500KB per image
  fonts: 100 * 1024, // 100KB per font
  
  // API budgets
  apiResponseTime: 1000, // 1 second
  databaseQueryTime: 500, // 500ms
} as const;

// Feature flags for performance optimizations
export const PERFORMANCE_FLAGS = {
  // Virtual scrolling
  enableVirtualScrolling: true,
  
  // Lazy loading
  enableLazyLoading: true,
  enableImageLazyLoading: true,
  
  // Caching
  enableQueryCaching: true,
  enableResultCaching: true,
  
  // Code splitting
  enableCodeSplitting: true,
  enablePreloading: true,
  
  // Compression
  enableGzipCompression: true,
  enableBrotliCompression: true,
  
  // Service worker
  enableServiceWorker: false, // Disabled for now
  
  // Resource hints
  enableResourceHints: true,
  
  // Critical CSS
  enableCriticalCSS: false, // Requires build setup
} as const;