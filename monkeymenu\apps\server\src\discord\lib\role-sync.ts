// Discord role mappings
const DISCORD_ROLE_MAPPING = {
	"system-admin": "1376990512560476202",
	leader: "1325385710945046679",
	"co-leader": "1330265362033541192",
	"monkey-mentor": "1369953001719988234",
	gorilla: "1325381427595841556",
	"primate-liaison": "1369953069017600052",
	baboon: "1325381512421445642",
	orangutan: "1325381581749354516",
	chimpanzee: "1325381637969674261",
	recruit: "1376990392725147688",
} as const;

// Basic faction role that everyone should have
const BASIC_FACTION_ROLE_ID = "1324623178806591599";

/**
 * Helper function to sync Discord roles based on MonkeyMenu role
 */
export async function syncDiscordRoles(
	guildId: string,
	userId: string,
	roleName: string | null,
	botToken: string,
	isInFaction: boolean,
): Promise<{ success: boolean; message: string }> {
	try {
		if (!guildId || !botToken) {
			return { success: false, message: "Missing guild ID or bot token" };
		}

		// First, get the user's current roles to see what needs to change
		const memberResponse = await fetch(
			`https://discord.com/api/v10/guilds/${guildId}/members/${userId}`,
			{
				headers: {
					Authorization: `Bot ${botToken}`,
					"Content-Type": "application/json",
				},
			},
		);

		if (!memberResponse.ok) {
			throw new Error(`Could not fetch user roles: ${memberResponse.status}`);
		}

		const memberData = (await memberResponse.json()) as { roles: string[] };
		const currentRoles = memberData.roles;

		// Get all faction role IDs for comparison
		const allFactionRoleIds = Object.values(DISCORD_ROLE_MAPPING) as string[];
		const currentFactionRoles = currentRoles.filter((roleId) =>
			allFactionRoleIds.includes(roleId),
		);
		const hasBasicFactionRole = currentRoles.includes(BASIC_FACTION_ROLE_ID);

		// Determine what the user should have
		const shouldHaveSpecificRole =
			isInFaction && roleName
				? DISCORD_ROLE_MAPPING[roleName as keyof typeof DISCORD_ROLE_MAPPING]
				: null;

		const actions: string[] = [];

		// Remove old faction roles if they have a different one
		for (const roleId of currentFactionRoles) {
			if (roleId !== shouldHaveSpecificRole) {
				const response = await fetch(
					`https://discord.com/api/v10/guilds/${guildId}/members/${userId}/roles/${roleId}`,
					{
						method: "DELETE",
						headers: {
							Authorization: `Bot ${botToken}`,
							"Content-Type": "application/json",
						},
					},
				);

				if (response.ok) {
					// Find role name for logging
					const roleName = Object.keys(DISCORD_ROLE_MAPPING).find(
						(key) =>
							DISCORD_ROLE_MAPPING[key as keyof typeof DISCORD_ROLE_MAPPING] ===
							roleId,
					);
					actions.push(`Removed ${roleName || "unknown"} role`);
				} else {
					const errorData = await response.text();
					throw new Error(
						`Discord API error: ${response.status} - ${errorData}`,
					);
				}
			}
		}

		// Add basic faction role if not present and user is in faction
		if (isInFaction && !hasBasicFactionRole) {
			const response = await fetch(
				`https://discord.com/api/v10/guilds/${guildId}/members/${userId}/roles/${BASIC_FACTION_ROLE_ID}`,
				{
					method: "PUT",
					headers: {
						Authorization: `Bot ${botToken}`,
						"Content-Type": "application/json",
					},
				},
			);

			if (response.ok) {
				actions.push("Added faction member role");
			} else {
				const errorData = await response.text();
				throw new Error(`Discord API error: ${response.status} - ${errorData}`);
			}
		}

		// Remove basic faction role if user is not in faction
		if (!isInFaction && hasBasicFactionRole) {
			const response = await fetch(
				`https://discord.com/api/v10/guilds/${guildId}/members/${userId}/roles/${BASIC_FACTION_ROLE_ID}`,
				{
					method: "DELETE",
					headers: {
						Authorization: `Bot ${botToken}`,
						"Content-Type": "application/json",
					},
				},
			);

			if (response.ok) {
				actions.push("Removed faction member role");
			} else {
				const errorData = await response.text();
				throw new Error(`Discord API error: ${response.status} - ${errorData}`);
			}
		}

		// Add specific role if needed and mapped
		if (
			shouldHaveSpecificRole &&
			!currentRoles.includes(shouldHaveSpecificRole)
		) {
			const response = await fetch(
				`https://discord.com/api/v10/guilds/${guildId}/members/${userId}/roles/${shouldHaveSpecificRole}`,
				{
					method: "PUT",
					headers: {
						Authorization: `Bot ${botToken}`,
						"Content-Type": "application/json",
					},
				},
			);

			if (response.ok) {
				actions.push(`Added ${roleName} role`);
			} else {
				const errorData = await response.text();
				throw new Error(`Discord API error: ${response.status} - ${errorData}`);
			}
		}

		return {
			success: true,
			message:
				actions.length > 0 ? actions.join(", ") : "Roles already correct",
		};
	} catch (error) {
		console.error("Discord role sync error:", error);
		return {
			success: false,
			message: error instanceof Error ? error.message : "Unknown error",
		};
	}
}
