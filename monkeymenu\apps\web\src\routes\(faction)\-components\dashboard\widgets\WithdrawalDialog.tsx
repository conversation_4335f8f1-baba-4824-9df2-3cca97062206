import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { trpc } from "@/lib/trpc-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AlertCircle, CheckCircle, CreditCard } from "lucide-react";
import React, { useState } from "react";
import { toast } from "sonner";
import {
	MAX_WITHDRAWAL_AMOUNT,
	formatCurrency,
	formatNumberWithCommas,
	processAmountInput,
} from "../../banking/utils";

interface WithdrawalDialogProps {
	isOpen: boolean;
	onOpenChange: (open: boolean) => void;
}

export function WithdrawalDialog({
	isOpen,
	onOpenChange,
}: WithdrawalDialogProps) {
	const queryClient = useQueryClient();
	const [amount, setAmount] = useState("");
	const [formError, setFormError] = useState("");
	const [successMessage, setSuccessMessage] = useState("");

	const { data: balance, isLoading } = useQuery(
		trpc.banking.getFactionBalance.queryOptions(),
	);

	const createWithdrawal = useMutation(
		trpc.banking.createWithdrawalRequest.mutationOptions({
			onError: (err) => {
				setFormError(err.message);
				setSuccessMessage("");
				toast.error(`Failed to create withdrawal: ${err.message}`);
			},
			onSuccess: (data) => {
				setAmount("");
				setFormError("");
				setSuccessMessage(
					`Withdrawal request for ${formatCurrency(data.amount)} submitted successfully!`,
				);
				toast.success("Withdrawal request submitted successfully!");

				// Invalidate relevant queries
				queryClient.invalidateQueries({
					predicate: (query) => {
						const queryKey = query.queryKey;
						return (
							Array.isArray(queryKey) &&
							Array.isArray(queryKey[0]) &&
							queryKey[0][0] === "banking"
						);
					},
				});

				// Close dialog after a brief delay to show success message
				setTimeout(() => {
					setSuccessMessage("");
					onOpenChange(false);
				}, 2000);
			},
		}),
	);

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		setFormError("");
		setSuccessMessage("");
		const numAmount = Number.parseInt(amount, 10);

		if (Number.isNaN(numAmount) || numAmount <= 0) {
			setFormError("Please enter a valid amount");
			return;
		}

		if (numAmount > MAX_WITHDRAWAL_AMOUNT) {
			setFormError(
				`Amount cannot exceed ${formatCurrency(MAX_WITHDRAWAL_AMOUNT)}.`,
			);
			return;
		}

		if (balance?.factionBalance && numAmount > balance.factionBalance.money) {
			setFormError("Amount exceeds available balance");
			return;
		}

		createWithdrawal.mutate({ amount: numAmount });
	};

	const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const rawValue = e.target.value;
		const processedValue = processAmountInput(rawValue);

		setAmount(processedValue);
		setFormError("");
		setSuccessMessage("");
	};

	// Reset form when dialog closes
	const handleOpenChange = (open: boolean) => {
		if (!open) {
			setAmount("");
			setFormError("");
			setSuccessMessage("");
		}
		onOpenChange(open);
	};

	return (
		<Dialog open={isOpen} onOpenChange={handleOpenChange}>
			<DialogContent className="max-w-md">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<CreditCard className="h-5 w-5" />
						Request Withdrawal
					</DialogTitle>
					<DialogDescription>
						Submit a withdrawal request from the faction bank. Enter the amount
						you'd like to withdraw.
					</DialogDescription>
				</DialogHeader>
				<div className="pt-2">
					{isLoading ? (
						<div className="flex items-center justify-center py-8">
							<div className="text-muted-foreground text-sm">
								Loading balance...
							</div>
						</div>
					) : (
						<form onSubmit={handleSubmit} className="space-y-4">
							<div className="space-y-2">
								<Label htmlFor="amount">
									Amount (you can use k, m, b suffixes)
								</Label>
								<Input
									id="amount"
									type="text"
									placeholder="Enter amount (e.g., 1000000 or 1m)"
									value={amount ? formatNumberWithCommas(amount) : ""}
									onChange={handleAmountChange}
									disabled={createWithdrawal.isPending}
								/>
								{balance?.factionBalance && (
									<p className="text-muted-foreground text-xs">
										Available: {formatCurrency(balance.factionBalance.money)}
									</p>
								)}
							</div>

							{formError && (
								<Alert variant="destructive">
									<AlertCircle className="h-4 w-4" />
									<AlertDescription>{formError}</AlertDescription>
								</Alert>
							)}

							{successMessage && (
								<Alert className="border-green-500 bg-green-50 text-green-800 dark:bg-green-950 dark:text-green-200">
									<CheckCircle className="h-4 w-4" />
									<AlertDescription>{successMessage}</AlertDescription>
								</Alert>
							)}

							<div className="flex gap-2 pt-2">
								<Button
									type="button"
									variant="outline"
									onClick={() => handleOpenChange(false)}
									className="flex-1"
									disabled={createWithdrawal.isPending}
								>
									Cancel
								</Button>
								<Button
									type="submit"
									disabled={createWithdrawal.isPending}
									className="flex-1"
								>
									{createWithdrawal.isPending
										? "Submitting..."
										: "Submit Request"}
								</Button>
							</div>
						</form>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}

// Accept any renderable React node. If it's an element, we'll augment its
// onClick handler; otherwise we'll render the node inside a default Button.
interface WithdrawalDialogTriggerProps {
	children: React.ReactNode;
}

export function WithdrawalDialogTrigger({
	children,
}: WithdrawalDialogTriggerProps) {
	const [isOpen, setIsOpen] = useState(false);

	const handleOpen = () => setIsOpen(true);

	let trigger: React.ReactNode;
	if (React.isValidElement(children)) {
		// Compose onClick if the element already has one
		const existingOnClick = (
			(children as React.ReactElement).props as {
				onClick?: (e: React.MouseEvent) => void;
			}
		).onClick;
		trigger = React.cloneElement(
			children as React.ReactElement<Record<string, unknown>>,
			{
				onClick: (e: React.MouseEvent) => {
					existingOnClick?.(e);
					handleOpen();
				},
			},
		);
	} else {
		// Fallback wrapper so arbitrary content can act as a trigger
		trigger = (
			<Button type="button" variant="outline" onClick={handleOpen}>
				{children}
			</Button>
		);
	}

	return (
		<>
			{trigger}
			<WithdrawalDialog isOpen={isOpen} onOpenChange={setIsOpen} />
		</>
	);
}
