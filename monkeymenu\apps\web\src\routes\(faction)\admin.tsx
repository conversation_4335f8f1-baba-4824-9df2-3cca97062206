import { HasPermission } from "@/components/permissions/PermissionGuards";
import { PageContainer } from "@/components/ui/page-container";
import { AdminDashboard } from "@/routes/(faction)/-components/admin-dashboard/";
import { PERMISSION_NAMES } from "@monkeymenu/shared";
import { createFileRoute } from "@tanstack/react-router";

export const Route = createFileRoute("/(faction)/admin")({
	component: AdminComponent,
});

function AdminComponent() {
	return (
		<PageContainer>
			<HasPermission
				permission={PERMISSION_NAMES.ADMIN_VIEW}
				fallback={
					<div className="flex min-h-[400px] items-center justify-center">
						<div className="space-y-4 text-center">
							<div className="text-6xl">🔒</div>
							<h2 className="font-bold text-2xl">Access Denied</h2>
							<p className="max-w-md text-muted-foreground">
								Admin permissions required (Monkey Mentor or higher) to access
								this dashboard.
							</p>
						</div>
					</div>
				}
			>
				<AdminDashboard />
			</HasPermission>
		</PageContainer>
	);
}
