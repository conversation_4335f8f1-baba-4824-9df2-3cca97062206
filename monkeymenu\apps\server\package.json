{"name": "server", "private": true, "type": "module", "scripts": {"dev": "pnpm env:sync && wrangler dev --port 3000", "env:sync": "doppler secrets download --no-file --format env > .dev.vars", "env:sync:prod": "doppler run --config prd -- tsx scripts/sync-secrets.ts", "check-types": "tsc --noEmit", "format": "biome format", "lint": "biome lint", "check": "biome check", "cf:deploy": "pnpm env:sync:prod && wrangler deploy && pnpm discord:sync", "cf:typegen": "wrangler types --env-interface CloudflareBindings", "db:touch": "wrangler d1 execute monkeymenu --local --command=\"SELECT 1\"", "db:generate": "drizzle-kit generate", "db:migrate": "wrangler d1 migrations apply monkeymenu --local", "db:migrate:prod": "wrangler d1 migrations apply monkeymenu --remote", "db:studio": "drizzle-kit studio", "db:studio:prod": "ENVIRONMENT=production drizzle-kit studio", "db:seed": "wrangler d1 execute DB --local --file=scripts/seed-permissions.sql && wrangler d1 execute DB --local --file=scripts/seed-guides.sql && wrangler d1 execute DB --local --file=scripts/seed-targets.sql && wrangler d1 execute DB --local --file=scripts/seed-announcements.sql", "db:seed:prod": "pnpm env:sync:prod && wrangler d1 execute DB --remote --file=scripts/seed-permissions.sql && wrangler d1 execute DB --remote --file=scripts/seed-guides.sql && wrangler d1 execute DB --remote --file=scripts/seed-targets.sql && wrangler d1 execute DB --remote --file=scripts/seed-announcements.sql", "discord:sync": "tsx scripts/sync-discord.ts"}, "dependencies": {"@hono/trpc-server": "^0.3.4", "@libsql/client": "^0.15.8", "@livestore/sync-cf": "^0.3.1", "@monkeymenu/shared": "workspace:*", "@trpc/client": "^11.3.1", "@trpc/server": "^11.3.1", "better-auth": "^1.2.8", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "hono": "^4.7.11", "resend": "^4.5.1", "slash-create": "^6.4.1", "tweetnacl": "^1.0.3", "zod": "^3.25.46"}, "devDependencies": {"@types/node": "^22.15.29", "drizzle-kit": "^0.31.1", "tsc-alias": "^1.8.16", "tsx": "^4.19.4", "typescript": "^5.8.3", "wrangler": "^4.20.0"}}