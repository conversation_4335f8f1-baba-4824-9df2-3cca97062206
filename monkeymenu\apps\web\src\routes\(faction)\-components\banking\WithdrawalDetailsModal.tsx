import { Badge } from "@/components/ui/badge";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import type { WithdrawalDetailsModalProps } from "./types";
import { formatCurrency, formatDate, getStatusInfo } from "./utils";

export function WithdrawalDetailsModal({
	withdrawal,
	isOpen,
	onClose,
}: WithdrawalDetailsModalProps) {
	if (!withdrawal) return null;

	const statusInfo = getStatusInfo(withdrawal.withdrawal.status);

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-md">
				<DialogHeader>
					<DialogTitle>Withdrawal Details</DialogTitle>
					<DialogDescription>
						View the details and status of this withdrawal request.
					</DialogDescription>
				</DialogHeader>
				<div className="space-y-4">
					<div className="flex items-center justify-between">
						<span className="font-medium">Amount:</span>
						<span className="font-bold text-lg">
							{formatCurrency(withdrawal.withdrawal.amount)}
						</span>
					</div>
					<div className="flex items-center justify-between">
						<span className="font-medium">Status:</span>
						<Badge className={statusInfo.color}>
							{statusInfo.emoji} {statusInfo.label}
						</Badge>
					</div>
					<div className="flex items-center justify-between">
						<span className="font-medium">Requested by:</span>
						<span>{withdrawal.user.tornUsername}</span>
					</div>
					<div className="flex items-center justify-between">
						<span className="font-medium">Date:</span>
						<span>{formatDate(withdrawal.withdrawal.createdAt)}</span>
					</div>
					{withdrawal.withdrawal.updatedAt !==
						withdrawal.withdrawal.createdAt && (
						<div className="flex items-center justify-between">
							<span className="font-medium">Last Updated:</span>
							<span>{formatDate(withdrawal.withdrawal.updatedAt)}</span>
						</div>
					)}
					{withdrawal.withdrawal.processedAt && (
						<div className="flex items-center justify-between">
							<span className="font-medium">Processed:</span>
							<span>{formatDate(withdrawal.withdrawal.processedAt)}</span>
						</div>
					)}
					{withdrawal.withdrawal.transactionId && (
						<div className="flex items-center justify-between">
							<span className="font-medium">Transaction ID:</span>
							<span className="font-mono text-sm">
								{withdrawal.withdrawal.transactionId}
							</span>
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}
