import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { authClient } from "@/lib/auth-client";
import { trpc } from "@/lib/trpc-client";
import { useQuery } from "@tanstack/react-query";
import { Link, createFileRoute } from "@tanstack/react-router";
import {
	ArrowRight,
	Award,
	CreditCard,
	ExternalLink,
	Globe,
	Search,
	Shield,
	Target,
	Users,
} from "lucide-react";

export const Route = createFileRoute("/")({
	component: RouteComponent,
});

function RouteComponent() {
	const { data: session } = authClient.useSession();

	// Query user profile to check faction membership
	const { data: userProfile } = useQuery({
		...trpc.user.getProfile.queryOptions(),
		enabled: !!session?.user?.id,
	});

	const isFactionMember = userProfile?.tornUser?.tornFactionId;

	const factionFeatures = [
		{
			icon: CreditCard,
			title: "Advanced Banking System",
			description:
				"Streamlined withdrawal management with real-time tracking, approval workflows, and transparent transaction history",
			highlight: "Member Exclusive",
		},
		{
			icon: Search,
			title: "Target Intelligence",
			description:
				"Advanced target analysis and intelligence gathering tools to give our faction a strategic advantage",
			highlight: "Tactical Edge",
		},
		{
			icon: Shield,
			title: "Member Management",
			description:
				"Comprehensive member onboarding, role management, and faction administration tools",
			highlight: "Organized Leadership",
		},
		{
			icon: Users,
			title: "Communication Hub",
			description:
				"Centralized announcements, guides, and resources to keep all members informed and coordinated",
			highlight: "Stay Connected",
		},
	];

	const factionStats = [
		{
			icon: Award,
			value: "Elite",
			label: "Faction Status",
			description: "Top-tier organization",
		},
		{
			icon: Target,
			value: "Strategic",
			label: "Operations",
			description: "Coordinated warfare",
		},
		{
			icon: Globe,
			value: "24/7",
			label: "Active",
			description: "Global presence",
		},
	];

	const whyJoinUs = [
		{
			title: "Professional Organization",
			description:
				"We run our faction like a well-oiled machine with clear structure, roles, and expectations.",
		},
		{
			title: "Advanced Tools",
			description:
				"Access to custom-built faction management tools that give us a competitive edge.",
		},
		{
			title: "Active Leadership",
			description:
				"Experienced leaders who are committed to faction growth and member development.",
		},
		{
			title: "Strategic Focus",
			description:
				"Coordinated operations, intelligent target selection, and tactical superiority.",
		},
	];

	return (
		<div className="min-h-screen">
			{/* Hero Section */}
			<section className="relative overflow-hidden bg-background py-20">
				<div className="container mx-auto px-4">
					<div className="mx-auto max-w-4xl text-center">
						<h1 className="mb-6 font-bold text-5xl tracking-tight md:text-7xl">
							<span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
								Menacing Monkeys
							</span>
						</h1>

						<p className="mx-auto mb-8 max-w-2xl text-lg text-muted-foreground md:text-xl">
							A professional, well-organized faction in Torn City. We combine
							strategic gameplay with our custom MonkeyMenu platform to dominate
							the competition and support our members.
						</p>

						<div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
							{session ? (
								isFactionMember ? (
									<Button asChild size="lg" className="text-lg">
										<Link to="/dashboard">
											Access Member Portal
											<ArrowRight className="ml-2 h-5 w-5" />
										</Link>
									</Button>
								) : (
									<Button asChild size="lg" className="text-lg">
										<Link to="/onboarding">
											Complete Member Setup
											<ArrowRight className="ml-2 h-5 w-5" />
										</Link>
									</Button>
								)
							) : (
								<>
									<Button asChild size="lg" className="text-lg">
										<Link to="/sign-in">
											Member Login
											<ArrowRight className="ml-2 h-5 w-5" />
										</Link>
									</Button>
									<Button variant="outline" size="lg" asChild>
										<a
											href="https://www.torn.com/factions.php?step=profile&ID=53100"
											target="_blank"
											rel="noopener noreferrer"
										>
											Apply to Join Menacing Monkeys
											<ExternalLink className="ml-2 h-4 w-4" />
										</a>
									</Button>{" "}
								</>
							)}
						</div>
					</div>
				</div>
			</section>

			{/* Stats Section */}
			<section className="border-b bg-muted/20 py-16">
				<div className="container mx-auto px-4">
					<div className="grid gap-8 md:grid-cols-3">
						{factionStats.map((stat) => (
							<div key={stat.label} className="text-center">
								<div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
									<stat.icon className="h-8 w-8 text-primary" />
								</div>
								<div className="font-bold text-3xl">{stat.value}</div>
								<div className="font-semibold text-lg">{stat.label}</div>
								<p className="text-muted-foreground text-sm">
									{stat.description}
								</p>
							</div>
						))}
					</div>
				</div>
			</section>

			{/* Member Tools Section */}
			<section className="py-20">
				<div className="container mx-auto px-4">
					<div className="mx-auto mb-16 max-w-3xl text-center">
						<h2 className="mb-4 font-bold text-3xl md:text-4xl">
							Member-Exclusive Tools
						</h2>
						<p className="text-lg text-muted-foreground">
							Our custom-built MonkeyMenu platform gives Menacing Monkeys
							members access to advanced tools and resources that provide a
							significant competitive advantage.
						</p>
					</div>

					<div className="grid gap-8 md:grid-cols-2">
						{factionFeatures.map((feature) => (
							<Card
								key={feature.title}
								className="group transition-all hover:shadow-lg"
							>
								<CardHeader>
									<div className="flex items-center gap-4">
										<div className="rounded-lg bg-primary/10 p-3">
											<feature.icon className="h-8 w-8 text-primary" />
										</div>
										<div>
											<CardTitle className="text-xl">{feature.title}</CardTitle>
											<Badge variant="secondary" className="mt-1 text-xs">
												{feature.highlight}
											</Badge>
										</div>
									</div>
								</CardHeader>
								<CardContent>
									<CardDescription className="text-base">
										{feature.description}
									</CardDescription>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Why Join Us Section */}
			<section className="bg-muted/20 py-20">
				<div className="container mx-auto px-4">
					<div className="mx-auto mb-16 max-w-3xl text-center">
						<h2 className="mb-4 font-bold text-3xl md:text-4xl">
							Why Join Menacing Monkeys?
						</h2>
						<p className="text-lg text-muted-foreground">
							We're not just another faction. We're a professional organization
							committed to excellence, strategy, and member success.
						</p>
					</div>

					<div className="grid gap-6 md:grid-cols-2">
						{whyJoinUs.map((reason, index) => (
							<div key={reason.title} className="flex gap-4">
								<div className="flex h-8 w-8 shrink-0 items-center justify-center rounded-full bg-primary font-semibold text-primary-foreground text-sm">
									{index + 1}
								</div>
								<div>
									<h3 className="mb-2 font-semibold text-lg">{reason.title}</h3>
									<p className="text-muted-foreground">{reason.description}</p>
								</div>
							</div>
						))}
					</div>
				</div>
			</section>
		</div>
	);
}
