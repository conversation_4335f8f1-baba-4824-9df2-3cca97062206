import type {
	AmountRange,
	WithdrawalStats,
	WithdrawalStatusOption,
	WithdrawalWithUser,
} from "./types";

// Constants
export const MAX_WITHDRAWAL_AMOUNT = 9_999_999_999_999;

export const WITHDRAWAL_STATUSES: WithdrawalStatusOption[] = [
	{
		id: "PENDING",
		label: "Pending",
		emoji: "⏳",
		color: "bg-yellow-100 text-yellow-800",
	},
	{
		id: "ACCEPTED",
		label: "Approved",
		emoji: "✅",
		color: "bg-green-100 text-green-800",
	},
	{
		id: "DECLINED",
		label: "Declined",
		emoji: "❌",
		color: "bg-red-100 text-red-800",
	},
	{
		id: "COMPLETED",
		label: "Completed",
		emoji: "🎉",
		color: "bg-blue-100 text-blue-800",
	},
	{
		id: "CANCELLED",
		label: "Cancelled",
		emoji: "🚫",
		color: "bg-gray-200 text-gray-800",
	},
	{
		id: "EXPIRED",
		label: "Expired",
		emoji: "⌛",
		color: "bg-gray-100 text-gray-600",
	},
];

export const AMOUNT_RANGES: AmountRange[] = [
	{ id: "all", label: "All Amounts" },
	{ id: "small", label: "Under $1M", min: 0, max: 999999 },
	{ id: "medium", label: "$1M - $10M", min: 1000000, max: 9999999 },
	{ id: "large", label: "$10M - $100M", min: 10000000, max: 99999999 },
	{
		id: "huge",
		label: "Over $100M",
		min: 100000000,
		max: Number.POSITIVE_INFINITY,
	},
];

// Utility functions
export const formatNumberWithCommas = (value: string): string => {
	const numStr = value.replace(/\D/g, "");
	if (!numStr) return "";
	return Number.parseInt(numStr, 10).toLocaleString();
};

export const removeNonDigits = (value: string): string => {
	return value.replace(/\D/g, "");
};

export const getStatusInfo = (status: string): WithdrawalStatusOption => {
	return (
		WITHDRAWAL_STATUSES.find((s) => s.id === status) || WITHDRAWAL_STATUSES[0]
	);
};

export const formatCurrency = (amount: number): string => {
	return `$${amount.toLocaleString()}`;
};

export const formatDate = (date: string): string => {
	return new Date(date).toLocaleDateString("en-US", {
		year: "numeric",
		month: "short",
		day: "numeric",
		hour: "2-digit",
		minute: "2-digit",
	});
};

export const processAmountInput = (rawValue: string): string => {
	let processedValue = "";
	const lowerRawValue = rawValue.toLowerCase();

	if (lowerRawValue.endsWith("k")) {
		const prefix = lowerRawValue.slice(0, -1);
		const numericPrefix = prefix.replace(/\D/g, "");
		if (numericPrefix) {
			const num = Number.parseInt(numericPrefix, 10);
			if (!Number.isNaN(num) && num >= 0) {
				if (num * 1000 > MAX_WITHDRAWAL_AMOUNT) {
					processedValue = MAX_WITHDRAWAL_AMOUNT.toString();
				} else {
					processedValue = (num * 1000).toString();
				}
			} else {
				processedValue = removeNonDigits(rawValue);
			}
		} else {
			processedValue = removeNonDigits(rawValue);
		}
	} else if (lowerRawValue.endsWith("m")) {
		const prefix = lowerRawValue.slice(0, -1);
		const numericPrefix = prefix.replace(/\D/g, "");
		if (numericPrefix) {
			const num = Number.parseInt(numericPrefix, 10);
			if (!Number.isNaN(num) && num >= 0) {
				if (num * 1000000 > MAX_WITHDRAWAL_AMOUNT) {
					processedValue = MAX_WITHDRAWAL_AMOUNT.toString();
				} else {
					processedValue = (num * 1000000).toString();
				}
			} else {
				processedValue = removeNonDigits(rawValue);
			}
		} else {
			processedValue = removeNonDigits(rawValue);
		}
	} else if (lowerRawValue.endsWith("b")) {
		const prefix = lowerRawValue.slice(0, -1);
		const numericPrefix = prefix.replace(/\D/g, "");
		if (numericPrefix) {
			const num = Number.parseInt(numericPrefix, 10);
			if (!Number.isNaN(num) && num >= 0) {
				if (num * 1000000000 > MAX_WITHDRAWAL_AMOUNT) {
					processedValue = MAX_WITHDRAWAL_AMOUNT.toString();
				} else {
					processedValue = (num * 1000000000).toString();
				}
			} else {
				processedValue = removeNonDigits(rawValue);
			}
		} else {
			processedValue = removeNonDigits(rawValue);
		}
	} else {
		processedValue = removeNonDigits(rawValue);
	}

	const finalNum = Number.parseInt(processedValue, 10);
	if (!Number.isNaN(finalNum) && finalNum > MAX_WITHDRAWAL_AMOUNT) {
		processedValue = MAX_WITHDRAWAL_AMOUNT.toString();
	}

	return processedValue;
};

export const calculateWithdrawalStats = (
	withdrawals: WithdrawalWithUser[],
): WithdrawalStats => {
	if (!withdrawals) {
		return {
			total: 0,
			pending: 0,
			approved: 0,
			declined: 0,
			totalAmount: 0,
			totalWithdrawn: 0,
		};
	}

	return withdrawals.reduce(
		(acc, withdrawal) => {
			acc.total++;
			// totalAmount will sum all non-declined requests for general overview
			if (withdrawal.withdrawal.status !== "DECLINED") {
				acc.totalAmount += withdrawal.withdrawal.amount;
			}

			switch (withdrawal.withdrawal.status) {
				case "PENDING":
					acc.pending++;
					break;
				case "ACCEPTED":
					acc.approved++;
					acc.totalWithdrawn += withdrawal.withdrawal.amount; // Specifically sum accepted amounts
					break;
				case "DECLINED":
					acc.declined++;
					break;
				case "COMPLETED":
					acc.approved++; // Treat completed as approved for stats summary
					acc.totalWithdrawn += withdrawal.withdrawal.amount;
					break;
				case "CANCELLED":
				case "EXPIRED":
					// For now, neither approved nor declined; just total counts
					break;
			}
			return acc;
		},
		{
			total: 0,
			pending: 0,
			approved: 0,
			declined: 0,
			totalAmount: 0,
			totalWithdrawn: 0,
		},
	);
};

export const filterWithdrawals = (
	withdrawals: WithdrawalWithUser[],
	searchQuery: string,
	selectedStatus: string,
	selectedAmountRange: string,
): WithdrawalWithUser[] => {
	if (!withdrawals) return [];

	return withdrawals.filter((withdrawal) => {
		const matchesSearch =
			searchQuery === "" ||
			withdrawal.user.tornUsername
				.toLowerCase()
				.includes(searchQuery.toLowerCase()) ||
			withdrawal.withdrawal.amount.toString().includes(searchQuery);

		const matchesStatus =
			selectedStatus === "all" ||
			withdrawal.withdrawal.status === selectedStatus;

		const matchesAmountRange = (() => {
			if (selectedAmountRange === "all") return true;
			const range = AMOUNT_RANGES.find((r) => r.id === selectedAmountRange);
			if (!range || !range.min || !range.max) return true;
			const amount = withdrawal.withdrawal.amount;
			return amount >= range.min && amount <= range.max;
		})();

		return matchesSearch && matchesStatus && matchesAmountRange;
	});
};
