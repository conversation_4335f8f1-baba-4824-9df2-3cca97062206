import { PERMISSIONS } from "@monkeymenu/shared";
import { and, eq } from "drizzle-orm";
import { z } from "zod";
import { account as accountTable, user as userTable } from "../db/schema/auth";
import { withdrawalRequest } from "../db/schema/banking";
import { tornUser } from "../db/schema/torn";
import { syncDiscordRoles } from "../discord/lib/role-sync";
import { initCrypto } from "../lib/crypto";
import { getUserPermissionContext } from "../lib/permissions";
import { verifyAndUpdateUserTornInfo } from "../lib/tornUserUtils";
import {
	factionPermissionProcedure,
	requirePermission,
	router,
} from "../lib/trpc";

export const botRouter = router({
	approveWithdrawal: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.BANKING_MANAGE_REQUESTS.name))
		.input(
			z.object({
				requestId: z.string(),
				requesterTornId: z.string().optional(),
				amount: z.number(),
			}),
		)
		.mutation(async ({ ctx, input }) => {
			const { requestId, amount } = input;

			// Verify the withdrawal request exists and is pending
			const withdrawal = await ctx.db
				.select({
					id: withdrawalRequest.id,
					status: withdrawalRequest.status,
					amount: withdrawalRequest.amount,
				})
				.from(withdrawalRequest)
				.where(eq(withdrawalRequest.id, requestId))
				.get();

			if (!withdrawal) {
				throw new Error("Withdrawal request not found");
			}

			if (withdrawal.status !== "PENDING") {
				throw new Error(`Request is already ${withdrawal.status}`);
			}

			if (withdrawal.amount !== amount) {
				throw new Error("Amount mismatch - potential security issue");
			}

			// Update the withdrawal to ACCEPTED
			const updatedWithdrawal = await ctx.db
				.update(withdrawalRequest)
				.set({
					status: "ACCEPTED",
					processedAt: new Date(),
					updatedAt: new Date(),
					processedById: ctx.session.userId,
				})
				.where(eq(withdrawalRequest.id, requestId))
				.returning()
				.get();

			return {
				success: true,
				withdrawal: updatedWithdrawal,
				message: "Withdrawal approved successfully",
			};
		}),

	rejectWithdrawal: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.BANKING_MANAGE_REQUESTS.name))
		.input(
			z.object({
				requestId: z.string(),
			}),
		)
		.mutation(async ({ ctx, input }) => {
			const { requestId } = input;

			// Verify the withdrawal request exists and is pending
			const withdrawal = await ctx.db
				.select({
					id: withdrawalRequest.id,
					status: withdrawalRequest.status,
				})
				.from(withdrawalRequest)
				.where(eq(withdrawalRequest.id, requestId))
				.get();

			if (!withdrawal) {
				throw new Error("Withdrawal request not found");
			}

			if (withdrawal.status !== "PENDING") {
				throw new Error(`Request is already ${withdrawal.status}`);
			}

			// Update the withdrawal to DECLINED
			const updatedWithdrawal = await ctx.db
				.update(withdrawalRequest)
				.set({
					status: "DECLINED",
					processedAt: new Date(),
					updatedAt: new Date(),
					processedById: ctx.session.userId,
				})
				.where(eq(withdrawalRequest.id, requestId))
				.returning()
				.get();

			return {
				success: true,
				withdrawal: updatedWithdrawal,
				message: "Withdrawal rejected successfully",
			};
		}),

	verifyDiscordUser: factionPermissionProcedure.mutation(async ({ ctx }) => {
		const userId = ctx.session.userId;

		// Get user's Discord account information
		const userAccount = await ctx.db
			.select({
				userId: userTable.id,
				userName: userTable.name,
				discordAccountId: accountTable.accountId,
				tornUserId: tornUser.tornUserId,
				tornApiKey: tornUser.tornApiKey,
				tornApiKeyVerified: tornUser.tornApiKeyVerified,
				accessSuspended: tornUser.accessSuspended,
			})
			.from(userTable)
			.leftJoin(
				accountTable,
				and(
					eq(accountTable.userId, userTable.id),
					eq(accountTable.providerId, "discord"),
				),
			)
			.leftJoin(tornUser, eq(userTable.id, tornUser.id))
			.where(eq(userTable.id, userId))
			.get();

		if (!userAccount) {
			throw new Error("User account not found");
		}

		if (!userAccount.discordAccountId) {
			throw new Error(
				"Discord account not linked. Please link your Discord account in your profile settings first.",
			);
		}

		if (!userAccount.tornApiKeyVerified) {
			throw new Error(
				"Torn API key not verified. Please verify your API key in your profile settings first.",
			);
		}

		if (userAccount.accessSuspended) {
			throw new Error(
				"Your access is currently suspended. Please contact an administrator.",
			);
		}

		// Re-verify and update user's Torn info if they have an API key
		let verificationResult = null;
		if (userAccount.tornApiKey && userAccount.tornApiKeyVerified) {
			try {
				initCrypto(ctx.env.TORN_API_KEY_ENCRYPTION_SECRET);
				verificationResult = await verifyAndUpdateUserTornInfo(
					ctx.db,
					userAccount.userId,
					userAccount.tornApiKey,
					ctx.env,
				);
			} catch (error) {
				console.error(`Error re-verifying user ${userAccount.userId}:`, error);
			}
		}

		// Get updated role information
		const updatedPermissions = await getUserPermissionContext(
			ctx.db,
			userAccount.userId,
		);

		// Check if user is in the faction
		const isInFaction = Boolean(
			userAccount.tornApiKeyVerified &&
				userAccount.tornUserId &&
				!userAccount.accessSuspended &&
				(verificationResult ? verificationResult.success : true),
		);

		// Sync Discord roles
		const roleSync = await syncDiscordRoles(
			ctx.env.DISCORD_GUILD_ID || "",
			userAccount.discordAccountId,
			updatedPermissions.roleName,
			ctx.env.DISCORD_BOT_TOKEN,
			isInFaction,
		);

		return {
			success: true,
			message: "Discord verification completed successfully",
			data: {
				userName: userAccount.userName,
				currentRole: updatedPermissions.roleName,
				tornUserId: userAccount.tornUserId,
				tornApiKeyVerified: userAccount.tornApiKeyVerified,
				discordRoleSync: {
					success: roleSync.success,
					message: roleSync.message,
				},
				verificationResult: verificationResult
					? {
							success: verificationResult.success,
							message: verificationResult.message,
						}
					: null,
			},
		};
	}),
});
