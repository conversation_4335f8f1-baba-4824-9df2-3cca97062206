import { useMemo } from "react";
import type { AnnouncementData, AnnouncementStats } from "../types";
import { ANNOUNCEMENT_CATEGORIES } from "../utils";

export function useAnnouncementFilters(
	announcements: AnnouncementData[] | undefined,
	searchQuery: string,
	selectedCategory: string,
) {
	const filteredAnnouncements = useMemo(() => {
		if (!announcements) return [];

		return announcements.filter((announcement) => {
			const matchesSearch =
				searchQuery === "" ||
				announcement.announcement.title
					.toLowerCase()
					.includes(searchQuery.toLowerCase()) ||
				announcement.announcement.content
					.toLowerCase()
					.includes(searchQuery.toLowerCase());

			const matchesCategory =
				selectedCategory === "all" ||
				announcement.announcement.category === selectedCategory;

			return matchesSearch && matchesCategory;
		});
	}, [announcements, searchQuery, selectedCategory]);

	const announcementStats = useMemo((): AnnouncementStats => {
		if (!announcements) return {};

		const stats: AnnouncementStats = { all: announcements.length };
		for (const cat of ANNOUNCEMENT_CATEGORIES) {
			stats[cat.id] = announcements.filter(
				(announcement) => announcement.announcement.category === cat.id,
			).length;
		}

		return stats;
	}, [announcements]);

	return {
		filteredAnnouncements,
		announcementStats,
	};
}
