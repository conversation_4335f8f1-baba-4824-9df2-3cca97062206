import { <PERSON>ert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useAppForm } from "@/components/ui/tanstack-form";
import { trpc } from "@/lib/trpc-client";
import {
	EXTERNAL_URLS,
	TOAST_MESSAGES,
	TornApiKeySchema,
} from "@monkeymenu/shared";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ExternalLink, HelpCircle, Key, Loader2 } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export function ApiKeyManagement() {
	const [isUpdating, setIsUpdating] = useState(false);
	const [invalidApiKey, setInvalidApiKey] = useState(false);
	const [showApiKey, setShowApiKey] = useState(false);

	// Get current user profile to check API key status
	const profile = useQuery(trpc.user.getProfile.queryOptions());

	// Get decrypted API key for display (only when showing)
	const decryptedApiKey = useQuery({
		...trpc.user.getDecryptedApiKey.queryOptions(),
		enabled: showApiKey && !!profile.data?.tornUser?.tornApiKey,
	});

	// API key update mutation
	const updateTornInfoMutation = useMutation(
		trpc.user.updateTornInfo.mutationOptions({
			onSuccess: (data: { message: string; success: boolean }) => {
				setIsUpdating(false);
				setInvalidApiKey(false);
				if (data.success) {
					toast.success(data.message);
					// Refetch profile to get updated info
					profile.refetch();
				} else {
					toast.error(data.message);
				}
			},
			onError: (error) => {
				setIsUpdating(false);

				// Check for Torn API key invalid error
				const errorMessage = error.message || "";
				if (
					errorMessage.toLowerCase().includes("invalid api key") ||
					errorMessage.toLowerCase().includes("incorrect key") ||
					errorMessage.includes("2") // Torn API error code for invalid key
				) {
					setInvalidApiKey(true);
					toast.error(TOAST_MESSAGES.API_KEY_INVALID);
				} else if (
					errorMessage.toLowerCase().includes("already linked") ||
					errorMessage.toLowerCase().includes("linked to another")
				) {
					setInvalidApiKey(true);
					toast.error(TOAST_MESSAGES.TORN_ACCOUNT_ALREADY_LINKED);
				} else {
					// Generic error fallback
					setInvalidApiKey(false);
					toast.error(
						errorMessage || TOAST_MESSAGES.TORN_API_KEY_UPDATE_FAILED,
					);
				}
			},
		}),
	);

	const form = useAppForm({
		defaultValues: {
			apiKey: "",
		},
		validators: {
			onChange: TornApiKeySchema,
		},
		onSubmit: async ({ value }) => {
			setIsUpdating(true);
			setInvalidApiKey(false);
			updateTornInfoMutation.mutate({
				apiKey: value.apiKey.trim(),
			});
		},
	});

	// Get API key status info
	const tornUser = profile.data?.tornUser;
	const hasApiKey = tornUser?.tornApiKey;
	const isVerified = tornUser?.tornApiKeyVerified;
	const isSuspended = tornUser?.accessSuspended;

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Key className="h-5 w-5" />
					Torn API Key
				</CardTitle>
				<div className="text-muted-foreground text-sm">
					Manage your Torn API key for faction features
				</div>
			</CardHeader>
			<CardContent className="space-y-4">
				{/* Current API Key Status */}
				{hasApiKey && (
					<div className="rounded-lg bg-muted/50 p-4">
						<div className="mb-2 flex items-center justify-between">
							<h4 className="font-medium">Current API Key Status</h4>
							<div className="flex items-center gap-2">
								{isVerified ? (
									<span className="font-medium text-green-600 text-sm">
										✅ Verified
									</span>
								) : (
									<span className="font-medium text-sm text-yellow-600">
										⏳ Pending
									</span>
								)}
								{isSuspended && (
									<span className="font-medium text-red-600 text-sm">
										🚫 Suspended
									</span>
								)}
							</div>
						</div>
						<div className="flex items-center gap-2">
							<span className="text-muted-foreground text-sm">API Key:</span>
							<code className="rounded bg-background px-2 py-1 font-mono text-sm">
								{showApiKey
									? decryptedApiKey.data || "••••••••••••••••"
									: "••••••••••••••••"}
							</code>
							<Button
								variant="ghost"
								size="sm"
								onClick={() => setShowApiKey(!showApiKey)}
								className="text-xs"
								disabled={decryptedApiKey.isLoading}
							>
								{showApiKey
									? "Hide"
									: decryptedApiKey.isLoading
										? "Loading..."
										: "Show"}
							</Button>
						</div>
						{tornUser?.tornUserId && (
							<p className="mt-1 text-muted-foreground text-sm">
								Torn ID: {tornUser.tornUserId}
							</p>
						)}
					</div>
				)}

				{/* API Key Update Form */}
				<div className="space-y-4">
					<div className="rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
						<div className="flex items-start gap-3">
							<HelpCircle className="mt-0.5 h-5 w-5 flex-shrink-0 text-blue-600" />
							<div>
								<h4 className="font-medium text-blue-900 dark:text-blue-100">
									{hasApiKey ? "Update API Key" : "Add API Key"}
								</h4>
								<p className="mt-1 text-blue-700 text-sm dark:text-blue-200">
									{hasApiKey
										? "Need to update your API key? Enter the new one below."
										: "Connect your Torn account by adding your API key."}
								</p>
								<a
									href={EXTERNAL_URLS.TORN_API_SETTINGS}
									target="_blank"
									rel="noopener noreferrer"
									className="mt-2 inline-flex items-center gap-1 text-blue-600 text-sm underline hover:text-blue-500"
								>
									Get your API key from Torn
									<ExternalLink className="h-3 w-3" />
								</a>
							</div>
						</div>
					</div>

					<form.AppForm>
						<form
							onSubmit={(e) => {
								e.preventDefault();
								e.stopPropagation();
								void form.handleSubmit();
							}}
						>
							<div className="space-y-4">
								<form.AppField name="apiKey">
									{(field) => (
										<field.FormItem>
											<field.FormLabel>
												{hasApiKey ? "New API Key" : "Torn API Key"}
											</field.FormLabel>
											<field.FormControl>
												<Input
													value={field.state.value}
													onChange={(e) => field.handleChange(e.target.value)}
													onBlur={field.handleBlur}
													placeholder="Enter your 16-character API key"
													type="password"
													autoComplete="off"
													disabled={isUpdating}
													className="font-mono"
												/>
											</field.FormControl>
											<field.FormMessage />
										</field.FormItem>
									)}
								</form.AppField>

								{invalidApiKey && (
									<Alert variant="destructive">
										<AlertDescription>
											{TOAST_MESSAGES.API_KEY_INVALID}
										</AlertDescription>
									</Alert>
								)}

								<div className="flex gap-2">
									<form.Subscribe>
										{(state) => (
											<Button
												type="submit"
												disabled={!state.canSubmit || isUpdating}
												className="flex items-center gap-2"
											>
												{isUpdating ? (
													<>
														<Loader2 className="h-4 w-4 animate-spin" />
														{hasApiKey ? "Updating..." : "Adding..."}
													</>
												) : (
													<>
														<Key className="h-4 w-4" />
														{hasApiKey ? "Update API Key" : "Add API Key"}
													</>
												)}
											</Button>
										)}
									</form.Subscribe>
								</div>
							</div>
						</form>
					</form.AppForm>
				</div>
			</CardContent>
		</Card>
	);
}
