import { sql } from "drizzle-orm";
import type { InferSelectModel } from "drizzle-orm";
import { index, integer, sqliteTable, text } from "drizzle-orm/sqlite-core";
import { user } from "./auth";

export const announcement = sqliteTable(
	"announcement",
	{
		id: integer().primaryKey(),
		title: text().notNull(),
		content: text().notNull(),
		category: text().notNull().default("general"),
		authorId: text("author_id")
			.notNull()
			.references(() => user.id, { onDelete: "cascade" }),
		createdAt: text("created_at", { mode: "text" })
			.notNull()
			.default(sql`CURRENT_TIMESTAMP`),
		updatedAt: text("updated_at", { mode: "text" })
			.notNull()
			.default(sql`CURRENT_TIMESTAMP`),
	},
	(table) => [
		index("idx_announcement_created_at").on(table.createdAt),
		index("idx_announcement_author_id").on(table.authorId),
		index("idx_announcement_category").on(table.category),
	],
);

export type Announcement = InferSelectModel<typeof announcement>;
