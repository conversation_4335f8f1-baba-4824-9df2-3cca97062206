import React from 'react';
import { useUser } from '@clerk/clerk-react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { useNavigate } from '@tanstack/react-router';
import { useSession } from '../../hooks/useSession';

interface FactionRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
  minimumRole?: string;
  fallback?: React.ReactNode;
}

export function FactionRoute({ 
  children, 
  requiredPermission, 
  minimumRole,
  fallback 
}: FactionRouteProps) {
  const { isSignedIn, isLoaded } = useUser();
  const navigate = useNavigate();
  const { convexUser, loading } = useSession();

  // Check if user is suspended
  const suspensionStatus = useQuery(
    api.users.getUserSuspensionStatus,
    convexUser?._id ? { userId: convexUser._id } : "skip"
  );

  React.useEffect(() => {
    if (isLoaded && !isSignedIn) {
      navigate({ to: '/sign-in' });
      return;
    }

    // If user data is loaded but they don't exist in our system
    if (isLoaded && isSignedIn && !loading && !convexUser) {
      navigate({ to: '/onboarding' });
      return;
    }

    // Check if user needs to complete onboarding
    if (convexUser && !convexUser.tornId) {
      navigate({ to: '/onboarding' });
      return;
    }

    // Check faction membership (hardcoded faction ID: 53100 - Menacing Monkeys)
    if (convexUser && convexUser.factionId !== 53100) {
      navigate({ to: '/access-denied?reason=faction' });
      return;
    }

    // Check if user is suspended
    if (suspensionStatus && suspensionStatus.isSuspended) {
      navigate({ 
        to: '/access-denied', 
        search: { 
          reason: 'suspended',
          details: suspensionStatus.reason 
        }
      });
      return;
    }

    // Check API key status
    if (convexUser && convexUser.tornApiKeyStatus !== 'verified') {
      navigate({ to: '/access-denied?reason=api-key' });
      return;
    }
  }, [
    isLoaded, 
    isSignedIn, 
    navigate, 
    convexUser, 
    loading, 
    suspensionStatus
  ]);

  // Show loading state
  if (!isLoaded || loading || (isSignedIn && !convexUser)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Not signed in
  if (!isSignedIn) {
    return null;
  }

  // User not in our system or incomplete onboarding
  if (!convexUser || !convexUser.tornId) {
    return null;
  }

  // Check specific permission if required
  if (requiredPermission) {
    const hasPermission = convexUser.permissions?.includes(requiredPermission) || 
                         convexUser.permissions?.includes('admin.view');
    
    if (!hasPermission) {
      return fallback || (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="text-6xl mb-4">🔒</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Restricted</h2>
            <p className="text-gray-600 mb-4">
              You don't have permission to access this feature.
            </p>
            <p className="text-sm text-gray-500">
              Required permission: <code className="bg-gray-100 px-2 py-1 rounded">{requiredPermission}</code>
            </p>
          </div>
        </div>
      );
    }
  }

  // Check minimum role if required
  if (minimumRole) {
    const hasMinimumRole = convexUser.role && 
                          convexUser.roleLevel && 
                          getRoleLevel(minimumRole) <= convexUser.roleLevel;
    
    if (!hasMinimumRole) {
      return fallback || (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="text-6xl mb-4">👑</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Insufficient Role</h2>
            <p className="text-gray-600 mb-4">
              You need a higher role to access this feature.
            </p>
            <p className="text-sm text-gray-500">
              Required role: <code className="bg-gray-100 px-2 py-1 rounded">{minimumRole}</code>
            </p>
            <p className="text-sm text-gray-500">
              Your role: <code className="bg-gray-100 px-2 py-1 rounded">{convexUser.role || 'None'}</code>
            </p>
          </div>
        </div>
      );
    }
  }

  return <>{children}</>;
}

// Helper function to get role level for comparison
function getRoleLevel(roleName: string): number {
  const roleLevels: Record<string, number> = {
    'recruit': 1,
    'chimpanzee': 2,
    'orangutan': 3,
    'baboon': 4,
    'primate-liaison': 5,
    'gorilla': 6,
    'monkey-mentor': 7,
    'co-leader': 8,
    'leader': 9,
    'system-administrator': 10,
  };
  
  return roleLevels[roleName.toLowerCase()] || 0;
}