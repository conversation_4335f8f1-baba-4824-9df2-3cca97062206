import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { ArrowRight } from "lucide-react";
import type { StepComponentProps } from "./types";

export function WelcomeStep({ onNextStep }: StepComponentProps) {
  return (
    <Card>
      <CardContent className="space-y-6 p-8">
        <div className="space-y-2 text-center">
          <h2 className="font-bold text-3xl tracking-tight">
            Welcome to MonkeyMenu
          </h2>
          <p className="mx-auto max-w-md text-muted-foreground leading-relaxed">
            Let's get your account set up so you can access all faction features
            and tools.
          </p>
        </div>

        <div className="space-y-4">
          <div className="text-center">
            <h3 className="font-semibold text-lg mb-3">What we'll set up:</h3>
            <div className="space-y-2 text-sm text-muted-foreground">
              <div className="flex items-center justify-center gap-2">
                <span className="w-2 h-2 bg-primary rounded-full"></span>
                Connect your Torn account for verification
              </div>
              <div className="flex items-center justify-center gap-2">
                <span className="w-2 h-2 bg-primary rounded-full"></span>
                Link Discord for notifications (optional)
              </div>
              <div className="flex items-center justify-center gap-2">
                <span className="w-2 h-2 bg-primary rounded-full"></span>
                Configure your permissions and access
              </div>
            </div>
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-8 pt-0">
        <Button onClick={onNextStep} className="w-full">
          Get Started
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  )
}