# MonkeyMenu Agent Guidelines

## Commands
- `pnpm dev` - Start all services (server, web, shared)
- `pnpm check` - Run Biome linting/formatting (auto-fix)
- `pnpm check-types` - TypeScript type checking across all packages
- `pnpm build` - Build all packages (turbo orchestrated)
- `pnpm -F server <cmd>` - Run command in server workspace
- `pnpm -F web <cmd>` - Run command in web workspace
- `pnpm -F @monkeymenu/shared <cmd>` - Run command in shared package

## Code Style (Biome)
- **Formatting**: Tabs (2 spaces), double quotes, semicolons, trailing commas
- **Imports**: Auto-organized, shared package imports via `@monkeymenu/shared`
- **TypeScript**: Strict mode, explicit types, proper error handling
- **Naming**: camelCase for functions/variables, PascalCase for types/components

## Architecture
- **Monorepo**: pnpm workspaces with Turbo build system
- **Server**: Hono + tRPC + Drizzle ORM + Cloudflare Workers
- **Web**: React 19 + TanStack Router/Query + Tailwind + Radix UI
- **Shared**: Common types, utils, schemas exported as `@monkeymenu/shared`

## Patterns
- tRPC routers in `/routers/` with proper error handling
- Database schema in `/db/schema/` using Drizzle ORM
- React components with hooks pattern, proper TypeScript types
- Shared utilities and types centralized in packages/shared