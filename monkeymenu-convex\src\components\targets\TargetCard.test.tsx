import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import { TargetCard } from './TargetCard'
import { renderWithProviders, mockTarget } from '../../test/utils'

describe('TargetCard', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders target information correctly', () => {
    renderWithProviders(<TargetCard target={mockTarget} />)

    expect(screen.getByText('target_user')).toBeInTheDocument()
    expect(screen.getByText('#67890')).toBeInTheDocument()
    expect(screen.getByText('Level 50')).toBeInTheDocument()
    expect(screen.getByText('Test Faction')).toBeInTheDocument()
    expect(screen.getByText('500,000')).toBeInTheDocument() // Respect
    expect(screen.getByText('2.50')).toBeInTheDocument() // Fair fight
  })

  it('displays battle stats when available', () => {
    renderWithProviders(<TargetCard target={mockTarget} />)

    expect(screen.getByText('100,000')).toBeInTheDocument() // Strength
    expect(screen.getByText('80,000')).toBeInTheDocument() // Defense
    expect(screen.getByText('90,000')).toBeInTheDocument() // Speed
    expect(screen.getByText('85,000')).toBeInTheDocument() // Dexterity
    expect(screen.getByText('355,000')).toBeInTheDocument() // Total stats
  })

  it('shows correct status styling and icon', () => {
    renderWithProviders(<TargetCard target={mockTarget} />)

    const statusElement = screen.getByText('active')
    expect(statusElement).toBeInTheDocument()
    expect(statusElement.closest('.bg-green-100')).toBeInTheDocument()
  })

  it('handles attack button click', () => {
    const mockOnAttack = vi.fn()
    renderWithProviders(
      <TargetCard target={mockTarget} onAttack={mockOnAttack} />
    )

    const attackButton = screen.getByText('⚔️ Attack')
    fireEvent.click(attackButton)

    expect(mockOnAttack).toHaveBeenCalledWith(mockTarget.tornId)
  })

  it('opens Torn attack page when no onAttack handler provided', () => {
    const originalOpen = window.open
    window.open = vi.fn()

    renderWithProviders(<TargetCard target={mockTarget} />)

    const attackButton = screen.getByText('⚔️ Attack')
    fireEvent.click(attackButton)

    expect(window.open).toHaveBeenCalledWith(
      `https://www.torn.com/loader.php?sid=attack&user2ID=${mockTarget.tornId}`,
      '_blank'
    )

    window.open = originalOpen
  })

  it('opens profile page when profile button clicked', () => {
    const originalOpen = window.open
    window.open = vi.fn()

    renderWithProviders(<TargetCard target={mockTarget} />)

    const profileButton = screen.getByText('👤 Profile')
    fireEvent.click(profileButton)

    expect(window.open).toHaveBeenCalledWith(
      `https://www.torn.com/profiles.php?XID=${mockTarget.tornId}`,
      '_blank'
    )

    window.open = originalOpen
  })

  it('disables attack button for hospitalized targets', () => {
    const hospitalizedTarget = {
      ...mockTarget,
      status: 'hospitalized',
    }

    renderWithProviders(<TargetCard target={hospitalizedTarget} />)

    const attackButton = screen.getByText('⚔️ Attack')
    expect(attackButton).toBeDisabled()
  })

  it('disables attack button for jailed targets', () => {
    const jailedTarget = {
      ...mockTarget,
      status: 'jailed',
    }

    renderWithProviders(<TargetCard target={jailedTarget} />)

    const attackButton = screen.getByText('⚔️ Attack')
    expect(attackButton).toBeDisabled()
  })

  it('hides actions when showActions is false', () => {
    renderWithProviders(
      <TargetCard target={mockTarget} showActions={false} />
    )

    expect(screen.queryByText('⚔️ Attack')).not.toBeInTheDocument()
    expect(screen.queryByText('👤 Profile')).not.toBeInTheDocument()
  })

  it('handles target without battle stats', () => {
    const targetWithoutStats = {
      ...mockTarget,
      battleStats: undefined,
    }

    renderWithProviders(<TargetCard target={targetWithoutStats} />)

    expect(screen.queryByText('Battle Stats:')).not.toBeInTheDocument()
    expect(screen.queryByText('Total Stats:')).not.toBeInTheDocument()
  })

  it('handles target without respect and fair fight', () => {
    const limitedTarget = {
      ...mockTarget,
      respect: undefined,
      fairFight: undefined,
    }

    renderWithProviders(<TargetCard target={limitedTarget} />)

    expect(screen.queryByText('Respect:')).not.toBeInTheDocument()
    expect(screen.queryByText('Fair Fight:')).not.toBeInTheDocument()
  })

  it('formats last updated time correctly', () => {
    const recentTarget = {
      ...mockTarget,
      lastUpdated: Date.now() - 30 * 60 * 1000, // 30 minutes ago
    }

    renderWithProviders(<TargetCard target={recentTarget} />)

    // Look for any text that contains "m ago" since the exact number might vary slightly
    expect(screen.getByText(/\d+m ago/)).toBeInTheDocument()
  })
})