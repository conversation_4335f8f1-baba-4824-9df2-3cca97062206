import { mutation, query, action } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";

// Torn API validation function - only needs API key
async function validateTornApiKey(apiKey: string) {
  try {
    // Use the API key to get the user's own data including faction position
    const response = await fetch(
      `https://api.torn.com/user/?selections=basic,profile,faction&key=${apiKey}`
    );
    
    if (!response.ok) {
      throw new Error("Invalid API key");
    }
    
    const data = await response.json();
    
    if (data.error) {
      throw new Error(data.error.error || "Invalid API key");
    }
    
    return {
      success: true,
      userData: {
        tornId: data.player_id,
        username: data.name,
        faction: data.faction?.faction_id ? {
          id: data.faction.faction_id,
          name: data.faction.faction_name,
          position: data.faction.position, // Add faction position
        } : null,
        level: data.level,
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "API validation failed"
    };
  }
}

// Get faction permissions based on faction ID
// Faction role hierarchy (higher number = higher rank)
const FACTION_ROLES = {
  SYSTEM_ADMIN: { name: "system-admin", displayName: "System Administrator", level: 10 },
  LEADER: { name: "leader", displayName: "Leader", level: 9 },
  CO_LEADER: { name: "co-leader", displayName: "Co-leader", level: 8 },
  MONKEY_MENTOR: { name: "monkey-mentor", displayName: "Monkey Mentor", level: 7 },
  GORILLA: { name: "gorilla", displayName: "Gorilla", level: 6 },
  PRIMATE_LIAISON: { name: "primate-liaison", displayName: "Primate Liaison", level: 5 },
  BABOON: { name: "baboon", displayName: "Baboon", level: 4 },
  ORANGUTAN: { name: "orangutan", displayName: "Orangutan", level: 3 },
  CHIMPANZEE: { name: "chimpanzee", displayName: "Chimpanzee", level: 2 },
  RECRUIT: { name: "recruit", displayName: "Recruit", level: 1 },
} as const;

// Map Torn faction positions to roles
const TORN_POSITION_MAPPING = {
  "Leader": "LEADER",
  "Co-leader": "CO_LEADER", 
  "Monkey Mentor": "MONKEY_MENTOR",
  "Gorilla": "GORILLA",
  "Primate Liaison": "PRIMATE_LIAISON",
  "Baboon": "BABOON",
  "Orangutan": "ORANGUTAN", 
  "Chimpanzee": "CHIMPANZEE",
  // Default for any unmapped positions
} as const;

// Role-based permission assignments matching original app
const DEFAULT_ROLE_PERMISSIONS = {
  "system-admin": [
    // All permissions - System Admin has complete access
    "guides.view", "guides.manage", "dashboard.view", "announcements.view", "announcements.manage",
    "admin.view", "admin.users.suspend", "admin.users.recheck", "admin.users.delete",
    "discord.manage.verification", "banking.view", "banking.request", "banking.requests.manage",
    "target.finder.view", "target.finder.manage.shared_lists", "wars.view"
  ],
  "leader": [
    // All permissions except Discord management
    "guides.view", "guides.manage", "dashboard.view", "announcements.view", "announcements.manage",
    "admin.view", "admin.users.suspend", "admin.users.recheck", "admin.users.delete",
    "banking.view", "banking.request", "banking.requests.manage", 
    "target.finder.view", "target.finder.manage.shared_lists", "wars.view"
  ],
  "co-leader": [
    // Most permissions except highest admin functions
    "guides.view", "guides.manage", "dashboard.view", "announcements.view", "announcements.manage",
    "admin.view", "admin.users.suspend", "admin.users.recheck", "admin.users.delete",
    "banking.view", "banking.request", "banking.requests.manage",
    "target.finder.view", "target.finder.manage.shared_lists", "wars.view"
  ],
  "monkey-mentor": [
    // Senior admin role with full management
    "guides.view", "guides.manage", "dashboard.view", "announcements.view", "announcements.manage",
    "admin.view", "admin.users.suspend", "admin.users.recheck", "admin.users.delete",
    "banking.view", "banking.request", "banking.requests.manage",
    "target.finder.view", "target.finder.manage.shared_lists", "wars.view"
  ],
  "gorilla": [
    // Banking management + basic permissions
    "guides.view", "dashboard.view", "announcements.view",
    "banking.view", "banking.request", "banking.requests.manage",
    "target.finder.view", "wars.view"
  ],
  "primate-liaison": [
    // Banking management + basic permissions  
    "guides.view", "dashboard.view", "announcements.view",
    "banking.view", "banking.request", "banking.requests.manage",
    "target.finder.view", "wars.view"
  ],
  "baboon": [
    // Banking requests + basic permissions
    "guides.view", "dashboard.view", "announcements.view",
    "banking.view", "banking.request", "target.finder.view", "wars.view"
  ],
  "orangutan": [
    // Banking requests + basic permissions
    "guides.view", "dashboard.view", "announcements.view", 
    "banking.view", "banking.request", "target.finder.view", "wars.view"
  ],
  "chimpanzee": [
    // Banking requests + basic permissions
    "guides.view", "dashboard.view", "announcements.view",
    "banking.view", "banking.request", "target.finder.view", "wars.view"
  ],
  "recruit": [
    // Very limited permissions
    "guides.view", "dashboard.view", "announcements.view",
    "banking.view", "banking.request", "target.finder.view", "wars.view"
  ],
} as const;

// Check if a Torn user ID should be assigned System Administrator role
function isSystemAdminTornId(
  tornUserId: string | number | null | undefined,
  env?: { SYSTEM_ADMIN_TORN_IDS?: string }
): boolean {
  if (!tornUserId || !env?.SYSTEM_ADMIN_TORN_IDS) {
    return false;
  }

  const adminIds = env.SYSTEM_ADMIN_TORN_IDS.split(",")
    .map((id) => id.trim())
    .filter((id) => id.length > 0);

  return adminIds.includes(String(tornUserId));
}

// Get role from Torn faction position
function getRoleFromTornPosition(tornPosition: string | null | undefined): keyof typeof FACTION_ROLES {
  if (!tornPosition) return "RECRUIT";
  
  const roleKey = TORN_POSITION_MAPPING[tornPosition as keyof typeof TORN_POSITION_MAPPING] as keyof typeof FACTION_ROLES;
  return roleKey || "RECRUIT"; // Default to recruit if position not found
}

// Get the appropriate role for a user based on their Torn faction position and admin status
function getUserRoleFromTornData(
  tornPosition: string | null | undefined,
  tornUserId: string | number | null | undefined,
  env?: { SYSTEM_ADMIN_TORN_IDS?: string }
): keyof typeof FACTION_ROLES {
  // Check if user should be System Admin first
  if (isSystemAdminTornId(tornUserId, env)) {
    return "SYSTEM_ADMIN";
  }

  // Otherwise, use normal faction position mapping
  return getRoleFromTornPosition(tornPosition);
}

// Get permissions based on faction role (with system admin check)
function getFactionPermissions(
  factionId?: number, 
  tornPosition?: string, 
  tornUserId?: number,
  env?: { SYSTEM_ADMIN_TORN_IDS?: string }
): string[] {
  // Only assign role-based permissions for faction 53100
  if (factionId !== 53100) {
    return ["banking.view", "target.finder.view", "announcements.view"];
  }
  
  const roleKey = getUserRoleFromTornData(tornPosition, tornUserId, env);
  const roleName = FACTION_ROLES[roleKey].name;
  
  return DEFAULT_ROLE_PERMISSIONS[roleName as keyof typeof DEFAULT_ROLE_PERMISSIONS] || DEFAULT_ROLE_PERMISSIONS.recruit;
}

// Action to validate API key and update user (can use fetch)
export const validateAndUpdateTornUser = action({
  args: {
    apiKey: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Validate the API key with Torn API
    const validation = await validateTornApiKey(args.apiKey);
    
    if (!validation.success) {
      throw new Error(validation.error);
    }

    // Call internal mutation to update the user
    const result = await ctx.runMutation(internal.torn.updateUserWithTornData, {
      clerkId: identity.subject,
      userData: validation.userData,
      apiKey: args.apiKey,
    });

    // Update user with Clerk identity info if it's a new user
    if (result.user && (!result.user.email || !result.user.avatar)) {
      await ctx.runMutation(internal.torn.updateUserIdentityInfo, {
        userId: result.user._id,
        email: identity.email || "",
        avatar: identity.pictureUrl || "",
      });
    }

    return result;
  },
});

// Internal mutation to update user with Torn data (called by action)
export const updateUserWithTornData = mutation({
  args: {
    clerkId: v.string(),
    userData: v.object({
      tornId: v.number(),
      username: v.string(),
      faction: v.optional(v.object({
        id: v.number(),
        name: v.string(),
        position: v.optional(v.string()),
      })),
      level: v.number(),
    }),
    apiKey: v.string(),
  },
  handler: async (ctx, args) => {
    const { userData, clerkId } = args;
    
    if (!userData) {
      throw new Error("Failed to retrieve user data");
    }

    // Check if another user already has this Torn ID
    const existingUserWithTornId = await ctx.db
      .query("users")
      .withIndex("by_torn_id", (q) => q.eq("tornId", userData.tornId))
      .first();

    if (existingUserWithTornId && existingUserWithTornId.clerkId !== clerkId) {
      throw new Error("This Torn account is already linked to another user");
    }

    // Get or create user
    let user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", clerkId))
      .first();

    const now = Date.now();
    const permissions = getFactionPermissions(
      userData.faction?.id, 
      userData.faction?.position, 
      userData.tornId,
      process.env
    );
    
    // Get role info for database storage (with system admin check)
    const roleKey = getUserRoleFromTornData(
      userData.faction?.position, 
      userData.tornId,
      process.env
    );
    const roleInfo = FACTION_ROLES[roleKey];
    const roleName = roleInfo.name;
    const roleLevel = roleInfo.level;

    if (user) {
      // Update existing user
      await ctx.db.patch(user._id, {
        tornId: userData.tornId,
        username: userData.username,
        faction: userData.faction?.name,
        factionId: userData.faction?.id,
        factionName: userData.faction?.name,
        level: userData.level,
        role: roleName, // Store faction role
        roleLevel: roleLevel, // Store role hierarchy level
        tornApiKey: args.apiKey, // Store API key for faction balance fetching
        permissions,
        updatedAt: now,
        lastSeen: now,
      });
    } else {
      // Create new user (we'll need to get identity info from the action)
      const userId = await ctx.db.insert("users", {
        clerkId: clerkId,
        tornId: userData.tornId,
        username: userData.username,
        email: "", // Will be updated by the action
        avatar: "", // Will be updated by the action
        faction: userData.faction?.name,
        factionId: userData.faction?.id,
        factionName: userData.faction?.name,
        level: userData.level,
        role: roleName, // Store faction role
        roleLevel: roleLevel, // Store role hierarchy level
        tornApiKey: args.apiKey, // Store API key for faction balance fetching
        permissions,
        isActive: true,
        lastSeen: now,
        createdAt: now,
        updatedAt: now,
      });
      
      user = await ctx.db.get(userId);
    }

    if (!user) {
      throw new Error("Failed to create or update user");
    }

    return {
      success: true,
      message: "Torn account verified and linked successfully",
      user: {
        _id: user._id,
        tornId: userData.tornId,
        username: userData.username,
        faction: userData.faction,
        level: userData.level,
        permissions,
        email: user.email,
        avatar: user.avatar,
      }
    };
  },
});

// Internal mutation to update user identity info
export const updateUserIdentityInfo = mutation({
  args: {
    userId: v.id("users"),
    email: v.string(),
    avatar: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.userId, {
      email: args.email,
      avatar: args.avatar,
      updatedAt: Date.now(),
    });
  },
});

// Get user's torn info
export const getUserTornInfo = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user || !user.tornId) {
      return null;
    }

    return {
      tornId: user.tornId,
      username: user.username,
      faction: user.faction,
      level: user.level,
      permissions: user.permissions,
    };
  },
});