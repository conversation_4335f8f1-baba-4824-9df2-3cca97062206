{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "globalEnv": ["USERPROFILE"], "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**"]}, "check-types": {"dependsOn": ["^build"]}, "lint": {"dependsOn": ["^lint"]}, "check": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}, "db:push": {"cache": false, "persistent": true}, "db:studio": {"cache": false, "persistent": true}, "db:migrate": {"cache": false, "persistent": true}, "db:generate": {"cache": false, "persistent": true}, "cf:typegen": {"cache": false}, "cf:deploy": {"cache": false, "dependsOn": ["build"]}}}