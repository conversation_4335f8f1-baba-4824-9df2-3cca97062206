import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	<PERSON>alogDescription,
	<PERSON><PERSON>Footer,
	<PERSON><PERSON>Header,
	<PERSON><PERSON><PERSON>itle,
	DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useTargetFinderLiveStoreSync } from "@/lib/target-finder-livestore-bridge";
import { trpc } from "@/lib/trpc-client";
import { useMutation } from "@tanstack/react-query";
import { Loader2, Plus, Target } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import type { AddTargetDialogProps, TargetWithStatus } from "./types";

// API returns a subset of TargetWithStatus (no updatedAt, optional status)
type TargetData = Omit<TargetWithStatus, "updatedAt"> & {
	status?: string;
};

export function AddTargetDialog({
	selectedList,
	onSuccess,
}: AddTargetDialogProps) {
	const [open, setOpen] = useState(false);
	const [tornId, setTornId] = useState("");

	// Get LiveStore sync functions
	const { syncNewTarget } = useTargetFinderLiveStoreSync();

	const addTargetToList = useMutation(
		trpc.targetFinder.addTargetToList.mutationOptions({
			onSuccess: async (newTarget) => {
				setOpen(false);
				setTornId("");
				onSuccess();
				// Success notification will be shown by the WebSocket system
				// when the target_added event is received
				console.log(
					"Target added successfully - waiting for WebSocket confirmation",
				);

				// Sync the new target to LiveStore using the returned data
				if (newTarget) {
					const syncResult = syncNewTarget(
						{
							id: newTarget.id,
							listId: newTarget.listId,
							name: newTarget.name,
							tornId: newTarget.tornId,
							status: (newTarget as Partial<TargetData>).status ?? "Unknown",
							profilePicture: (newTarget as Partial<TargetData>).profilePicture,
							createdAt: new Date(newTarget.createdAt),
						},
						"manual",
					);

					if (syncResult.success) {
						console.log(
							`[LiveStore] Successfully synced newly added target: ${newTarget.name} (${newTarget.tornId})`,
						);
					} else if (syncResult.reason === "duplicate") {
						console.log(`[LiveStore] Target already synced: ${newTarget.name}`);
					} else {
						console.error(
							"Failed to sync new target to LiveStore:",
							syncResult.error,
						);
					}
				}
			},
			onError: (error) => {
				toast.error("Failed to add target", {
					description: error.message,
				});
			},
		}),
	);

	const createOrGetCustomList = useMutation(
		trpc.targetFinder.createOrGetCustomList.mutationOptions({
			onError: (error) => {
				toast.error("Failed to create custom list", {
					description: error.message,
				});
			},
		}),
	);

	const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault();
		if (!tornId || selectedList === "") return;

		try {
			if (selectedList === "Custom List") {
				// For custom lists, ensure the list exists first
				await createOrGetCustomList.mutateAsync();
			}

			// Use the unified addTargetToList endpoint for both custom and shared lists
			await addTargetToList.mutateAsync({
				tornId,
				listName: selectedList,
			});
		} catch (error) {
			// Error handling is done in the mutation callbacks
		}
	};

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger asChild>
				<Button disabled={selectedList === ""} className="gap-2">
					<Plus className="h-4 w-4" />
					Add Target
				</Button>
			</DialogTrigger>
			<DialogContent className="sm:max-w-md">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<Target className="h-5 w-5" />
						Add Target to {selectedList}
					</DialogTitle>
					<DialogDescription>
						Enter the Torn ID of the player you want to add to your target list.
					</DialogDescription>
				</DialogHeader>
				<form onSubmit={handleSubmit}>
					<div className="grid gap-4 py-4">
						<div className="grid gap-2">
							<div className="font-medium text-sm">Torn ID</div>
							<Input
								id="tornId"
								value={tornId}
								onChange={(e) => setTornId(e.target.value)}
								placeholder="Enter Torn ID..."
								disabled={addTargetToList.isPending}
								className="text-center"
							/>
						</div>
					</div>
					<DialogFooter>
						<Button type="submit" disabled={addTargetToList.isPending}>
							{addTargetToList.isPending ? (
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
							) : (
								<Plus className="mr-2 h-4 w-4" />
							)}
							Add Target
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
