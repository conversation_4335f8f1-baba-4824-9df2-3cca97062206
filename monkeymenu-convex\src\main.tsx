import React from 'react'
import ReactD<PERSON> from 'react-dom/client'
import { ConvexReactClient } from 'convex/react'
import { <PERSON><PERSON>rov<PERSON>, useAuth } from '@clerk/clerk-react'
import { ConvexProviderWithClerk } from 'convex/react-clerk'
import { RouterProvider, createRouter } from '@tanstack/react-router'
import { ErrorBoundary } from './lib/error-handling'
import { logger } from './lib/logging'

import './index.css'
import { routeTree } from './routeTree.gen'

// Import your publishable key
const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY

if (!PUBLISHABLE_KEY) {
  throw new Error("Missing Publishable Key")
}

const convex = new ConvexReactClient(import.meta.env.VITE_CONVEX_URL as string)

// Create a new router instance
const router = createRouter({ routeTree })

// Register the router instance for type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

// Initialize logging
logger.info('Application starting', 'main', 'init');

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ErrorBoundary>
      <ClerkProvider publishableKey={PUBLISHABLE_KEY}>
        <ConvexProviderWithClerk client={convex} useAuth={useAuth}>
          <ErrorBoundary>
            <RouterProvider router={router} />
          </ErrorBoundary>
        </ConvexProviderWithClerk>
      </ClerkProvider>
    </ErrorBoundary>
  </React.StrictMode>,
)
