import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { ChevronDown, Shield } from "lucide-react";
import { useState } from "react";
import { Skeleton } from "./Skeleton";
import type { RoleHierarchyProps } from "./types";
import { getReadablePermissionName, getRoleColor } from "./utils";

export function RoleHierarchy({
	roles,
	isLoading,
	currentUserRoleName,
}: RoleHierarchyProps) {
	const [expandedRoles, setExpandedRoles] = useState<Set<number>>(new Set());

	if (isLoading) {
		return (
			<div className="space-y-4">
				{Array.from({ length: 4 }, () => (
					<Skeleton key={crypto.randomUUID()} className="h-32 w-full" />
				))}
			</div>
		);
	}

	return (
		<Card>
			<CardHeader>
				<CardTitle>Role Hierarchy & Permissions</CardTitle>
				<CardDescription>
					Complete overview of all system roles and their permissions
				</CardDescription>
			</CardHeader>
			<CardContent>
				<div className="space-y-4">
					{roles
						?.sort((a, b) => b.role.hierarchyLevel - a.role.hierarchyLevel)
						.map((roleData) => {
							const isCurrentUserRole =
								currentUserRoleName === roleData.role.name;
							const roleColor = getRoleColor(roleData.role.hierarchyLevel);

							return (
								<div
									key={roleData.role.id}
									className={`rounded-lg border p-4 ${
										isCurrentUserRole
											? "bg-primary/5 ring-2 ring-primary/20"
											: ""
									}`}
								>
									<div className="mb-3 flex items-center gap-3">
										<div className={`rounded-lg p-2 ${roleColor.background}`}>
											<Shield className={`h-4 w-4 ${roleColor.text}`} />
										</div>
										<div className="flex-1">
											<div className="flex items-center gap-2">
												<h4 className="font-medium">
													{roleData.role.displayName}
												</h4>
												{isCurrentUserRole && (
													<Badge variant="default" className="text-xs">
														Your Role
													</Badge>
												)}
											</div>
											<p className="text-muted-foreground text-sm">
												Level {roleData.role.hierarchyLevel} •{" "}
												{roleData.permissions.length} permissions
											</p>
										</div>
									</div>

									{/* Collapsible Permissions */}
									{roleData.permissions.length > 0 ? (
										<Collapsible
											open={expandedRoles.has(roleData.role.id)}
											onOpenChange={(open) => {
												const newExpanded = new Set(expandedRoles);
												if (open) {
													newExpanded.add(roleData.role.id);
												} else {
													newExpanded.delete(roleData.role.id);
												}
												setExpandedRoles(newExpanded);
											}}
										>
											<CollapsibleTrigger asChild>
												<Button
													variant="ghost"
													size="sm"
													className="h-auto w-full justify-between p-2 text-sm"
												>
													<span>
														{expandedRoles.has(roleData.role.id)
															? "Hide permissions"
															: `View ${roleData.permissions.length} permissions`}
													</span>
													<ChevronDown
														className={`h-4 w-4 transition-transform ${
															expandedRoles.has(roleData.role.id)
																? "rotate-180"
																: ""
														}`}
													/>
												</Button>
											</CollapsibleTrigger>
											<CollapsibleContent className="mt-2">
												<div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
													{roleData.permissions.map((permission) => (
														<div
															key={permission.name}
															className="flex items-center gap-2 rounded border bg-muted/10 p-2"
														>
															<div className="h-2 w-2 rounded-full bg-green-500" />
															<span className="text-xs">
																{getReadablePermissionName(permission.name)}
															</span>
														</div>
													))}
												</div>
											</CollapsibleContent>
										</Collapsible>
									) : (
										<p className="text-muted-foreground text-sm italic">
											No permissions assigned to this role
										</p>
									)}
								</div>
							);
						})}
				</div>
			</CardContent>
		</Card>
	);
}
