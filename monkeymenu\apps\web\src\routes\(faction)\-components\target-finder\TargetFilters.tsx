import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { usePermissions } from "@/hooks/usePermissions";
import { PERMISSION_NAMES } from "@monkeymenu/shared";
import {
	AlertCircle,
	CheckCircle,
	Grid,
	Heart,
	List,
	Loader2,
	RefreshCw,
	Search,
	SlidersHorizontal,
} from "lucide-react";
import type { TargetFilterProps } from "./types";

export function TargetFilters({
	selectedList,
	setSelectedList,
	searchTerm,
	setSearchTerm,
	statusFilter,
	setStatusFilter,
	viewMode,
	setViewMode,
	lists,
	listsLoading,
	warInfo,
	enemyFactionId,
	localCooldown,
	onRefresh,
	isRefreshing,
	targetStats,
}: TargetFilterProps) {
	const { data: permissions } = usePermissions();

	// Get active filter count
	const activeFilters = statusFilter !== "all" ? 1 : 0;

	return (
		<div className="space-y-4">
			{/* Target List Selector */}
			<Select value={selectedList} onValueChange={setSelectedList}>
				<SelectTrigger className="w-full">
					<SelectValue placeholder="Choose a target list..." />
				</SelectTrigger>
				<SelectContent>
					{listsLoading ? (
						<SelectItem value="loading" disabled>
							Loading lists...
						</SelectItem>
					) : (
						<>
							{/* Enemy faction option when at war */}
							{enemyFactionId && warInfo?.wars?.ranked && (
								<SelectItem
									key="enemy-faction"
									value={`Enemy Faction: ${
										warInfo.wars.ranked.factions.find((f) => f.id !== 53100)
											?.name || "Unknown"
									}`}
								>
									{warInfo.wars.ranked.factions.find((f) => f.id !== 53100)
										?.name || "Unknown"}{" "}
									(War)
								</SelectItem>
							)}

							{/* Regular lists */}
							{lists?.map((list) => {
								const canViewPersonal = permissions?.permissions.includes(
									PERMISSION_NAMES.TARGET_FINDER_VIEW,
								);
								const canViewShared = permissions?.permissions.includes(
									PERMISSION_NAMES.TARGET_FINDER_VIEW,
								);

								if (list.name === "Custom List" && !canViewPersonal) {
									return null;
								}
								if (
									(list.isExternal ||
										(list.userId === null && list.name !== "Custom List")) &&
									!canViewShared
								) {
									return null;
								}

								return (
									<SelectItem key={list.id} value={list.name}>
										{list.name}
										{list.isExternal
											? " (External)"
											: list.userId
												? " (Personal)"
												: " (Shared)"}
									</SelectItem>
								);
							})}
						</>
					)}
				</SelectContent>
			</Select>

			{/* Search and Filters Row */}
			<div className="flex flex-row items-center justify-between gap-2">
				{/* Search Bar */}
				<div className="relative min-w-0 flex-1">
					<Search className="absolute top-2.5 left-2 h-4 w-4 text-muted-foreground" />
					<Input
						placeholder="Search targets..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="pl-8"
					/>
				</div>

				{/* Action Buttons */}
				<div className="flex items-center gap-2">
					<Button
						variant="outline"
						size="sm"
						onClick={onRefresh}
						disabled={!selectedList || isRefreshing || localCooldown > 0}
						className="gap-2"
					>
						{isRefreshing ? (
							<Loader2 className="h-4 w-4 animate-spin" />
						) : (
							<RefreshCw className="h-4 w-4" />
						)}
						Refresh
						{localCooldown > 0 ? ` (${Math.ceil(localCooldown)}s)` : ""}
					</Button>

					<Popover>
						<PopoverTrigger asChild>
							<Button variant="outline" size="sm" className="relative">
								<SlidersHorizontal className="mr-2 h-4 w-4" />
								Filters
								{activeFilters > 0 && (
									<span className="-top-1 -right-1 absolute flex h-5 w-5 items-center justify-center rounded-full bg-blue-500 text-white text-xs">
										{activeFilters}
									</span>
								)}
							</Button>
						</PopoverTrigger>
						<PopoverContent className="w-80" align="end">
							<div className="space-y-4">
								{/* Status Filter */}
								{selectedList !== "" && (
									<div>
										<label
											htmlFor="target-status-filter"
											className="mb-2 block font-medium text-sm"
										>
											Status
										</label>
										<Select
											value={statusFilter}
											onValueChange={setStatusFilter}
										>
											<SelectTrigger id="target-status-filter">
												<SelectValue placeholder="Filter by status" />
											</SelectTrigger>
											<SelectContent>
												<SelectItem value="all">
													All ({targetStats.all || 0})
												</SelectItem>
												{targetStats.okay > 0 && (
													<SelectItem value="okay">
														<div className="flex items-center gap-2">
															<CheckCircle className="h-3 w-3" />
															Available ({targetStats.okay})
														</div>
													</SelectItem>
												)}
												{targetStats.hospitalized > 0 && (
													<SelectItem value="hospitalized">
														<div className="flex items-center gap-2">
															<Heart className="h-3 w-3" />
															Hospitalized ({targetStats.hospitalized})
														</div>
													</SelectItem>
												)}
												{targetStats.error > 0 && (
													<SelectItem value="error">
														<div className="flex items-center gap-2">
															<AlertCircle className="h-3 w-3" />
															Errors ({targetStats.error})
														</div>
													</SelectItem>
												)}
											</SelectContent>
										</Select>
									</div>
								)}

								{/* View Mode */}
								<div>
									<div className="mb-2 font-medium text-sm">View Mode</div>
									<div className="flex gap-2">
										<Button
											variant={viewMode === "list" ? "default" : "outline"}
											size="sm"
											onClick={() => setViewMode("list")}
											className="flex-1"
										>
											<List className="mr-2 h-4 w-4" />
											List
										</Button>
										<Button
											variant={viewMode === "grid" ? "default" : "outline"}
											size="sm"
											onClick={() => setViewMode("grid")}
											className="flex-1"
										>
											<Grid className="mr-2 h-4 w-4" />
											Grid
										</Button>
									</div>
								</div>
							</div>
						</PopoverContent>
					</Popover>
				</div>
			</div>
		</div>
	);
}
