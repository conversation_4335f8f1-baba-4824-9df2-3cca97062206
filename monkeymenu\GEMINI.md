# MonkeyMenu - Gemini AI Assistant Guidelines

This is a full-stack TypeScript monorepo for MonkeyMenu, a cloud-based faction management application for the game Torn City. This guide provides context for AI assistance with development tasks.

## Project Architecture

### High-Level Structure

monkeymenu/
├── apps/
│ ├── server/ # Cloudflare Workers API (Hono + tRPC)
│ └── web/ # React frontend (TanStack ecosystem)
├── packages/
│ └── shared/ # Common utilities and types
└── [config files] # Monorepo configuration

### Technology Stack

**Backend (Server)**
- **Runtime**: Cloudflare Workers
- **Framework**: Hono (lightweight web framework)
- **API**: tRPC (end-to-end type safety)
- **Database**: Drizzle ORM + Cloudflare D1 (SQLite)
- **Auth**: better-auth
- **Discord**: Bot integration with slash commands
- **Language**: TypeScript

**Frontend (Web)**
- **Framework**: React 19 with modern patterns
- **Routing**: TanStack Router (file-based)
- **State**: TanStack Query (server state) + TanStack Store (client state)
- **Styling**: Tailwind CSS + Radix UI components
- **Forms**: TanStack Form
- **Build**: Vite
- **Language**: TypeScript

**Development Tools**
- **Monorepo**: Turbo + pnpm workspaces
- **Code Quality**: Biome (linting/formatting)
- **Type Safety**: TypeScript (strict mode)
- **Environment**: Doppler (env management)

## Development Commands

### Starting Development
```bash
pnpm dev              # Start all services
pnpm dev:server       # Server only
pnpm dev:web          # Web app only
```

### Database Operations
```bash
pnpm db:generate      # Generate migrations
pnpm db:migrate       # Apply migrations locally
pnpm db:studio        # Open Drizzle Studio
pnpm db:migrate:prod  # Apply to production
```

### Code Quality
```bash
pnpm check           # Biome linting/formatting
pnpm check-types     # TypeScript checking
```

### Deployment
```bash
pnpm cf:deploy       # Deploy to Cloudflare
```

## Key File Locations

### Server (apps/server/)
- **Entry**: `src/index.ts` (Hono app setup)
- **API Routes**: `src/routers/` (tRPC routers by feature)
- **Database**: `src/db/schema/` (Drizzle schemas)
- **Discord**: `src/discord/commands/` (slash commands)
- **Middleware**: `src/middlewares/`
- **Utilities**: `src/lib/`
- **Cron Jobs**: `src/cron/`
- **Config**: `drizzle.config.ts`, `wrangler.jsonc`

### Web (apps/web/)
- **Entry**: `src/main.tsx` (React app setup)
- **Routes**: `src/routes/` (file-based routing)
  - `(auth)/` - Authentication routes
  - `(faction)/` - Faction features
  - `(protected)/` - Protected routes
- **Components**: `src/components/`
  - `ui/` - Radix-based reusable components
  - `navbar/` - Navigation
  - `permissions/` - Permission guards
- **Hooks**: `src/hooks/` (custom React hooks)
- **Utils**: `src/lib/` (utilities and configs)
- **Styles**: `src/styles/` (Tailwind + themes)

### Shared (packages/shared/)
- **Types**: Common TypeScript types
- **Utilities**: Shared functions
- **Constants**: Application constants
- **Schemas**: Validation schemas

## Development Patterns

### API Development
- Use tRPC for type-safe endpoints
- Organize routers by feature/domain
- Use Drizzle ORM for database operations
- Handle authentication with better-auth
- Follow RESTful principles where applicable

### Frontend Development
- Use file-based routing with TanStack Router
- Implement responsive design (mobile-first)
- Use TanStack Query for server state
- Follow component composition patterns
- Implement proper loading/error states
- Use Radix UI for accessible components

### Database Development
- Define schemas in separate files
- Use migrations for schema changes
- Implement proper indexes for performance
- Use transactions for multi-table operations
- Follow naming conventions (snake_case)

### Code Quality Standards
- Strict TypeScript configuration
- Biome for consistent formatting
- Pre-commit hooks for quality checks
- Use meaningful variable/function names
- Write self-documenting code
- Implement proper error handling

## Common Tasks

### Adding New API Endpoint
1. Create/modify router in `apps/server/src/routers/`
2. Define types in shared package if needed
3. Export router from `apps/server/src/routers/index.ts`
4. Use in frontend with tRPC client

### Adding New Route
1. Create file in `apps/web/src/routes/`
2. Use appropriate route group: `(auth)`, `(faction)`, `(protected)`
3. Implement loading states and error boundaries
4. Add navigation links if needed

### Database Schema Changes
1. Modify schema files in `apps/server/src/db/schema/`
2. Run `pnpm db:generate` to create migration
3. Review generated migration
4. Run `pnpm db:migrate` to apply locally
5. Test changes thoroughly

### Adding UI Components
1. Create in `apps/web/src/components/ui/` for reusable components
2. Use Radix UI primitives as base
3. Apply Tailwind classes for styling
4. Export from component index if applicable
5. Follow accessibility best practices

## Environment Variables

- **Local**: Managed via Doppler, stored in `.dev.vars` (server) and `.env.development` (web)
- **Production**: Automatically synced during deployment
- Never commit environment files to git

## Authentication & Permissions

- Uses better-auth for session management
- Permission-based access control
- Route-level protection with guards
- Component-level permission checks

## Testing Guidance

- Focus on type safety through TypeScript
- Test API endpoints through tRPC
- Use React Testing Library for component tests
- Test database operations with local D1

## Deployment Process

1. All code goes through type checking
2. Biome ensures code quality
3. Builds are orchestrated by Turbo
4. Server deploys to Cloudflare Workers
5. Web app builds for static hosting
6. Database migrations run automatically

## Getting Help

When asking for assistance:
1. Specify which app/package you're working in
2. Include relevant file paths
3. Mention if it's related to a specific feature
4. Provide error messages or expected behavior
5. Include relevant code context

This monorepo prioritizes type safety, developer experience, and modern patterns. All suggestions should align with these principles and the established architecture.