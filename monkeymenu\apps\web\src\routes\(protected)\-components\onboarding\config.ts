import {
	EXTERNAL_URLS,
	TOAST_MESSAGES,
	TornApiKeySchema,
} from "@monkeymenu/shared";

// Configuration for role assignment polling
export const ROLE_POLLING_CONFIG = {
	INTERVAL_MS: 500, // Poll every 500ms
	MAX_ATTEMPTS: 20, // Maximum 10 seconds (20 * 500ms)
	TIMEOUT_MS: 10000, // 10 second absolute timeout
};

// Onboarding steps configuration
export const ONBOARDING_STEPS = [
	{
		id: "welcome",
		title: "Welcome",
		description: "Introduction to MonkeyMenu",
	},
	{
		id: "theme-setup",
		title: "Theme Setup",
		description: "Customize your experience",
	},
	{
		id: "api-setup",
		title: "API Setup",
		description: "Connect your Torn account",
	},
	{
		id: "feature-tour",
		title: "Feature Tour",
		description: "Discover what you can do",
	},
	{
		id: "discord-setup",
		title: "Discord Setup",
		description: "Connect & join our server",
	},
	{
		id: "completion",
		title: "Complete",
		description: "All set up!",
	},
] as const;

// Platform features configuration
export const PLATFORM_FEATURES = [
	{
		icon: "CreditCard" as const,
		title: "Faction Banking",
		description:
			"Submit withdrawal requests, track your transaction history, and monitor faction finances. Features approval workflows, real-time status updates, and detailed spending analytics to help manage your faction funds efficiently.",
		category: "Financial",
	},
	{
		icon: "Search" as const,
		title: "Target Finder",
		description:
			"Discover optimal attack targets using advanced filtering and analysis tools. Search by stats, level ranges, online status, and faction affiliations. Get detailed target intelligence to maximize your attack success rate.",
		category: "Combat",
	},
	{
		icon: "Zap" as const,
		title: "Announcements",
		description:
			"Stay updated with important faction news, event notifications, and leadership messages. Receive real-time updates about faction activities, wars, and strategic announcements from your leadership team.",
		category: "Information",
	},
	{
		icon: "BookOpen" as const,
		title: "Guides & Resources",
		description:
			"Access comprehensive faction guides, strategy tutorials, and helpful resources curated by leadership. Learn faction procedures, combat strategies, and get tips to improve your gameplay and contribution.",
		category: "Information",
	},
	{
		icon: "Users" as const,
		title: "Member Profiles",
		description:
			"Manage your personal profile, update your information, and view your faction statistics. Track your progress, contributions, and role within the faction. Connect social accounts and customize your experience.",
		category: "Social",
	},
	{
		icon: "Shield" as const,
		title: "Admin Panel",
		description:
			"Comprehensive faction management tools for leadership. Manage member roles, permissions, and access levels. Monitor faction activities, approve banking requests, and oversee faction operations.",
		category: "Management",
		adminOnly: true,
	},
];

export function getInitialStepFromRedirect() {
	if (typeof window !== "undefined") {
		const searchParams = new URLSearchParams(window.location.search);
		const linkStatus = searchParams.get("linkStatus");
		const savedStep = sessionStorage.getItem("onboarding-step");

		if (linkStatus === "success" && savedStep) {
			sessionStorage.removeItem("onboarding-step");
			return savedStep;
		}
	}
	return null;
}

// API key validation
export { TornApiKeySchema };

// External URLs
export { EXTERNAL_URLS, TOAST_MESSAGES };
