import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useAppForm } from "@/components/ui/tanstack-form";
import { trpc } from "@/lib/trpc-client";
import {
	IMAGE_CONFIG,
	UserProfileSchema,
	VALIDATION_LIMITS,
	getInitials,
} from "@monkeymenu/shared";
import { useMutation } from "@tanstack/react-query";
import { Camera, Loader2, User } from "lucide-react";
import { useRef, useState } from "react";
import { toast } from "sonner";
import type { ProfileData } from "./types";

interface UserProfileCardProps {
	profile?: ProfileData | null;
	isLoading: boolean;
	onProfileUpdate: () => void;
}

export function UserProfileCard({
	profile,
	isLoading,
	onProfileUpdate,
}: UserProfileCardProps) {
	const [isEditing, setIsEditing] = useState(false);
	const fileInputRef = useRef<HTMLInputElement>(null);

	const updateMutation = useMutation(
		trpc.user.updateProfile.mutationOptions({
			onSuccess: () => {
				onProfileUpdate();
				setIsEditing(false);
				toast.success("Profile updated successfully");
			},
			onError: (error) => {
				toast.error(error.message || "Failed to update profile");
			},
		}),
	);

	const form = useAppForm({
		defaultValues: {
			name: "",
		},
		validators: {
			onChange: UserProfileSchema,
		},
		onSubmit: async ({ value }) => {
			if (profile?.user) {
				const userData = profile.user;
				updateMutation.mutate({
					name: value.name.trim(),
					image: userData.image || undefined,
				});
			}
		},
	});

	// Update form when profile data loads
	if (profile?.user) {
		const userData = profile.user;
		// Only update the form if we haven't edited the name yet
		if (form.getFieldValue("name") === "") {
			form.setFieldValue("name", userData.name || "New User");
		}
	}

	const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (!file) return;

		if (!file.type.startsWith("image/")) {
			toast.error("Please select an image file");
			return;
		}

		// Create an image element to check dimensions
		const img = new Image();
		img.src = URL.createObjectURL(file);
		img.onload = () => {
			if (
				img.width > IMAGE_CONFIG.MAX_SIZE ||
				img.height > IMAGE_CONFIG.MAX_SIZE
			) {
				toast.error(
					`Image must be ${IMAGE_CONFIG.MAX_SIZE}x${IMAGE_CONFIG.MAX_SIZE} pixels or smaller`,
				);
				return;
			}

			const reader = new FileReader();
			reader.onload = async (e) => {
				const base64Image = e.target?.result as string;
				if (!profile?.user) return;

				const userData = profile.user;
				updateMutation.mutate({
					name: userData.name || "New User",
					image: base64Image,
				});
			};
			reader.readAsDataURL(file);
		};
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<User className="h-5 w-5" />
					User Profile
				</CardTitle>
			</CardHeader>
			{isLoading ? (
				<CardContent className="flex items-center justify-center py-4">
					<div className="text-center text-muted-foreground">
						<Loader2 className="mx-auto mb-1 animate-spin" />
						Loading profile...
					</div>
				</CardContent>
			) : !profile ? (
				<CardContent className="py-3">
					<div className="text-center text-muted-foreground">
						Profile not available
					</div>
				</CardContent>
			) : (
				<CardContent>
					{(() => {
						const userData = profile.user;
						return (
							<div className="flex flex-col items-center sm:flex-row sm:items-start sm:space-x-4">
								<div className="group relative">
									<div
										className={`relative h-20 w-20 overflow-hidden rounded-full bg-muted ${isEditing ? "ring-2 ring-primary ring-offset-2" : ""}`}
									>
										{userData.image ? (
											<img
												src={userData.image}
												alt={userData.name || "User"}
												className="h-full w-full rounded-full object-cover"
											/>
										) : (
											<div className="flex h-full w-full items-center justify-center rounded-full bg-primary font-bold text-3xl text-primary-foreground">
												{getInitials(userData.name, userData.email)}
											</div>
										)}
										<Button
											variant="ghost"
											size="icon"
											className={`absolute inset-0 flex h-full w-full items-center justify-center bg-black/50 transition-opacity ${isEditing ? "opacity-100 hover:opacity-80" : "opacity-0 group-hover:opacity-100"}`}
											onClick={() => fileInputRef.current?.click()}
										>
											{isEditing ? (
												<Camera className="h-5 w-5 text-white" />
											) : (
												<span className="text-sm text-white">Change</span>
											)}
										</Button>
									</div>
									<input
										ref={fileInputRef}
										type="file"
										accept="image/*"
										className="hidden"
										onChange={handleFileChange}
									/>
								</div>
								<div className="w-full flex-1 text-center sm:text-left">
									{isEditing ? (
										<form.AppForm>
											<form
												onSubmit={(e) => {
													e.preventDefault();
													e.stopPropagation();
													void form.handleSubmit();
												}}
												className="space-y-3"
											>
												<form.AppField name="name">
													{(field) => (
														<field.FormItem>
															<field.FormLabel>Display Name</field.FormLabel>
															<field.FormControl>
																<div className="relative">
																	<Input
																		value={field.state.value}
																		onChange={(e) =>
																			field.handleChange(e.target.value)
																		}
																		onBlur={field.handleBlur}
																		className="text-lg"
																		maxLength={VALIDATION_LIMITS.USER_NAME_MAX}
																		disabled={updateMutation.isPending}
																	/>
																	<span className="-translate-y-1/2 absolute top-1/2 right-2 text-muted-foreground text-sm">
																		{field.state.value.length}/
																		{VALIDATION_LIMITS.USER_NAME_MAX}
																	</span>
																</div>
															</field.FormControl>
															<field.FormMessage />
														</field.FormItem>
													)}
												</form.AppField>
												<div className="flex space-x-2">
													<form.Subscribe>
														{(state) => (
															<Button
																type="submit"
																disabled={
																	!state.canSubmit ||
																	state.isSubmitting ||
																	updateMutation.isPending
																}
															>
																{updateMutation.isPending
																	? "Saving..."
																	: "Save"}
															</Button>
														)}
													</form.Subscribe>
													<Button
														variant="outline"
														onClick={() => {
															setIsEditing(false);
															form.setFieldValue(
																"name",
																userData.name || "New User",
															);
														}}
													>
														Cancel
													</Button>
												</div>
											</form>
										</form.AppForm>
									) : (
										<div className="space-y-0.5">
											<div className="flex items-start justify-between">
												<h3 className="font-semibold text-2xl">
													{userData.name || "New User"}
												</h3>
												<Button
													variant="outline"
													size="sm"
													onClick={() => setIsEditing(true)}
												>
													Edit
												</Button>
											</div>
											<p className="text-muted-foreground">{userData.email}</p>
											<p className="text-muted-foreground/50 text-sm italic">
												Member since{" "}
												{new Date(userData.createdAt).toLocaleDateString()}
											</p>
										</div>
									)}
								</div>
							</div>
						);
					})()}
				</CardContent>
			)}
		</Card>
	);
}
