import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { SiDiscord } from "@icons-pack/react-simple-icons";
import { DiscordConnectionButton, DiscordJoinButton } from "./DiscordButtons";
import { DiscordSyncButton } from "./DiscordSyncButton";

export function DiscordIntegration() {
	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<SiDiscord className="h-5 w-5" />
					Discord Integration
				</CardTitle>
				<div className="text-muted-foreground text-sm">
					Connect your Discord account and join our server for enhanced features
				</div>
			</CardHeader>
			<CardContent className="space-y-4">
				<div>
					<h3 className="font-semibold text-lg">Account Connection</h3>
					<p className="text-muted-foreground">
						Link your Discord account to receive notifications, access faction
						features, and get automatic role assignments
					</p>
				</div>
				<div className="flex flex-col gap-2">
					<DiscordConnectionButton />
					<DiscordSyncButton />
					<DiscordJoinButton />
				</div>
			</CardContent>
		</Card>
	);
}
