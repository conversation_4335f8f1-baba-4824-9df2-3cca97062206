import { <PERSON>flare<PERSON>orkerServer, SlashCreator } from "slash-create/web";

// Discord commands
import { BalanceCommand } from "./commands/balance";
import { WithdrawCommand } from "./commands/withdraw";
import { TargetsCommand } from "./commands/targets";
import { WarCommand } from "./commands/war";
import { HelpCommand } from "./commands/help";
import { LinkCommand } from "./commands/link";

interface Env {
  DISCORD_APPLICATION_ID: string;
  DISCORD_PUBLIC_KEY: string;
  DISCORD_BOT_TOKEN: string;
  CONVEX_URL: string;
}

// Global variables for the worker
let creator: SlashCreator;

function initializeDiscordBot(env: Env) {
  if (creator) return { creator };

  // Create SlashCreator instance
  creator = new SlashCreator({
    applicationID: env.DISCORD_APPLICATION_ID,
    publicKey: env.DISCORD_PUBLIC_KEY,
    token: env.DISCORD_BOT_TOKEN,
  });

  // Set up CloudflareWorkerServer
  const server = new CloudflareWorkerServer();
  creator.withServer(server);

  // Register commands with Convex URL
  creator.registerCommand(new BalanceCommand(creator, env.CONVEX_URL));
  creator.registerCommand(new WithdrawCommand(creator, env.CONVEX_URL));
  creator.registerCommand(new TargetsCommand(creator, env.CONVEX_URL));
  creator.registerCommand(new WarCommand(creator, env.CONVEX_URL));
  creator.registerCommand(new HelpCommand(creator, env.CONVEX_URL));
  creator.registerCommand(new LinkCommand(creator, env.CONVEX_URL));

  // Set up event handlers
  creator.on("warn", (message) => console.warn("[Discord Bot]", message));
  creator.on("error", (error) => 
    console.error("[Discord Bot]", error.stack || error.toString())
  );
  creator.on("commandRun", (command, _, ctx) =>
    console.info(
      `[Discord Bot] ${ctx.user.username}#${ctx.user.discriminator} (${ctx.user.id}) ran command ${command.commandName}`
    )
  );
  creator.on("commandError", (command, error) =>
    console.error(
      `[Discord Bot] Command ${command.commandName} errored:`,
      error.stack || error.toString()
    )
  );

  return { creator };
}

export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    const url = new URL(request.url);

    // Handle Discord interactions
    if (url.pathname === "/discord/interactions" && request.method === "POST") {
      const { creator } = initializeDiscordBot(env);
      const server = creator.server as CloudflareWorkerServer;
      return server.fetch(request, env, ctx);
    }

    // Health check endpoint
    if (url.pathname === "/health") {
      return new Response("Discord Worker is healthy", { status: 200 });
    }

    // Default response
    return new Response("Discord Worker - Use /discord/interactions for Discord commands", {
      status: 404,
    });
  },
};
