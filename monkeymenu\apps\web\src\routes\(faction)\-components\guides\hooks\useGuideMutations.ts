import { trpc } from "@/lib/trpc-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export function useGuides() {
	return useQuery({
		...trpc.guides.getAll.queryOptions(),
		refetchOnWindowFocus: true,
		refetchInterval: 5 * 60 * 1000,
		refetchIntervalInBackground: false,
	});
}

export function useGuideMutations() {
	const queryClient = useQueryClient();

	const createMutation = useMutation(
		trpc.guides.create.mutationOptions({
			onSuccess: () => {
				queryClient.invalidateQueries({
					queryKey: [["guides", "getAll"]],
				});
				queryClient.invalidateQueries({
					queryKey: [["guides", "getById"]],
				});
				toast.success("Guide created successfully");
			},
			onError: (error) => {
				toast.error(`Failed to create guide: ${error.message}`);
			},
		}),
	);

	const updateMutation = useMutation(
		trpc.guides.update.mutationOptions({
			onSuccess: () => {
				queryClient.invalidateQueries({
					queryKey: [["guides", "getAll"]],
				});
				queryClient.invalidateQueries({
					queryKey: [["guides", "getById"]],
				});
				toast.success("Guide updated successfully");
			},
			onError: (error) => {
				toast.error(`Failed to update guide: ${error.message}`);
			},
		}),
	);

	const deleteMutation = useMutation(
		trpc.guides.delete.mutationOptions({
			onSuccess: () => {
				queryClient.invalidateQueries({
					queryKey: [["guides", "getAll"]],
				});
				queryClient.invalidateQueries({
					queryKey: [["guides", "getById"]],
				});
				toast.success("Guide deleted successfully");
			},
			onError: (error) => {
				toast.error(`Failed to delete guide: ${error.message}`);
			},
		}),
	);

	return {
		createMutation,
		updateMutation,
		deleteMutation,
	};
}
