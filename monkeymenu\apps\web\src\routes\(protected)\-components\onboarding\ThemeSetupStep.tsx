import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	THEME_STYLES,
	useTheme,
	useThemeStyle,
} from "@/lib/combined-theme-provider";
import { ArrowRight, Moon, Sun } from "lucide-react";
import type { StepComponentProps } from "./types";

export function ThemeSetupStep({ onNextStep, onPrevStep }: StepComponentProps) {
	const { theme, setTheme } = useTheme();
	const { themeStyle, setThemeStyle } = useThemeStyle();

	const themeInfo = {
		default: {
			name: "Default",
			description: "Clean and professional",
			primaryColor: "oklch(0.3261 0 0)", // Dark gray
			accentColor: "oklch(0.7716 0 0)", // Medium gray
			preview: "Clean, minimal design with subtle grays",
		},
		"t3-chat": {
			name: "T3 Chat",
			description: "Modern with pink/purple accents",
			primaryColor: "oklch(0.5316 0.1409 355.1999)", // Pink/magenta
			accentColor: "oklch(0.6038 0.2363 344.4657)", // Light pink
			preview: "Vibrant pink theme with modern styling",
		},
		claude: {
			name: "Claude",
			description: "Warm orange and amber tones",
			primaryColor: "oklch(0.6171 0.1375 39.0427)", // Orange
			accentColor: "oklch(0.5583 0.1276 42.9956)", // Warm orange
			preview: "Warm and inviting with orange highlights",
		},
	} as const;

	return (
		<Card>
			<CardContent className="space-y-6 p-8">
				{/* Header */}
				<div className="space-y-2 text-center">
					<h2 className="font-bold text-2xl tracking-tight">
						Choose Your Style
					</h2>
					<p className="mx-auto max-w-md text-muted-foreground leading-relaxed">
						Pick your preferred theme and colors. You can always change this
						later.
					</p>
				</div>

				{/* Theme Selector */}
				<div className="space-y-6">
					{/* Light/Dark Mode Toggle */}
					<div>
						<h3 className="mb-3 font-medium">Choose your mode</h3>
						<div className="flex gap-3">
							<Button
								variant={theme === "light" ? "default" : "outline"}
								onClick={() => setTheme("light")}
								className="h-12 flex-1 gap-2"
							>
								<Sun className="h-4 w-4" />
								<span>Light</span>
							</Button>
							<Button
								variant={theme === "dark" ? "default" : "outline"}
								onClick={() => setTheme("dark")}
								className="h-12 flex-1 gap-2"
							>
								<Moon className="h-4 w-4" />
								<span>Dark</span>
							</Button>
						</div>
					</div>

					{/* Theme Style Selection */}
					<div>
						<h3 className="mb-3 font-medium">Pick your color style</h3>
						<Select value={themeStyle} onValueChange={setThemeStyle}>
							<SelectTrigger className="w-full">
								<SelectValue>
									<div className="flex items-center gap-3">
										{/* Color Preview for Selected */}
										<div className="flex gap-1.5">
											<div
												className="h-3 w-3 rounded-full border border-border/50"
												style={{
													backgroundColor: themeInfo[themeStyle].primaryColor,
												}}
											/>
											<div
												className="h-3 w-3 rounded-full border border-border/50"
												style={{
													backgroundColor: themeInfo[themeStyle].accentColor,
												}}
											/>
										</div>
										<span>{themeInfo[themeStyle].name}</span>
									</div>
								</SelectValue>
							</SelectTrigger>
							<SelectContent>
								{THEME_STYLES.map((style) => {
									const info = themeInfo[style.value as keyof typeof themeInfo];
									return (
										<SelectItem key={style.value} value={style.value}>
											<div className="flex items-center gap-3">
												{/* Color Preview in Dropdown */}
												<div className="flex gap-1.5">
													<div
														className="h-3 w-3 rounded-full border border-border/50"
														style={{ backgroundColor: info.primaryColor }}
													/>
													<div
														className="h-3 w-3 rounded-full border border-border/50"
														style={{ backgroundColor: info.accentColor }}
													/>
												</div>
												<div className="flex-1">
													<div className="font-medium">{info.name}</div>
													<div className="text-muted-foreground text-sm">
														{info.description}
													</div>
												</div>
											</div>
										</SelectItem>
									);
								})}
							</SelectContent>
						</Select>
					</div>

					{/* Preview Note */}
					<div className="text-center">
						<p className="text-muted-foreground text-xs">
							Changes apply instantly. You can customize this later in settings.
						</p>
					</div>
				</div>
			</CardContent>

			<CardFooter className="flex gap-3 p-8 pt-0">
				<Button
					type="button"
					variant="outline"
					onClick={onPrevStep}
					className="flex-1"
				>
					Back
				</Button>
				<Button onClick={onNextStep} className="flex-1" size="lg">
					Continue
					<ArrowRight className="ml-2 h-4 w-4" />
				</Button>
			</CardFooter>
		</Card>
	);
}
