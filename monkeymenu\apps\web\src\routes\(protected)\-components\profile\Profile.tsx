import { trpc } from "@/lib/trpc-client";
import { useQuery } from "@tanstack/react-query";
import { useLocation } from "@tanstack/react-router";
import { useEffect } from "react";
import { toast } from "sonner";
import { ApiKeyManagement } from "./ApiKeyManagement";
import { DiscordIntegration } from "./DiscordIntegration";
import { ThemeSettings } from "./ThemeSettings";
import { UserProfileCard } from "./UserProfileCard";
import type { ProfileData } from "./types";

export function Profile() {
	const location = useLocation();

	// Check for account linking success from URL parameters
	useEffect(() => {
		const searchParams = new URLSearchParams(location.search);
		const linkStatus = searchParams.get("linkStatus");

		if (linkStatus === "success") {
			toast.success("Discord account successfully linked!");
			// Remove the query parameter to prevent showing the message again on refresh
			const newUrl = new URL(window.location.href);
			newUrl.searchParams.delete("linkStatus");
			window.history.replaceState({}, "", newUrl.toString());
		}
	}, [location.search]);

	const profile = useQuery(trpc.user.getProfile.queryOptions());

	const handleProfileUpdate = () => {
		profile.refetch();
	};

	return (
		<div className="container mx-auto w-full min-w-0 max-w-[90vw] px-3 py-2 sm:max-w-2xl sm:px-4 md:max-w-3xl">
			<div className="mx-auto w-full max-w-xl space-y-8">
				<div className="space-y-4">
					<h2 className="text-center font-bold text-2xl">Profile</h2>

					<UserProfileCard
						profile={profile.data as ProfileData | null}
						isLoading={profile.isLoading}
						onProfileUpdate={handleProfileUpdate}
					/>

					{/* API Key Management Section */}
					<ApiKeyManagement />

					{/* Theme Settings Section */}
					<ThemeSettings />

					{/* Discord Connection Section */}
					<DiscordIntegration />
				</div>
			</div>
		</div>
	);
}
