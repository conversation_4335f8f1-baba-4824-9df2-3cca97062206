import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { trpc } from "@/lib/trpc-client";
import { useQuery } from "@tanstack/react-query";
import { Award, Calendar, Eye, Swords, Target, Trophy } from "lucide-react";
import { useMemo, useState } from "react";
import { AttackTimelineDialog } from "./AttackTimelineDialog";
import { ChainDetailsDialog } from "./ChainDetailsDialog";
import { PlayerPerformanceDialog } from "./PlayerPerformanceDialog";
import { WarDialogContent } from "./WarDialogContent";
import type {
	AttacksData,
	ChainReportsData,
	Faction,
	RankedWar,
	WarReportData,
} from "./types";
import { MY_FACTION_ID, formatDate } from "./utils";

export function Wars() {
	const { data, isLoading, error } = useQuery({
		...trpc.wars.listRankedWars.queryOptions(),
	});

	const [selectedWarId, setSelectedWarId] = useState<number | null>(null);
	const [selectedChainId, setSelectedChainId] = useState<number>(0);
	const [selectedPlayerId, setSelectedPlayerId] = useState<number | undefined>(
		undefined,
	);
	const [chainDetailsOpen, setChainDetailsOpen] = useState(false);
	const [playerPerformanceOpen, setPlayerPerformanceOpen] = useState(false);
	const [timelineOpen, setTimelineOpen] = useState(false);

	const warReportQuery = useQuery({
		...trpc.wars.getRankedWarReport.queryOptions({ warId: selectedWarId ?? 0 }),
		enabled: selectedWarId !== null,
	});

	const chainReportsQuery = useQuery({
		...trpc.wars.getWarChainReports.queryOptions({ warId: selectedWarId ?? 0 }),
		enabled: selectedWarId !== null,
	});

	const attacksQuery = useQuery({
		...trpc.wars.getWarAttacks.queryOptions({ warId: selectedWarId ?? 0 }),
		enabled: selectedWarId !== null,
	});

	// Calculate war statistics
	const warStats = useMemo(() => {
		if (!data?.rankedWars) {
			return { total: 0, wins: 0, losses: 0, winRate: 0 };
		}

		const total = data.rankedWars.length;

		// Completed wars are those with a non-null winner
		const completedWars = data.rankedWars.filter(
			(war: RankedWar) => war.winner !== null,
		);

		const wins = completedWars.filter(
			(war: RankedWar) => war.winner === MY_FACTION_ID,
		).length;

		const losses = completedWars.length - wins;

		const winRate =
			completedWars.length > 0 ? (wins / completedWars.length) * 100 : 0;

		return { total, wins, losses, winRate };
	}, [data?.rankedWars]);

	return (
		<div className="space-y-6">
			{/* Header Section */}
			<div>
				<h1 className="font-bold text-3xl text-foreground">⚔️ Ranked Wars</h1>
				<p className="text-muted-foreground">
					View faction war history and detailed battle reports
				</p>
			</div>

			{/* Statistics Cards */}
			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="font-medium text-sm">Total Wars</CardTitle>
						<Swords className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="font-bold text-2xl">{warStats.total}</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="font-medium text-sm">Wins</CardTitle>
						<Trophy className="h-4 w-4 text-green-600" />
					</CardHeader>
					<CardContent>
						<div className="font-bold text-2xl text-green-600">
							{warStats.wins}
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="font-medium text-sm">Losses</CardTitle>
						<Target className="h-4 w-4 text-red-600" />
					</CardHeader>
					<CardContent>
						<div className="font-bold text-2xl text-red-600">
							{warStats.losses}
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="font-medium text-sm">Win Rate</CardTitle>
						<Award className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="font-bold text-2xl">
							{warStats.winRate.toFixed(1)}%
						</div>
					</CardContent>
				</Card>
			</div>

			{isLoading && <Skeleton className="h-48 w-full" />}
			{error && (
				<p className="text-destructive">Failed to load wars: {String(error)}</p>
			)}
			{data && (
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Calendar className="h-5 w-5" />
							War History
						</CardTitle>
					</CardHeader>
					<CardContent>
						{/* Desktop Table View */}
						<div className="hidden lg:block">
							<Table>
								<TableHeader>
									<TableRow>
										<TableHead>War ID</TableHead>
										<TableHead>Opponent</TableHead>
										<TableHead>Started</TableHead>
										<TableHead>Ended</TableHead>
										<TableHead>Result</TableHead>
										<TableHead>Score</TableHead>
										<TableHead />
									</TableRow>
								</TableHeader>
								<TableBody>
									{data.rankedWars.map((war) => {
										const opponent = war.factions.find(
											(f: Faction) => f.id !== MY_FACTION_ID,
										);
										const ourFaction = war.factions.find(
											(f: Faction) => f.id === MY_FACTION_ID,
										);
										const isCompleted = war.winner !== null;
										const isWin = isCompleted && war.winner === MY_FACTION_ID;
										const statusLabel = isCompleted
											? isWin
												? "Victory"
												: "Defeat"
											: "In Progress";
										const badgeVariant = isCompleted
											? isWin
												? "default"
												: "destructive"
											: "secondary";
										return (
											<TableRow key={war.id}>
												<TableCell className="font-mono">#{war.id}</TableCell>
												<TableCell className="font-medium">
													{opponent?.name ?? "-"}
												</TableCell>
												<TableCell>{formatDate(war.start)}</TableCell>
												<TableCell>{formatDate(war.end)}</TableCell>
												<TableCell>
													<Badge variant={badgeVariant}>{statusLabel}</Badge>
												</TableCell>
												<TableCell>
													<span
														className={
															isWin
																? "font-semibold text-green-600"
																: "text-red-600"
														}
													>
														{ourFaction?.score ?? 0}
													</span>
													{" / "}
													<span
														className={
															!isWin
																? "font-semibold text-green-600"
																: "text-red-600"
														}
													>
														{opponent?.score ?? 0}
													</span>
												</TableCell>
												<TableCell>
													<Dialog>
														<DialogTrigger asChild>
															<Button
																variant="outline"
																size="sm"
																onClick={() => setSelectedWarId(war.id)}
																disabled={!isCompleted}
															>
																<Eye className="mr-2 h-4 w-4" />
																View Report
															</Button>
														</DialogTrigger>
														<DialogContent className="max-h-[90vh] w-full max-w-7xl overflow-y-auto">
															{selectedWarId === war.id && (
																<>
																	{warReportQuery.isLoading && (
																		<div className="space-y-4 p-6">
																			<Skeleton className="h-32 w-full" />
																			<Skeleton className="h-64 w-full" />
																		</div>
																	)}
																	{warReportQuery.error && (
																		<div className="p-6">
																			<p className="text-destructive">
																				Failed to load report:{" "}
																				{String(warReportQuery.error)}
																			</p>
																		</div>
																	)}
																	{warReportQuery.data && (
																		<WarDialogContent
																			warReport={
																				warReportQuery.data
																					.warReport as WarReportData
																			}
																			chainReports={
																				chainReportsQuery.data as ChainReportsData
																			}
																			attacksData={
																				attacksQuery.data as AttacksData
																			}
																			warId={war.id}
																			statusLabel={statusLabel}
																			badgeVariant={badgeVariant}
																			onOpenChainDetails={(chainId) => {
																				setSelectedChainId(chainId);
																				setChainDetailsOpen(true);
																			}}
																			onOpenPlayerPerformance={(playerId) => {
																				setSelectedPlayerId(playerId);
																				setPlayerPerformanceOpen(true);
																			}}
																			onOpenTimeline={() =>
																				setTimelineOpen(true)
																			}
																		/>
																	)}
																</>
															)}
														</DialogContent>
													</Dialog>
												</TableCell>
											</TableRow>
										);
									})}
								</TableBody>
							</Table>
						</div>

						{/* Mobile Card View - Continue with mobile responsiveness but keeping it shorter */}
						<div className="space-y-3 lg:hidden">
							{data.rankedWars.map((war) => {
								const opponent = war.factions.find(
									(f: Faction) => f.id !== MY_FACTION_ID,
								);
								const ourFaction = war.factions.find(
									(f: Faction) => f.id === MY_FACTION_ID,
								);
								const isCompleted = war.winner !== null;
								const isWin = isCompleted && war.winner === MY_FACTION_ID;
								const statusLabel = isCompleted
									? isWin
										? "Victory"
										: "Defeat"
									: "In Progress";
								const badgeVariant = isCompleted
									? isWin
										? "default"
										: "destructive"
									: "secondary";

								return (
									<Card key={war.id} className="p-4">
										<div className="space-y-3">
											<div className="flex items-center justify-between">
												<div className="min-w-0 flex-1">
													<div className="truncate font-medium">
														War #{war.id} vs {opponent?.name ?? "Unknown"}
													</div>
													<div className="text-muted-foreground text-sm">
														{formatDate(war.start)} - {formatDate(war.end)}
													</div>
												</div>
												<Badge variant={badgeVariant}>{statusLabel}</Badge>
											</div>
											<div className="flex items-center justify-between">
												<div className="text-sm">
													Score: {ourFaction?.score ?? 0} /{" "}
													{opponent?.score ?? 0}
												</div>
												<Dialog>
													<DialogTrigger asChild>
														<Button
															variant="outline"
															size="sm"
															onClick={() => setSelectedWarId(war.id)}
															disabled={!isCompleted}
														>
															<Eye className="mr-2 h-4 w-4" />
															View
														</Button>
													</DialogTrigger>
													<DialogContent className="max-h-[90vh] w-full max-w-7xl overflow-y-auto">
														{selectedWarId === war.id && (
															<>
																{warReportQuery.isLoading && (
																	<div className="space-y-4 p-6">
																		<Skeleton className="h-32 w-full" />
																		<Skeleton className="h-64 w-full" />
																	</div>
																)}
																{warReportQuery.error && (
																	<div className="p-6">
																		<p className="text-destructive">
																			Failed to load report:{" "}
																			{String(warReportQuery.error)}
																		</p>
																	</div>
																)}
																{warReportQuery.data && (
																	<WarDialogContent
																		warReport={
																			warReportQuery.data
																				.warReport as WarReportData
																		}
																		chainReports={
																			chainReportsQuery.data as ChainReportsData
																		}
																		attacksData={
																			attacksQuery.data as AttacksData
																		}
																		warId={war.id}
																		statusLabel={statusLabel}
																		badgeVariant={badgeVariant}
																		onOpenChainDetails={(chainId) => {
																			setSelectedChainId(chainId);
																			setChainDetailsOpen(true);
																		}}
																		onOpenPlayerPerformance={(playerId) => {
																			setSelectedPlayerId(playerId);
																			setPlayerPerformanceOpen(true);
																		}}
																		onOpenTimeline={() => setTimelineOpen(true)}
																	/>
																)}
															</>
														)}
													</DialogContent>
												</Dialog>
											</div>
										</div>
									</Card>
								);
							})}
						</div>
					</CardContent>
				</Card>
			)}

			{/* Dialog Components */}
			<ChainDetailsDialog
				isOpen={chainDetailsOpen}
				onClose={() => setChainDetailsOpen(false)}
				warId={selectedWarId ?? 0}
				chainId={selectedChainId}
			/>

			<PlayerPerformanceDialog
				isOpen={playerPerformanceOpen}
				onClose={() => setPlayerPerformanceOpen(false)}
				warId={selectedWarId ?? 0}
				playerId={selectedPlayerId}
			/>

			<AttackTimelineDialog
				isOpen={timelineOpen}
				onClose={() => setTimelineOpen(false)}
				warId={selectedWarId ?? 0}
			/>
		</div>
	);
}
