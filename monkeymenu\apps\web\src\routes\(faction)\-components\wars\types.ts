// Type definitions for the frontend
export interface FactionMember {
	id: number;
	name: string;
	level: number;
	attacks: number;
	score: number;
}

export interface Faction {
	id: number;
	name: string;
	score: number;
	attacks: number;
	members: FactionMember[];
}

export interface RankedWar {
	id: number;
	factions: Faction[];
	winner: number | null;
	start: number;
	end?: number | null;
}

export interface ChainAttacker {
	id: number;
	name?: string;
	respect: {
		total: number;
		average: number;
		best: number;
	};
	attacks: {
		total: number;
		war: number;
		assists: number;
	};
}

export interface CombinedChainStats {
	playerId: number;
	playerName?: string;
	totalHits: number;
	totalWarHits: number;
	totalAssists: number;
	totalRespect: number;
	averageRespect: number;
	bestRespect: number;
	chainCount: number;
}

export interface MemberStats {
	attackerId: number;
	attackerName?: string;
	totalAttacks: number;
	insideHits: number;
	outsideHits: number;
	assists: number;
	totalRespect: number;
}

export interface ChainReportDetails {
	chain: number;
	respect: number;
	war: number;
	targets: number;
	leave: number;
	mug: number;
	hospitalize: number;
	overseas: number;
	assists: number;
	retaliations: number;
	draws: number;
	escapes: number;
	losses: number;
	best: number;
	members: number;
}

// Match server response where details can be optional
export interface ChainReportFromServer {
	id: number;
	details?: ChainReportDetails;
	attackers?: ChainAttacker[];
}

// Add new interfaces for better organization
export interface WarOverviewStats {
	duration: string;
	winner: string;
	status: string;
	totalChains: number;
	totalRespect: number;
}

// Add proper type definitions for the war dialog props
export interface WarReportData {
	factions: Faction[];
	winner: number | null;
	start: number;
	end: number | null;
	forfeit?: boolean;
}

export interface ChainReportsData {
	chainReports: ChainReportFromServer[];
	playerNames: Record<string, string>;
	combinedChainStats: CombinedChainStats[];
}

export interface AttacksData {
	assists: number;
	insideHits: number;
	outsideHits: number;
	totalRespect: number;
	memberStats: MemberStats[];
}

export interface WarDialogContentProps {
	warReport: WarReportData;
	chainReports: ChainReportsData | undefined;
	attacksData: AttacksData | undefined;
	warId: number;
	statusLabel: string;
	badgeVariant: "default" | "destructive" | "secondary";
	onOpenChainDetails: (chainId: number) => void;
	onOpenPlayerPerformance: (playerId?: number) => void;
	onOpenTimeline: () => void;
}

// Player Performance Dialog types
export interface PerformanceTrends {
	dailyBreakdown: Array<{
		day: number;
		attacks: number;
		respect: number;
	}>;
	hourlyBreakdown: Array<{
		hour: number;
		attacks: number;
		efficiency: number;
	}>;
}

export interface ChainContribution {
	chainId: number;
	attacks: number;
	respect: number;
	chainPosition: string;
	performance: "Above Average" | "Below Average" | "Average";
	warHits: number;
	assists: number;
}

export interface BattleEffectiveness {
	hitAccuracy: number;
	targetPrioritization: {
		insideTargetRatio: number;
		warTargetFocus: number;
	};
	timingAnalysis: {
		averageTimeBetweenAttacks: number;
		peakActivityWindow: string;
		consistencyScore: number;
	};
}

export interface ComparativeRanking {
	respectRank: number;
	attackCountRank: number;
	efficiencyRank: number;
	participationRank: number;
}

export interface OverallStats {
	totalAttacks: number;
	totalRespect: number;
	averageRespect: number;
	bestRespect: number;
	chainAttacks: number;
	nonChainAttacks: number;
	insideHits: number;
	assists: number;
	participationRate: number;
	activeHours: number;
}

export interface PlayerDetail {
	playerId: number;
	playerName?: string;
	overallStats: OverallStats;
	comparativeRanking: ComparativeRanking;
	battleEffectiveness: BattleEffectiveness;
	performanceTrends: PerformanceTrends;
	chainContributions: ChainContribution[];
}

export interface PlayerPerformanceData {
	playerDetails: PlayerDetail[];
}

// Attack Timeline Dialog types
export interface HourlyActivity {
	hour: number;
	intensity: number;
}

export interface TopPerformer {
	playerId: number;
	playerName?: string;
	respect: number;
}

export interface PeriodStats {
	totalAttacks: number;
	uniqueAttackers: number;
	respectEarned: number;
	insideHits: number;
	outsideHits: number;
	assists: number;
	chainAttacks: number;
	nonChainAttacks: number;
}

export interface TimelinePeriod {
	timestamp: number;
	period: string;
	stats: PeriodStats;
	topPerformers: TopPerformer[];
}

export interface ActivityHeatmap {
	hourlyActivity: HourlyActivity[];
}

export interface AttackTimelineData {
	heatmap: ActivityHeatmap;
	timeline: TimelinePeriod[];
}

// Dialog props interfaces
export interface PlayerPerformanceDialogProps {
	isOpen: boolean;
	onClose: () => void;
	warId: number;
	playerId?: number;
}

export interface AttackTimelineDialogProps {
	isOpen: boolean;
	onClose: () => void;
	warId: number;
}
