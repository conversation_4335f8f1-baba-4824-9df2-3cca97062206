import { useState } from 'react';
import { useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';

interface CreateWarDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CreateWarDialog({ isOpen, onClose }: CreateWarDialogProps) {
  const [factionId, setFactionId] = useState(53100); // Default our faction
  const [factionName, setFactionName] = useState('Our Faction');
  const [enemyFactionId, setEnemyFactionId] = useState(0);
  const [enemyFactionName, setEnemyFactionName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const createWar = useMutation(api.wars.createWar);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!enemyFactionId || !enemyFactionName.trim()) return;

    setIsSubmitting(true);
    try {
      await createWar({
        factionId,
        factionName,
        enemyFactionId,
        enemyFactionName: enemyFactionName.trim(),
        startTime: Date.now(),
      });
      
      onClose();
      resetForm();
    } catch (error) {
      console.error('Failed to create war:', error);
      alert('Failed to create war');
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setEnemyFactionId(0);
    setEnemyFactionName('');
  };

  const handleClose = () => {
    onClose();
    resetForm();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">⚔️ Start New War</h2>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Our Faction (read-only) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Our Faction
              </label>
              <div className="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-gray-600">
                {factionName} (ID: {factionId})
              </div>
            </div>

            {/* Enemy Faction ID */}
            <div>
              <label htmlFor="enemyFactionId" className="block text-sm font-medium text-gray-700 mb-1">
                Enemy Faction ID *
              </label>
              <input
                type="number"
                id="enemyFactionId"
                value={enemyFactionId || ''}
                onChange={(e) => setEnemyFactionId(parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter enemy faction ID"
                required
                min="1"
              />
            </div>

            {/* Enemy Faction Name */}
            <div>
              <label htmlFor="enemyFactionName" className="block text-sm font-medium text-gray-700 mb-1">
                Enemy Faction Name *
              </label>
              <input
                type="text"
                id="enemyFactionName"
                value={enemyFactionName}
                onChange={(e) => setEnemyFactionName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter enemy faction name"
                required
              />
            </div>

            {/* Info */}
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Note:</strong> The war will start immediately. Make sure the enemy faction ID and name are correct.
              </p>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting || !enemyFactionId || !enemyFactionName.trim()}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Starting War...' : '⚔️ Start War'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}