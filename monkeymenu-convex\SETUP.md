# MonkeyMenu Convex Migration Setup

This document outlines the steps to set up and run the Convex migration of MonkeyMenu.

## Prerequisites

- Node.js 18+ installed
- npm or pnpm package manager
- Convex account (sign up at https://convex.dev)
- Clerk account for authentication (sign up at https://clerk.com)

## Setup Steps

### 1. Install Dependencies

```bash
npm install
# or
pnpm install
```

### 2. Set up Convex

```bash
# Initialize Convex project
npx convex dev --until-success

# This will:
# - Create a new Convex project
# - Generate the convex/_generated folder
# - Give you a CONVEX_URL for your .env file
```

### 3. Set up Clerk Authentication

1. Go to https://clerk.com and create a new application
2. Copy your publishable key
3. Add it to your `.env.local` file

### 4. Environment Variables

Create a `.env.local` file based on `.env.example`:

```bash
cp .env.example .env.local
```

Fill in the required values:
- `VITE_CONVEX_URL` - From step 2
- `VITE_CLERK_PUBLISHABLE_KEY` - From step 3
- `VITE_TORN_API_KEY` - Your Torn API key (optional for development)

### 5. Database Schema

The database schema is already defined in `convex/schema.ts`. When you run `convex dev`, it will automatically create the tables.

### 6. Start Development

```bash
npm run dev
```

This will start both the Vite dev server and Convex in development mode.

## Project Structure Explanation

### Backend (Convex Functions)

- `convex/schema.ts` - Database schema definition
- `convex/users.ts` - User management functions
- `convex/announcements.ts` - Announcements system
- `convex/banking.ts` - Banking and transactions
- `convex/_generated/` - Auto-generated Convex files

### Frontend (React + Vite)

- `src/main.tsx` - Entry point with Convex and Clerk providers
- `src/components/` - Reusable UI components
- `src/routes/` - Application routes
- `src/lib/` - Utility functions and helpers

## Migration Notes

### Key Differences from Original MonkeyMenu

1. **Database**: Using Convex's built-in database instead of external database
2. **API**: Convex functions replace tRPC endpoints
3. **Real-time**: Native real-time subscriptions via Convex
4. **Authentication**: Clerk integration instead of custom auth
5. **Deployment**: Single command deployment with Convex

### Data Migration

To migrate data from the original MonkeyMenu:

1. Export data from the original database
2. Create migration scripts in `convex/migrations/`
3. Use Convex functions to import the data

### Features to Implement

- [ ] User authentication and profiles
- [ ] Banking system with transactions
- [ ] Announcements management
- [ ] Target finder with real-time updates
- [ ] War tracking and statistics
- [ ] Discord bot integration
- [ ] Permissions and role management
- [ ] Guides system
- [ ] Admin dashboard

## Development Workflow

1. **Backend Changes**: Modify functions in `convex/` folder
2. **Frontend Changes**: Modify components and routes in `src/` folder
3. **Schema Changes**: Update `convex/schema.ts` and restart `convex dev`
4. **Testing**: Use Convex dashboard for function testing
5. **Deployment**: Run `npm run build` for production build

## Useful Commands

```bash
# Development
npm run dev              # Start both Convex and Vite
npm run dev:web          # Start only Vite
npm run dev:convex       # Start only Convex

# Building
npm run build           # Build for production
npm run preview         # Preview production build

# Linting
npm run lint           # Run ESLint
npm run type-check     # Run TypeScript checks

# Convex specific
npx convex dashboard   # Open Convex dashboard
npx convex deploy      # Deploy to production
npx convex import      # Import data
npx convex export      # Export data
```

## Next Steps

1. Complete the basic authentication flow
2. Implement the banking system
3. Add real-time features for wars and targets
4. Set up Discord bot integration
5. Create admin interfaces
6. Add comprehensive testing
7. Set up CI/CD pipeline
