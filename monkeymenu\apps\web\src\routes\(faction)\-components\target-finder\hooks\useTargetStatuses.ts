import { useEffect, useRef, useState } from "react";
import type { CooldownData, TargetWithStatus } from "../types";

export function useTargetStatuses(
	targets: TargetWithStatus[],
	cooldownData: CooldownData | null | undefined,
) {
	const [localStatuses, setLocalStatuses] = useState<Record<string, string>>(
		{},
	);
	const [localCooldown, setLocalCooldown] = useState<number>(0);

	const intervalRef = useRef<NodeJS.Timeout | null>(null);
	const cooldownIntervalRef = useRef<NodeJS.Timeout | null>(null);

	// Initialize local statuses for new targets (especially enemy faction members)
	useEffect(() => {
		if (targets.length === 0) return;

		setLocalStatuses((prev) => {
			const updated = { ...prev };
			for (const target of targets) {
				// Only set if not already in local statuses
				if (!updated[target.tornId] && target.status.includes("Hospitalized")) {
					updated[target.tornId] = target.status;
				}
			}
			return updated;
		});
	}, [targets]);

	// Real-time countdown timer for hospitalized targets
	useEffect(() => {
		if (targets.length === 0) return;

		if (intervalRef.current) clearInterval(intervalRef.current);

		intervalRef.current = setInterval(() => {
			setLocalStatuses((prev) => {
				const updated: Record<string, string> = { ...prev };

				for (const target of targets) {
					// Check both local status and original target status
					const currentStatus = prev[target.tornId] ?? target.status;
					if (currentStatus?.startsWith("Hospitalized")) {
						const match = currentStatus.match(/(\d+)m (\d+)s/);
						if (match?.[1] && match[2]) {
							let m = Number.parseInt(match[1], 10);
							let s = Number.parseInt(match[2], 10);

							if (s > 0) {
								s--;
							} else if (m > 0) {
								m--;
								s = 59;
							}

							if (m === 0 && s === 0) {
								updated[target.tornId] = "Okay";
							} else {
								updated[target.tornId] = `Hospitalized (${m}m ${s}s)`;
							}
						}
					}
				}

				return updated;
			});
		}, 1000);

		return () => {
			if (intervalRef.current) clearInterval(intervalRef.current);
		};
	}, [targets]);

	// Real-time countdown timer for cooldown, now driven by cooldownData.remaining
	useEffect(() => {
		// Clear any existing interval when effect re-runs or component unmounts
		if (cooldownIntervalRef.current) {
			clearInterval(cooldownIntervalRef.current);
			cooldownIntervalRef.current = null;
		}

		const initialCooldownTime = cooldownData?.remaining;

		if (initialCooldownTime && initialCooldownTime > 0) {
			setLocalCooldown(initialCooldownTime); // Initialize or reset localCooldown

			cooldownIntervalRef.current = setInterval(() => {
				setLocalCooldown((prev) => {
					if (prev <= 1) {
						if (cooldownIntervalRef.current) {
							clearInterval(cooldownIntervalRef.current);
							cooldownIntervalRef.current = null;
						}
						return 0;
					}
					return prev - 1;
				});
			}, 1000);
		} else {
			setLocalCooldown(0); // Ensure cooldown is 0 if no initial positive value
		}

		// Cleanup function for when the component unmounts or dependencies change
		return () => {
			if (cooldownIntervalRef.current) {
				clearInterval(cooldownIntervalRef.current);
				cooldownIntervalRef.current = null;
			}
		};
	}, [cooldownData?.remaining]); // Depend on the source cooldown value

	return {
		localStatuses,
		localCooldown,
	};
}
