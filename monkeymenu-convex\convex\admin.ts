import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { getUserByClerkIdInternal } from "./users";

export const getSystemStats = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user || !user.permissions.includes("admin.view")) {
      throw new Error("Not authorized");
    }

    // Get counts from each table
    const users = await ctx.db.query("users").collect();
    const transactions = await ctx.db.query("transactions").collect();
    const wars = await ctx.db.query("wars").collect();
    const guides = await ctx.db.query("guides").collect();
    const announcements = await ctx.db.query("announcements").collect();

    const now = Date.now();
    const weekAgo = now - 7 * 24 * 60 * 60 * 1000;

    return {
      users: {
        total: users.length,
        active: users.filter(u => u.isActive).length,
        recentRegistrations: users.filter(u => u.createdAt > weekAgo).length,
      },
      banking: {
        totalTransactions: transactions.length,
        pendingWithdrawals: await ctx.db
          .query("withdrawalRequests")
          .filter((q) => q.eq(q.field("status"), "PENDING"))
          .collect()
          .then(results => results.length),
      },
      wars: {
        active: wars.filter(w => w.status === "active").length,
        total: wars.length,
      },
      guides: {
        published: guides.filter(g => g.isPublished).length,
        total: guides.length,
      },
      announcements: {
        active: announcements.filter(a => a.isActive).length,
        total: announcements.length,
      },
      performance: {
        averageResponseTime: 120,
        successRate: 99.8,
        errorRate: 0.2,
      },
      recentActions: [
        {
          type: "user",
          description: "New user registration: user123",
          timestamp: now - 1000 * 60 * 30, // 30 minutes ago
        },
        {
          type: "banking",
          description: "Withdrawal request processed",
          timestamp: now - 1000 * 60 * 60, // 1 hour ago
        },
        {
          type: "system",
          description: "Scheduled backup completed",
          timestamp: now - 1000 * 60 * 60 * 2, // 2 hours ago
        },
      ],
    };
  },
});

export const getAllUsers = query({
  args: {
    includeInactive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user || !user.permissions.includes("admin.view")) {
      throw new Error("Not authorized");
    }

    let query = ctx.db.query("users");
    
    if (!args.includeInactive) {
      query = query.filter((q) => q.eq(q.field("isActive"), true));
    }

    return await query.order("desc").collect();
  },
});

export const getAvailablePermissions = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user || !user.permissions.includes("admin.view")) {
      throw new Error("Not authorized");
    }

    // Return available permissions matching our aligned permission system
    return [
      { name: "admin.view", description: "Administrative access" },
      { name: "admin.users.suspend", description: "Suspend users" },
      { name: "admin.users.recheck", description: "Recheck user API keys" },
      { name: "admin.users.delete", description: "Delete users" },
      { name: "dashboard.view", description: "View dashboard" },
      { name: "banking.view", description: "View banking information" },
      { name: "banking.request", description: "Request banking operations" },
      { name: "banking.requests.manage", description: "Manage banking requests" },
      { name: "wars.view", description: "View war information" },
      { name: "announcements.view", description: "View announcements" },
      { name: "announcements.manage", description: "Manage all announcements" },
      { name: "guides.view", description: "View guides" },
      { name: "guides.manage", description: "Manage all guides" },
      { name: "target.finder.view", description: "View target finder" },
      { name: "target.finder.manage.shared_lists", description: "Manage shared target lists" },
      { name: "discord.manage.verification", description: "Manage Discord verification" },
    ];
  },
});

export const updateUserPermissions = mutation({
  args: {
    userId: v.id("users"),
    permissions: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user || !user.permissions.includes("admin.view")) {
      throw new Error("Not authorized");
    }

    await ctx.db.patch(args.userId, {
      permissions: args.permissions,
      updatedAt: Date.now(),
    });
  },
});

export const toggleUserStatus = mutation({
  args: {
    userId: v.id("users"),
    isActive: v.boolean(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user || !user.permissions.includes("admin.view")) {
      throw new Error("Not authorized");
    }

    await ctx.db.patch(args.userId, {
      isActive: args.isActive,
      updatedAt: Date.now(),
    });
  },
});

export const getDatabaseStats = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user || !user.permissions.includes("admin.view")) {
      throw new Error("Not authorized");
    }

    // Get stats for each table
    const tables = {
      users: await ctx.db.query("users").collect(),
      accounts: await ctx.db.query("accounts").collect(),
      transactions: await ctx.db.query("transactions").collect(),
      announcements: await ctx.db.query("announcements").collect(),
      guides: await ctx.db.query("guides").collect(),
      wars: await ctx.db.query("wars").collect(),
      targets: await ctx.db.query("targets").collect(),
      withdrawalRequests: await ctx.db.query("withdrawalRequests").collect(),
    };

    const totalRecords = Object.values(tables).reduce((sum, records) => sum + records.length, 0);

    const tableStats = Object.entries(tables).reduce((stats, [tableName, records]) => {
      stats[tableName] = {
        count: records.length,
        lastUpdated: records.length > 0 
          ? Math.max(...records.map(r => r.updatedAt || r.createdAt))
          : null,
      };
      return stats;
    }, {} as Record<string, { count: number; lastUpdated: number | null }>);

    return {
      totalRecords,
      tables: tableStats,
      lastBackup: Date.now() - 24 * 60 * 60 * 1000, // Mock: 1 day ago
    };
  },
});

export const getTableData = query({
  args: {
    tableName: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user || !user.permissions.includes("admin.view")) {
      throw new Error("Not authorized");
    }

    const limit = args.limit || 50;

    // Map table names to actual queries
    switch (args.tableName) {
      case "users":
        return await ctx.db.query("users").order("desc").take(limit);
      case "accounts":
        return await ctx.db.query("accounts").order("desc").take(limit);
      case "transactions":
        return await ctx.db.query("transactions").order("desc").take(limit);
      case "announcements":
        return await ctx.db.query("announcements").order("desc").take(limit);
      case "guides":
        return await ctx.db.query("guides").order("desc").take(limit);
      case "wars":
        return await ctx.db.query("wars").order("desc").take(limit);
      case "targets":
        return await ctx.db.query("targets").order("desc").take(limit);
      case "withdrawalRequests":
        return await ctx.db.query("withdrawalRequests").order("desc").take(limit);
      default:
        throw new Error("Invalid table name");
    }
  },
});

export const exportTableData = mutation({
  args: {
    tableName: v.string(),
    format: v.union(v.literal("json"), v.literal("csv")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user || !user.permissions.includes("admin.view")) {
      throw new Error("Not authorized");
    }

    // Get all data from the specified table
    let data: any[] = [];
    
    switch (args.tableName) {
      case "users":
        data = await ctx.db.query("users").collect();
        break;
      case "accounts":
        data = await ctx.db.query("accounts").collect();
        break;
      case "transactions":
        data = await ctx.db.query("transactions").collect();
        break;
      case "announcements":
        data = await ctx.db.query("announcements").collect();
        break;
      case "guides":
        data = await ctx.db.query("guides").collect();
        break;
      case "wars":
        data = await ctx.db.query("wars").collect();
        break;
      case "targets":
        data = await ctx.db.query("targets").collect();
        break;
      case "withdrawalRequests":
        data = await ctx.db.query("withdrawalRequests").collect();
        break;
      default:
        throw new Error("Invalid table name");
    }

    if (args.format === "json") {
      return {
        data: JSON.stringify(data, null, 2),
        count: data.length,
      };
    } else {
      // Convert to CSV
      if (data.length === 0) {
        return { data: "", count: 0 };
      }

      const headers = Object.keys(data[0]);
      const csvRows = [
        headers.join(","),
        ...data.map(row => 
          headers.map(header => {
            const value = row[header];
            // Escape CSV values
            if (typeof value === "string" && (value.includes(",") || value.includes('"') || value.includes("\n"))) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value;
          }).join(",")
        )
      ];

      return {
        data: csvRows.join("\n"),
        count: data.length,
      };
    }
  },
});

export const createBackup = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user || !user.permissions.includes("admin.view")) {
      throw new Error("Not authorized");
    }

    // In a real implementation, this would create an actual backup
    // For now, we'll just return a mock backup ID
    const backupId = `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    return {
      backupId,
      timestamp: Date.now(),
    };
  },
});

export const cleanupOldData = mutation({
  args: {
    tableName: v.string(),
    olderThanDays: v.number(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user || !user.permissions.includes("admin.view")) {
      throw new Error("Not authorized");
    }

    const cutoffDate = Date.now() - (args.olderThanDays * 24 * 60 * 60 * 1000);
    let deletedCount = 0;

    // Only allow cleanup for certain tables and with safety checks
    switch (args.tableName) {
      case "transactions":
        const oldTransactions = await ctx.db
          .query("transactions")
          .filter((q) => q.lt(q.field("createdAt"), cutoffDate))
          .collect();
        
        for (const transaction of oldTransactions) {
          await ctx.db.delete(transaction._id);
          deletedCount++;
        }
        break;
        
      case "sessions":
        const oldSessions = await ctx.db
          .query("sessions")
          .filter((q) => q.lt(q.field("createdAt"), cutoffDate))
          .collect();
        
        for (const session of oldSessions) {
          await ctx.db.delete(session._id);
          deletedCount++;
        }
        break;
        
      default:
        throw new Error("Cleanup not allowed for this table");
    }

    return {
      deletedCount,
      tableName: args.tableName,
      cutoffDate,
    };
  },
});

export const importData = mutation({
  args: {
    tableName: v.string(),
    data: v.string(), // JSON string
    mode: v.union(v.literal("replace"), v.literal("append")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user || !user.permissions.includes("admin.view")) {
      throw new Error("Not authorized");
    }

    let importData: any[];
    try {
      importData = JSON.parse(args.data);
    } catch (error) {
      throw new Error("Invalid JSON data");
    }

    if (!Array.isArray(importData)) {
      throw new Error("Data must be an array");
    }

    let importedCount = 0;
    let errorCount = 0;

    // If replace mode, clear existing data (be very careful with this)
    if (args.mode === "replace") {
      // Only allow replace for certain safe tables
      const allowedTables = ["guides", "announcements"];
      if (!allowedTables.includes(args.tableName)) {
        throw new Error("Replace mode not allowed for this table");
      }

      // Get all existing records and delete them
      const existingRecords = await ctx.db.query(args.tableName as any).collect();
      for (const record of existingRecords) {
        await ctx.db.delete(record._id);
      }
    }

    // Import new data
    for (const item of importData) {
      try {
        // Remove _id and _creationTime fields if they exist
        const { _id, _creationTime, ...cleanItem } = item;
        
        // Add current timestamp for createdAt and updatedAt
        const now = Date.now();
        const itemWithTimestamps = {
          ...cleanItem,
          createdAt: cleanItem.createdAt || now,
          updatedAt: now,
        };

        // Insert based on table type
        switch (args.tableName) {
          case "guides":
            await ctx.db.insert("guides", itemWithTimestamps);
            break;
          case "announcements":
            await ctx.db.insert("announcements", itemWithTimestamps);
            break;
          case "targets":
            await ctx.db.insert("targets", itemWithTimestamps);
            break;
          default:
            throw new Error("Import not supported for this table");
        }
        
        importedCount++;
      } catch (error) {
        console.error(`Failed to import item:`, error);
        errorCount++;
      }
    }

    return {
      importedCount,
      errorCount,
      total: importData.length,
      tableName: args.tableName,
      mode: args.mode,
    };
  },
});

export const getBackupHistory = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user || !user.permissions.includes("admin.view")) {
      throw new Error("Not authorized");
    }

    // In a real implementation, this would query a backups table
    // For now, return mock data
    return [
      {
        id: "backup_1",
        timestamp: Date.now() - 24 * 60 * 60 * 1000,
        type: "full",
        size: "2.4 MB",
        status: "completed",
        tables: ["users", "accounts", "transactions", "guides", "announcements"],
      },
      {
        id: "backup_2", 
        timestamp: Date.now() - 7 * 24 * 60 * 60 * 1000,
        type: "incremental",
        size: "1.1 MB", 
        status: "completed",
        tables: ["transactions", "warAttacks"],
      },
      {
        id: "backup_3",
        timestamp: Date.now() - 14 * 24 * 60 * 60 * 1000,
        type: "full",
        size: "2.1 MB",
        status: "completed", 
        tables: ["users", "accounts", "transactions", "guides", "announcements"],
      },
    ];
  },
});

export const restoreFromBackup = mutation({
  args: {
    backupId: v.string(),
    tables: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user || !user.permissions.includes("admin.view")) {
      throw new Error("Not authorized");
    }

    // In a real implementation, this would restore from actual backup files
    // For now, return a mock response
    return {
      backupId: args.backupId,
      tablesRestored: args.tables || ["users", "accounts", "transactions"],
      timestamp: Date.now(),
      status: "completed",
    };
  },
});

export const createScheduledBackup = mutation({
  args: {
    frequency: v.union(v.literal("daily"), v.literal("weekly"), v.literal("monthly")),
    tables: v.array(v.string()),
    retentionDays: v.number(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user || !user.permissions.includes("admin.view")) {
      throw new Error("Not authorized");
    }

    // In a real implementation, this would create a cron job or scheduled task
    return {
      scheduleId: `schedule_${Date.now()}`,
      frequency: args.frequency,
      tables: args.tables,
      retentionDays: args.retentionDays,
      nextRun: Date.now() + (args.frequency === "daily" ? 24 * 60 * 60 * 1000 : 
                           args.frequency === "weekly" ? 7 * 24 * 60 * 60 * 1000 :
                           30 * 24 * 60 * 60 * 1000),
      created: Date.now(),
    };
  },
});