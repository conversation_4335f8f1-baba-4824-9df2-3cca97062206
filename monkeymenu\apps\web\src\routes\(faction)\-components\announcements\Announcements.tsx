import { useAnnouncementsLiveStoreSync } from "@/lib/announcements-livestore-bridge";
import { useStore } from "@livestore/react";
import { useState } from "react";
import { announcements$ } from "../../../../livestore/queries";
import type { Announcement } from "../../../../livestore/types";
import { AnnouncementFilters } from "./AnnouncementFilters";
import { AnnouncementHeader } from "./AnnouncementHeader";
import { AnnouncementViewer } from "./AnnouncementViewer";
import { AnnouncementsList } from "./AnnouncementsList";
import { CreateAnnouncementDialog } from "./CreateAnnouncementDialog";
import {
	useAnnouncementFilters,
	useAnnouncementMutations,
	useAnnouncements,
} from "./hooks";
import type { AnnouncementData, ViewMode } from "./types";

export function Announcements() {
	console.log("📢 Announcements component rendering");

	const { store } = useStore();

	// Set up LiveStore sync (this will sync tRPC data to LiveStore automatically)
	useAnnouncementsLiveStoreSync();

	const [showCreateForm, setShowCreateForm] = useState(false);
	const [editingAnnouncement, setEditingAnnouncement] =
		useState<AnnouncementData | null>(null);
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedCategory, setSelectedCategory] = useState<string>("all");
	const [viewMode, setViewMode] = useState<ViewMode>("grid");
	const [selectedAnnouncement, setSelectedAnnouncement] =
		useState<AnnouncementData | null>(null);

	console.log("📢 About to start data queries");

	// Get real-time data from LiveStore
	let liveStoreAnnouncements: Announcement[] = [];

	try {
		const announcementsResults = store.useQuery(announcements$);
		liveStoreAnnouncements = Array.isArray(announcementsResults)
			? (announcementsResults as Announcement[])
			: [];
	} catch (error) {
		console.error("❌ Error in LiveStore queries:", error);
	}

	// Fallback to tRPC data if LiveStore is not available
	const tRPCAnnouncements = useAnnouncements();

	console.log("📢 Announcements query result:", {
		liveStoreAnnouncements,
		tRPCAnnouncements: tRPCAnnouncements.data,
		isLoading: tRPCAnnouncements.isLoading,
	});

	// Use LiveStore data when available, fallback to tRPC
	const announcementsData =
		liveStoreAnnouncements.length > 0
			? liveStoreAnnouncements.map((announcement) => ({
					announcement: {
						id: Number(announcement.id),
						title: announcement.title,
						content: announcement.content,
						category: announcement.category,
						authorId: announcement.authorId,
						createdAt:
							typeof announcement.createdAt === "string"
								? announcement.createdAt
								: announcement.createdAt.toISOString(),
						updatedAt:
							typeof announcement.updatedAt === "string"
								? announcement.updatedAt
								: announcement.updatedAt.toISOString(),
					},
					author: {
						id: announcement.authorId,
						name: announcement.authorName,
						image: null, // Not available in LiveStore
					},
				}))
			: tRPCAnnouncements.data || [];

	const { deleteMutation } = useAnnouncementMutations();
	const { filteredAnnouncements, announcementStats } = useAnnouncementFilters(
		announcementsData,
		searchQuery,
		selectedCategory,
	);

	const handleCreateNew = () => {
		setEditingAnnouncement(null);
		setShowCreateForm(true);
	};

	const handleEdit = (announcement: AnnouncementData) => {
		setEditingAnnouncement(announcement);
		setShowCreateForm(true);
	};

	const handleDelete = (announcement: AnnouncementData) => {
		deleteMutation.mutate({
			id: announcement.announcement.id,
		});
	};

	const handleView = (announcement: AnnouncementData) => {
		setSelectedAnnouncement(announcement);
	};

	const handleCloseCreateDialog = (open: boolean) => {
		if (!open) {
			setEditingAnnouncement(null);
		}
		setShowCreateForm(open);
	};

	const handleCloseViewer = (open: boolean) => {
		if (!open) {
			setSelectedAnnouncement(null);
		}
	};

	return (
		<div className="space-y-6">
			<AnnouncementHeader onCreateNew={handleCreateNew} />

			<AnnouncementFilters
				searchQuery={searchQuery}
				onSearchChange={setSearchQuery}
				selectedCategory={selectedCategory}
				onCategoryChange={setSelectedCategory}
				viewMode={viewMode}
				onViewModeChange={setViewMode}
				announcementStats={announcementStats}
			/>

			<CreateAnnouncementDialog
				open={showCreateForm}
				onOpenChange={handleCloseCreateDialog}
				editingAnnouncement={editingAnnouncement}
			/>

			<AnnouncementViewer
				announcement={selectedAnnouncement}
				open={!!selectedAnnouncement}
				onOpenChange={handleCloseViewer}
			/>

			<AnnouncementsList
				announcements={filteredAnnouncements}
				viewMode={viewMode}
				isLoading={tRPCAnnouncements.isLoading}
				error={tRPCAnnouncements.error}
				searchQuery={searchQuery}
				onView={handleView}
				onEdit={handleEdit}
				onDelete={handleDelete}
				onCreateNew={handleCreateNew}
			/>
		</div>
	);
}
