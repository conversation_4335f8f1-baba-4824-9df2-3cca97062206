import { useState, useMemo } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from "../../../convex/_generated/dataModel";

interface TransactionHistoryProps {
  userId: Id<"users">;
  limit?: number;
  showFilters?: boolean;
  withdrawalRequests?: Array<{
    _id: string;
    amount: number;
    status: string;
    createdAt: number;
    processedAt?: number;
    reason?: string;
    userId: Id<"users">;
  }>;
}

export function TransactionHistory({ userId, limit, showFilters = true, withdrawalRequests }: TransactionHistoryProps) {
  const [filters, setFilters] = useState({
    status: ''
  });

  // Get withdrawal requests from Convex (fallback if withdrawalRequests not provided)
  const userWithdrawals = useQuery(
    api.banking.getUserWithdrawalRequests,
    withdrawalRequests ? "skip" : {
      userId,
      limit: limit || 20,
    }
  );

  // Apply client-side filtering to withdrawal requests
  const displayData = useMemo(() => {
    let data = withdrawalRequests || userWithdrawals;
    if (!data) return data;

    // Apply filters to the data
    let filtered = data;

    if (filters.status) {
      filtered = filtered.filter(item => item.status.toLowerCase() === filters.status.toLowerCase());
    }

    // Currency filter is not needed since we only deal with cash withdrawals

    return filtered;
  }, [withdrawalRequests, userWithdrawals, filters]);

  // Handle permission errors
  const hasError = displayData === null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace('$', '$');
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'accepted': return 'text-blue-600 bg-blue-100';
      case 'failed': return 'text-red-600 bg-red-100';
      case 'cancelled': return 'text-gray-600 bg-gray-100';
      case 'expired': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-xl font-semibold mb-6 text-gray-900">Withdrawal History</h3>
      
      {showFilters && (
        <div className="mb-6">
          <div className="max-w-xs">
            <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700 mb-1">
              Filter by Status
            </label>
            <select
              id="status-filter"
              value={filters.status}
              onChange={(e) => setFilters({ status: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Statuses</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="failed">Failed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        </div>
      )}

      {hasError ? (
        <div className="text-center py-8 bg-red-50 rounded-lg">
          <p className="text-red-600">Unable to load withdrawal history. You may not have the required permissions.</p>
        </div>
      ) : !displayData ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : displayData.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No withdrawal requests found.</p>
        </div>
      ) : (
        <>
          <div className="space-y-3">
            {displayData?.map((request: any) => (
              <div key={request._id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-4">
                  <div className="text-2xl">
                    💰
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <p className="font-medium text-gray-900">
                        Withdrawal Request
                      </p>
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(request.status)}`}>
                        {request.status}
                      </span>
                    </div>
                    <p className="text-sm text-gray-500">
                      {new Date(request.createdAt).toLocaleString()}
                    </p>
                    {request.reason && (
                      <p className="text-xs text-gray-400">Reason: {request.reason}</p>
                    )}
                  </div>
                </div>
                
                <div className="text-right">
                  <p className="font-semibold text-orange-600">
                    -{formatCurrency(request.amount)}
                  </p>
                  <p className="text-xs text-gray-500 uppercase">
                    cash
                  </p>
                </div>
              </div>
            ))}
          </div>

        </>
      )}
    </div>
  );
}