import { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from "../../../convex/_generated/dataModel";

interface TransactionHistoryProps {
  userId: Id<"users">;
  limit?: number;
  showFilters?: boolean;
  combinedHistory?: Array<{
    id: string;
    type: 'transaction' | 'withdrawal_request';
    amount: number;
    status: string;
    createdAt: number;
    processedAt?: number;
    reference?: string;
    withdrawalRequestId?: string;
  }>;
}

export function TransactionHistory({ userId, limit, showFilters = true, combinedHistory }: TransactionHistoryProps) {
  const [filters, setFilters] = useState({
    type: '',
    status: '',
    currency: 'cash'
  });

  // Get transaction history from Convex (fallback if combinedHistory not provided)
  const transactions = useQuery(
    api.banking.getTransactionHistory,
    combinedHistory ? "skip" : {
      userId,
      currency: filters.currency || undefined,
      type: filters.type || undefined,
      status: filters.status || undefined,
      limit: limit || 20,
      offset: 0,
    }
  );

  // Use combined history if provided, otherwise use transactions
  const displayData = combinedHistory || transactions?.transactions;

  // Handle permission errors
  const hasError = displayData === null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace('$', '$');
  };

  const getTransactionIcon = (type: string, itemType?: string) => {
    if (itemType === 'withdrawal_request') {
      return '📋'; // Clipboard icon for withdrawal requests
    }

    switch (type) {
      case 'deposit': return '📥';
      case 'withdrawal': return '📤';
      case 'transfer': return '🔄';
      case 'fee': return '💳';
      case 'withdrawal_cancelled': return '❌';
      default: return '💰';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'accepted': return 'text-blue-600 bg-blue-100';
      case 'failed': return 'text-red-600 bg-red-100';
      case 'cancelled': return 'text-gray-600 bg-gray-100';
      case 'expired': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const isIncoming = (item: any) => {
    // Withdrawal requests are always outgoing
    if (item.type === 'withdrawal_request') return false;
    // For transactions, check if it's incoming
    return item.toAccount && item.toAccount.userId === userId;
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-xl font-semibold mb-6 text-gray-900">Transaction History</h3>
      
      {showFilters && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div>
            <label htmlFor="type-filter" className="block text-sm font-medium text-gray-700 mb-1">
              Type
            </label>
            <select
              id="type-filter"
              value={filters.type}
              onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Types</option>
              <option value="deposit">Deposit</option>
              <option value="withdrawal">Withdrawal</option>
              <option value="transfer">Transfer</option>
              <option value="fee">Fee</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              id="status-filter"
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Statuses</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="failed">Failed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="currency-filter" className="block text-sm font-medium text-gray-700 mb-1">
              Currency
            </label>
            <select
              id="currency-filter"
              value={filters.currency}
              onChange={(e) => setFilters(prev => ({ ...prev, currency: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="cash">Cash</option>
              <option value="points">Points</option>
              <option value="tokens">Tokens</option>
            </select>
          </div>
        </div>
      )}

      {hasError ? (
        <div className="text-center py-8 bg-red-50 rounded-lg">
          <p className="text-red-600">Unable to load transaction history. You may not have the required permissions.</p>
        </div>
      ) : !displayData ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : displayData.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No transactions found.</p>
        </div>
      ) : (
        <>
          <div className="space-y-3">
            {displayData?.map((item: any) => (
              <div key={item.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-4">
                  <div className="text-2xl">
                    {getTransactionIcon(item.type, item.type)}
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <p className="font-medium text-gray-900 capitalize">
                        {item.type === 'withdrawal_request' ? 'Withdrawal Request' : item.type.replace('_', ' ')}
                      </p>
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(item.status)}`}>
                        {item.status}
                      </span>
                    </div>
                    <p className="text-sm text-gray-500">
                      {new Date(item.createdAt).toLocaleString()}
                    </p>
                    {item.reference && (
                      <p className="text-xs text-gray-400">Ref: {item.reference}</p>
                    )}
                  </div>
                </div>
                
                <div className="text-right">
                  <p className={`font-semibold ${
                    item.type === 'withdrawal_request' ? 'text-orange-600' :
                    isIncoming(item) ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {item.type === 'withdrawal_request' ? '⏳ ' :
                     isIncoming(item) ? '+' : '-'}{formatCurrency(item.amount)}
                  </p>
                  <p className="text-xs text-gray-500 uppercase">
                    {item.currency || 'cash'}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination */}
          {transactions?.hasMore && (
            <div className="flex justify-center mt-6">
              <button
                onClick={() => {}}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Load More
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
}