// Shared types for Wars components

export interface ChainAttacker {
  id: number;
  name?: string;
  attacks: {
    total: number;
    war: number;
    assists: number;
  };
  respect: {
    total: number;
    average: number;
    best: number;
  };
}

export interface AttackTimelineDialogProps {
  isOpen: boolean;
  onClose: () => void;
  warId: number;
}

export interface PlayerPerformanceDialogProps {
  isOpen: boolean;
  onClose: () => void;
  warId: number;
  playerId?: number;
}