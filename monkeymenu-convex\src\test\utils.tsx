import React from 'react'
import { render, RenderOptions, RenderResult } from '@testing-library/react'
import { vi } from 'vitest'

// Test utilities for rendering components with providers

interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  // Add custom options here if needed
  withRouter?: boolean
  withAuth?: boolean
  withConvex?: boolean
}

// Mock providers for testing
function TestWrapper({ children }: { children: React.ReactNode }) {
  return (
    <div data-testid="test-wrapper">
      {children}
    </div>
  )
}

// Custom render function with providers
export function renderWithProviders(
  ui: React.ReactElement,
  options: CustomRenderOptions = {}
): RenderResult {
  const { withRouter = false, withAuth = false, withConvex = false, ...renderOptions } = options

  function Wrapper({ children }: { children: React.ReactNode }) {
    let wrapped = children

    if (withConvex) {
      // Mock ConvexProvider
      wrapped = <div data-testid="convex-provider">{wrapped}</div>
    }

    if (withAuth) {
      // Mock ClerkProvider
      wrapped = <div data-testid="clerk-provider">{wrapped}</div>
    }

    if (withRouter) {
      // Mock RouterProvider
      wrapped = <div data-testid="router-provider">{wrapped}</div>
    }

    return <TestWrapper>{wrapped}</TestWrapper>
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Mock data generators
export const mockUser = {
  _id: 'test-user-id' as any,
  clerkId: 'clerk_test_user_id',
  username: 'testuser',
  tornId: 12345,
  permissions: ['user'],
  discordId: null,
  bankingEnabled: true,
  createdAt: Date.now(),
  lastLogin: Date.now(),
}

export const mockAdminUser = {
  ...mockUser,
  permissions: ['user', 'admin'],
}

export const mockTransaction = {
  _id: 'test-transaction-id' as any,
  userId: mockUser._id,
  type: 'withdrawal' as const,
  amount: 1000000,
  status: 'completed' as const,
  description: 'Test withdrawal',
  createdAt: Date.now(),
  processedAt: Date.now(),
  processedBy: mockAdminUser._id,
}

export const mockTarget = {
  _id: 'test-target-id' as any,
  tornId: 67890,
  username: 'target_user',
  level: 50,
  faction: 'Test Faction',
  status: 'active',
  respect: 500000,
  fairFight: 2.5,
  battleStats: {
    strength: 100000,
    defense: 80000,
    speed: 90000,
    dexterity: 85000,
  },
  lastUpdated: Date.now(),
}

export const mockAnnouncement = {
  _id: 'test-announcement-id' as any,
  title: 'Test Announcement',
  content: 'This is a test announcement content.',
  priority: 'normal' as const,
  createdBy: mockAdminUser._id,
  createdAt: Date.now(),
  expiresAt: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days from now
  isActive: true,
}

export const mockWar = {
  _id: 'test-war-id' as any,
  name: 'Test War',
  enemyFaction: 'Enemy Faction',
  startTime: Date.now() - 60 * 60 * 1000, // 1 hour ago
  endTime: Date.now() + 23 * 60 * 60 * 1000, // 23 hours from now
  status: 'active' as const,
  ourScore: 150,
  theirScore: 120,
  attacks: [],
  createdBy: mockAdminUser._id,
  createdAt: Date.now(),
}

export const mockGuide = {
  _id: 'test-guide-id' as any,
  title: 'Test Guide',
  content: 'This is a test guide content.',
  category: 'general',
  tags: ['test', 'guide'],
  isPublished: true,
  createdBy: mockAdminUser._id,
  createdAt: Date.now(),
  updatedAt: Date.now(),
}

// Mock API responses
export function createMockQueryResponse<T>(data: T, loading = false, error?: Error) {
  return {
    data: loading ? undefined : data,
    isLoading: loading,
    error: error || null,
  };
}

// Mock mutation functions
export const createMockMutation = (mockImplementation?: () => Promise<any>) => 
  vi.fn(mockImplementation || (() => Promise.resolve()))

// Utility to wait for async operations
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// Mock localStorage
export const mockLocalStorage = (() => {
  let store: Record<string, string> = {}

  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value.toString()
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key]
    }),
    clear: vi.fn(() => {
      store = {}
    }),
    key: vi.fn((index: number) => Object.keys(store)[index] || null),
    get length() {
      return Object.keys(store).length
    },
  }
})()

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

// Mock fetch
export const mockFetch = vi.fn()
global.fetch = mockFetch

// Helper to setup Convex mocks
export function setupConvexMocks() {
  const { useQuery, useMutation } = require('convex/react')
  
  // Reset all mocks
  vi.clearAllMocks()
  
  return {
    useQuery: useQuery as ReturnType<typeof vi.fn>,
    useMutation: useMutation as ReturnType<typeof vi.fn>,
  }
}

// Helper to mock API errors
export function mockApiError(message: string, status = 500) {
  const error = new Error(message)
  ;(error as any).status = status
  return error
}

// Helper to mock permissions
export function mockPermissions(permissions: string[] = ['user']) {
  const usePermissions = vi.fn(() => ({
    hasPermission: (permission: string) => permissions.includes(permission),
    canAccessBanking: permissions.includes('banking') || permissions.includes('admin'),
    canManageBanking: permissions.includes('admin'),
    canViewTargets: () => permissions.includes('targets') || permissions.includes('admin'),
    canManageTargets: permissions.includes('admin'),
    canViewWars: permissions.includes('wars') || permissions.includes('admin'),
    canManageWars: permissions.includes('admin'),
    canViewGuides: () => true,
    canManageGuides: permissions.includes('admin'),
    canViewAnnouncements: () => true,
    canManageAnnouncements: permissions.includes('admin'),
    canViewAnalytics: permissions.includes('analytics') || permissions.includes('admin'),
    canAccessAdmin: permissions.includes('admin'),
  }))

  vi.doMock('../../hooks/usePermissions', () => ({
    usePermissions,
  }))

  return { usePermissions }
}