{"name": "slash-create", "version": "6.5.0", "description": "Create and sync Discord slash commands!", "main": "./lib/index.js", "types": "./lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./web": {"types": "./lib/web/index.d.ts", "default": "./lib/web/index.js"}, "./lib/*.js": "./lib/*.js", "./package.json": "./package.json"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://snazzah.com/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/Snazzah/slash-create.git"}, "bugs": {"url": "https://github.com/Snazzah/slash-create/issues"}, "homepage": "https://slash-create.js.org/", "keywords": ["api", "discord", "discordapp", "slash-command", "slash-commands", "discord-interactions", "wrapper"], "scripts": {"build": "rimraf lib && tsc && tsx scripts/postbuild", "build:prepare": "shx test -d ./lib || npm run build", "changelog": "tsx scripts/changelog", "docs": "typedoc --json .tmp/typedoc-out.json --excludeExternals --plugin typedoc-plugin-as-member-of && tsx scripts/docs", "lint": "eslint --ext .ts ./src", "lint:sarif": "eslint --ext .ts ./src -f @microsoft/sarif -o eslint-results.sarif --quiet", "lint:fix": "eslint --ext .ts ./src --fix", "test": "mocha -r tsx --extension ts 'test/**/*.ts'", "gpr": "tsx scripts/gpr", "prepare": "npm run build:prepare", "prepublishOnly": "(shx test -d ./lib || (echo \"lib folder does not exist\" && exit 1)) && npm run lint:fix"}, "funding": {"url": "https://github.com/sponsors/<PERSON><PERSON>zzah"}, "engines": {"node": ">=16"}, "dependencies": {"eventemitter3": "^5.0.1", "lodash.isequal": "^4.5.0", "tweetnacl": "^1.0.3", "undici": "^5.28.4"}, "devDependencies": {"@microsoft/eslint-formatter-sarif": "^3.0.0", "@sinonjs/fake-timers": "6.0.1", "@types/aws-lambda": "^8.10.130", "@types/chai": "^4.3.11", "@types/chai-as-promised": "^7.1.8", "@types/lodash": "^4.14.202", "@types/lodash.isequal": "^4.5.8", "@types/mocha": "^10.0.6", "@types/node": "^20.10.3", "@types/sinonjs__fake-timers": "6.0.1", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "chai-nock": "^1.3.0", "colors": "1.4.0", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "fastify": "^4.25.2", "mocha": "^10.2.0", "nock": "^13.4.0", "prettier": "^3.1.0", "rimraf": "^5.0.5", "shx": "^0.3.4", "ts-docgen": "github:dbots-pkg/ts-docgen#main", "tsx": "^4.20.3", "typedoc": "0.19.2", "typedoc-plugin-as-member-of": "^1.0.2", "typescript": "4.4.2"}, "peerDependencies": {"express": "^4", "fastify": "^3 || ^4"}, "peerDependenciesMeta": {"express": {"optional": true}, "fastify": {"optional": true}}}