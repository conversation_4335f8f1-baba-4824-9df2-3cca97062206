import { Badge } from "@/components/ui/badge";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { SearchInput } from "@/components/ui/search-input";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { trpc } from "@/lib/trpc-client";
import { useQuery } from "@tanstack/react-query";
import { Clock, Swords, Target, TrendingUp, Trophy, User } from "lucide-react";
import { useMemo, useState } from "react";
import type { PlayerPerformanceDialogProps } from "./types";
import { formatDuration } from "./utils";

export function PlayerPerformanceDialog({
	isOpen,
	onClose,
	warId,
	playerId,
}: PlayerPerformanceDialogProps) {
	const [playerSearch, setPlayerSearch] = useState("");

	const playerPerformanceQuery = useQuery({
		...trpc.wars.getPlayerWarPerformance.queryOptions({ warId, playerId }),
		enabled: isOpen,
	});

	const filteredPlayers = useMemo(() => {
		if (!playerPerformanceQuery.data?.playerDetails || !playerSearch) {
			return playerPerformanceQuery.data?.playerDetails || [];
		}
		return playerPerformanceQuery.data.playerDetails.filter(
			(player) =>
				player.playerName?.toLowerCase().includes(playerSearch.toLowerCase()) ||
				player.playerId.toString().includes(playerSearch),
		);
	}, [playerPerformanceQuery.data?.playerDetails, playerSearch]);

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-h-[90vh] w-full max-w-6xl overflow-y-auto px-4 sm:px-6">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<User className="h-5 w-5" />
						Player Performance Analysis
					</DialogTitle>
					<DialogDescription>
						Detailed individual performance metrics and battle effectiveness
					</DialogDescription>
				</DialogHeader>

				{!playerId && (
					<SearchInput
						placeholder="Search players by name or ID..."
						value={playerSearch}
						onChange={(e) => setPlayerSearch(e.target.value)}
						onClear={() => setPlayerSearch("")}
						className="w-full max-w-md"
					/>
				)}

				{playerPerformanceQuery.isLoading && (
					<div className="space-y-4">
						{[
							"player-skeleton-1",
							"player-skeleton-2",
							"player-skeleton-3",
						].map((key) => (
							<Skeleton key={key} className="h-32 w-full" />
						))}
					</div>
				)}

				{playerPerformanceQuery.error && (
					<div className="rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-950/20">
						<p className="text-red-800 dark:text-red-200">
							Failed to load player performance:{" "}
							{String(playerPerformanceQuery.error)}
						</p>
					</div>
				)}

				{playerPerformanceQuery.data && (
					<div className="space-y-6">
						{filteredPlayers.length === 0 ? (
							<div className="py-8 text-center">
								<User className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
								<p className="text-muted-foreground">
									No players found matching your search.
								</p>
							</div>
						) : (
							<div className="space-y-4">
								{filteredPlayers.map((player) => (
									<Card key={player.playerId} className="w-full">
										<CardHeader>
											<div className="space-y-3">
												<div className="flex items-start justify-between">
													<div className="min-w-0 flex-1">
														<CardTitle className="flex items-center gap-2">
															<div className="rounded-lg bg-primary/10 p-2">
																<User className="h-5 w-5 text-primary" />
															</div>
															<span className="truncate">
																{player.playerName ||
																	`Player #${player.playerId}`}
															</span>
														</CardTitle>
														<CardDescription className="mt-1">
															Participation:{" "}
															{player.overallStats.participationRate.toFixed(1)}
															% • Active: {player.overallStats.activeHours}h
														</CardDescription>
													</div>
												</div>
												<div className="flex flex-wrap gap-2">
													<Badge
														variant="outline"
														className="flex items-center gap-1"
													>
														<Trophy className="h-3 w-3" />
														<span className="hidden sm:inline">Respect #</span>
														<span className="sm:hidden">#</span>
														{player.comparativeRanking.respectRank}
													</Badge>
													<Badge
														variant="outline"
														className="flex items-center gap-1"
													>
														<Swords className="h-3 w-3" />
														<span className="hidden sm:inline">Attacks #</span>
														<span className="sm:hidden">#</span>
														{player.comparativeRanking.attackCountRank}
													</Badge>
												</div>
											</div>
										</CardHeader>
										<CardContent>
											<Tabs defaultValue="overview" className="w-full">
												<TabsList className="grid w-full grid-cols-4">
													<TabsTrigger
														value="overview"
														className="text-xs sm:text-sm"
													>
														<span className="hidden sm:inline">Overview</span>
														<span className="sm:hidden">📊</span>
													</TabsTrigger>
													<TabsTrigger
														value="trends"
														className="text-xs sm:text-sm"
													>
														<span className="hidden sm:inline">Trends</span>
														<span className="sm:hidden">📈</span>
													</TabsTrigger>
													<TabsTrigger
														value="chains"
														className="text-xs sm:text-sm"
													>
														<span className="hidden sm:inline">Chains</span>
														<span className="sm:hidden">🔗</span>
													</TabsTrigger>
													<TabsTrigger
														value="effectiveness"
														className="text-xs sm:text-sm"
													>
														<span className="hidden sm:inline">
															Effectiveness
														</span>
														<span className="sm:hidden">⚡</span>
													</TabsTrigger>
												</TabsList>

												<TabsContent value="overview" className="space-y-4">
													<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
														<div className="rounded-lg bg-muted/50 p-3 text-center">
															<div className="font-bold text-2xl">
																{player.overallStats.totalAttacks}
															</div>
															<div className="text-muted-foreground text-sm">
																Total Attacks
															</div>
														</div>
														<div className="rounded-lg bg-muted/50 p-3 text-center">
															<div className="font-bold text-2xl">
																{player.overallStats.totalRespect.toFixed(1)}
															</div>
															<div className="text-muted-foreground text-sm">
																Total Respect
															</div>
														</div>
														<div className="rounded-lg bg-muted/50 p-3 text-center">
															<div className="font-bold text-2xl">
																{player.overallStats.averageRespect.toFixed(2)}
															</div>
															<div className="text-muted-foreground text-sm">
																Avg Respect
															</div>
														</div>
														<div className="rounded-lg bg-muted/50 p-3 text-center">
															<div className="font-bold text-2xl">
																{player.overallStats.bestRespect.toFixed(2)}
															</div>
															<div className="text-muted-foreground text-sm">
																Best Respect
															</div>
														</div>
													</div>

													<div className="grid gap-4 md:grid-cols-3">
														<div className="space-y-2">
															<h4 className="font-medium text-sm">
																Attack Distribution
															</h4>
															<div className="space-y-1">
																<div className="flex justify-between text-sm">
																	<span>Chain Attacks:</span>
																	<strong>
																		{player.overallStats.chainAttacks}
																	</strong>
																</div>
																<div className="flex justify-between text-sm">
																	<span>Non-Chain:</span>
																	<strong>
																		{player.overallStats.nonChainAttacks}
																	</strong>
																</div>
																<div className="flex justify-between text-sm">
																	<span>Inside Hits:</span>
																	<strong>
																		{player.overallStats.insideHits}
																	</strong>
																</div>
																<div className="flex justify-between text-sm">
																	<span>Assists:</span>
																	<strong>{player.overallStats.assists}</strong>
																</div>
															</div>
														</div>
														<div className="space-y-2">
															<h4 className="font-medium text-sm">Rankings</h4>
															<div className="space-y-1">
																<div className="flex justify-between text-sm">
																	<span>Respect Rank:</span>
																	<Badge variant="outline">
																		#{player.comparativeRanking.respectRank}
																	</Badge>
																</div>
																<div className="flex justify-between text-sm">
																	<span>Attack Rank:</span>
																	<Badge variant="outline">
																		#{player.comparativeRanking.attackCountRank}
																	</Badge>
																</div>
																<div className="flex justify-between text-sm">
																	<span>Efficiency Rank:</span>
																	<Badge variant="outline">
																		#{player.comparativeRanking.efficiencyRank}
																	</Badge>
																</div>
																<div className="flex justify-between text-sm">
																	<span>Participation Rank:</span>
																	<Badge variant="outline">
																		#
																		{
																			player.comparativeRanking
																				.participationRank
																		}
																	</Badge>
																</div>
															</div>
														</div>
														<div className="space-y-2">
															<h4 className="font-medium text-sm">
																Battle Effectiveness
															</h4>
															<div className="space-y-1">
																<div className="flex justify-between text-sm">
																	<span>Hit Accuracy:</span>
																	<strong>
																		{player.battleEffectiveness.hitAccuracy.toFixed(
																			1,
																		)}
																		%
																	</strong>
																</div>
																<div className="flex justify-between text-sm">
																	<span>Inside Target %:</span>
																	<strong>
																		{player.battleEffectiveness.targetPrioritization.insideTargetRatio.toFixed(
																			1,
																		)}
																		%
																	</strong>
																</div>
																<div className="flex justify-between text-sm">
																	<span>Peak Activity:</span>
																	<strong>
																		{
																			player.battleEffectiveness.timingAnalysis
																				.peakActivityWindow
																		}
																	</strong>
																</div>
																<div className="flex justify-between text-sm">
																	<span>Consistency:</span>
																	<strong>
																		{player.battleEffectiveness.timingAnalysis.consistencyScore.toFixed(
																			1,
																		)}
																		/100
																	</strong>
																</div>
															</div>
														</div>
													</div>
												</TabsContent>

												<TabsContent value="trends" className="space-y-4">
													<Card>
														<CardHeader>
															<CardTitle className="flex items-center gap-2">
																<TrendingUp className="h-4 w-4" />
																Performance Trends
															</CardTitle>
														</CardHeader>
														<CardContent>
															<div className="grid gap-4 md:grid-cols-2">
																<div>
																	<h4 className="mb-3 font-medium text-sm">
																		Daily Breakdown
																	</h4>
																	<div className="space-y-2">
																		{player.performanceTrends.dailyBreakdown.map(
																			(day) => (
																				<div
																					key={day.day}
																					className="space-y-1"
																				>
																					<div className="flex justify-between text-sm">
																						<span>Day {day.day + 1}</span>
																						<span>
																							{day.attacks} attacks •{" "}
																							{day.respect.toFixed(1)} respect
																						</span>
																					</div>
																					<Progress
																						value={
																							(day.attacks /
																								Math.max(
																									...player.performanceTrends.dailyBreakdown.map(
																										(d) => d.attacks,
																									),
																								)) *
																							100
																						}
																						className="h-1"
																					/>
																				</div>
																			),
																		)}
																	</div>
																</div>
																<div>
																	<h4 className="mb-3 font-medium text-sm">
																		Top Hours
																	</h4>
																	<div className="space-y-1">
																		{player.performanceTrends.hourlyBreakdown
																			.sort((a, b) => b.attacks - a.attacks)
																			.slice(0, 5)
																			.map((hour) => (
																				<div
																					key={hour.hour}
																					className="flex justify-between text-sm"
																				>
																					<span>Hour {hour.hour + 1}</span>
																					<span>
																						{hour.attacks} attacks •{" "}
																						{hour.efficiency.toFixed(2)} avg
																					</span>
																				</div>
																			))}
																	</div>
																</div>
															</div>
														</CardContent>
													</Card>
												</TabsContent>

												<TabsContent value="chains" className="space-y-4">
													<Card>
														<CardHeader>
															<CardTitle className="flex items-center gap-2">
																<span className="text-lg">🔗</span>
																Chain Contributions
															</CardTitle>
														</CardHeader>
														<CardContent>
															{player.chainContributions.length > 0 ? (
																<div className="space-y-3">
																	{player.chainContributions.map((chain) => (
																		<div
																			key={chain.chainId}
																			className="rounded-lg border p-3"
																		>
																			<div className="flex items-center justify-between">
																				<div>
																					<h5 className="font-medium">
																						Chain #{chain.chainId}
																					</h5>
																					<p className="text-muted-foreground text-sm">
																						{chain.attacks} attacks •{" "}
																						{chain.respect.toFixed(1)} respect
																					</p>
																				</div>
																				<div className="flex gap-2">
																					<Badge
																						variant="outline"
																						className="text-xs"
																					>
																						{chain.chainPosition}
																					</Badge>
																					<Badge
																						variant={
																							chain.performance ===
																							"Above Average"
																								? "default"
																								: chain.performance ===
																										"Below Average"
																									? "destructive"
																									: "secondary"
																						}
																						className="text-xs"
																					>
																						{chain.performance}
																					</Badge>
																				</div>
																			</div>
																			<div className="mt-2 flex gap-4 text-muted-foreground text-xs">
																				<span>War Hits: {chain.warHits}</span>
																				<span>Assists: {chain.assists}</span>
																			</div>
																		</div>
																	))}
																</div>
															) : (
																<div className="py-8 text-center">
																	<span className="mx-auto mb-4 block text-4xl">
																		🔗
																	</span>
																	<p className="text-muted-foreground">
																		No chain contributions
																	</p>
																</div>
															)}
														</CardContent>
													</Card>
												</TabsContent>

												<TabsContent
													value="effectiveness"
													className="space-y-4"
												>
													<div className="grid gap-4 md:grid-cols-2">
														<Card>
															<CardHeader>
																<CardTitle className="flex items-center gap-2 text-base">
																	<Target className="h-4 w-4" />
																	Target Priority
																</CardTitle>
															</CardHeader>
															<CardContent className="space-y-3">
																<div className="space-y-2">
																	<div className="flex justify-between text-sm">
																		<span>Inside Target Focus:</span>
																		<strong>
																			{player.battleEffectiveness.targetPrioritization.insideTargetRatio.toFixed(
																				1,
																			)}
																			%
																		</strong>
																	</div>
																	<Progress
																		value={
																			player.battleEffectiveness
																				.targetPrioritization.insideTargetRatio
																		}
																		className="h-2"
																	/>
																</div>
																<div className="space-y-2">
																	<div className="flex justify-between text-sm">
																		<span>War Target Focus:</span>
																		<strong>
																			{player.battleEffectiveness.targetPrioritization.warTargetFocus.toFixed(
																				1,
																			)}
																			%
																		</strong>
																	</div>
																	<Progress
																		value={
																			player.battleEffectiveness
																				.targetPrioritization.warTargetFocus
																		}
																		className="h-2"
																	/>
																</div>
															</CardContent>
														</Card>

														<Card>
															<CardHeader>
																<CardTitle className="flex items-center gap-2 text-base">
																	<Clock className="h-4 w-4" />
																	Timing Analysis
																</CardTitle>
															</CardHeader>
															<CardContent className="space-y-2">
																<div className="flex justify-between text-sm">
																	<span>Avg Time Between Attacks:</span>
																	<strong>
																		{formatDuration(
																			player.battleEffectiveness.timingAnalysis
																				.averageTimeBetweenAttacks,
																		)}
																	</strong>
																</div>
																<div className="flex justify-between text-sm">
																	<span>Peak Activity Window:</span>
																	<strong>
																		{
																			player.battleEffectiveness.timingAnalysis
																				.peakActivityWindow
																		}
																	</strong>
																</div>
																<div className="flex justify-between text-sm">
																	<span>Consistency Score:</span>
																	<div className="flex items-center gap-2">
																		<strong>
																			{player.battleEffectiveness.timingAnalysis.consistencyScore.toFixed(
																				1,
																			)}
																			/100
																		</strong>
																		<Progress
																			value={
																				player.battleEffectiveness
																					.timingAnalysis.consistencyScore
																			}
																			className="h-2 w-16"
																		/>
																	</div>
																</div>
															</CardContent>
														</Card>
													</div>
												</TabsContent>
											</Tabs>
										</CardContent>
									</Card>
								))}
							</div>
						)}
					</div>
				)}
			</DialogContent>
		</Dialog>
	);
}
