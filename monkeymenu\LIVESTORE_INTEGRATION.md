# LiveStore Integration Guide

This document explains how LiveStore has been integrated into MonkeyMenu for real-time banking features.

## What's Been Added

### 1. Dependencies
- `@livestore/livestore` - Core LiveStore library
- `@livestore/react` - React integration
- `@livestore/adapter-web` - Web platform adapter
- `@livestore/sync-cf` - Cloudflare Workers sync backend
- `@livestore/devtools-vite` - Development tools

### 2. Schema Definition
**File:** `apps/web/src/livestore/schema.ts`

Defines the banking events and state:
- **Events**: `withdrawalCreated`, `withdrawalStatusUpdated`, `factionBalanceUpdated`
- **Tables**: `withdrawalRequests`, `factionBalance`, `bankingUIState`
- **Materializers**: Map events to state changes

### 3. Queries
**File:** `apps/web/src/livestore/queries.ts`

Pre-defined queries for common banking operations:
- `myWithdrawals$` - User's withdrawal history
- `factionBalance$` - Current faction balance
- `pendingWithdrawals$` - Admin view of pending requests

### 4. Server Integration
**Files:** 
- `apps/server/src/lib/livestore-sync.ts` - Durable Object for sync
- `apps/server/wrangler.jsonc` - Updated with LiveStore Durable Object
- `apps/server/src/index.ts` - Added LiveStore WebSocket endpoint

### 5. Web App Integration
**Files:**
- `apps/web/src/lib/livestore-provider.tsx` - React provider component
- `apps/web/src/main.tsx` - Integrated provider into app
- `apps/web/vite.config.ts` - Added LiveStore devtools

### 6. Demo Component
**File:** `apps/web/src/routes/(faction)/-components/banking/LiveStoreBankingDemo.tsx`

A complete demo showing:
- Real-time balance updates
- Withdrawal creation and status updates
- Admin approval/rejection
- Local-first data with sync

## How to Test

1. **Start the development servers:**
   ```bash
   # Terminal 1: Start the server
   cd apps/server
   pnpm dev

   # Terminal 2: Start the web app
   cd apps/web
   pnpm dev
   ```

2. **Access the LiveStore demo:**
   - Go to `/banking` in your app
   - Click on the "LiveStore Demo" tab
   - Try creating withdrawals, updating balances, and approving/declining requests

3. **Open multiple tabs:**
   - Open the banking page in multiple browser tabs
   - Make changes in one tab and see them instantly appear in others
   - This demonstrates real-time sync across clients

## Key Features

### ✅ Real-time Sync
- Changes made in one browser tab instantly appear in others
- WebSocket connection to Cloudflare Workers for live updates

### ✅ Local-first
- Data is stored locally in SQLite (OPFS)
- App works offline and syncs when connection is restored

### ✅ Event Sourcing
- All changes are captured as immutable events
- Complete audit trail of all banking operations

### ✅ Optimistic Updates
- UI updates immediately, no loading states needed
- Conflicts are resolved automatically

## Next Steps

### Integration with Existing System
To fully integrate with your existing banking system:

1. **Update tRPC mutations** to also commit LiveStore events
2. **Sync existing data** from your database to LiveStore on app load
3. **Replace WebSocket system** with LiveStore's built-in real-time sync
4. **Add authentication** to the LiveStore sync payload

### Production Considerations
1. **Authentication**: Replace the temporary auth token with real JWT validation
2. **Permissions**: Integrate with your existing permission system
3. **Data Migration**: Sync existing banking data to LiveStore
4. **Error Handling**: Add proper error boundaries and retry logic

## Development Tools

### LiveStore Devtools
- Access at `http://localhost:5173/_livestore` (when running dev server)
- Inspect real-time data, events, and sync status
- Execute queries and events manually

### Database Access
- Local SQLite database stored in OPFS (browser storage)
- Use browser dev tools → Application → Storage to inspect

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   React App     │    │  Cloudflare      │    │   Other Clients │
│                 │    │  Workers         │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │ LiveStore   │◄┼────┼►│ Sync Backend │◄┼────┼►│ LiveStore   │ │
│ │ (SQLite)    │ │    │ │ (D1 + DO)    │ │    │ │ (SQLite)    │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ └─────────────┘ │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │                 │
│ │ tRPC Client │◄┼────┼►│ tRPC Server  │ │    │                 │
│ └─────────────┘ │    │ └──────────────┘ │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

LiveStore provides the real-time layer while your existing tRPC system handles business logic and external integrations.