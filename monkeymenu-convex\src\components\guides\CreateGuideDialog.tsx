import React, { useState, useEffect } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import rehypeRaw from 'rehype-raw';
import 'highlight.js/styles/github.css';
import { GUIDE_CATEGORIES, getCategoryInfo, validateGuideForm } from './utils';

interface CreateGuideDialogProps {
  isOpen: boolean;
  onClose: () => void;
  editGuide?: {
    _id: Id<"guides">;
    title: string;
    content: string;
    category: string;
    tags: string[];
    isPublished: boolean;
  } | null;
}

export const CreateGuideDialog: React.FC<CreateGuideDialogProps> = ({
  isOpen,
  onClose,
  editGuide,
}) => {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [category, setCategory] = useState('');
  const [tags, setTags] = useState('');
  const [isPublished, setIsPublished] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState<'write' | 'preview'>('write');

  const createGuide = useMutation(api.guides.createGuide);
  const updateGuide = useMutation(api.guides.updateGuide);
  const categories = useQuery(api.guides.getCategories) || [];

  useEffect(() => {
    if (editGuide) {
      setTitle(editGuide.title);
      setContent(editGuide.content);
      setCategory(editGuide.category);
      setTags(editGuide.tags.join(', '));
      setIsPublished(editGuide.isPublished);
    } else {
      setTitle('');
      setContent('');
      setCategory('');
      setTags('');
      setIsPublished(false);
    }
  }, [editGuide, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim() || !content.trim() || !category.trim()) return;

    setIsSubmitting(true);
    try {
      const tagsArray = tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
      
      if (editGuide) {
        await updateGuide({
          id: editGuide._id,
          title: title.trim(),
          content: content.trim(),
          category: category.trim(),
          tags: tagsArray,
          isPublished,
        });
      } else {
        await createGuide({
          title: title.trim(),
          content: content.trim(),
          category: category.trim(),
          tags: tagsArray,
        });
      }
      
      onClose();
    } catch (error) {
      console.error('Error saving guide:', error);
      alert('Failed to save guide. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">
            {editGuide ? 'Edit Guide' : 'Create New Guide'}
          </h2>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
              Title *
            </label>
            <input
              type="text"
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter guide title"
              required
            />
          </div>

          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
              Category *
            </label>
            <div className="space-y-3">
              <select
                id="category"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">Select a category</option>
                {GUIDE_CATEGORIES.map(cat => (
                  <option key={cat.value} value={cat.value}>
                    {cat.emoji} {cat.label}
                  </option>
                ))}
              </select>
              
              {category && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-lg">{getCategoryInfo(category).emoji}</span>
                    <span className="font-medium text-blue-900">{getCategoryInfo(category).label}</span>
                  </div>
                  <p className="text-sm text-blue-700">{getCategoryInfo(category).description}</p>
                </div>
              )}
              
              <div className="flex gap-2">
                <input
                  type="text"
                  placeholder="Or enter custom category"
                  value={category && !GUIDE_CATEGORIES.find(cat => cat.value === category) ? category : ''}
                  onChange={(e) => setCategory(e.target.value)}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
              </div>
            </div>
          </div>

          <div>
            <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-2">
              Tags
            </label>
            <input
              type="text"
              id="tags"
              value={tags}
              onChange={(e) => setTags(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter tags separated by commas"
            />
            <p className="text-sm text-gray-500 mt-1">
              Separate multiple tags with commas (e.g., beginners, combat, strategy)
            </p>
          </div>

          <div>
            <div className="flex justify-between items-center mb-3">
              <label className="block text-sm font-medium text-gray-700">
                Content *
              </label>
              <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
                <button
                  type="button"
                  onClick={() => setActiveTab('write')}
                  className={`px-3 py-1 text-sm rounded-md transition-colors ${
                    activeTab === 'write'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Write
                </button>
                <button
                  type="button"
                  onClick={() => setActiveTab('preview')}
                  className={`px-3 py-1 text-sm rounded-md transition-colors ${
                    activeTab === 'preview'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Preview
                </button>
              </div>
            </div>

            {activeTab === 'write' ? (
              <textarea
                id="content"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                rows={12}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
                placeholder="# Your Guide Title

Write your guide content here using markdown formatting:

## Headers
- **Bold text**
- *Italic text*
- [Links](https://example.com)
- `Code snippets`

```javascript
// Code blocks with syntax highlighting
console.log('Hello world!');
```

> Blockquotes for important notes

1. Numbered lists
2. Work great too"
                required
              />
            ) : (
              <div className="w-full min-h-[300px] px-4 py-2 border border-gray-300 rounded-lg bg-gray-50">
                {content.trim() ? (
                  <div className="prose prose-sm max-w-none">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      rehypePlugins={[rehypeHighlight, rehypeRaw]}
                      components={{
                        code: ({node, inline, className, children, ...props}) => {
                          const match = /language-(\w+)/.exec(className || '');
                          return !inline && match ? (
                            <pre className={className} {...props}>
                              <code>{children}</code>
                            </pre>
                          ) : (
                            <code className="bg-gray-100 px-1 py-0.5 rounded text-sm" {...props}>
                              {children}
                            </code>
                          );
                        },
                        h1: ({children}) => <h1 className="text-2xl font-bold mb-4 text-gray-900">{children}</h1>,
                        h2: ({children}) => <h2 className="text-xl font-bold mb-3 text-gray-900">{children}</h2>,
                        h3: ({children}) => <h3 className="text-lg font-bold mb-2 text-gray-900">{children}</h3>,
                        p: ({children}) => <p className="mb-3 text-gray-700 leading-relaxed">{children}</p>,
                        ul: ({children}) => <ul className="mb-3 list-disc list-inside text-gray-700">{children}</ul>,
                        ol: ({children}) => <ol className="mb-3 list-decimal list-inside text-gray-700">{children}</ol>,
                        li: ({children}) => <li className="mb-1">{children}</li>,
                        blockquote: ({children}) => (
                          <blockquote className="border-l-4 border-blue-500 pl-4 mb-3 italic text-gray-600 bg-blue-50 py-2">
                            {children}
                          </blockquote>
                        ),
                        a: ({children, href}) => (
                          <a href={href} className="text-blue-600 hover:text-blue-800 underline" target="_blank" rel="noopener noreferrer">
                            {children}
                          </a>
                        ),
                      }}
                    >
                      {content}
                    </ReactMarkdown>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-64 text-gray-400">
                    <div className="text-center">
                      <svg className="mx-auto h-12 w-12 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                      </svg>
                      <p>Start writing to see preview</p>
                    </div>
                  </div>
                )}
              </div>
            )}
            
            <p className="text-sm text-gray-500 mt-1">
              Supports GitHub Flavored Markdown with syntax highlighting, tables, and more.
            </p>
          </div>

          {editGuide && (
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isPublished"
                checked={isPublished}
                onChange={(e) => setIsPublished(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isPublished" className="ml-2 block text-sm text-gray-700">
                Publish this guide (make it visible to all users)
              </label>
            </div>
          )}

          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || !title.trim() || !content.trim() || !category.trim()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isSubmitting ? 'Saving...' : (editGuide ? 'Update Guide' : 'Create Guide')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};