import { trpc } from "@/lib/trpc-client";
import { useQuery } from "@tanstack/react-query";

export function useAnnouncements(hasPermission: boolean) {
	return useQuery({
		...trpc.announcements.getAll.queryOptions(),
		select: (data) => data?.slice(0, 2), // Only get latest 2
		enabled: hasPermission,
	});
}

export function useBankingData(hasPermission: boolean) {
	const balance = useQuery({
		...trpc.banking.getFactionBalance.queryOptions(),
		enabled: hasPermission,
	});

	const myWithdrawals = useQuery({
		...trpc.banking.getMyWithdrawals.queryOptions(),
		select: (data) => data?.slice(0, 3), // Only get latest 3
		enabled: hasPermission,
	});

	return {
		balance,
		myWithdrawals,
	};
}
