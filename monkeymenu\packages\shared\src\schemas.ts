import { z } from "zod";
import { VALIDATION_LIMITS } from "./constants";

// User Profile Schemas
export const UserProfileSchema = z.object({
	name: z
		.string()
		.min(VALIDATION_LIMITS.USER_NAME_MIN, "Name is required")
		.max(
			VALIDATION_LIMITS.USER_NAME_MAX,
			`Name cannot be longer than ${VALIDATION_LIMITS.USER_NAME_MAX} characters`,
		)
		.refine((str) => str.trim().length >= VALIDATION_LIMITS.USER_NAME_MIN, {
			message: "Name cannot be only whitespace",
		}),
	image: z.string().optional(),
});

// Torn API Schemas
export const TornApiKeySchema = z.object({
	apiKey: z
		.string()
		.min(1, "API Key is required")
		.length(
			VALIDATION_LIMITS.TORN_API_KEY_LENGTH,
			`API Key must be exactly ${VALIDATION_LIMITS.TORN_API_KEY_LENGTH} characters`,
		)
		.regex(/^[A-Za-z0-9]+$/, "API Key must only contain letters and numbers"),
});

// Guide Schemas
export const GuideFormSchema = z.object({
	title: z
		.string()
		.min(
			VALIDATION_LIMITS.GUIDE_TITLE_MIN,
			"Title must be at least 3 characters long",
		)
		.max(VALIDATION_LIMITS.GUIDE_TITLE_MAX, "Title is too long"),
	content: z
		.string()
		.min(VALIDATION_LIMITS.GUIDE_CONTENT_MIN, "Content is required"),
	category: z.string().min(1, "Category is required"),
});

export const GuideUpdateSchema = GuideFormSchema.extend({
	id: z.number(),
});

// Announcement Schemas
export const AnnouncementFormSchema = z.object({
	title: z
		.string()
		.min(
			VALIDATION_LIMITS.ANNOUNCEMENT_TITLE_MIN,
			"Title must be at least 3 characters long",
		)
		.max(VALIDATION_LIMITS.ANNOUNCEMENT_TITLE_MAX, "Title is too long"),
	content: z
		.string()
		.min(VALIDATION_LIMITS.ANNOUNCEMENT_CONTENT_MIN, "Content is required"),
	category: z.string().min(1, "Category is required").default("general"),
	isUrgent: z.boolean().optional().default(false),
});

export const AnnouncementUpdateSchema = AnnouncementFormSchema.extend({
	id: z.number(),
});

// Role Assignment Schema
export const RoleAssignmentSchema = z.object({
	userId: z.string().min(1, "User ID is required"),
	roleId: z.number().min(1, "Role selection is required"),
});

// Email Schema
export const EmailSchema = z.object({
	email: z.string().email("Invalid email address"),
});

// OTP Schema
export const OTPSchema = z.object({
	otp: z
		.string()
		.min(
			VALIDATION_LIMITS.OTP_MIN_LENGTH,
			`OTP must be at least ${VALIDATION_LIMITS.OTP_MIN_LENGTH} characters`,
		),
});

// Export type inference helpers
export type UserProfileForm = z.infer<typeof UserProfileSchema>;
export type TornApiKey = z.infer<typeof TornApiKeySchema>;
export type GuideForm = z.infer<typeof GuideFormSchema>;
export type GuideUpdate = z.infer<typeof GuideUpdateSchema>;
export type AnnouncementForm = z.infer<typeof AnnouncementFormSchema>;
export type AnnouncementUpdate = z.infer<typeof AnnouncementUpdateSchema>;

export type RoleAssignment = z.infer<typeof RoleAssignmentSchema>;
export type Email = z.infer<typeof EmailSchema>;
export type OTP = z.infer<typeof OTPSchema>;
