import { <PERSON>ert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { SuspensionAlert } from "@/components/ui/suspension-alert";
import { useAccessStatus } from "@/hooks/useAccessStatus";
import { trpc } from "@/lib/trpc-client";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

export function SuspensionDemo() {
	const { accessStatus, isSuspended, refetch } = useAccessStatus();

	const suspendMutation = useMutation(
		trpc.user.suspendUser.mutationOptions({
			onSuccess: () => {
				toast.success("Access suspended for testing");
				refetch();
			},
			onError: (error) => {
				toast.error(`Failed to suspend access: ${error.message}`);
			},
		}),
	);

	const restoreMutation = useMutation(
		trpc.user.restoreUser.mutationOptions({
			onSuccess: () => {
				toast.success("Access restored");
				refetch();
			},
			onError: (error) => {
				toast.error(`Failed to restore access: ${error.message}`);
			},
		}),
	);

	return (
		<Card className="w-full max-w-2xl">
			<CardHeader>
				<CardTitle>Real-time Access Suspension Demo</CardTitle>
				<CardDescription>
					Test the real-time access suspension system that automatically pauses
					access when Torn API key issues are detected.
				</CardDescription>
			</CardHeader>
			<CardContent className="space-y-4">
				{isSuspended && accessStatus && (
					<SuspensionAlert
						reason={accessStatus.reason}
						suspendedAt={
							accessStatus.suspendedAt
								? new Date(accessStatus.suspendedAt)
								: null
						}
						onAccessRestored={() => refetch()}
					/>
				)}

				<div className="space-y-2">
					<h3 className="font-semibold">Current Status</h3>
					<div className="space-y-1 text-sm">
						<p>
							<span className="font-medium">Access Status:</span>{" "}
							<span className={isSuspended ? "text-red-600" : "text-green-600"}>
								{isSuspended ? "Suspended" : "Active"}
							</span>
						</p>
						{accessStatus?.reason && (
							<p>
								<span className="font-medium">Reason:</span>{" "}
								{accessStatus.reason}
							</p>
						)}
						{accessStatus &&
							"lastError" in accessStatus &&
							accessStatus.lastError && (
								<p>
									<span className="font-medium">Last API Error:</span>{" "}
									{accessStatus.lastError}
								</p>
							)}
					</div>
				</div>

				<Alert>
					<AlertDescription>
						<strong>How it works:</strong> When features like banking make Torn
						API calls, if error 18 (API key paused) or other
						suspension-triggering errors are returned, the system will
						automatically suspend the user's access instead of waiting for the
						next cron job check.
					</AlertDescription>
				</Alert>

				<div className="flex gap-2">
					<Button
						onClick={() =>
							suspendMutation.mutate("Demo suspension for testing")
						}
						disabled={suspendMutation.isPending || isSuspended}
						variant="destructive"
						size="sm"
					>
						{suspendMutation.isPending ? "Suspending..." : "Test Suspension"}
					</Button>

					<Button
						onClick={() => restoreMutation.mutate()}
						disabled={restoreMutation.isPending || !isSuspended}
						variant="default"
						size="sm"
					>
						{restoreMutation.isPending ? "Restoring..." : "Test Restore"}
					</Button>

					<Button onClick={() => refetch()} variant="outline" size="sm">
						Refresh Status
					</Button>
				</div>

				<div className="text-muted-foreground text-xs">
					<p>
						<strong>Suspension Triggers:</strong>
					</p>
					<ul className="mt-1 list-inside list-disc space-y-1">
						<li>Error 18: API key paused by owner</li>
						<li>Error 13: API key disabled due to inactivity</li>
						<li>Error 10: API key owner in federal jail</li>
						<li>Error 16: Access level insufficient</li>
						<li>Error 2: Incorrect API key</li>
						<li>Error 12: Key read error</li>
					</ul>
				</div>
			</CardContent>
		</Card>
	);
}
