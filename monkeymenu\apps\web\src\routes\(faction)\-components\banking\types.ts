export interface BankingPermissions {
	canManageWithdrawals: boolean;
}

export interface FactionBalance {
	money: number;
}

export interface WithdrawalRequest {
	id: string;
	amount: number;
	status: string;
	createdAt: string;
	updatedAt: string;
	processedAt: string | null;
	transactionId: string | null;
	requestedById: string;
	processedById: string | null;
	requestedByName?: string;
	requestedByTornId?: number;
}

export interface WithdrawalWithUser {
	withdrawal: WithdrawalRequest;
	user: {
		tornUsername: string;
		tornUserId: string;
	};
}

export interface WithdrawalStats {
	total: number;
	pending: number;
	approved: number;
	declined: number;
	totalAmount: number;
	totalWithdrawn: number;
}

export interface WithdrawalStatusOption {
	id: string;
	label: string;
	emoji: string;
	color: string;
}

export interface AmountRange {
	id: string;
	label: string;
	min?: number;
	max?: number;
}

export interface BankingFormProps {
	balance?: { factionBalance: FactionBalance } | null;
	isLoadingBalance: boolean;
	onSubmitSuccess: () => void;
}

export interface WithdrawalCardProps {
	withdrawal: WithdrawalWithUser;
	onViewDetails: (withdrawal: WithdrawalWithUser) => void;
	onApprove?: (withdrawalId: string) => void;
	onDecline?: (withdrawalId: string) => void;
	showActions?: boolean;
	isProcessing?: boolean;
}

export interface WithdrawalDetailsModalProps {
	withdrawal: WithdrawalWithUser | null;
	isOpen: boolean;
	onClose: () => void;
}

export interface StatisticsCardsProps {
	balance?: { factionBalance: FactionBalance } | null;
	isLoadingBalance: boolean;
	stats: WithdrawalStats;
}

export interface WithdrawalFiltersProps {
	searchQuery: string;
	onSearchChange: (query: string) => void;
	selectedStatus: string;
	onStatusChange: (status: string) => void;
	selectedAmountRange: string;
	onAmountRangeChange: (range: string) => void;
	viewMode: "grid" | "list";
	onViewModeChange: (mode: "grid" | "list") => void;
}

export interface HistoryTabProps {
	withdrawals: WithdrawalWithUser[];
	isLoading: boolean;
	onWithdrawalSelect: (withdrawal: WithdrawalWithUser) => void;
}

export interface AdminTabProps {
	withdrawals: WithdrawalWithUser[];
	isLoading: boolean;
	onWithdrawalSelect: (withdrawal: WithdrawalWithUser) => void;
	onApprove: (withdrawalId: string) => void;
	onDecline: (withdrawalId: string) => void;
	isProcessing: boolean;
}
