import { createFileRoute } from '@tanstack/react-router';
import { ProtectedRoute } from '../components/auth/ProtectedRoute';
import { PermissionGuard } from '../components/auth/PermissionGuard';
import { TargetFinder } from '../components/targets/TargetFinder';
import { PERMISSION_NAMES } from '../lib/permissions';

export const Route = createFileRoute('/targets')({
  component: TargetsPage,
});

function TargetsPage() {
  return (
    <ProtectedRoute>
      <PermissionGuard permission={PERMISSION_NAMES.TARGET_FINDER_VIEW}>
        <TargetFinder />
      </PermissionGuard>
    </ProtectedRoute>
  );
}