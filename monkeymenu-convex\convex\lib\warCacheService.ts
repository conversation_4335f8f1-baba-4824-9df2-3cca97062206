// War Cache Service for Convex
// Intelligent caching system for war data to optimize performance

import { GenericQueryCtx, GenericMutationCtx } from "convex/server";
import { DataModel } from "../_generated/dataModel";
import { TornAPI, TornAttack, TornChainInfo, TornChainReport, TornRankedWar } from "./tornApi";

type QueryCtx = GenericQueryCtx<DataModel>;
type MutationCtx = GenericMutationCtx<DataModel>;

export class WarCacheService {
  constructor(
    private ctx: QueryCtx | MutationCtx,
    private tornApi: TornAPI
  ) {}

  /**
   * Get war report with intelligent caching
   * - Returns cached data for completed wars
   * - Fetches fresh data for ongoing wars
   */
  async getWarReport(warId: number): Promise<TornRankedWar> {
    const cached = await this.ctx.db
      .query("cachedWarReports")
      .withIndex("by_war_id", (q) => q.eq("warId", warId))
      .first();

    // If we have cached data and the war is completed, return it
    if (cached?.isCompleted) {
      return JSON.parse(cached.reportData);
    }

    // Fetch fresh data from API
    const freshReport = await this.tornApi.getRankedWarReport(warId);

    // Cache the data
    await this.cacheWarReport(warId, freshReport);

    return freshReport;
  }

  /**
   * Get chain reports for a war with intelligent caching
   */
  async getWarChainReports(warId: number) {
    const warReport = await this.getWarReport(warId);
    const isWarCompleted = warReport.end !== null;

    // Get any cached chain reports
    const cachedChains = await this.ctx.db
      .query("cachedChainReports")
      .withIndex("by_war_id", (q) => q.eq("warId", warId))
      .collect();

    // For completed wars, we can trust all cached data
    if (isWarCompleted && cachedChains.length > 0) {
      // Check if we need to fetch any missing chains
      const warStart = warReport.start;
      const warEnd = warReport.end!;

      // Get faction chains from API to see if we're missing any
      const chains = await this.getAllChainsInTimeRange(warStart, warEnd);
      const relevantChains = chains;

      const cachedChainIds = new Set(cachedChains.map((c) => c.chainId));
      const missingChains = relevantChains.filter(
        (ch) => !cachedChainIds.has(ch.id)
      );

      // Fetch and cache missing chains
      for (const chain of missingChains) {
        try {
          const chainReport = await this.tornApi.getChainReport(chain.id);
          await this.cacheChainReport(warId, chainReport);
          // Add to cached list for return
          cachedChains.push({
            chainId: chain.id,
            warId,
            reportData: JSON.stringify(chainReport),
            startTime: chain.start,
            endTime: chain.end,
            chainLength: chainReport.details.chain,
            totalRespect: chainReport.details.respect,
            attackerCount: chainReport.attackers?.length || 0,
            cachedAt: Date.now(),
            _id: "" as any, // Will be set by DB
            _creationTime: Date.now(),
          });
        } catch (err) {
          console.error(`Failed to fetch chain report ${chain.id}:`, err);
        }
      }
    }

    // If war is ongoing or we have no cached data, fetch fresh
    if (!isWarCompleted || cachedChains.length === 0) {
      return await this.fetchAndCacheChainReports(warId);
    }

    // Return cached chain reports
    const chainReports = cachedChains.map((cached) =>
      JSON.parse(cached.reportData)
    );

    // Build player names mapping from cached attacks
    const playerNames = await this.getPlayerNamesFromCache(warId);

    // Calculate combined stats from cached data
    const combinedChainStats = this.calculateCombinedChainStats(
      chainReports,
      playerNames
    );

    return { chainReports, playerNames, combinedChainStats };
  }

  /**
   * Get war attacks with intelligent caching
   */
  async getWarAttacks(warId: number, fetchLatest = false): Promise<TornAttack[]> {
    const warReport = await this.getWarReport(warId);
    const isWarCompleted = warReport.end !== null;
    const warStart = warReport.start;
    const warEnd = warReport.end || Math.floor(Date.now() / 1000);

    // For completed wars, use cached data if available
    if (isWarCompleted && !fetchLatest) {
      const cachedAttacks = await this.ctx.db
        .query("cachedWarAttacks")
        .withIndex("by_war_id", (q) => q.eq("warId", warId))
        .order("desc")
        .collect();

      if (cachedAttacks.length > 0) {
        return cachedAttacks.map((cached) => JSON.parse(cached.attackData));
      }
    }

    // Fetch fresh attacks from API using time range parameters
    const collected: TornAttack[] = [];
    // Add buffer to catch final attacks that complete after official end time
    const bufferedEnd = warEnd + 300; // 5 minute buffer
    let cursor: number | undefined = bufferedEnd;
    
    while (true) {
      const batch = await this.tornApi.getFactionAttacks(
        100,
        "DESC",
        warStart,
        cursor
      );
      if (batch.length === 0) break;

      // Filter to actual time range (remove buffer for final results)
      const withinRange = batch.filter(
        (a) => a.started >= warStart && a.started <= warEnd
      );
      collected.push(...withinRange);

      // Continue pagination only if we got a full batch (100 items)
      // AND the oldest attack is still within our range
      if (batch.length < 100) break;

      const oldest = batch[batch.length - 1];
      if (oldest.started <= warStart) break;

      cursor = oldest.started - 1;
    }

    // Cache attacks if war is completed
    if (isWarCompleted) {
      await this.cacheWarAttacks(warId, collected);
    }

    return collected;
  }

  /**
   * Cache a war report
   */
  private async cacheWarReport(warId: number, report: TornRankedWar) {
    if (!("mutate" in this.ctx)) {
      throw new Error("Cannot cache data from a query context");
    }

    const factionIds = report.factions.map((f) => f.id).join(",");
    const isCompleted = report.end !== null;
    const now = Date.now();

    // Check if already cached
    const existing = await this.ctx.db
      .query("cachedWarReports")
      .withIndex("by_war_id", (q) => q.eq("warId", warId))
      .first();

    if (existing) {
      await this.ctx.db.patch(existing._id, {
        reportData: JSON.stringify(report),
        endTime: report.end || undefined,
        winner: report.winner || undefined,
        isCompleted,
        lastUpdated: now,
      });
    } else {
      await this.ctx.db.insert("cachedWarReports", {
        warId,
        reportData: JSON.stringify(report),
        factionIds,
        startTime: report.start,
        endTime: report.end || undefined,
        winner: report.winner || undefined,
        isCompleted,
        cachedAt: now,
        lastUpdated: now,
      });
    }
  }

  /**
   * Cache a chain report
   */
  private async cacheChainReport(warId: number, chainReport: TornChainReport) {
    if (!("mutate" in this.ctx)) {
      throw new Error("Cannot cache data from a query context");
    }

    // Check if already exists
    const existing = await this.ctx.db
      .query("cachedChainReports")
      .withIndex("by_chain_id", (q) => q.eq("chainId", chainReport.id))
      .first();

    if (!existing) {
      await this.ctx.db.insert("cachedChainReports", {
        chainId: chainReport.id,
        warId,
        reportData: JSON.stringify(chainReport),
        startTime: chainReport.start,
        endTime: chainReport.end,
        chainLength: chainReport.details.chain,
        totalRespect: chainReport.details.respect,
        attackerCount: chainReport.attackers?.length || 0,
        cachedAt: Date.now(),
      });
    }
  }

  /**
   * Cache war attacks
   */
  private async cacheWarAttacks(warId: number, attacks: TornAttack[]) {
    if (!("mutate" in this.ctx)) {
      throw new Error("Cannot cache data from a query context");
    }

    // Clear existing cache for this war first to avoid conflicts
    const existingAttacks = await this.ctx.db
      .query("cachedWarAttacks")
      .withIndex("by_war_id", (q) => q.eq("warId", warId))
      .collect();

    for (const attack of existingAttacks) {
      await this.ctx.db.delete(attack._id);
    }

    // Insert new attacks in batches
    const batchSize = 50; // Conservative batch size for Convex
    for (let i = 0; i < attacks.length; i += batchSize) {
      const batch = attacks.slice(i, i + batchSize);
      
      for (const attack of batch) {
        try {
          await this.ctx.db.insert("cachedWarAttacks", {
            attackId: String(attack.id),
            warId,
            attackData: JSON.stringify(attack),
            attackerId: attack.attacker?.id || 0,
            defenderId: attack.defender?.id || 0,
            attackerFactionId: attack.attacker?.faction?.id || undefined,
            defenderFactionId: attack.defender?.faction?.id || undefined,
            timestamp: attack.started,
            chainId: attack.chain || undefined,
            result: attack.result,
            respect: Number(attack.modifiers?.fair_fight) || 0,
            cachedAt: Date.now(),
          });
        } catch (error) {
          console.error(`Failed to insert attack ${attack.id}:`, error);
        }
      }
    }
  }

  /**
   * Fetch and cache chain reports (with intelligent caching like original app)
   */
  private async fetchAndCacheChainReports(warId: number) {
    const warReport = await this.getWarReport(warId);
    const warStart = warReport.start;
    const warEnd = warReport.end;

    const relevantChains = await this.getAllChainsInTimeRange(warStart, warEnd || Date.now() / 1000);

    // Check for cached chain reports first (like original app)
    const cachedChains = await this.ctx.db
      .query("cachedChainReports")
      .withIndex("by_war_id", (q) => q.eq("warId", warId))
      .collect();

    const cachedChainIds = new Set(cachedChains.map((c) => c.chainId));
    const missingChains = relevantChains.filter(
      (ch) => !cachedChainIds.has(ch.id)
    );

    console.log(`Found ${cachedChains.length} cached chains, need to fetch ${missingChains.length} missing chains`);

    const chainReports: TornChainReport[] = [];
    const playerNamesMap = new Map<string, string>();

    // Add cached chain reports first
    for (const cached of cachedChains) {
      try {
        const report = JSON.parse(cached.reportData);
        chainReports.push(report);
      } catch (err) {
        console.error(`Failed to parse cached chain report ${cached.chainId}:`, err);
      }
    }

    // Fetch all attacks during the war period to build player name mapping
    const collected = await this.getWarAttacks(warId, true);

    // Build player name mapping from attacks
    for (const attack of collected) {
      if (attack.attacker?.id && attack.attacker?.name) {
        playerNamesMap.set(attack.attacker.id.toString(), attack.attacker.name);
      }
    }

    // Fetch ONLY missing chain reports SEQUENTIALLY with delays (like original app)
    if (missingChains.length > 0) {
      console.log(`Fetching ${missingChains.length} missing chain reports sequentially...`);
      
      for (let i = 0; i < missingChains.length; i++) {
        const chain = missingChains[i];
        try {
          console.log(`Fetching chain report ${chain.id} (${i + 1}/${missingChains.length})...`);
          const report = await this.tornApi.getChainReport(chain.id);
          chainReports.push(report);

          // Cache the chain report immediately
          await this.cacheChainReport(warId, report);
          
          // Add delay between requests to avoid rate limiting (like original app)
          if (i < missingChains.length - 1) {
            console.log('Waiting 500ms before next chain request...');
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        } catch (err) {
          console.error(`Failed to fetch chain report ${chain.id}:`, err);
          // Continue with next chain instead of failing entirely
        }
      }
    } else {
      console.log('All chain reports found in cache, no API calls needed!');
    }

    const playerNames = Object.fromEntries(playerNamesMap);
    const combinedChainStats = this.calculateCombinedChainStats(
      chainReports,
      playerNames
    );

    return { chainReports, playerNames, combinedChainStats };
  }

  /**
   * Get player names from cached attack data
   */
  private async getPlayerNamesFromCache(warId: number): Promise<Record<string, string>> {
    const cachedAttacks = await this.ctx.db
      .query("cachedWarAttacks")
      .withIndex("by_war_id", (q) => q.eq("warId", warId))
      .collect();

    const playerNames: Record<string, string> = {};
    for (const cached of cachedAttacks) {
      const attack = JSON.parse(cached.attackData);
      if (attack.attacker?.id && attack.attacker?.name) {
        playerNames[attack.attacker.id.toString()] = attack.attacker.name;
      }
    }

    return playerNames;
  }

  /**
   * Calculate combined chain statistics
   */
  private calculateCombinedChainStats(
    chainReports: TornChainReport[],
    playerNames: Record<string, string>
  ) {
    const combinedStatsMap = new Map();

    for (const chainReport of chainReports) {
      if (chainReport.attackers) {
        for (const attacker of chainReport.attackers) {
          const playerId = attacker.id;
          const playerName = playerNames[playerId.toString()];

          if (!combinedStatsMap.has(playerId)) {
            combinedStatsMap.set(playerId, {
              playerId,
              playerName,
              totalHits: 0,
              totalWarHits: 0,
              totalAssists: 0,
              totalRespect: 0,
              bestRespect: 0,
              chainCount: 0,
              respectValues: [],
            });
          }

          const stats = combinedStatsMap.get(playerId);
          stats.totalHits += attacker.attacks.total;
          stats.totalWarHits += attacker.attacks.war;
          stats.totalAssists += attacker.attacks.assists;
          stats.totalRespect += attacker.respect.total;
          stats.bestRespect = Math.max(
            stats.bestRespect,
            attacker.respect.best
          );
          stats.chainCount++;
          stats.respectValues.push(attacker.respect.total);
        }
      }
    }

    return Array.from(combinedStatsMap.values()).map(
      (stats: any) => ({
        playerId: stats.playerId,
        playerName: stats.playerName,
        totalHits: stats.totalHits,
        totalWarHits: stats.totalWarHits,
        totalAssists: stats.totalAssists,
        totalRespect: stats.totalRespect,
        averageRespect:
          stats.totalHits > 0 ? stats.totalRespect / stats.totalHits : 0,
        bestRespect: stats.bestRespect,
        chainCount: stats.chainCount,
      })
    );
  }

  /**
   * Get all chains within a time range with pagination
   */
  private async getAllChainsInTimeRange(warStart: number, warEnd: number): Promise<TornChainInfo[]> {
    const allChains: TornChainInfo[] = [];
    // Add buffer to ensure we don't miss chain end times
    const bufferedEnd = warEnd + 300; // 5 minute buffer
    let cursor: number | undefined = bufferedEnd;

    while (true) {
      const batch = await this.tornApi.getFactionChains(
        100,
        "DESC",
        cursor,
        warStart
      );
      if (batch.length === 0) break;

      allChains.push(...batch);

      // Continue pagination only if we got a full batch (100 items)
      // AND the oldest chain is still within our range
      if (batch.length < 100) break;

      const oldest = batch[batch.length - 1];
      if (oldest.start <= warStart) break;

      cursor = oldest.start - 1;
    }

    return allChains;
  }

  /**
   * Clear cache for a specific war (useful for debugging or data updates)
   */
  async clearWarCache(warId: number) {
    if (!("mutate" in this.ctx)) {
      throw new Error("Cannot clear cache from a query context");
    }

    // Clear war report cache
    const warReport = await this.ctx.db
      .query("cachedWarReports")
      .withIndex("by_war_id", (q) => q.eq("warId", warId))
      .first();
    if (warReport) {
      await this.ctx.db.delete(warReport._id);
    }

    // Clear chain reports cache
    const chainReports = await this.ctx.db
      .query("cachedChainReports")
      .withIndex("by_war_id", (q) => q.eq("warId", warId))
      .collect();
    for (const report of chainReports) {
      await this.ctx.db.delete(report._id);
    }

    // Clear attacks cache
    const attacks = await this.ctx.db
      .query("cachedWarAttacks")
      .withIndex("by_war_id", (q) => q.eq("warId", warId))
      .collect();
    for (const attack of attacks) {
      await this.ctx.db.delete(attack._id);
    }

    // Clear stats cache (we'll need to add a proper query for this)
    // For now, just clear all stats for this war - this is inefficient but works
    const stats = await this.ctx.db
      .query("cachedWarStats")
      .withIndex("by_war_id", (q) => q.eq("warId", warId))
      .collect();
    for (const stat of stats) {
      await this.ctx.db.delete(stat._id);
    }
  }

  /**
   * Get cache statistics for monitoring
   */
  async getCacheStats() {
    const [warReports, chainReports, attacks, stats] = await Promise.all([
      this.ctx.db.query("cachedWarReports").collect(),
      this.ctx.db.query("cachedChainReports").collect(),
      this.ctx.db.query("cachedWarAttacks").collect(),
      this.ctx.db.query("cachedWarStats").collect(),
    ]);

    return {
      cachedWarReports: warReports.length,
      cachedChainReports: chainReports.length,
      cachedAttacks: attacks.length,
      cachedStats: stats.length,
    };
  }
}