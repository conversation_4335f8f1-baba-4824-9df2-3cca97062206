import { useStore } from "@livestore/react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useEffect, useRef } from "react";
import { events } from "../livestore/schema";
import { useLiveStoreReady } from "./livestore-ready";
import { trpc } from "./trpc-client";

/**
 * Enhanced bridge hook that syncs tRPC banking data with LiveStore
 * Maintains all existing functionality while adding real-time sync capabilities
 */
export function useBankingLiveStoreSync() {
	const { store } = useStore();
	const queryClient = useQueryClient();
	const syncedWithdrawalsRef = useRef(new Set<string>());
	const lastSyncedBalanceRef = useRef<{ money: number; points: number } | null>(
		null,
	);
	const storeReady = useLiveStoreReady();

	// Get permissions to determine what data to sync
	const { data: permissions } = useQuery({
		...trpc.banking.getBankingManagementPermissions.queryOptions(),
	});

	// Sync faction balance from tRPC to LiveStore
	const { data: balance } = useQuery({
		...trpc.banking.getFactionBalance.queryOptions(),
		staleTime: 30000, // 30 seconds - balance changes frequently
	});

	// Sync user's withdrawals from tRPC to LiveStore
	const { data: myWithdrawals } = useQuery({
		...trpc.banking.getMyWithdrawals.queryOptions(),
	});

	// Sync all withdrawals (admin only) from tRPC to LiveStore
	const { data: allWithdrawals } = useQuery({
		...trpc.banking.getAllWithdrawals.queryOptions({}),
		enabled: permissions?.canManageWithdrawals ?? false,
	});

	// Initialize LiveStore with real balance data once the store is ready
	useEffect(() => {
		if (!storeReady) return;
		if (lastSyncedBalanceRef.current) return; // already initialized

		try {
			if (balance?.factionBalance) {
				const { money, points } = balance.factionBalance;
				console.log(
					`[LiveStore] Initializing with real faction balance: $${money.toLocaleString()}, ${points} points`,
				);
				store.commit(
					events.factionBalanceUpdated({
						money,
						points,
						updatedAt: new Date(),
					}),
				);
				lastSyncedBalanceRef.current = { money, points };
			}
		} catch (error) {
			console.error("Failed to initialize LiveStore with real data:", error);
		}
	}, [storeReady, balance, store]);

	// Reset sync tracking when permissions change or when major data changes occur
	useEffect(() => {
		if (permissions) {
			console.log(
				"[LiveStore] Permissions updated, clearing sync tracking for fresh sync",
			);
			syncedWithdrawalsRef.current.clear();
			lastSyncedBalanceRef.current = null; // Reset balance tracking too
		}
	}, [permissions]);

	// Sync faction balance to LiveStore when tRPC data updates
	useEffect(() => {
		if (balance?.factionBalance) {
			const { money, points } = balance.factionBalance;
			const lastSynced = lastSyncedBalanceRef.current;

			// Only sync if balance has actually changed
			if (
				!lastSynced ||
				lastSynced.money !== money ||
				lastSynced.points !== points
			) {
				try {
					console.log(
						`[LiveStore] Syncing faction balance: $${money.toLocaleString()}, ${points} points`,
					);
					store.commit(
						events.factionBalanceUpdated({
							money,
							points,
							updatedAt: new Date(),
						}),
					);

					// Track the synced balance to prevent duplicates
					lastSyncedBalanceRef.current = { money, points };
				} catch (error) {
					console.error("Failed to sync balance to LiveStore:", error);
				}
			}
		}
	}, [balance, store]);

	// Sync user withdrawals to LiveStore
	useEffect(() => {
		if (myWithdrawals) {
			for (const withdrawal of myWithdrawals) {
				// Only sync if we haven't already synced this withdrawal
				if (!syncedWithdrawalsRef.current.has(withdrawal.id)) {
					try {
						console.log(
							`[LiveStore] Syncing withdrawal ${withdrawal.id} from tRPC to LiveStore`,
						);

						// Create withdrawal in LiveStore
						store.commit(
							events.withdrawalCreated({
								id: withdrawal.id,
								amount: withdrawal.amount,
								requestedById: withdrawal.requestedById,
								requestedByName: "You", // For user's own withdrawals
								requestedByTornId: undefined, // We don't have this in the user query
								createdAt: new Date(withdrawal.createdAt),
							}),
						);

						// If status is not PENDING, update it
						if (withdrawal.status !== "PENDING") {
							console.log(
								`[LiveStore] Syncing withdrawal status update ${withdrawal.id}: ${withdrawal.status}`,
							);
							store.commit(
								events.withdrawalStatusUpdated({
									id: withdrawal.id,
									status: withdrawal.status as
										| "PENDING"
										| "ACCEPTED"
										| "DECLINED"
										| "COMPLETED"
										| "CANCELLED"
										| "EXPIRED",
									processedById: withdrawal.processedById || undefined,
									processedByName: "Admin", // Generic for processed withdrawals
									processedAt: withdrawal.processedAt
										? new Date(withdrawal.processedAt)
										: undefined,
									transactionId: withdrawal.transactionId || undefined,
								}),
							);
						}

						// Mark as synced to prevent duplicate attempts
						syncedWithdrawalsRef.current.add(withdrawal.id);
					} catch (error) {
						console.error(
							`Failed to sync withdrawal ${withdrawal.id} to LiveStore:`,
							error,
						);
						// Don't add to synced set if it failed, so we can retry
					}
				}
			}
		}
	}, [myWithdrawals, store]);

	// Sync all withdrawals to LiveStore (admin only)
	useEffect(() => {
		if (allWithdrawals?.requests && permissions?.canManageWithdrawals) {
			for (const request of allWithdrawals.requests) {
				// Only sync if we haven't already synced this withdrawal
				if (!syncedWithdrawalsRef.current.has(request.id)) {
					try {
						console.log(
							`[LiveStore] Syncing admin withdrawal ${request.id} from tRPC to LiveStore`,
						);

						// Create withdrawal in LiveStore
						store.commit(
							events.withdrawalCreated({
								id: request.id,
								amount: request.amount,
								requestedById: request.requestedById,
								requestedByName: request.requestedByName || "Unknown User",
								requestedByTornId: request.requestedByTornId
									? Number(request.requestedByTornId)
									: undefined,
								createdAt: new Date(request.createdAt),
							}),
						);

						// If status is not PENDING, update it
						if (request.status !== "PENDING") {
							console.log(
								`[LiveStore] Syncing admin withdrawal status update ${request.id}: ${request.status}`,
							);
							store.commit(
								events.withdrawalStatusUpdated({
									id: request.id,
									status: request.status as
										| "PENDING"
										| "ACCEPTED"
										| "DECLINED"
										| "COMPLETED"
										| "CANCELLED"
										| "EXPIRED",
									processedById: request.processedById || undefined,
									processedByName: "Admin", // Generic for processed withdrawals
									processedAt: request.processedAt
										? new Date(request.processedAt)
										: undefined,
									transactionId: request.transactionId || undefined,
								}),
							);
						}

						// Mark as synced to prevent duplicate attempts
						syncedWithdrawalsRef.current.add(request.id);
					} catch (error) {
						console.error(
							`Failed to sync withdrawal ${request.id} to LiveStore:`,
							error,
						);
						// Don't add to synced set if it failed, so we can retry
					}
				}
			}
		}
	}, [allWithdrawals, permissions, store]);

	// Listen to React Query cache updates and sync to LiveStore
	useEffect(() => {
		const handleCacheChange = () => {
			// Re-sync data when React Query cache is invalidated
			// This handles WebSocket updates that invalidate the cache
			const balanceQuery = queryClient.getQueryData([
				["banking", "getFactionBalance"],
			]);

			// Sync updated balance
			if (
				balanceQuery &&
				typeof balanceQuery === "object" &&
				"factionBalance" in balanceQuery
			) {
				const balance = balanceQuery.factionBalance as {
					money: number;
					points: number;
				};

				const lastSynced = lastSyncedBalanceRef.current;

				// Only sync if balance has actually changed
				if (
					!lastSynced ||
					lastSynced.money !== balance.money ||
					lastSynced.points !== balance.points
				) {
					try {
						console.log(
							`[LiveStore] Syncing balance from cache update: $${balance.money.toLocaleString()}, ${balance.points} points`,
						);
						store.commit(
							events.factionBalanceUpdated({
								money: balance.money,
								points: balance.points,
								updatedAt: new Date(),
							}),
						);

						// Track the synced balance to prevent duplicates
						lastSyncedBalanceRef.current = {
							money: balance.money,
							points: balance.points,
						};
					} catch (error) {
						console.error(
							"Failed to sync updated balance to LiveStore:",
							error,
						);
					}
				}
			}
		};

		// Set up cache listener for real-time updates
		const unsubscribe = queryClient
			.getQueryCache()
			.subscribe(handleCacheChange);
		return unsubscribe;
	}, [queryClient, store]);

	// Utility functions for manual sync (useful for immediate updates after mutations)
	const syncNewWithdrawal = (withdrawal: {
		id: string;
		amount: number;
		requestedById: string;
		requestedByName?: string;
		requestedByTornId?: number;
		createdAt: Date;
	}) => {
		try {
			console.log(
				`[LiveStore] Manually syncing new withdrawal ${withdrawal.id}`,
			);
			store.commit(
				events.withdrawalCreated({
					id: withdrawal.id,
					amount: withdrawal.amount,
					requestedById: withdrawal.requestedById,
					requestedByName: withdrawal.requestedByName || "Unknown User",
					requestedByTornId: withdrawal.requestedByTornId,
					createdAt: withdrawal.createdAt,
				}),
			);
			syncedWithdrawalsRef.current.add(withdrawal.id);
		} catch (error) {
			console.error("Failed to sync new withdrawal to LiveStore:", error);
		}
	};

	const syncWithdrawalStatusUpdate = (update: {
		id: string;
		status:
			| "PENDING"
			| "ACCEPTED"
			| "DECLINED"
			| "COMPLETED"
			| "CANCELLED"
			| "EXPIRED";
		processedById?: string;
		processedByName?: string;
		processedAt?: Date;
		transactionId?: string;
	}) => {
		try {
			console.log(
				`[LiveStore] Manually syncing withdrawal status update ${update.id}: ${update.status}`,
			);
			store.commit(
				events.withdrawalStatusUpdated({
					id: update.id,
					status: update.status,
					processedById: update.processedById,
					processedByName: update.processedByName || "Admin",
					processedAt: update.processedAt || new Date(),
					transactionId: update.transactionId,
				}),
			);
		} catch (error) {
			console.error(
				"Failed to sync withdrawal status update to LiveStore:",
				error,
			);
		}
	};

	const syncBalanceUpdate = (balance: { money: number; points: number }) => {
		const lastSynced = lastSyncedBalanceRef.current;

		// Only sync if balance has actually changed
		if (
			!lastSynced ||
			lastSynced.money !== balance.money ||
			lastSynced.points !== balance.points
		) {
			try {
				console.log(
					`[LiveStore] Manually syncing balance update: $${balance.money.toLocaleString()}, ${balance.points} points`,
				);
				store.commit(
					events.factionBalanceUpdated({
						money: balance.money,
						points: balance.points,
						updatedAt: new Date(),
					}),
				);

				// Track the synced balance to prevent duplicates
				lastSyncedBalanceRef.current = {
					money: balance.money,
					points: balance.points,
				};
			} catch (error) {
				console.error("Failed to sync balance update to LiveStore:", error);
			}
		}
	};

	return {
		syncNewWithdrawal,
		syncWithdrawalStatusUpdate,
		syncBalanceUpdate,
		permissions,
	};
}

/**
 * Hook to listen to WebSocket updates and sync them to LiveStore
 */
export function useWebSocketLiveStoreSync() {
	useEffect(() => {
		// Note: WebSocket integration would go here
		// This would integrate with the existing useBankingWebSocket hook
		// to sync real-time updates to LiveStore

		return () => {
			// Cleanup if needed
		};
	}, []);
}
