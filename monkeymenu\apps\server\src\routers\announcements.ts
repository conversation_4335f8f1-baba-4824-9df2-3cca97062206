import {
	AnnouncementFormSchema,
	AnnouncementUpdateSchema,
	PERMISSIONS,
} from "@monkeymenu/shared";
import { desc, eq } from "drizzle-orm";
import { z } from "zod";
import { announcement } from "../db/schema/announcements";
import { user } from "../db/schema/auth";
import { DiscordNotificationService } from "../lib/discord-notification-service";
import {
	factionPermissionProcedure,
	requirePermission,
	router,
} from "../lib/trpc";

export const announcementsRouter = router({
	// Get all announcements (requires view permission)
	getAll: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.ANNOUNCEMENTS_VIEW.name))
		.query(async ({ ctx }) => {
			return await ctx.db
				.select({
					announcement: announcement,
					author: {
						id: user.id,
						name: user.name,
						image: user.image,
					},
				})
				.from(announcement)
				.innerJoin(user, eq(announcement.authorId, user.id))
				.orderBy(desc(announcement.createdAt));
		}),

	// Get a single announcement by ID (requires view permission)
	getById: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.ANNOUNCEMENTS_VIEW.name))
		.input(z.object({ id: z.number() }))
		.query(async ({ ctx, input }) => {
			return await ctx.db
				.select({
					announcement: announcement,
					author: {
						id: user.id,
						name: user.name,
						image: user.image,
					},
				})
				.from(announcement)
				.innerJoin(user, eq(announcement.authorId, user.id))
				.where(eq(announcement.id, input.id))
				.get();
		}),

	// Create a new announcement (requires manage permission)
	create: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.ANNOUNCEMENTS_MANAGE.name))
		.input(AnnouncementFormSchema)
		.mutation(async ({ ctx, input }) => {
			// Create the announcement
			const newAnnouncement = await ctx.db
				.insert(announcement)
				.values({
					title: input.title,
					content: input.content,
					category: input.category,
					authorId: ctx.session.userId,
				})
				.returning()
				.get();

			// Always send Discord notification (don't fail if Discord is not configured)
			try {
				// Get author information for Discord notification
				const author = await ctx.db
					.select({
						name: user.name,
					})
					.from(user)
					.where(eq(user.id, ctx.session.userId))
					.get();

				const discordService = new DiscordNotificationService(ctx.env);
				await discordService.sendAnnouncementNotification({
					title: input.title,
					content: input.content,
					authorName: author?.name || "Unknown User",
					announcementId: newAnnouncement.id,
					isUrgent: input.isUrgent,
				});
			} catch (error) {
				console.error(
					"Failed to send Discord notification for announcement:",
					error,
				);
				// Don't fail the request if Discord notification fails
			}

			return newAnnouncement;
		}),

	// Update an existing announcement (requires manage permission)
	update: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.ANNOUNCEMENTS_MANAGE.name))
		.input(AnnouncementUpdateSchema)
		.mutation(async ({ ctx, input }) => {
			// Get the existing announcement (optional, could be removed if not needed for other logic)
			const existingAnnouncement = await ctx.db
				.select()
				.from(announcement)
				.where(eq(announcement.id, input.id))
				.get();

			if (!existingAnnouncement) {
				throw new Error("Announcement not found");
			}

			// Permission is now handled by .use(requirePermission(...)) above

			return await ctx.db
				.update(announcement)
				.set({
					title: input.title,
					content: input.content,
					category: input.category,
					updatedAt: new Date().toISOString(),
				})
				.where(eq(announcement.id, input.id))
				.returning()
				.get();
		}),

	// Delete an announcement (requires manage permission)
	delete: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.ANNOUNCEMENTS_MANAGE.name))
		.input(z.object({ id: z.number() }))
		.mutation(async ({ ctx, input }) => {
			// Get the existing announcement (optional, could be removed if not needed for other logic)
			const existingAnnouncement = await ctx.db
				.select()
				.from(announcement)
				.where(eq(announcement.id, input.id))
				.get();

			if (!existingAnnouncement) {
				throw new Error("Announcement not found");
			}

			// Permission is now handled by .use(requirePermission(...)) above

			await ctx.db
				.delete(announcement)
				.where(eq(announcement.id, input.id))
				.run();
			return { success: true };
		}),
});
