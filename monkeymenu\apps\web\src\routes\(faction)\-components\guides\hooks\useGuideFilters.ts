import { useMemo } from "react";
import type { GuideFilters, GuideStats } from "../types";
import { GUIDE_CATEGORIES } from "../utils";

interface GuideData {
	guide: {
		id: number;
		title: string;
		content: string;
		category: string;
		createdAt: string;
		updatedAt: string;
	};
	author?: {
		name: string;
	};
}

export function useGuideFilters(
	guides: GuideData[] | undefined,
	filters: GuideFilters,
) {
	const filteredGuides = useMemo(() => {
		if (!guides) return [];

		return guides.filter((guide) => {
			const matchesSearch =
				filters.searchQuery === "" ||
				guide.guide.title
					.toLowerCase()
					.includes(filters.searchQuery.toLowerCase()) ||
				guide.guide.content
					.toLowerCase()
					.includes(filters.searchQuery.toLowerCase());

			const category = guide.guide.category;
			const matchesCategory =
				filters.selectedCategory === "all" ||
				category === filters.selectedCategory;

			return matchesSearch && matchesCategory;
		});
	}, [guides, filters.searchQuery, filters.selectedCategory]);

	const guideStats = useMemo(() => {
		if (!guides) return {};

		const stats: GuideStats = { all: guides.length };
		for (const cat of GUIDE_CATEGORIES) {
			stats[cat.id] = guides.filter(
				(guide) => guide.guide.category === cat.id,
			).length;
		}

		return stats;
	}, [guides]);

	return {
		filteredGuides,
		guideStats,
	};
}
