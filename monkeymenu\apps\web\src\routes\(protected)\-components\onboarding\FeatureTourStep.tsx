import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
	ArrowRight,
	BookOpen,
	ChevronDown,
	ChevronRight,
	CreditCard,
	Search,
	Shield,
	Users,
	Zap,
} from "lucide-react";
import { useState } from "react";
import { PLATFORM_FEATURES } from "./config";
import type { StepComponentProps } from "./types";

const ICON_MAP = {
	CreditCard,
	Search,
	Zap,
	BookOpen,
	Users,
	Shield,
} as const;

export function FeatureTourStep({
	onNextStep,
	onPrevStep,
}: StepComponentProps) {
	const [expandedFeatures, setExpandedFeatures] = useState<Set<string>>(
		new Set(),
	);

	const toggleFeatureExpansion = (featureTitle: string) => {
		const newExpanded = new Set(expandedFeatures);
		if (newExpanded.has(featureTitle)) {
			newExpanded.delete(featureTitle);
		} else {
			newExpanded.add(featureTitle);
		}
		setExpandedFeatures(newExpanded);
	};

	return (
		<Card>
			<CardContent className="space-y-6 p-8">
				{/* Header */}
				<div className="space-y-2 text-center">
					<h2 className="font-bold text-2xl tracking-tight">You're All Set!</h2>
					<p className="mx-auto max-w-md text-muted-foreground leading-relaxed">
						Here's what you can now access in MonkeyMenu
					</p>
				</div>

				{/* Features Grid */}
				<div className="grid gap-3 md:grid-cols-2">
					{PLATFORM_FEATURES.filter((feature) => !feature.adminOnly).map(
						(feature) => {
							const isExpanded = expandedFeatures.has(feature.title);
							const IconComponent =
								ICON_MAP[feature.icon as keyof typeof ICON_MAP];
							return (
								<Collapsible
									key={feature.title}
									open={isExpanded}
									onOpenChange={() => toggleFeatureExpansion(feature.title)}
								>
									<div className="rounded-lg border transition-all hover:border-primary/30 hover:shadow-sm">
										<CollapsibleTrigger asChild>
											<div className="flex w-full cursor-pointer items-center gap-3 p-4 transition-all hover:bg-muted/50">
												<div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-lg bg-primary/10">
													<IconComponent className="h-5 w-5 text-primary" />
												</div>
												<div className="flex-1 text-left">
													<h3 className="font-medium text-base">
														{feature.title}
													</h3>
													<div className="font-medium text-primary text-xs">
														{feature.category}
													</div>
												</div>
												<div className="text-muted-foreground transition-transform duration-200">
													{isExpanded ? (
														<ChevronDown className="h-4 w-4" />
													) : (
														<ChevronRight className="h-4 w-4" />
													)}
												</div>
											</div>
										</CollapsibleTrigger>
										<CollapsibleContent>
											<div className="border-t px-4 pt-3 pb-4">
												<p className="text-muted-foreground text-sm leading-relaxed">
													{feature.description}
												</p>
											</div>
										</CollapsibleContent>
									</div>
								</Collapsible>
							);
						},
					)}
				</div>
			</CardContent>

			<CardFooter className="flex gap-3 p-8 pt-0">
				<Button
					type="button"
					variant="outline"
					onClick={onPrevStep}
					className="flex-1"
				>
					Back
				</Button>
				<Button onClick={onNextStep} className="flex-1">
					Continue
					<ArrowRight className="ml-2 h-4 w-4" />
				</Button>
			</CardFooter>
		</Card>
	);
}
