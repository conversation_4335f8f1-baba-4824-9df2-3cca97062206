import { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';

interface ChainDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  warId: number;
  chainId: number;
}

export function ChainDetailsDialog({
  isOpen,
  onClose,
  warId,
  chainId,
}: ChainDetailsDialogProps) {
  const [activeTab, setActiveTab] = useState<'timeline' | 'attackers' | 'sequence'>('timeline');

  const chainDetailsQuery = useQuery(
    api.warsAdvanced.getIndividualChainDetails,
    isOpen && chainId > 0 ? { warId, chainId } : "skip"
  );

  const formatDateTime = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    }
    return `${secs}s`;
  };

  if (!isOpen) return null;

  const chainData = chainDetailsQuery;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[70] p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-white border-b border-gray-200 p-6 z-10">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                🔗 Chain #{chainId} - Detailed Analysis
              </h3>
              <p className="text-gray-600 text-sm mt-1">
                Deep dive into chain performance, attack sequence, and metrics
                {chainData && (
                  <span className="ml-2 text-gray-500">
                    Chain Report: {chainData.chainReport.details ? "✓" : "✗"} •
                    Attackers: {chainData.chainReport.attackers?.length || 0} •
                    Attack Sequence: {chainData.attackSequence.length} •
                    Timeline Periods: {chainData.chainMetrics.timelineBreakdown.length}
                  </span>
                )}
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl font-bold"
            >
              ×
            </button>
          </div>

          {/* Tab Navigation */}
          <div className="flex space-x-1 mt-4">
            {[
              { id: 'timeline', label: 'Timeline', icon: '⏰' },
              { id: 'attackers', label: 'Attackers', icon: '👑' },
              { id: 'sequence', label: 'Attack Sequence', icon: '🎯' },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-100 text-blue-800 border border-blue-200'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span className="hidden sm:inline">{tab.label}</span>
                <span className="sm:hidden">{tab.icon}</span>
              </button>
            ))}
          </div>
        </div>

        <div className="p-6">
          {!chainData ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Loading chain details...</span>
            </div>
          ) : (
            <>
              {/* Chain Overview Cards */}
              <div className="grid gap-4 md:grid-cols-3 mb-6">
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-blue-500">⚔️</span>
                    <h4 className="font-semibold text-gray-900">Chain Summary</h4>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Total Hits:</span>
                      <strong>{chainData.chainReport.details.chain}</strong>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Respect:</span>
                      <strong>{chainData.chainReport.details.respect.toFixed(1)}</strong>
                    </div>
                    <div className="flex justify-between">
                      <span>War Hits:</span>
                      <strong>{chainData.chainReport.details.war}</strong>
                    </div>
                    <div className="flex justify-between">
                      <span>Average/Hit:</span>
                      <strong>
                        {(chainData.chainReport.details.respect / chainData.chainReport.details.chain).toFixed(2)}
                      </strong>
                    </div>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-green-500">👥</span>
                    <h4 className="font-semibold text-gray-900">Participation</h4>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Core Contributors:</span>
                      <strong className="text-green-600">
                        {chainData.chainMetrics.participationDepth.coreContributors}
                      </strong>
                    </div>
                    <div className="flex justify-between">
                      <span>Regular Contributors:</span>
                      <strong className="text-blue-600">
                        {chainData.chainMetrics.participationDepth.regularContributors}
                      </strong>
                    </div>
                    <div className="flex justify-between">
                      <span>Casual Contributors:</span>
                      <strong className="text-gray-600">
                        {chainData.chainMetrics.participationDepth.casualContributors}
                      </strong>
                    </div>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-purple-500">⚡</span>
                    <h4 className="font-semibold text-gray-900">Efficiency</h4>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Avg Time/Hit:</span>
                      <strong>
                        {formatDuration(chainData.chainMetrics.efficiencyMetrics.averageTimeBetweenHits)}
                      </strong>
                    </div>
                    <div className="flex justify-between">
                      <span>Peak Hour:</span>
                      <strong>
                        Hour {chainData.chainMetrics.efficiencyMetrics.peakActivity.hour}
                      </strong>
                    </div>
                    <div className="flex justify-between">
                      <span>Respect/Hour:</span>
                      <strong>
                        {chainData.chainMetrics.efficiencyMetrics.respectPerHour.toFixed(1)}
                      </strong>
                    </div>
                  </div>
                </div>
              </div>

              {/* Tab Content */}
              {activeTab === 'timeline' && (
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    ⏰ Hourly Breakdown
                  </h4>
                  
                  {chainData.chainMetrics.timelineBreakdown && chainData.chainMetrics.timelineBreakdown.length > 0 ? (
                    <div className="space-y-3">
                      {chainData.chainMetrics.timelineBreakdown.map((hour) => {
                        const maxAttacks = Math.max(
                          1,
                          ...chainData.chainMetrics.timelineBreakdown.map((h) => h.attackCount)
                        );
                        const progressWidth = (hour.attackCount / maxAttacks) * 100;
                        
                        return (
                          <div key={hour.hourBlock} className="space-y-2">
                            <div className="flex items-center justify-between text-sm">
                              <span className="font-medium">Hour {hour.hourBlock + 1}</span>
                              <span className="text-gray-600">
                                {hour.attackCount} attacks • {hour.respectEarned.toFixed(1)} respect
                              </span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${progressWidth}%` }}
                              />
                            </div>
                            <div className="flex justify-between text-gray-500 text-xs">
                              <span>{hour.uniqueAttackers} unique attackers</span>
                              <span>
                                {hour.attackCount > 0
                                  ? (hour.respectEarned / hour.attackCount).toFixed(2)
                                  : "0"} avg respect
                              </span>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="py-8 text-center text-gray-500">
                      <span className="text-4xl mb-4 block">⏰</span>
                      <p>No timeline data available for this chain</p>
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'attackers' && (
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    👑 Top Contributors
                  </h4>
                  
                  {chainData.chainReport.attackers && chainData.chainReport.attackers.length > 0 ? (
                    <>
                      {/* Desktop Table View */}
                      <div className="hidden lg:block overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Player</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Total Hits</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">War Hits</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Assists</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Total Respect</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Best Hit</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {chainData.chainReport.attackers
                              .sort((a: any, b: any) => b.respect.total - a.respect.total)
                              .map((attacker: any) => (
                                <tr key={attacker.id} className="hover:bg-gray-50">
                                  <td className="px-6 py-4 whitespace-nowrap font-medium text-gray-900">
                                    {attacker.name || `Player #${attacker.id}`}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {attacker.attacks.total}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {attacker.attacks.war}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {attacker.attacks.assists}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">
                                    {attacker.respect.total.toFixed(2)}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {attacker.respect.best.toFixed(2)}
                                  </td>
                                </tr>
                              ))}
                          </tbody>
                        </table>
                      </div>

                      {/* Mobile Card View */}
                      <div className="space-y-3 lg:hidden">
                        {chainData.chainReport.attackers
                          .sort((a: any, b: any) => b.respect.total - a.respect.total)
                          .map((attacker: any) => (
                            <div key={attacker.id} className="border border-gray-200 rounded-lg p-4">
                              <div className="space-y-3">
                                <div className="flex items-center justify-between">
                                  <div className="min-w-0 flex-1">
                                    <div className="font-medium text-gray-900 truncate">
                                      {attacker.name || `Player #${attacker.id}`}
                                    </div>
                                    <div className="text-gray-600 text-sm">
                                      {attacker.attacks.total} total hits • {attacker.attacks.war} war hits
                                    </div>
                                  </div>
                                  <div className="text-right">
                                    <div className="font-semibold text-lg">
                                      {attacker.respect.total.toFixed(2)}
                                    </div>
                                    <div className="text-gray-600 text-sm">total respect</div>
                                  </div>
                                </div>

                                <div className="grid grid-cols-2 gap-3 text-center">
                                  <div>
                                    <div className="text-gray-600 text-sm">Assists</div>
                                    <div className="font-medium">{attacker.attacks.assists}</div>
                                  </div>
                                  <div>
                                    <div className="text-gray-600 text-sm">Best Hit</div>
                                    <div className="font-medium">{attacker.respect.best.toFixed(2)}</div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                      </div>
                    </>
                  ) : (
                    <div className="py-8 text-center text-gray-500">
                      <span className="text-4xl mb-4 block">👥</span>
                      <p>No attacker data available</p>
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'sequence' && (
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    🎯 Attack Sequence ({chainData.attackSequence.length} attacks)
                  </h4>
                  
                  {chainData.attackSequence && chainData.attackSequence.length > 0 ? (
                    <div className="max-h-96 space-y-2 overflow-y-auto">
                      {chainData.attackSequence.map((attack, index) => {
                        // Calculate chain progress by counting attacks that advance the chain
                        const chainProgress = chainData.attackSequence
                          .slice(0, index + 1)
                          .filter((a) =>
                            !["Assist", "Lost", "Escape", "Interrupted", "Defeated", "Timeout", "Stalemate"].includes(a.result)
                          ).length;

                        return (
                          <div key={attack.attackId} className="space-y-2">
                            {/* Main Chain Attack */}
                            <div className="flex items-start gap-3 rounded-lg border border-gray-200 p-3 hover:bg-gray-50 transition-colors">
                              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 font-bold text-blue-700 text-xs">
                                {attack.chainPosition}
                              </div>
                              <div className="flex-1 space-y-2">
                                <div className="space-y-1">
                                  <div className="flex items-center gap-2 text-sm">
                                    <span className="font-medium text-gray-900 truncate">
                                      {attack.attacker.name || `Player #${attack.attacker.id}`}
                                    </span>
                                    <span className="text-gray-500">→</span>
                                    <span className="text-gray-700 truncate">
                                      {attack.defender.name || `Player #${attack.defender.id}`}
                                    </span>
                                  </div>
                                  <div className="flex flex-wrap items-center gap-1">
                                    {attack.isWarTarget && (
                                      <span className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs border border-red-200">
                                        War Target
                                      </span>
                                    )}
                                    {attack.assists && attack.assists.length > 0 && (
                                      <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs border border-blue-200">
                                        <span className="hidden sm:inline">
                                          +{attack.assists.length} assist{attack.assists.length !== 1 ? 's' : ''}
                                        </span>
                                        <span className="sm:hidden">+{attack.assists.length}</span>
                                      </span>
                                    )}
                                  </div>
                                </div>
                                <div className="flex flex-wrap items-center gap-2 text-gray-600 text-xs sm:gap-4">
                                  <span>{formatDateTime(attack.timestamp)}</span>
                                  <span>Result: {attack.result}</span>
                                  <span>Respect: {attack.respect.toFixed(2)}</span>
                                  <span>Chain: {chainProgress}</span>
                                </div>
                              </div>
                            </div>

                            {/* Related Assists */}
                            {attack.assists && attack.assists.length > 0 && (
                              <div className="ml-11 space-y-1">
                                {attack.assists.map((assist) => (
                                  <div
                                    key={assist.attackId}
                                    className="flex items-center gap-3 rounded-lg border border-dashed border-blue-200 bg-blue-50 p-2 text-sm"
                                  >
                                    <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 font-bold text-blue-700 text-xs">
                                      A
                                    </div>
                                    <div className="flex-1 space-y-1">
                                      <div className="flex items-center gap-2">
                                        <span className="font-medium text-blue-900 text-sm">
                                          {assist.attacker.name || `Player #${assist.attacker.id}`}
                                        </span>
                                        <span className="text-blue-600 text-xs">assisted</span>
                                      </div>
                                      <div className="flex items-center gap-4 text-blue-600 text-xs">
                                        <span>{formatDateTime(assist.timestamp)}</span>
                                        <span>Result: {assist.result}</span>
                                        <span>Respect: {assist.respect.toFixed(2)}</span>
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="py-8 text-center text-gray-500">
                      <span className="text-4xl mb-4 block">🎯</span>
                      <p>No attack sequence data available for this chain</p>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}