import { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { useQuery, useAction, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { WithdrawalRequest } from './WithdrawalRequest';
import { TransactionHistory } from './TransactionHistory';
import { BankingStats } from './BankingStats';
import { AdminPanel } from './AdminPanel';
import { ErrorBoundary } from './ErrorBoundary';
import { usePermissions } from '../../hooks/usePermissions';
import { useSession } from '../../hooks/useSession';
import { Button } from '../ui/button';
import { Plus } from 'lucide-react';

export function BankingDashboard() {
  // ALL HOOKS MUST BE CALLED FIRST, BEFORE ANY CONDITIONAL LOGIC
  const { canAccessBanking, canManageBanking } = usePermissions();
  const { convexUser, isLoadingSession } = useSession();
  const [showWithdrawalForm, setShowWithdrawalForm] = useState(false);

  // Ref for scrolling to withdrawal form
  const withdrawalFormRef = useRef<HTMLDivElement>(null);


  // Check if user has API key
  const [showApiKeyWarning, setShowApiKeyWarning] = useState(false);
  const [apiKeyInput, setApiKeyInput] = useState('');
  const [showApiKeyInput, setShowApiKeyInput] = useState(false);

  // Mutation to update API key
  const updateApiKey = useMutation(api.banking.updateUserApiKey);
  const migrateApiKey = useMutation(api.banking.migrateApiKey);
  const refreshPermissions = useAction(api.banking.refreshUserPermissions);

  // Get faction balance from Torn API
  const getFactionBalance = useAction(api.banking.getFactionBalance);
  const [factionBalance, setFactionBalance] = useState<any>(null);

  // Get pending withdrawal requests for admins
  const pendingRequests = useQuery(
    api.banking.getPendingWithdrawalRequests,
    canManageBanking() ? {} : "skip"
  );

  const allTransactions = useQuery(api.banking.getUserTransactions,
    convexUser ? { userId: convexUser._id, limit: 10 } : "skip"
  );
  const userWithdrawals = useQuery(api.banking.getUserWithdrawalRequests,
    convexUser ? { userId: convexUser._id, limit: 10 } : "skip"
  );

  // Combine transactions and withdrawal requests for unified history
  const combinedHistory = useMemo(() => {
    if (!allTransactions || !userWithdrawals) return [];

    const transactionItems = allTransactions.map((tx: any) => ({
      id: tx._id,
      type: 'transaction' as const,
      amount: tx.amount,
      status: tx.status,
      createdAt: tx.createdAt,
      processedAt: tx.processedAt,
      reference: tx.reference,
      withdrawalRequestId: tx.withdrawalRequestId,
    }));

    const withdrawalItems = userWithdrawals.map((wr: any) => ({
      id: wr._id,
      type: 'withdrawal_request' as const,
      amount: wr.amount,
      status: wr.status.toLowerCase(),
      createdAt: wr.createdAt,
      processedAt: wr.processedAt,
      reference: `withdrawal-${wr._id}`,
      withdrawalRequestId: wr._id,
    }));

    // Combine and sort by creation date (newest first)
    return [...transactionItems, ...withdrawalItems]
      .sort((a, b) => b.createdAt - a.createdAt)
      .slice(0, 20); // Show last 20 items
  }, [allTransactions, userWithdrawals]);

  useEffect(() => {
    if (convexUser) {
      getFactionBalance({})
        .then(setFactionBalance)
        .catch((error: any) => {
          console.error('Failed to fetch faction balance:', error);
          // Temporary fallback: Show 40M balance when API key is missing
          if (error.message && error.message.includes('User API key not found')) {
            setShowApiKeyWarning(true);
            setFactionBalance({
              factionId: 53100,
              factionName: 'Your Faction',
              totalMoney: 40000000,
              availableFunds: 40000000,
              members: 50,
              lastUpdated: Date.now()
            });
          }
        });
    }
  }, [convexUser, getFactionBalance]);

  // Callback for showing/hiding withdrawal form
  const handleToggleWithdrawalForm = useCallback(() => {
    setShowWithdrawalForm(prev => {
      const newValue = !prev;
      // If we're showing the form, scroll to it after a brief delay to allow rendering
      if (newValue) {
        setTimeout(() => {
          withdrawalFormRef.current?.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }, 100);
      }
      return newValue;
    });
  }, []);



  // Handle API key submission
  const handleApiKeySubmit = async () => {
    if (!apiKeyInput.trim()) return;
    
    try {
      await updateApiKey({ apiKey: apiKeyInput.trim() });
      setApiKeyInput('');
      setShowApiKeyInput(false);
      setShowApiKeyWarning(false);
      
      // Refresh the faction balance
      getFactionBalance({})
        .then(setFactionBalance)
        .catch((error: any) => {
          console.error('Failed to fetch faction balance:', error);
        });
        
    } catch (error: any) {
      console.error('Failed to update API key:', error);
      alert('Failed to update API key: ' + (error.message || error));
    }
  };

  // Handle API key migration
  const handleApiKeyMigration = async () => {
    try {
      const result = await migrateApiKey({});
      console.log('Migration result:', result);
      
      if (result.success) {
        alert('API key migrated successfully! The page will now reload.');
        window.location.reload();
      } else {
        alert('No migration needed - your API key is already in the correct format.');
      }
    } catch (error: any) {
      console.error('Failed to migrate API key:', error);
      alert('Failed to migrate API key: ' + (error.message || error));
    }
  };

  // Handle permission refresh (for debugging access issues only)
  const handlePermissionRefresh = async () => {
    try {
      const result = await refreshPermissions({});
      console.log('Permission refresh result:', result);
      
      if (result.success) {
        alert(`Permissions refreshed successfully! You now have ${result.newPermissions.length} permissions for role: ${result.role}. The page will reload.`);
        window.location.reload();
      }
    } catch (error: any) {
      console.error('Failed to refresh permissions:', error);
      alert('Failed to refresh permissions: ' + (error.message || error));
    }
  };

  const currentUser = convexUser;

  if (!canAccessBanking()) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Restricted</h2>
          <p className="text-gray-600">You don't have permission to access banking features.</p>
          {convexUser && (
            <div className="mt-4 p-4 bg-gray-100 rounded-lg">
              <p className="text-sm text-gray-700 mb-3">
                Debug: User found but no banking permissions. Current permissions: {convexUser.permissions?.join(', ') || 'none'}
              </p>
              <button
                onClick={handlePermissionRefresh}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium"
              >
                Refresh Permissions
              </button>
            </div>
          )}
        </div>
      </div>
    );
  }

  if (isLoadingSession || !currentUser) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }


  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">💰 Faction Banking</h1>
          <p className="text-gray-600">Manage faction withdrawals and view your transaction history</p>
        </div>

        <Button onClick={handleToggleWithdrawalForm}>
          <Plus className="mr-2 h-4 w-4" />
          New Withdrawal
        </Button>
      </div>

      {/* API Key Warning */}
      {showApiKeyWarning && (
        <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-yellow-400 text-xl">⚠️</span>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">
                  Torn API Key Missing
                </h3>
                <div className="mt-2 text-sm text-yellow-700">
                  <p>Your Torn API key is not configured. Showing fallback balance data.</p>
                  {!showApiKeyInput && (
                    <p className="mt-1">To see real faction balance, please add your API key or try migrating from onboarding.</p>
                  )}
                </div>
              </div>
            </div>
            <div className="flex-shrink-0 flex gap-2">
              {!showApiKeyInput ? (
                <>
                  <button
                    onClick={handleApiKeyMigration}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium"
                  >
                    Fix API Key
                  </button>
                  <button
                    onClick={() => setShowApiKeyInput(true)}
                    className="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-sm font-medium"
                  >
                    Add API Key
                  </button>
                </>
              ) : (
                <button
                  onClick={() => setShowApiKeyInput(false)}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm font-medium"
                >
                  Cancel
                </button>
              )}
            </div>
          </div>
          
          {showApiKeyInput && (
            <div className="mt-4 flex gap-2">
              <input
                type="text"
                value={apiKeyInput}
                onChange={(e) => setApiKeyInput(e.target.value)}
                placeholder="Enter your 16-character Torn API key"
                className="flex-1 px-3 py-2 border border-yellow-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 text-sm"
                maxLength={16}
              />
              <button
                onClick={handleApiKeySubmit}
                disabled={!apiKeyInput.trim() || apiKeyInput.length !== 16}
                className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded text-sm font-medium"
              >
                Save
              </button>
            </div>
          )}
        </div>
      )}



      {/* Banking Statistics */}
      <ErrorBoundary>
        <BankingStats userId={currentUser._id} factionBalance={factionBalance} />
      </ErrorBoundary>

      {/* Withdrawal Form Card */}
      {showWithdrawalForm && (
        <div ref={withdrawalFormRef}>
          <ErrorBoundary>
            <WithdrawalRequest onCancel={() => setShowWithdrawalForm(false)} />
          </ErrorBoundary>
        </div>
      )}

      {/* Admin Panel - Auto-show when there are pending requests */}
      {canManageBanking() && pendingRequests && pendingRequests.length > 0 && (
        <ErrorBoundary>
          <AdminPanel />
        </ErrorBoundary>
      )}


      {/* Transaction History */}
      <ErrorBoundary>
        <TransactionHistory userId={currentUser._id} combinedHistory={combinedHistory} />
      </ErrorBoundary>
    </div>
  );
}