@import "tailwindcss";
@import "tw-animate-css";
@import "highlight.js/styles/github-dark.css";

@custom-variant dark (&:is(.dark *));

@theme inline {
	/* Colors */
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);

	/* Charts */
	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);

	/* Sidebar */
	--color-sidebar: var(--sidebar);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);

	/* Typography */
	--font-sans: var(--font-sans);
	--font-mono: var(--font-mono);
	--font-serif: var(--font-serif);

	/* Radius - Use theme values with fallbacks */
	--radius-sm: calc(var(--radius, 0.5rem) - 4px);
	--radius-md: calc(var(--radius, 0.5rem) - 2px);
	--radius-lg: var(--radius, 0.5rem);
	--radius-xl: calc(var(--radius, 0.5rem) + 4px);

	/* Shadows */
	--shadow-2xs: var(--shadow-2xs, 0 1px 3px 0px hsl(0 0% 0% / 0.05));
	--shadow-xs: var(--shadow-xs, 0 1px 3px 0px hsl(0 0% 0% / 0.05));
	--shadow-sm: var(
		--shadow-sm,
		0 1px 3px 0px hsl(0 0% 0% / 0.1),
		0 1px 2px -1px hsl(0 0% 0% / 0.1)
	);
	--shadow: var(
		--shadow,
		0 1px 3px 0px hsl(0 0% 0% / 0.1),
		0 1px 2px -1px hsl(0 0% 0% / 0.1)
	);
	--shadow-md: var(
		--shadow-md,
		0 1px 3px 0px hsl(0 0% 0% / 0.1),
		0 2px 4px -1px hsl(0 0% 0% / 0.1)
	);
	--shadow-lg: var(
		--shadow-lg,
		0 1px 3px 0px hsl(0 0% 0% / 0.1),
		0 4px 6px -1px hsl(0 0% 0% / 0.1)
	);
	--shadow-xl: var(
		--shadow-xl,
		0 1px 3px 0px hsl(0 0% 0% / 0.1),
		0 8px 10px -1px hsl(0 0% 0% / 0.1)
	);
	--shadow-2xl: var(--shadow-2xl, 0 1px 3px 0px hsl(0 0% 0% / 0.25));

	/* Letter spacing */
	--tracking-normal: var(--tracking-normal, 0em);
	--tracking-tight: var(
		--tracking-tight,
		calc(var(--tracking-normal, 0em) - 0.025em)
	);
	--tracking-wide: var(
		--tracking-wide,
		calc(var(--tracking-normal, 0em) + 0.025em)
	);
	--tracking-tighter: var(
		--tracking-tighter,
		calc(var(--tracking-normal, 0em) - 0.05em)
	);
	--tracking-wider: var(
		--tracking-wider,
		calc(var(--tracking-normal, 0em) + 0.05em)
	);
	--tracking-widest: var(
		--tracking-widest,
		calc(var(--tracking-normal, 0em) + 0.1em)
	);
}

@layer base {
	* {
		@apply border-border outline-ring/50;
		font-family: "Geist Mono", monospace;
	}
	body {
		@apply bg-background text-foreground;
	}
}

@layer utilities {
	.line-clamp-2 {
		overflow: hidden;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
	}

	.line-clamp-3 {
		overflow: hidden;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 3;
	}
}

@keyframes chain-border-blink {
	0%,
	100% {
		opacity: 1;
	}
	50% {
		opacity: 0.3;
	}
}

/* Overlay for chain statuses */
body.chain-good::before,
body.chain-warning::before,
body.chain-critical::before {
	content: "";
	position: fixed;
	inset: 0;
	border: 4px solid transparent;
	pointer-events: none;
	z-index: 1000;
}

body.chain-good::before {
	border-color: rgba(34, 197, 94, 0.9); /* green-500 */
}

body.chain-warning::before {
	border-color: rgba(251, 146, 60, 0.9); /* orange-400 */
}

body.chain-critical::before {
	border-color: rgba(239, 68, 68, 0.9); /* red-500 */
	animation: chain-border-blink 1s infinite;
}
