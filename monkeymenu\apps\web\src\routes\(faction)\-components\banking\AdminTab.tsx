import { HasPermission } from "@/components/permissions/PermissionGuards";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { PERMISSION_NAMES } from "@monkeymenu/shared";
import { useState } from "react";
import { WithdrawalCard } from "./WithdrawalCard";
import { WithdrawalFilters } from "./WithdrawalFilters";
import type { AdminTabProps } from "./types";

export function AdminTab({
	withdrawals,
	isLoading,
	onWithdrawalSelect,
	onApprove,
	onDecline,
	isProcessing,
}: AdminTabProps) {
	const [viewMode, setViewMode] = useState<"grid" | "list">("list");

	return (
		<HasPermission permission={PERMISSION_NAMES.BANKING_MANAGE_REQUESTS}>
			<Card>
				<CardHeader>
					<CardTitle>Pending Withdrawal Approvals</CardTitle>
					<p className="text-muted-foreground text-sm">
						Review and process all pending withdrawal requests from users.
					</p>
				</CardHeader>
				<CardContent className="space-y-6">
					{isLoading ? (
						<div className="py-8 text-center">
							Loading pending withdrawals...
						</div>
					) : withdrawals.length === 0 ? (
						<div className="py-8 text-center">
							<p className="text-muted-foreground">
								No pending withdrawals to display.
							</p>
						</div>
					) : (
						<>
							<WithdrawalFilters
								searchQuery=""
								onSearchChange={() => {}}
								selectedStatus="all"
								onStatusChange={() => {}}
								selectedAmountRange="all"
								onAmountRangeChange={() => {}}
								viewMode={viewMode}
								onViewModeChange={setViewMode}
							/>

							<div
								className={
									viewMode === "grid"
										? "grid gap-4 md:grid-cols-2 lg:grid-cols-3"
										: "space-y-4"
								}
							>
								{withdrawals.map((withdrawal) => (
									<WithdrawalCard
										key={withdrawal.withdrawal.id}
										withdrawal={withdrawal}
										onViewDetails={onWithdrawalSelect}
										onApprove={onApprove}
										onDecline={onDecline}
										showActions={true}
										isProcessing={isProcessing}
									/>
								))}
							</div>
						</>
					)}
				</CardContent>
			</Card>
		</HasPermission>
	);
}
