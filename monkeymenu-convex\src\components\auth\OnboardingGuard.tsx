import { useNavigate, useLocation } from "@tanstack/react-router";
import { useEffect, ReactNode } from "react";
import { useOnboardingStatus } from "../../hooks/useOnboardingStatus";

interface OnboardingGuardProps {
  children: ReactNode;
}

export function OnboardingGuard({ children }: OnboardingGuardProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const { needsOnboarding, isLoading } = useOnboardingStatus();

  // Don't redirect if already on onboarding page
  const isOnOnboardingPage = location.pathname === '/onboarding';

  useEffect(() => {
    if (!isLoading && needsOnboarding && !isOnOnboardingPage) {
      navigate({ to: "/onboarding" });
    }
  }, [needsOnboarding, isLoading, navigate, isOnOnboardingPage]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (needsOnboarding && !isOnOnboardingPage) {
    return null; // Will redirect to onboarding
  }

  return <>{children}</>;
}