import { HasPermission } from "@/components/permissions/PermissionGuards";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import { PERMISSION_NAMES } from "@monkeymenu/shared";
import { ExternalLink, Swords, Trash2 } from "lucide-react";
import type { TargetCardProps } from "./types";
import { getStatusInfo } from "./utils";

export function TargetCard({
	target,
	currentStatus,
	selectedList,
	onRemoveTarget,
	isRemoving,
}: TargetCardProps) {
	const statusInfo = getStatusInfo(currentStatus);
	const isHospitalized = currentStatus.includes("Hospitalized");
	const hasError =
		currentStatus.includes("Error") || currentStatus === "Fetch Error";
	const canAttack = !isHospitalized && !hasError;

	return (
		<Card key={target.id} className="group transition-all hover:shadow-md">
			<CardHeader className="pb-3">
				<div className="flex items-start justify-between">
					<div className="flex items-center gap-3">
						<Avatar className="h-10 w-10">
							<AvatarImage
								src={target.profilePicture || ""}
								alt={target.name}
							/>
							<AvatarFallback className="bg-primary font-semibold text-primary-foreground text-sm">
								{target.name.charAt(0).toUpperCase()}
							</AvatarFallback>
						</Avatar>
						<div className="min-w-0 flex-1">
							<CardTitle className="line-clamp-1 text-base">
								{target.name}
							</CardTitle>
							<p className="font-mono text-muted-foreground text-sm">
								#{target.tornId}
							</p>
						</div>
					</div>
					<div className="flex gap-1 opacity-0 transition-opacity group-hover:opacity-100">
						{/* Show remove button for Custom List */}
						{selectedList === "Custom List" && (
							<HasPermission permission={PERMISSION_NAMES.TARGET_FINDER_VIEW}>
								<Tooltip>
									<TooltipTrigger asChild>
										<Button
											variant="ghost"
											size="sm"
											onClick={() => onRemoveTarget(target.tornId)}
											disabled={isRemoving}
											className="h-8 w-8 p-0 text-destructive hover:bg-destructive/10 hover:text-destructive"
										>
											<Trash2 className="h-4 w-4" />
										</Button>
									</TooltipTrigger>
									<TooltipContent>
										<p>Remove from list</p>
									</TooltipContent>
								</Tooltip>
							</HasPermission>
						)}

						{/* Show remove button for shared lists (non-custom, non-external) */}
						{selectedList &&
							selectedList !== "Custom List" &&
							!selectedList.startsWith("Baldr's") &&
							!selectedList.startsWith("Enemy Faction:") && (
								<HasPermission
									permission={
										PERMISSION_NAMES.TARGET_FINDER_MANAGE_SHARED_LISTS
									}
								>
									<Tooltip>
										<TooltipTrigger asChild>
											<Button
												variant="ghost"
												size="sm"
												onClick={() => onRemoveTarget(target.tornId)}
												disabled={isRemoving}
												className="h-8 w-8 p-0 text-destructive hover:bg-destructive/10 hover:text-destructive"
											>
												<Trash2 className="h-4 w-4" />
											</Button>
										</TooltipTrigger>
										<TooltipContent>
											<p>Remove from shared list</p>
										</TooltipContent>
									</Tooltip>
								</HasPermission>
							)}
					</div>
				</div>
				<div className="flex items-center gap-2">
					<div
						className={`rounded-full p-1 ${statusInfo.bgColor} ${statusInfo.borderColor} border`}
					>
						<statusInfo.icon className={`h-3 w-3 ${statusInfo.color}`} />
					</div>
					<Badge variant={statusInfo.variant} className="font-medium">
						{currentStatus}
					</Badge>
				</div>
			</CardHeader>
			<CardFooter className="pt-0">
				{!canAttack ? (
					<Tooltip>
						<TooltipTrigger asChild>
							<Button variant="outline" disabled className="w-full gap-2">
								<Swords className="h-4 w-4 opacity-50" />
								Cannot Attack
							</Button>
						</TooltipTrigger>
						<TooltipContent>
							<p>
								{isHospitalized
									? "Cannot attack - target is hospitalized"
									: "Cannot attack - status unknown"}
							</p>
						</TooltipContent>
					</Tooltip>
				) : (
					<Button variant="outline" className="w-full gap-2" asChild>
						<a
							href={`https://www.torn.com/loader.php?sid=attack&user2ID=${target.tornId}`}
							target="_blank"
							rel="noopener noreferrer"
						>
							<Swords className="h-4 w-4" />
							Attack
							<ExternalLink className="h-3 w-3" />
						</a>
					</Button>
				)}
			</CardFooter>
		</Card>
	);
}
