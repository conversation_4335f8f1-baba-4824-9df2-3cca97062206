
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';

export function TargetStats() {
  const stats = useQuery(api.targets.getTargetStats);

  if (!stats) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="text-center">
                <div className="h-8 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">📊 Target Statistics</h2>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
          <div className="text-sm text-gray-600">Total Targets</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">{stats.averageLevel}</div>
          <div className="text-sm text-gray-600">Avg Level</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">
            {stats.averageRespect.toLocaleString()}
          </div>
          <div className="text-sm text-gray-600">Avg Respect</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">
            {stats.averageFairFight}
          </div>
          <div className="text-sm text-gray-600">Avg Fair Fight</div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Status Distribution */}
        <div>
          <h3 className="text-sm font-semibold text-gray-700 mb-3">Status Distribution</h3>
          <div className="space-y-2">
            {Object.entries(stats.byStatus).map(([status, count]) => {
              const percentage = stats.total > 0 ? Math.round(((count as number) / stats.total) * 100) : 0;
              const statusIcons = {
                active: '🟢',
                inactive: '⚫',
                hospitalized: '🏥',
                jailed: '🔒',
              };
              
              return (
                <div key={status} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span>{statusIcons[status as keyof typeof statusIcons] || '❓'}</span>
                    <span className="text-sm capitalize text-gray-700">{status}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600 w-12 text-right">
                      {count as number} ({percentage}%)
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Level Distribution */}
        <div>
          <h3 className="text-sm font-semibold text-gray-700 mb-3">Level Distribution</h3>
          <div className="space-y-2">
            {Object.entries(stats.byLevel).map(([level, count]) => {
              const percentage = stats.total > 0 ? Math.round(((count as number) / stats.total) * 100) : 0;
              const levelLabels = {
                low: '1-50',
                medium: '51-100', 
                high: '101+',
              };
              const levelColors = {
                low: 'bg-green-600',
                medium: 'bg-yellow-600',
                high: 'bg-red-600',
              };
              
              return (
                <div key={level} className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">
                    Level {levelLabels[level as keyof typeof levelLabels]}
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${levelColors[level as keyof typeof levelColors]}`}
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600 w-12 text-right">
                      {String(count)} ({String(Number(percentage))}%)
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}