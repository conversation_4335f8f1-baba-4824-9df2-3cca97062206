import { PERMISSIONS } from "@monkeymenu/shared";
import {
	type CommandContext,
	SlashCommand,
	type SlashC<PERSON>,
} from "slash-create/web";
import { initDb } from "../../db";
import { checkDiscordUserPermission } from "../../lib/discord-permissions";
import type { AppBindings } from "../../lib/types";

// Configuration constants
const EMBED_CONFIG = {
	COLOR: 0x00bfff,
	WEBSITE_URL: "https://monkeymenu.app",
	SETTINGS_URL: "https://monkeymenu.app/settings",
	TORN_API_URL: "https://www.torn.com/preferences.php#tab=api",
	FAVICON_URL: "https://monkeymenu.app/favicon.ico",
} as const;

export default class SendVerifyEmbedCommand extends SlashCommand {
	constructor(creator: SlashCreator) {
		super(creator, {
			name: "sendverifyembed",
			description: "Send a verification embed to a channel (System Admin only)",
			options: [
				{
					type: 7, // CHANNEL
					name: "channel",
					description:
						"Channel to send the verification embed to (defaults to current channel)",
					required: false,
					channel_types: [0], // GUILD_TEXT
				},
			],
		});
	}

	async run(ctx: CommandContext) {
		const discordUserId = ctx.user.id;
		const discordUserTag = `${ctx.user.username}#${ctx.user.discriminator}`;
		const targetChannel = ctx.options.channel;

		// Debug logging for channel option
		console.log("Channel option debug:", {
			targetChannel,
			channelType: typeof targetChannel,
			isString: typeof targetChannel === "string",
			isObject: typeof targetChannel === "object",
			currentChannelId: ctx.channelID,
		});

		await ctx.defer(true); // Ephemeral response for confirmation only

		try {
			// Get environment from creator options
			const env = (this.creator.options as { env?: AppBindings["Bindings"] })
				.env;
			if (!env) {
				throw new Error("Environment not available in creator options");
			}
			const db = initDb(env);

			// Check if user has the required permission using our granular system
			const permissionCheck = await checkDiscordUserPermission(
				db,
				discordUserId,
				PERMISSIONS.DISCORD_MANAGE_VERIFICATION.name, // Only users with Discord verification management permission can send verification embeds
			);

			if (!permissionCheck.isLinked) {
				return {
					content:
						"❌ Your Discord account is not linked to MonkeyMenu. Please link your account at https://monkeymenu.app/settings to use this command.",
					ephemeral: true,
				};
			}

			if (!permissionCheck.hasPermission) {
				return {
					content:
						"❌ You do not have permission to use this command. (Requires: System Administrator)",
					ephemeral: true,
				};
			}

			// Create verification embed
			const embed = {
				color: EMBED_CONFIG.COLOR,
				title: "🔒 Account Verification",
				description: `Link your **Discord account** with **MonkeyMenu** to access all faction features and sync your roles!\n\n**📝 Step 1:** [Sign up or log in to MonkeyMenu](${EMBED_CONFIG.WEBSITE_URL})\n**⚙️ Step 2:** Navigate to your [**Settings**](${EMBED_CONFIG.SETTINGS_URL}) page\n**🔑 Step 3:** Enter your **Limited Access API Key** from Torn\n**🔗 Step 4:** Link your **Discord account** in the Discord section\n**✅ Step 5:** Click the **Verify** button below to complete the process!\n\n**📋 Requirements:**\n• Must be a member of **Menacing Monkeys** faction (#53100)\n• Use a **Limited Access** API key (recommended for security)\n• Have a verified email on your Torn account\n• Link the same Discord account you're using in this server\n\n*Need help? Get your API key from [Torn's API settings](${EMBED_CONFIG.TORN_API_URL}) or contact faction leadership*`,
				footer: {
					text: "Menacing Monkeys • Verification System",
					icon_url: EMBED_CONFIG.FAVICON_URL,
				},
				timestamp: new Date().toISOString(),
			};

			const components = [
				{
					type: 1, // ACTION_ROW
					components: [
						{
							type: 2, // BUTTON
							style: 5, // LINK
							label: "🔗 Verify Account",
							url: `${env.APP_URL}/api/bot/verify-account`,
							emoji: {
								name: "✅",
							},
						},
					],
				},
			];

			// Determine target channel - handle different formats (string ID vs object with id property)
			let channelId: string;
			let channelMention: string;

			if (targetChannel) {
				// Handle case where targetChannel is just the channel ID string
				if (typeof targetChannel === "string") {
					channelId = targetChannel;
					channelMention = `<#${targetChannel}>`;
				}
				// Handle case where targetChannel is an object with id property
				else if (targetChannel.id) {
					channelId = targetChannel.id;
					channelMention = `<#${targetChannel.id}>`;
				}
				// Fallback to current channel
				else {
					channelId = ctx.channelID;
					channelMention = "this channel";
				}
			} else {
				channelId = ctx.channelID;
				channelMention = "this channel";
			}

			// Send the embed to the specified channel using Discord API directly
			// We can't use ctx.send() for different channels, so we use the Discord API
			const response = await fetch(
				`https://discord.com/api/v10/channels/${channelId}/messages`,
				{
					method: "POST",
					headers: {
						Authorization: `Bot ${env.DISCORD_BOT_TOKEN}`,
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						embeds: [embed],
						components: components,
					}),
				},
			);

			if (!response.ok) {
				const errorData = await response.text();
				throw new Error(
					`Failed to send message to channel: ${response.status} - ${errorData}`,
				);
			}

			// Log the action for audit purposes with permission context
			console.info(
				`[ADMIN ACTION] Verification embed sent by ${discordUserTag} (Discord: ${discordUserId}, User: ${permissionCheck.userId}) to channel ${channelId}. Role: ${permissionCheck.permissionContext?.roleName || "Unknown"}`,
			);

			return {
				content: `✅ Verification embed successfully sent to ${channelMention}!`,
				ephemeral: true,
			};
		} catch (error) {
			console.error(
				`[ERROR] Failed to send verification embed. User: ${discordUserTag} (${discordUserId})`,
				error,
			);

			// More specific error messages based on error type
			if (error instanceof Error) {
				if (error.message.includes("Environment not available")) {
					return {
						content:
							"❌ System configuration error. Please contact an administrator.",
						ephemeral: true,
					};
				}
				if (error.message.includes("Permission check failed")) {
					return {
						content: "❌ Unable to verify permissions. Please try again later.",
						ephemeral: true,
					};
				}
			}

			return {
				content:
					"❌ An unexpected error occurred while sending the verification embed. Please try again later.",
				ephemeral: true,
			};
		}
	}
}
