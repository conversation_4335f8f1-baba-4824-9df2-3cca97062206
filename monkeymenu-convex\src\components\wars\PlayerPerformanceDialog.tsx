import { useState, useMemo } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';

export interface PlayerPerformanceDialogProps {
  isOpen: boolean;
  onClose: () => void;
  warId: number;
  playerId?: number;
}

export function PlayerPerformanceDialog({
  isOpen,
  onClose,
  warId,
  playerId,
}: PlayerPerformanceDialogProps) {
  const [playerSearch, setPlayerSearch] = useState("");
  const [activeTab, setActiveTab] = useState<'overview' | 'trends' | 'chains' | 'effectiveness'>('overview');

  const playerPerformanceQuery = useQuery(
    api.warsAdvanced.getPlayerWarPerformance,
    isOpen ? { warId, playerId } : "skip"
  );

  const filteredPlayers = useMemo(() => {
    if (!playerPerformanceQuery?.playerDetails || !playerSearch) {
      return playerPerformanceQuery?.playerDetails || [];
    }
    return playerPerformanceQuery.playerDetails.filter(
      (player) =>
        player.playerName?.toLowerCase().includes(playerSearch.toLowerCase()) ||
        player.playerId.toString().includes(playerSearch),
    );
  }, [playerPerformanceQuery?.playerDetails, playerSearch]);

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m`;
    }
    return `${seconds}s`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[70] p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-white border-b border-gray-200 p-6 z-10">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                👥 Player Performance Analysis
              </h3>
              <p className="text-gray-600 text-sm mt-1">
                Detailed individual performance metrics and battle effectiveness
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl font-bold"
            >
              ×
            </button>
          </div>

          {!playerId && (
            <div className="mt-4">
              <input
                type="text"
                placeholder="Search players by name or ID..."
                value={playerSearch}
                onChange={(e) => setPlayerSearch(e.target.value)}
                className="w-full max-w-md px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          )}
        </div>

        <div className="p-6">
          {!playerPerformanceQuery ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Loading player performance...</span>
            </div>
          ) : filteredPlayers.length === 0 ? (
            <div className="py-8 text-center text-gray-500">
              <span className="text-4xl mb-4 block">👥</span>
              <p>No players found matching your search.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredPlayers.map((player) => (
                <div key={player.playerId} className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="mb-6">
                    <div className="flex items-start justify-between">
                      <div className="min-w-0 flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="rounded-lg bg-blue-100 p-2">
                            <span className="text-blue-600 text-lg">👤</span>
                          </div>
                          <div>
                            <h4 className="text-lg font-bold text-gray-900 truncate">
                              {player.playerName || `Player #${player.playerId}`}
                            </h4>
                            <p className="text-gray-600 text-sm">
                              Participation: {player.overallStats.participationRate.toFixed(1)}% • 
                              Active: {player.overallStats.activeHours}h
                            </p>
                          </div>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs border border-yellow-200 flex items-center gap-1">
                            🏆 <span className="hidden sm:inline">Respect #</span>
                            <span className="sm:hidden">#</span>
                            {player.comparativeRanking.respectRank}
                          </span>
                          <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs border border-blue-200 flex items-center gap-1">
                            ⚔️ <span className="hidden sm:inline">Attacks #</span>
                            <span className="sm:hidden">#</span>
                            {player.comparativeRanking.attackCountRank}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Tab Navigation */}
                  <div className="flex space-x-1 mb-6">
                    {[
                      { id: 'overview', label: 'Overview', icon: '📊' },
                      { id: 'trends', label: 'Trends', icon: '📈' },
                      { id: 'chains', label: 'Chains', icon: '🔗' },
                      { id: 'effectiveness', label: 'Effectiveness', icon: '⚡' },
                    ].map((tab) => (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id as any)}
                        className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                          activeTab === tab.id
                            ? 'bg-blue-100 text-blue-800 border border-blue-200'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                      >
                        <span className="hidden sm:inline">{tab.label}</span>
                        <span className="sm:hidden">{tab.icon}</span>
                      </button>
                    ))}
                  </div>

                  {/* Tab Content */}
                  {activeTab === 'overview' && (
                    <div className="space-y-4">
                      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <div className="rounded-lg bg-blue-50 p-3 text-center">
                          <div className="font-bold text-2xl text-blue-600">
                            {player.overallStats.totalAttacks}
                          </div>
                          <div className="text-blue-700 text-sm">Total Attacks</div>
                        </div>
                        <div className="rounded-lg bg-green-50 p-3 text-center">
                          <div className="font-bold text-2xl text-green-600">
                            {player.overallStats.totalRespect.toFixed(1)}
                          </div>
                          <div className="text-green-700 text-sm">Total Respect</div>
                        </div>
                        <div className="rounded-lg bg-purple-50 p-3 text-center">
                          <div className="font-bold text-2xl text-purple-600">
                            {player.overallStats.averageRespect.toFixed(2)}
                          </div>
                          <div className="text-purple-700 text-sm">Avg Respect</div>
                        </div>
                        <div className="rounded-lg bg-orange-50 p-3 text-center">
                          <div className="font-bold text-2xl text-orange-600">
                            {player.overallStats.bestRespect.toFixed(2)}
                          </div>
                          <div className="text-orange-700 text-sm">Best Respect</div>
                        </div>
                      </div>

                      <div className="grid gap-4 md:grid-cols-3">
                        <div className="space-y-2">
                          <h5 className="font-medium text-gray-900 text-sm">Attack Distribution</h5>
                          <div className="space-y-1 text-sm">
                            <div className="flex justify-between">
                              <span>Chain Attacks:</span>
                              <strong>{player.overallStats.chainAttacks}</strong>
                            </div>
                            <div className="flex justify-between">
                              <span>Non-Chain:</span>
                              <strong>{player.overallStats.nonChainAttacks}</strong>
                            </div>
                            <div className="flex justify-between">
                              <span>Inside Hits:</span>
                              <strong>{player.overallStats.insideHits}</strong>
                            </div>
                            <div className="flex justify-between">
                              <span>Assists:</span>
                              <strong>{player.overallStats.assists}</strong>
                            </div>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <h5 className="font-medium text-gray-900 text-sm">Rankings</h5>
                          <div className="space-y-1 text-sm">
                            <div className="flex justify-between">
                              <span>Respect Rank:</span>
                              <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">
                                #{player.comparativeRanking.respectRank}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Attack Rank:</span>
                              <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">
                                #{player.comparativeRanking.attackCountRank}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Efficiency Rank:</span>
                              <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">
                                #{player.comparativeRanking.efficiencyRank}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Participation Rank:</span>
                              <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">
                                #{player.comparativeRanking.participationRank}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <h5 className="font-medium text-gray-900 text-sm">Battle Effectiveness</h5>
                          <div className="space-y-1 text-sm">
                            <div className="flex justify-between">
                              <span>Hit Accuracy:</span>
                              <strong>{player.battleEffectiveness.hitAccuracy.toFixed(1)}%</strong>
                            </div>
                            <div className="flex justify-between">
                              <span>Inside Target %:</span>
                              <strong>
                                {player.battleEffectiveness.targetPrioritization.insideTargetRatio.toFixed(1)}%
                              </strong>
                            </div>
                            <div className="flex justify-between">
                              <span>Peak Activity:</span>
                              <strong>{player.battleEffectiveness.timingAnalysis.peakActivityWindow}</strong>
                            </div>
                            <div className="flex justify-between">
                              <span>Consistency:</span>
                              <strong>
                                {player.battleEffectiveness.timingAnalysis.consistencyScore.toFixed(1)}/100
                              </strong>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeTab === 'trends' && (
                    <div className="space-y-4">
                      <div className="bg-white border border-gray-200 rounded-lg p-4">
                        <h5 className="font-medium text-gray-900 mb-3 text-sm">📈 Performance Trends</h5>
                        <div className="grid gap-4 md:grid-cols-2">
                          <div>
                            <h6 className="mb-3 font-medium text-gray-700 text-sm">Daily Breakdown</h6>
                            <div className="space-y-2">
                              {player.performanceTrends.dailyBreakdown.map((day) => {
                                const maxAttacks = Math.max(
                                  ...player.performanceTrends.dailyBreakdown.map((d) => d.attacks)
                                );
                                const progressWidth = maxAttacks > 0 ? (day.attacks / maxAttacks) * 100 : 0;
                                
                                return (
                                  <div key={day.day} className="space-y-1">
                                    <div className="flex justify-between text-sm">
                                      <span>Day {day.day + 1}</span>
                                      <span>
                                        {day.attacks} attacks • {day.respect.toFixed(1)} respect
                                      </span>
                                    </div>
                                    <div className="w-full bg-gray-200 rounded-full h-1">
                                      <div
                                        className="bg-blue-600 h-1 rounded-full transition-all"
                                        style={{ width: `${progressWidth}%` }}
                                      />
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                          <div>
                            <h6 className="mb-3 font-medium text-gray-700 text-sm">Top Hours</h6>
                            <div className="space-y-1">
                              {player.performanceTrends.hourlyBreakdown
                                .sort((a, b) => b.attacks - a.attacks)
                                .slice(0, 5)
                                .map((hour) => (
                                  <div key={hour.hour} className="flex justify-between text-sm">
                                    <span>Hour {hour.hour + 1}</span>
                                    <span>
                                      {hour.attacks} attacks • {hour.efficiency.toFixed(2)} avg
                                    </span>
                                  </div>
                                ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeTab === 'chains' && (
                    <div className="space-y-4">
                      <div className="bg-white border border-gray-200 rounded-lg p-4">
                        <h5 className="font-medium text-gray-900 mb-3 text-sm flex items-center gap-2">
                          🔗 Chain Contributions
                        </h5>
                        
                        {player.chainContributions.length > 0 ? (
                          <div className="space-y-3">
                            {player.chainContributions.map((chain) => (
                              <div key={chain.chainId} className="rounded-lg border border-gray-200 p-3">
                                <div className="flex items-center justify-between">
                                  <div>
                                    <h6 className="font-medium text-gray-900">Chain #{chain.chainId}</h6>
                                    <p className="text-gray-600 text-sm">
                                      {chain.attacks} attacks • {chain.respect.toFixed(1)} respect
                                    </p>
                                  </div>
                                  <div className="flex gap-2">
                                    <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs border border-gray-200">
                                      {chain.chainPosition}
                                    </span>
                                    <span className={`px-2 py-1 rounded text-xs border ${
                                      chain.performance === "Above Average"
                                        ? "bg-green-100 text-green-800 border-green-200"
                                        : chain.performance === "Below Average"
                                        ? "bg-red-100 text-red-800 border-red-200"
                                        : "bg-gray-100 text-gray-800 border-gray-200"
                                    }`}>
                                      {chain.performance}
                                    </span>
                                  </div>
                                </div>
                                <div className="mt-2 flex gap-4 text-gray-600 text-xs">
                                  <span>War Hits: {chain.warHits}</span>
                                  <span>Assists: {chain.assists}</span>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="py-8 text-center text-gray-500">
                            <span className="text-2xl mb-2 block">🔗</span>
                            <p>No chain contributions</p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {activeTab === 'effectiveness' && (
                    <div className="space-y-4">
                      <div className="grid gap-4 md:grid-cols-2">
                        <div className="bg-white border border-gray-200 rounded-lg p-4">
                          <h5 className="font-medium text-gray-900 mb-3 text-sm flex items-center gap-2">
                            🎯 Target Priority
                          </h5>
                          <div className="space-y-3">
                            <div className="space-y-2">
                              <div className="flex justify-between text-sm">
                                <span>Inside Target Focus:</span>
                                <strong>
                                  {player.battleEffectiveness.targetPrioritization.insideTargetRatio.toFixed(1)}%
                                </strong>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                  className="bg-green-600 h-2 rounded-full transition-all"
                                  style={{
                                    width: `${player.battleEffectiveness.targetPrioritization.insideTargetRatio}%`
                                  }}
                                />
                              </div>
                            </div>
                            <div className="space-y-2">
                              <div className="flex justify-between text-sm">
                                <span>War Target Focus:</span>
                                <strong>
                                  {player.battleEffectiveness.targetPrioritization.warTargetFocus.toFixed(1)}%
                                </strong>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                  className="bg-red-600 h-2 rounded-full transition-all"
                                  style={{
                                    width: `${player.battleEffectiveness.targetPrioritization.warTargetFocus}%`
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="bg-white border border-gray-200 rounded-lg p-4">
                          <h5 className="font-medium text-gray-900 mb-3 text-sm flex items-center gap-2">
                            ⏰ Timing Analysis
                          </h5>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span>Avg Time Between Attacks:</span>
                              <strong>
                                {formatDuration(player.battleEffectiveness.timingAnalysis.averageTimeBetweenAttacks)}
                              </strong>
                            </div>
                            <div className="flex justify-between">
                              <span>Peak Activity Window:</span>
                              <strong>{player.battleEffectiveness.timingAnalysis.peakActivityWindow}</strong>
                            </div>
                            <div className="flex justify-between">
                              <span>Consistency Score:</span>
                              <div className="flex items-center gap-2">
                                <strong>
                                  {player.battleEffectiveness.timingAnalysis.consistencyScore.toFixed(1)}/100
                                </strong>
                                <div className="w-16 bg-gray-200 rounded-full h-2">
                                  <div
                                    className="bg-blue-600 h-2 rounded-full transition-all"
                                    style={{
                                      width: `${player.battleEffectiveness.timingAnalysis.consistencyScore}%`
                                    }}
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}