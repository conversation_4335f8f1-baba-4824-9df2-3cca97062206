import {
	Card,
	CardContent,
	CardDescription,
	Card<PERSON><PERSON>er,
	CardTitle,
} from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { trpc } from "@/lib/trpc-client";
import { useQuery } from "@tanstack/react-query";
import {
	AlertTriangle,
	Banknote,
	BookOpen,
	Check,
	Megaphone,
	X,
} from "lucide-react";
import { useMemo } from "react";

type ActivityItem = {
	type: "announcement" | "withdrawal" | "guide";
	icon: React.ReactNode;
	title: string;
	description: string;
	timestamp: Date;
	status?: string;
};

export function ActivityFeed() {
	const announcementsQuery = useQuery(trpc.announcements.getAll.queryOptions());
	const guidesQuery = useQuery(trpc.guides.getAll.queryOptions());
	const withdrawalsQuery = useQuery(
		trpc.banking.getAllWithdrawals.queryOptions({}),
	);

	const isLoading =
		announcementsQuery.isLoading ||
		guidesQuery.isLoading ||
		withdrawalsQuery.isLoading;

	const activities = useMemo(() => {
		const combined: ActivityItem[] = [];

		// Debug logging
		console.log("ActivityFeed Debug:", {
			announcements: announcementsQuery.data?.length || 0,
			guides: guidesQuery.data?.length || 0,
			withdrawals: withdrawalsQuery.data?.requests?.length || 0,
			announcementsError: announcementsQuery.error,
			guidesError: guidesQuery.error,
			withdrawalsError: withdrawalsQuery.error,
		});

		// Process announcements
		if (announcementsQuery.data) {
			for (const a of announcementsQuery.data) {
				// Handle both ISO strings and unix timestamps
				const timestamp = Number.isNaN(Number(a.announcement.createdAt))
					? new Date(a.announcement.createdAt)
					: new Date(Number(a.announcement.createdAt));

				console.log("Processing announcement:", {
					title: a.announcement.title,
					createdAt: a.announcement.createdAt,
					timestamp: timestamp.toISOString(),
					isRecent: timestamp > new Date(Date.now() - 24 * 60 * 60 * 1000),
				});

				combined.push({
					type: "announcement",
					icon: <Megaphone className="h-4 w-4" />,
					title: "New Announcement",
					description: `"${a.announcement.title}" was published.`,
					timestamp,
				});
			}
		}

		// Process guides
		if (guidesQuery.data) {
			for (const g of guidesQuery.data) {
				// Handle both ISO strings and unix timestamps
				const timestamp = Number.isNaN(Number(g.guide.createdAt))
					? new Date(g.guide.createdAt)
					: new Date(Number(g.guide.createdAt));

				console.log("Processing guide:", {
					title: g.guide.title,
					createdAt: g.guide.createdAt,
					timestamp: timestamp.toISOString(),
					isRecent: timestamp > new Date(Date.now() - 24 * 60 * 60 * 1000),
				});

				combined.push({
					type: "guide",
					icon: <BookOpen className="h-4 w-4" />,
					title: "New Guide",
					description: `"${g.guide.title}" was published.`,
					timestamp,
				});
			}
		}

		// Process withdrawals
		if (withdrawalsQuery.data?.requests) {
			for (const w of withdrawalsQuery.data.requests) {
				let icon = <Banknote className="h-4 w-4" />;
				let title = "Withdrawal Request";
				let description = `${w.requestedByName || "A user"} requested $${w.amount.toLocaleString()}.`;

				switch (w.status) {
					case "ACCEPTED":
						icon = <Check className="h-4 w-4 text-green-500" />;
						title = "Withdrawal Approved";
						description = `Request for $${w.amount.toLocaleString()} from ${w.requestedByName} was approved.`;
						break;
					case "DECLINED":
						icon = <X className="h-4 w-4 text-red-500" />;
						title = "Withdrawal Declined";
						description = `Request for $${w.amount.toLocaleString()} from ${w.requestedByName} was declined.`;
						break;
					case "PENDING":
						icon = <AlertTriangle className="h-4 w-4 text-yellow-500" />;
						title = "Withdrawal Pending";
						break;
				}

				console.log("Processing withdrawal:", {
					amount: w.amount,
					status: w.status,
					createdAt: w.createdAt,
					timestamp: new Date(w.createdAt).toISOString(),
					isRecent:
						new Date(w.createdAt) > new Date(Date.now() - 24 * 60 * 60 * 1000),
				});

				combined.push({
					type: "withdrawal",
					icon,
					title,
					description,
					timestamp: new Date(w.createdAt),
					status: w.status,
				});
			}
		}

		// Filter to last 7 days instead of 24 hours for better debugging
		const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
		const filtered = combined
			.filter((activity) => activity.timestamp > sevenDaysAgo)
			.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

		console.log("ActivityFeed Final Result:", {
			totalCombined: combined.length,
			filteredCount: filtered.length,
			sevenDaysAgo: sevenDaysAgo.toISOString(),
			activities: filtered.map((a) => ({
				type: a.type,
				title: a.title,
				timestamp: a.timestamp.toISOString(),
			})),
		});

		return filtered;
	}, [
		announcementsQuery.data,
		guidesQuery.data,
		withdrawalsQuery.data,
		announcementsQuery.error,
		guidesQuery.error,
		withdrawalsQuery.error,
	]);

	const timeAgo = (date: Date) => {
		const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000);
		let interval = seconds / 31536000;
		if (interval > 1) return `${Math.floor(interval)}y ago`;
		interval = seconds / 2592000;
		if (interval > 1) return `${Math.floor(interval)}mo ago`;
		interval = seconds / 86400;
		if (interval > 1) return `${Math.floor(interval)}d ago`;
		interval = seconds / 3600;
		if (interval > 1) return `${Math.floor(interval)}h ago`;
		interval = seconds / 60;
		if (interval > 1) return `${Math.floor(interval)}m ago`;
		return `${Math.floor(seconds)}s ago`;
	};

	return (
		<Card className="flex flex-col">
			<CardHeader data-card-header>
				<CardTitle>Recent Activity</CardTitle>
				<CardDescription>Latest actions within the faction.</CardDescription>
			</CardHeader>
			<CardContent data-card-content className="flex flex-1 flex-col">
				{isLoading ? (
					<div className="space-y-4">
						{["1", "2", "3", "4", "5"].map((k) => (
							<Skeleton key={`sk-${k}`} className="h-12 w-full" />
						))}
					</div>
				) : activities.length > 0 ? (
					<ScrollArea className="max-h-96 flex-1">
						<ul className="space-y-4 pr-4">
							{activities.map((activity, index) => (
								<li
									key={`${activity.type}-${activity.timestamp.getTime()}-${index}`}
									className="flex items-start gap-3"
								>
									<div className="mt-1 flex h-8 w-8 items-center justify-center rounded-full bg-secondary">
										{activity.icon}
									</div>
									<div className="flex-1">
										<p className="font-medium text-sm">{activity.title}</p>
										<p className="text-muted-foreground text-sm">
											{activity.description}
										</p>
									</div>
									<div className="text-right text-muted-foreground text-xs">
										{timeAgo(activity.timestamp)}
									</div>
								</li>
							))}
						</ul>
					</ScrollArea>
				) : (
					<div className="flex flex-1 items-center justify-center">
						<p className="text-muted-foreground text-sm">
							No recent activity to display.
						</p>
					</div>
				)}
			</CardContent>
		</Card>
	);
}
