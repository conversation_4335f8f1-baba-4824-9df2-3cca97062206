import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	Card<PERSON>ooter,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Calendar, Eye, User } from "lucide-react";
import type { WithdrawalCardProps } from "./types";
import { formatCurrency, formatDate, getStatusInfo } from "./utils";

export function WithdrawalCard({
	withdrawal,
	onViewDetails,
	onApprove,
	onDecline,
	showActions = false,
	isProcessing = false,
}: WithdrawalCardProps) {
	const statusInfo = getStatusInfo(withdrawal.withdrawal.status);

	return (
		<Card className="transition-shadow hover:shadow-md">
			<CardHeader className="pb-3">
				<div className="flex items-center justify-between">
					<CardTitle className="text-lg">
						{formatCurrency(withdrawal.withdrawal.amount)}
					</CardTitle>
					<Badge className={statusInfo.color}>
						{statusInfo.emoji} {statusInfo.label}
					</Badge>
				</div>
			</CardHeader>
			<CardContent className="space-y-2">
				<div className="flex items-center gap-2 text-muted-foreground text-sm">
					<User className="h-4 w-4" />
					{withdrawal.user.tornUsername}
				</div>
				<div className="flex items-center gap-2 text-muted-foreground text-sm">
					<Calendar className="h-4 w-4" />
					{formatDate(withdrawal.withdrawal.createdAt)}
				</div>
			</CardContent>
			<CardFooter className="flex flex-col gap-2 sm:flex-row">
				<Button
					variant="outline"
					size="sm"
					onClick={() => onViewDetails(withdrawal)}
					className="w-full sm:w-auto"
				>
					<Eye className="mr-1 h-4 w-4" />
					View Details
				</Button>
				{showActions && withdrawal.withdrawal.status === "PENDING" && (
					<div className="flex w-full gap-2 sm:w-auto">
						<Button
							size="sm"
							onClick={() => onApprove?.(withdrawal.withdrawal.id)}
							disabled={isProcessing}
							className="flex-1 sm:flex-none"
						>
							Approve
						</Button>
						<Button
							variant="destructive"
							size="sm"
							onClick={() => onDecline?.(withdrawal.withdrawal.id)}
							disabled={isProcessing}
							className="flex-1 sm:flex-none"
						>
							Decline
						</Button>
					</div>
				)}
			</CardFooter>
		</Card>
	);
}
