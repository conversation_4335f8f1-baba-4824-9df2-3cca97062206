import { SlashCommand, BaseSlashCreator } from "slash-create";

export abstract class <PERSON><PERSON><PERSON>mand extends Slash<PERSON>ommand {
  protected convexUrl: string;

  constructor(creator: BaseSlashCreator, convexUrl: string, options: any) {
    super(creator, options);
    this.convexUrl = convexUrl;
  }

  /**
   * Handle bot command through Convex HTTP API
   */
  protected async handleBotCommand(discordId: string, command: string, args: string[] = []) {
    try {
      // Use fetch to call Convex HTTP API directly
      const response = await fetch(`${this.convexUrl}/api/action`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          path: 'discord:handleBotCommand',
          args: {
            discordId,
            command,
            args,
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error("Error handling bot command:", error);
      return {
        success: false,
        message: "❌ An error occurred while processing your command.",
      };
    }
  }

  /**
   * Create error response
   */
  protected createErrorResponse(message: string) {
    return {
      content: message,
      ephemeral: true,
    };
  }

  /**
   * Create success response
   */
  protected createSuccessResponse(message: string, ephemeral: boolean = false) {
    return {
      content: message,
      ephemeral,
    };
  }

  /**
   * Create embed response
   */
  protected createEmbedResponse(embed: any, ephemeral: boolean = false) {
    return {
      embeds: [embed],
      ephemeral,
    };
  }
}
