import { useUser } from '@clerk/clerk-react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { useEffect, useCallback, useRef } from 'react';

export function useSession() {
  const { user, isSignedIn, isLoaded } = useUser();
  
  // Get the current user from Convex
  const convexUser = useQuery(
    api.users.getCurrentUser,
    isSignedIn ? {} : "skip"
  );

  // Mutation to create user if they don't exist
  const createUser = useMutation(api.users.createUserWithDefaults);
  
  // Track if we've already attempted user creation to prevent infinite loops
  const userCreationAttempted = useRef(false);

  // Stable callback for user creation
  const handleUserCreation = useCallback(async () => {
    if (userCreationAttempted.current) {
      return;
    }
    
    userCreationAttempted.current = true;
    
    try {
      await createUser();
    } catch (error) {
      console.error('Failed to create user:', error);
      // Reset flag on error so we can retry
      userCreationAttempted.current = false;
    }
  }, [createUser]);

  // Auto-create user if they're signed in but don't exist in Convex
  useEffect(() => {
    if (isSignedIn && isLoaded && convexUser === null && !userCreationAttempted.current) {
      handleUserCreation();
    }
  }, [isSignedIn, isLoaded, convexUser, handleUserCreation]);

  // Reset creation flag when user successfully loads
  useEffect(() => {
    if (convexUser) {
      userCreationAttempted.current = false;
    }
  }, [convexUser]);

  const isLoadingSession = !isLoaded || (isSignedIn && convexUser === undefined);

  return {
    user: user as any,
    isSignedIn,
    isLoaded,
    convexUser,
    isLoadingSession,
  };
} 