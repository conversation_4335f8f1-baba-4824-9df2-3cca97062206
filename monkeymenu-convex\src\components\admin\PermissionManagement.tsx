import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { usePermissions } from '../../hooks/usePermissions';
import { HasPermission } from '../auth/HasPermission';
import { HasMinimumRole } from '../auth/HasMinimumRole';

interface RoleHierarchy {
  name: string;
  level: number;
  description: string;
  permissions: string[];
}

const ROLE_HIERARCHY: RoleHierarchy[] = [
  {
    name: 'System Administrator',
    level: 10,
    description: 'Full system access with all permissions',
    permissions: ['admin.all']
  },
  {
    name: 'Leader',
    level: 9,
    description: 'Complete admin access except Discord management',
    permissions: [
      'admin.view', 'admin.users.suspend', 'admin.users.recheck', 'admin.users.delete',
      'banking.view', 'banking.request', 'banking.requests.manage',
      'guides.view', 'guides.manage',
      'announcements.view', 'announcements.manage',
      'wars.view', 'target.finder.view', 'target.finder.manage.shared_lists',
      'dashboard.view'
    ]
  },
  {
    name: 'Co-leader',
    level: 8,
    description: 'Same permissions as Leader',
    permissions: [
      'admin.view', 'admin.users.suspend', 'admin.users.recheck', 'admin.users.delete',
      'banking.view', 'banking.request', 'banking.requests.manage',
      'guides.view', 'guides.manage',
      'announcements.view', 'announcements.manage',
      'wars.view', 'target.finder.view', 'target.finder.manage.shared_lists',
      'dashboard.view'
    ]
  },
  {
    name: 'Monkey Mentor',
    level: 7,
    description: 'Senior admin role with most permissions',
    permissions: [
      'admin.view', 'admin.users.recheck',
      'banking.view', 'banking.request', 'banking.requests.manage',
      'guides.view', 'guides.manage',
      'announcements.view', 'announcements.manage',
      'wars.view', 'target.finder.view', 'target.finder.manage.shared_lists',
      'dashboard.view'
    ]
  },
  {
    name: 'Gorilla',
    level: 6,
    description: 'Banking management and basic permissions',
    permissions: [
      'banking.view', 'banking.request', 'banking.requests.manage',
      'guides.view', 'announcements.view', 'wars.view',
      'target.finder.view', 'dashboard.view'
    ]
  },
  {
    name: 'Primate Liaison',
    level: 5,
    description: 'Banking requests and basic permissions',
    permissions: [
      'banking.view', 'banking.request',
      'guides.view', 'announcements.view', 'wars.view',
      'target.finder.view', 'dashboard.view'
    ]
  },
  {
    name: 'Baboon',
    level: 4,
    description: 'Banking requests and basic permissions',
    permissions: [
      'banking.view', 'banking.request',
      'guides.view', 'announcements.view', 'wars.view',
      'target.finder.view', 'dashboard.view'
    ]
  },
  {
    name: 'Orangutan',
    level: 3,
    description: 'Banking requests and basic permissions',
    permissions: [
      'banking.view', 'banking.request',
      'guides.view', 'announcements.view', 'wars.view',
      'target.finder.view', 'dashboard.view'
    ]
  },
  {
    name: 'Chimpanzee',
    level: 2,
    description: 'Basic permissions and banking requests',
    permissions: [
      'banking.view', 'banking.request',
      'guides.view', 'announcements.view', 'wars.view',
      'target.finder.view', 'dashboard.view'
    ]
  },
  {
    name: 'Recruit',
    level: 1,
    description: 'Minimal view-only permissions',
    permissions: [
      'guides.view', 'announcements.view', 'wars.view',
      'target.finder.view', 'dashboard.view'
    ]
  }
];

export function PermissionManagement() {
  const [selectedUser, setSelectedUser] = useState<string>('');
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [suspensionReason, setSuspensionReason] = useState('');
  const [showSuspendDialog, setShowSuspendDialog] = useState(false);
  const [userToSuspend, setUserToSuspend] = useState<string>('');

  const users = useQuery(api.admin.getAllUsers);
  const permissions = usePermissions();
  
  const suspendUser = useMutation(api.users.suspendUser);
  const restoreUser = useMutation(api.users.restoreUser);

  const handleSuspendUser = async (userId: string) => {
    setUserToSuspend(userId);
    setShowSuspendDialog(true);
  };

  const confirmSuspension = async () => {
    if (!userToSuspend || !suspensionReason.trim()) return;

    try {
      await suspendUser({
        userId: userToSuspend as any,
        reason: suspensionReason.trim(),
        type: 'manual'
      });
      
      setShowSuspendDialog(false);
      setUserToSuspend('');
      setSuspensionReason('');
    } catch (error) {
      console.error('Failed to suspend user:', error);
      alert('Failed to suspend user. Please try again.');
    }
  };

  const handleRestoreUser = async (userId: string) => {
    if (!confirm('Are you sure you want to restore this user?')) return;

    try {
      await restoreUser({ userId: userId as any });
    } catch (error) {
      console.error('Failed to restore user:', error);
      alert('Failed to restore user. Please try again.');
    }
  };

  if (!permissions.isAdmin()) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Restricted</h2>
          <p className="text-gray-600">You don't have permission to manage permissions.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Role Hierarchy Display */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Role Hierarchy</h2>
        <div className="bg-white rounded-lg shadow border border-gray-200">
          <div className="p-6">
            <div className="space-y-4">
              {ROLE_HIERARCHY.map((role, index) => (
                <div
                  key={role.name}
                  className={`p-4 rounded-lg border-l-4 ${
                    role.level >= 9 ? 'border-red-500 bg-red-50' :
                    role.level >= 7 ? 'border-orange-500 bg-orange-50' :
                    role.level >= 5 ? 'border-yellow-500 bg-yellow-50' :
                    role.level >= 3 ? 'border-blue-500 bg-blue-50' :
                    'border-gray-500 bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-3">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        role.level >= 9 ? 'bg-red-100 text-red-800' :
                        role.level >= 7 ? 'bg-orange-100 text-orange-800' :
                        role.level >= 5 ? 'bg-yellow-100 text-yellow-800' :
                        role.level >= 3 ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        Level {role.level}
                      </span>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {role.name}
                      </h3>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-3">{role.description}</p>
                  <div className="flex flex-wrap gap-1">
                    {role.permissions.map((permission) => (
                      <span
                        key={permission}
                        className="px-2 py-1 bg-white border border-gray-200 rounded text-xs font-mono text-gray-700"
                      >
                        {permission}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* User Management Section */}
      <HasPermission permission="admin.users.suspend">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">User Suspension Management</h2>
          <div className="bg-white rounded-lg shadow border border-gray-200">
            <div className="p-6">
              {users === undefined ? (
                <div className="animate-pulse space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="h-16 bg-gray-200 rounded"></div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {users.map((user) => (
                    <div
                      key={user._id}
                      className={`p-4 rounded-lg border ${
                        user.isSuspended 
                          ? 'border-red-200 bg-red-50' 
                          : 'border-gray-200 bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div>
                            <h3 className="font-semibold text-gray-900">
                              {user.username}
                            </h3>
                            <div className="flex items-center gap-3 text-sm text-gray-600">
                              <span>Role: {user.role || 'No role assigned'}</span>
                              <span>Level: {user.roleLevel || 'N/A'}</span>
                              <span>Faction: {user.factionName || 'No faction'}</span>
                            </div>
                          </div>
                          {user.isSuspended && (
                            <div className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">
                              Suspended
                            </div>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-2">
                          {user.isSuspended ? (
                            <button
                              onClick={() => handleRestoreUser(user._id)}
                              className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors text-sm"
                              disabled={permissions.canManageUser && !permissions.canManageUser(user)}
                            >
                              Restore
                            </button>
                          ) : (
                            <button
                              onClick={() => handleSuspendUser(user._id)}
                              className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors text-sm"
                              disabled={permissions.canManageUser && !permissions.canManageUser(user)}
                            >
                              Suspend
                            </button>
                          )}
                        </div>
                      </div>
                      
                      {user.isSuspended && user.suspensionReason && (
                        <div className="mt-3 p-3 bg-white border border-red-200 rounded text-sm">
                          <strong>Reason:</strong> {user.suspensionReason}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </HasPermission>

      {/* Suspension Dialog */}
      {showSuspendDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Suspend User
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Suspension Reason *
                  </label>
                  <textarea
                    value={suspensionReason}
                    onChange={(e) => setSuspensionReason(e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="Enter the reason for suspension..."
                    required
                  />
                </div>
              </div>
              
              <div className="flex justify-end gap-3 mt-6">
                <button
                  onClick={() => {
                    setShowSuspendDialog(false);
                    setUserToSuspend('');
                    setSuspensionReason('');
                  }}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmSuspension}
                  disabled={!suspensionReason.trim()}
                  className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Suspend User
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}