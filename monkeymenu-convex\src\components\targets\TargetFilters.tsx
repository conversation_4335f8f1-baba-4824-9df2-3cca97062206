import React from 'react';

type StatusFilter = "all" | "okay" | "hospitalized" | "error";
type ViewMode = "grid" | "list";

interface TargetFiltersProps {
  selectedList: string;
  setSelectedList: (listName: string) => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  statusFilter: StatusFilter;
  setStatusFilter: (filter: StatusFilter) => void;
  viewMode: ViewMode;
  setViewMode: (mode: ViewMode) => void;
  lists: any[];
  listsLoading: boolean;
  warInfo: any;
  enemyFactionId: string | null;
  cooldownData: any;
  cooldownRemaining?: number;
  isOnCooldown?: boolean;
  formatCooldownTime?: () => string;
  onRefresh: () => void;
  isRefreshing: boolean;
  targetStats: {
    all: number;
    okay: number;
    hospitalized: number;
    error: number;
  };
}

export function TargetFilters({
  selectedList,
  setSelectedList,
  searchTerm,
  setSearchTerm,
  statusFilter,
  setStatusFilter,
  viewMode,
  setViewMode,
  lists,
  listsLoading,
  warInfo,
  enemyFactionId,
  cooldownData,
  cooldownRemaining,
  isOnCooldown,
  formatCooldownTime,
  onRefresh,
  isRefreshing,
  targetStats,
}: TargetFiltersProps) {
  // Create full list including enemy faction list if at war
  const allLists = React.useMemo(() => {
    const regularLists = lists || [];
    const finalLists = [...regularLists];

    // Add enemy faction list if at war
    if (warInfo?.wars?.ranked && enemyFactionId) {
      const ourFactionId = 53100;
      const enemyFaction = warInfo.wars.ranked.factions.find(
        (faction: any) => faction.id !== ourFactionId
      );
      
      if (enemyFaction) {
        finalLists.push({
          id: `enemy-faction-${enemyFactionId}`,
          name: `Enemy Faction: ${enemyFaction.name}`,
          isExternal: true,
          isShared: false,
        });
      }
    }

    return finalLists;
  }, [lists, warInfo, enemyFactionId]);

  // Use real-time cooldown if available, otherwise fallback to static data
  const effectiveCooldownRemaining = cooldownRemaining ?? (cooldownData?.remaining || 0);
  const effectiveIsOnCooldown = isOnCooldown ?? (effectiveCooldownRemaining > 0);
  const effectiveFormatCooldownTime = formatCooldownTime ?? (() => `${effectiveCooldownRemaining}s`);

  return (
    <div className="space-y-4">
      {/* List Selection and Controls */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
          {/* List Selection */}
          <div className="flex-1 max-w-md">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Target List
            </label>
            <select
              value={selectedList}
              onChange={(e) => setSelectedList(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={listsLoading}
            >
              <option value="">Select a target list...</option>
              {allLists.map((list) => (
                <option key={list.id} value={list.name}>
                  {list.isExternal ? "🎯 " : "📋 "}{list.name}
                  {list.isShared ? " (Shared)" : ""}
                </option>
              ))}
            </select>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700">View:</span>
            <button
              onClick={() => setViewMode("grid")}
              className={`px-3 py-1 text-sm rounded ${
                viewMode === "grid"
                  ? "bg-blue-600 text-white"
                  : "bg-gray-200 text-gray-700 hover:bg-gray-300"
              }`}
            >
              Grid
            </button>
            <button
              onClick={() => setViewMode("list")}
              className={`px-3 py-1 text-sm rounded ${
                viewMode === "list"
                  ? "bg-blue-600 text-white"
                  : "bg-gray-200 text-gray-700 hover:bg-gray-300"
              }`}
            >
              List
            </button>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      {selectedList && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex flex-col lg:flex-row lg:items-center gap-4">
            {/* Search */}
            <div className="flex-1">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by name or Torn ID..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Status Filter Buttons */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700">Status:</span>
              {[
                { key: "all", label: "All", count: targetStats.all },
                { key: "okay", label: "Okay", count: targetStats.okay },
                { key: "hospitalized", label: "Hospital", count: targetStats.hospitalized },
                { key: "error", label: "Errors", count: targetStats.error },
              ].map(({ key, label, count }) => (
                <button
                  key={key}
                  onClick={() => setStatusFilter(key as StatusFilter)}
                  className={`px-3 py-1 text-sm rounded-full transition-colors ${
                    statusFilter === key
                      ? key === "okay"
                        ? "bg-green-600 text-white"
                        : key === "hospitalized"
                        ? "bg-orange-600 text-white"
                        : key === "error"
                        ? "bg-red-600 text-white"
                        : "bg-blue-600 text-white"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                  }`}
                >
                  {label} ({count})
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Real-time Cooldown Warning */}
      {effectiveIsOnCooldown && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="text-yellow-600 mr-3">⏱️</div>
              <div>
                <p className="text-sm text-yellow-800">
                  <strong>Cooldown active:</strong> Please wait before refreshing target data.
                </p>
                <p className="text-xs text-yellow-700 mt-1">
                  This prevents API spam and ensures fresh combat data for all users.
                </p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-lg font-mono font-bold text-yellow-800">
                {effectiveFormatCooldownTime()}
              </div>
              <div className="text-xs text-yellow-600">remaining</div>
            </div>
          </div>
          
          {/* Progress bar for cooldown */}
          <div className="mt-3">
            <div className="w-full bg-yellow-200 rounded-full h-2">
              <div 
                className="bg-yellow-600 h-2 rounded-full transition-all duration-1000"
                style={{ 
                  width: `${cooldownData?.cooldownUntil ? 
                    Math.max(0, ((cooldownData.cooldownUntil - Date.now()) / (cooldownData.cooldownDuration || 30000)) * 100) :
                    0
                  }%` 
                }}
              />
            </div>
          </div>
        </div>
      )}

      {/* War Status */}
      {warInfo?.wars?.ranked && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="text-red-600 mr-3">⚔️</div>
              <div>
                <p className="text-sm font-medium text-red-800">
                  Active War: {warInfo.wars.ranked.factions.find((f: any) => f.id !== 53100)?.name}
                </p>
                <p className="text-xs text-red-700">
                  Score: {warInfo.wars.ranked.factions.find((f: any) => f.id === 53100)?.score || 0} - {warInfo.wars.ranked.factions.find((f: any) => f.id !== 53100)?.score || 0}
                </p>
              </div>
            </div>
            <button
              onClick={onRefresh}
              disabled={isRefreshing}
              className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 disabled:opacity-50"
            >
              {isRefreshing ? "Loading..." : "Refresh War Data"}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}