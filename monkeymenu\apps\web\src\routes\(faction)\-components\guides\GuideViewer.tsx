import {
	Dialog,
	DialogContent,
	DialogDescription,
	Di<PERSON><PERSON>eader,
	DialogTitle,
} from "@/components/ui/dialog";
import { BookOpen, Calendar, Clock, User } from "lucide-react";
import ReactMarkdown from "react-markdown";
import rehypeHighlight from "rehype-highlight";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";

interface GuideData {
	guide: {
		id: number;
		title: string;
		content: string;
		category: string;
		createdAt: string;
		updatedAt: string;
	};
	author?: {
		name: string;
	};
}

interface GuideViewerProps {
	guide: GuideData | null;
	onClose: () => void;
}

export function GuideViewer({ guide, onClose }: GuideViewerProps) {
	if (!guide) return null;

	return (
		<Dialog open={!!guide} onOpenChange={onClose}>
			<DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<BookOpen className="h-5 w-5" />
						{guide.guide.title}
					</DialogTitle>
					<DialogDescription>
						Read the full guide content below.
					</DialogDescription>
					<div className="flex items-center gap-4 text-muted-foreground text-sm">
						<div className="flex items-center gap-1">
							<User className="h-4 w-4" />
							{guide.author?.name || "Unknown"}
						</div>
						<div className="flex items-center gap-1">
							<Calendar className="h-4 w-4" />
							{new Date(guide.guide.createdAt).toLocaleDateString()}
						</div>
						{guide.guide.updatedAt !== guide.guide.createdAt && (
							<div className="flex items-center gap-1">
								<Clock className="h-4 w-4" />
								Updated {new Date(guide.guide.updatedAt).toLocaleDateString()}
							</div>
						)}
					</div>
				</DialogHeader>
				<div className="prose prose-sm dark:prose-invert max-w-none">
					<ReactMarkdown
						remarkPlugins={[remarkGfm]}
						rehypePlugins={[rehypeHighlight, rehypeRaw]}
					>
						{guide.guide.content}
					</ReactMarkdown>
				</div>
			</DialogContent>
		</Dialog>
	);
}
