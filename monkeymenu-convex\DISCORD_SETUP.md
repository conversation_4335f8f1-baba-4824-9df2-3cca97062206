# Discord Bot Setup Guide

This guide explains how to set up and run the MonkeyMenu Discord bot with Convex integration.

## Prerequisites

1. Node.js 18+ installed
2. A Discord application and bot token
3. Convex deployment running
4. MonkeyMenu web app configured

## Step 1: Create Discord Application

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Click "New Application" and give it a name (e.g., "MonkeyMenu Bot")
3. Go to the "Bot" section
4. Click "Add Bot" 
5. Copy the bot token (you'll need this for `.env`)

## Step 2: Bot Permissions

In the Discord Developer Portal, go to "OAuth2" > "URL Generator":

**Scopes:**
- `bot`
- `applications.commands` (for future slash commands)

**Bot Permissions:**
- Send Messages
- Embed Links  
- Read Message History
- Use Slash Commands
- View Channels

Copy the generated URL and use it to invite the bot to your Discord server.

## Step 3: Environment Configuration

Copy `.env.example` to `.env` and configure:

```bash
# Required
DISCORD_BOT_TOKEN=your_bot_token_here
CONVEX_URL=your_convex_deployment_url
DISCORD_COMMAND_PREFIX=!

# Optional - for channel-specific notifications
DISCORD_ANNOUNCEMENTS_CHANNEL_ID=*********
DISCORD_NOTIFICATIONS_CHANNEL_ID=*********  
DISCORD_ADMIN_CHANNEL_ID=*********
```

## Step 4: Install Dependencies

```bash
npm install discord.js tsx
```

## Step 5: Run the Bot

Development (with auto-restart):
```bash
npm run discord:dev
```

Production:
```bash
npm run discord:bot
```

## Step 6: Link User Accounts

Users need to link their Discord accounts to use bot commands:

1. Users log into the MonkeyMenu web app
2. Go to Profile/Settings page
3. Use the "Link Discord" component
4. Enter their Discord ID, username, and discriminator

**Getting Discord ID:**
1. Enable Developer Mode in Discord settings
2. Right-click on username in Discord
3. Select "Copy ID"

## Available Bot Commands

### 💰 Banking Commands
- `!balance` - Check account balances
- `!withdraw <amount>` - Request a withdrawal

### 🎯 Target Commands  
- `!targets` - Show available targets
- `!targets level 1-50` - Filter by level range
- `!targets status active` - Filter by status
- `!targets level 20-40 status active` - Multiple filters

### ⚔️ War Commands
- `!war` or `!war active` - Show active wars
- `!war stats` - Show detailed war statistics

### 🔗 Utility Commands
- `!help` - Show help message
- `!link` - Check account linking status

## Admin Features

Admins can access the Discord Management dashboard in the web app to:

- View bot statistics (users, linkage rate)
- Send bulk notifications to users
- Monitor bot command usage
- Manage bot permissions

## Notifications

The bot can send automatic notifications for:

- **Banking:** Withdrawal requests, approvals, completions
- **Announcements:** New faction announcements
- **Wars:** War start/end notifications  
- **Targets:** Target update notifications

Configure notification channels in your `.env` file.

## Troubleshooting

### Bot Not Responding
1. Check bot token is correct
2. Verify bot has message permissions in the channel
3. Check Convex URL is accessible
4. Look at console logs for errors

### Commands Not Working
1. Ensure user has linked their Discord account
2. Check user has required permissions in MonkeyMenu
3. Verify command syntax

### Permission Errors
1. Make sure bot has required Discord permissions
2. Check user permissions in MonkeyMenu database
3. Verify Convex functions are deployed

## Deployment

For production deployment:

1. Set up environment variables on your server
2. Use a process manager like PM2:
   ```bash
   pm2 start "npm run discord:bot" --name monkeymenu-bot
   ```
3. Set up logging and monitoring
4. Configure auto-restart on crashes

## Development

### Adding New Commands

1. Add command handler in `convex/discord/commands.ts`
2. Register command in `convex/discord.ts` `handleBotCommand` function
3. Test with the bot

### Adding Notifications

1. Create notification function in `convex/discord/notifications.ts`
2. Call from relevant mutation/action
3. Configure channel IDs in environment

## Security Notes

- Keep bot token secure and never commit to git
- Use environment variables for all sensitive data
- Validate all user inputs in bot commands
- Implement rate limiting for bot commands
- Regular security updates for dependencies