# Wrangler configuration for Discord Worker
name = "monkeymenu-discord-bot"
main = "index.ts"
compatibility_date = "2024-01-01"

# Worker configuration
[build]
command = "npx tsc"

# Environment variables
[vars]
# These will be set via wrangler secrets or environment
# DISCORD_APPLICATION_ID = ""
# DISCORD_PUBLIC_KEY = ""
# DISCORD_BOT_TOKEN = ""
# CONVEX_URL = ""

# Development environment
[env.development]
name = "monkeymenu-discord-bot-dev"

# Staging environment  
[env.staging]
name = "monkeymenu-discord-bot-staging"

# Production environment
[env.production]
name = "monkeymenu-discord-bot-prod"

# Routes for the Discord Worker
[[routes]]
pattern = "discord-bot.monkeymenu.com/*"
zone_name = "monkeymenu.com"

# Development routes
[env.development.routes]
pattern = "discord-bot-dev.monkeymenu.com/*"
zone_name = "monkeymenu.com"

# Staging routes
[env.staging.routes]
pattern = "discord-bot-staging.monkeymenu.com/*"
zone_name = "monkeymenu.com"
