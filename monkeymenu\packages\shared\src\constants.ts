// Application-wide constants

// App Configuration
export const APP_CONFIG = {
	NAME: "MonkeyMenu",
	DESCRIPTION: "Faction management and guides platform",
	VERSION: "1.0.0",
} as const;

// External URLs
export const EXTERNAL_URLS = {
	TORN_API_SETTINGS: "https://www.torn.com/preferences.php#tab=api",
	DISCORD_INVITE: "https://discord.gg/S7bMtEXuyp",
} as const;

// Cache Configuration (in milliseconds)
export const CACHE_CONFIG = {
	PERMISSIONS_CACHE_TIME: 300000, // 5 minutes
	ROLES_CACHE_TIME: 600000, // 10 minutes
	USER_ROLES_CACHE_TIME: 300000, // 5 minutes
} as const;

// Toast Messages
export const TOAST_MESSAGES = {
	API_KEY_INVALID: "Invalid API key. Please check your API key and try again.",
	TORN_API_KEY_UPDATE_FAILED:
		"Failed to update Torn API key. Please try again.",
	VERIFICATION_SUCCESS: "API key verified successfully!",
	PERMISSION_DENIED: "You don't have permission to perform this action.",
	NETWORK_ERROR: "Network error. Please check your connection and try again.",
	API_KEY_SUSPENDED:
		"Your access has been temporarily suspended due to API key issues.",
	API_KEY_PAUSED:
		"Your API key has been paused. Please check your Torn settings.",
	ACCESS_RESTORED: "Your access has been restored successfully.",
	TORN_ACCOUNT_ALREADY_LINKED:
		"This Torn account is already linked to another MonkeyMenu user. Each Torn account can only be linked to one account.",
} as const;

// Validation Constants
export const VALIDATION_LIMITS = {
	USER_NAME_MIN: 1,
	USER_NAME_MAX: 50,
	GUIDE_TITLE_MIN: 3,
	GUIDE_TITLE_MAX: 255,
	GUIDE_CONTENT_MIN: 1,
	ANNOUNCEMENT_TITLE_MIN: 3,
	ANNOUNCEMENT_TITLE_MAX: 255,
	ANNOUNCEMENT_CONTENT_MIN: 1,

	TORN_API_KEY_LENGTH: 16,
	OTP_MIN_LENGTH: 6,
} as const;

// Image Configuration
export const IMAGE_CONFIG = {
	MAX_SIZE: 512, // 512x512 pixels
	ALLOWED_TYPES: [
		"image/jpeg",
		"image/png",
		"image/webp",
		"image/gif",
	] as const,
	MAX_FILE_SIZE: 1024 * 1024 * 2, // 2MB
} as const;

// Pagination
export const PAGINATION = {
	DEFAULT_PAGE_SIZE: 20,
	MAX_PAGE_SIZE: 100,
} as const;

// Cache TTL (in seconds)
export const CACHE_TTL = {
	USER_PERMISSIONS: 300, // 5 minutes
	TORN_API_RESPONSE: 600, // 10 minutes
	STATIC_DATA: 3600, // 1 hour
} as const;

// UI Constants
export const UI_CONFIG = {
	ANIMATION_DURATION: 200,
	DEBOUNCE_DELAY: 300,
	TOAST_DURATION: 5000,
} as const;

// Error Messages
export const ERROR_MESSAGES = {
	UNAUTHORIZED: "You are not authorized to perform this action",
	FORBIDDEN: "Access denied",
	NOT_FOUND: "Resource not found",
	VALIDATION_FAILED: "Validation failed",
	INTERNAL_ERROR: "An internal error occurred",
	RATE_LIMITED: "Too many requests, please try again later",
	INVALID_API_KEY: "Invalid API key provided",
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
	CREATED: "Successfully created",
	UPDATED: "Successfully updated",
	DELETED: "Successfully deleted",
	SAVED: "Successfully saved",
	SENT: "Successfully sent",
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
	OK: 200,
	CREATED: 201,
	NO_CONTENT: 204,
	BAD_REQUEST: 400,
	UNAUTHORIZED: 401,
	FORBIDDEN: 403,
	NOT_FOUND: 404,
	CONFLICT: 409,
	UNPROCESSABLE_ENTITY: 422,
	TOO_MANY_REQUESTS: 429,
	INTERNAL_SERVER_ERROR: 500,
} as const;

// Regular Expressions
export const REGEX_PATTERNS = {
	EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
	TORN_API_KEY: /^[A-Za-z0-9]{16}$/,
	SLUG: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
	USERNAME: /^[a-zA-Z0-9_-]{3,20}$/,
} as const;
