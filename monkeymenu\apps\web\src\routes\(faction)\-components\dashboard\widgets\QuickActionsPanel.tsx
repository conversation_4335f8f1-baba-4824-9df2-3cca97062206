import { But<PERSON> } from "@/components/ui/button";
import {
	<PERSON>,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { usePermissions } from "@/hooks/usePermissions";
import { trpc } from "@/lib/trpc-client";
import { PERMISSION_NAMES } from "@monkeymenu/shared";
import { useQuery } from "@tanstack/react-query";
import { Link } from "@tanstack/react-router";
import { Banknote, CreditCard, Crosshair, Plus, Target } from "lucide-react";
import { useMemo } from "react";
import { WithdrawalDialogTrigger } from "./WithdrawalDialog";

// War types from server
interface RankedWarFaction {
	id: number;
	name: string;
	score: number;
	chain: number;
}

interface RankedWar {
	id: number;
	start: number;
	end: number;
	target: number;
	winner: number;
	factions: RankedWarFaction[];
}

export function QuickActionsPanel() {
	const permissions = usePermissions();

	// Check permissions for quick actions
	const canViewBanking =
		permissions.data?.permissions.includes(PERMISSION_NAMES.BANKING_VIEW) ??
		false;
	const canViewTargetFinder =
		permissions.data?.permissions.includes(
			PERMISSION_NAMES.TARGET_FINDER_VIEW,
		) ?? false;
	const canManageBankingRequests =
		permissions.data?.permissions.includes(
			PERMISSION_NAMES.BANKING_MANAGE_REQUESTS,
		) ?? false;

	// Fetch war information to determine if there's an ongoing war
	const { data: warData } = useQuery({
		...trpc.wars.listRankedWars.queryOptions(),
	});

	// Determine if there's an ongoing war (same logic as WarStatus component)
	const currentWarInfo = useMemo(() => {
		if (!warData?.rankedWars || warData.rankedWars.length === 0) {
			return null;
		}

		const wars = warData.rankedWars;
		const now = Math.floor(Date.now() / 1000);

		// Only check for current wars (started but not finished, winner is null)
		const currentWar = wars.find(
			(war: RankedWar) =>
				war.start <= now && war.winner === null && (!war.end || war.end > now),
		);

		if (!currentWar) return null;

		const ourFactionId = 53100; // Replace with actual faction ID if dynamic
		const enemyFaction = currentWar.factions.find(
			(faction: RankedWarFaction) => faction.id !== ourFactionId,
		);

		return enemyFaction
			? {
					enemyName: enemyFaction.name,
					enemyId: enemyFaction.id,
				}
			: null;
	}, [warData?.rankedWars]);

	const hasOngoingWar = currentWarInfo !== null;

	const pendingRequestsQuery = useQuery({
		...trpc.banking.getAllWithdrawals.queryOptions({ status: "PENDING" }),
		enabled: canManageBankingRequests,
	});
	const pendingCount = pendingRequestsQuery.data?.requests.length ?? 0;

	return (
		<Card className="flex flex-col">
			<CardHeader data-card-header>
				<CardTitle className="flex items-center gap-2">
					<Plus className="h-5 w-5" />
					Quick Actions
				</CardTitle>
				<CardDescription>Essential shortcuts</CardDescription>
			</CardHeader>
			<CardContent data-card-content className="flex flex-1 flex-col">
				<div className="grid gap-3 overflow-y-auto">
					{/* Withdrawal Dialog - Always available if user has banking permissions */}
					{canViewBanking && (
						<WithdrawalDialogTrigger>
							<Button
								variant="default"
								size="sm"
								className="h-auto w-full justify-start p-3"
							>
								<div className="flex items-center gap-3">
									<div className="rounded-md bg-background/10 p-1.5">
										<CreditCard className="h-4 w-4" />
									</div>
									<div className="flex-1 text-left">
										<div className="font-medium text-sm">New Withdrawal</div>
										<div className="text-xs opacity-70">
											Request faction funds
										</div>
									</div>
								</div>
							</Button>
						</WithdrawalDialogTrigger>
					)}

					{/* War-specific actions - Only show when there's an ongoing war */}
					{hasOngoingWar && canViewTargetFinder && (
						<>
							<Button
								variant="secondary"
								size="sm"
								asChild
								className="h-auto justify-start p-3"
							>
								<Link to="/target-finder" className="flex items-center gap-3">
									<div className="rounded-md bg-background/10 p-1.5">
										<Crosshair className="h-4 w-4" />
									</div>
									<div className="flex-1 text-left">
										<div className="font-medium text-sm">Find Inside Hit</div>
										<div className="text-xs opacity-70">
											Hunt {currentWarInfo?.enemyName} members
										</div>
									</div>
								</Link>
							</Button>

							<Button
								variant="secondary"
								size="sm"
								asChild
								className="h-auto justify-start p-3"
							>
								<Link to="/target-finder" className="flex items-center gap-3">
									<div className="rounded-md bg-background/10 p-1.5">
										<Target className="h-4 w-4" />
									</div>
									<div className="flex-1 text-left">
										<div className="font-medium text-sm">Find Outside Hit</div>
										<div className="text-xs opacity-70">
											Target anyone online
										</div>
									</div>
								</Link>
							</Button>
						</>
					)}

					{/* Admin actions */}
					{canManageBankingRequests && pendingCount > 0 && (
						<Button
							variant="destructive"
							size="sm"
							onClick={() => {
								window.location.href = "/banking?tab=admin";
							}}
							className="h-auto animate-pulse justify-start p-3"
						>
							<div className="flex items-center gap-3">
								<div className="rounded-md bg-background/10 p-1.5">
									<Banknote className="h-4 w-4" />
								</div>
								<div className="flex-1 text-left">
									<div className="font-medium text-sm">Pending Requests</div>
									<div className="text-xs opacity-70">
										Review {pendingCount} request{pendingCount > 1 ? "s" : ""}
									</div>
								</div>
							</div>
						</Button>
					)}
				</div>

				{!canViewBanking &&
					!(hasOngoingWar && canViewTargetFinder) &&
					!(canManageBankingRequests && pendingCount > 0) && (
						<div className="flex flex-1 items-center justify-center text-muted-foreground text-sm">
							No quick actions available.
						</div>
					)}
			</CardContent>
		</Card>
	);
}
