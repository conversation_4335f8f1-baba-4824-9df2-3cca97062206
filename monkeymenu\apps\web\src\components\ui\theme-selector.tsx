import {
	THEME_STYLES,
	useTheme,
	useThemeStyle,
} from "@/lib/combined-theme-provider";
import { Moon, Palette, Sun } from "lucide-react";
import { Button } from "./button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "./dropdown-menu";

const THEME_MODES = [
	{ value: "light" as const, label: "Light", icon: Sun },
	{ value: "dark" as const, label: "Dark", icon: Moon },
] as const;

export function ThemeSelector() {
	const { theme, setTheme } = useTheme();
	const { themeStyle, setThemeStyle } = useThemeStyle();

	const formatDisplayName = (
		name: string | undefined,
		fallback = "System",
	): string => {
		if (!name || name.trim() === "") return fallback;
		return name
			.trim()
			.split("-")
			.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
			.join(" ");
	};

	return (
		<div className="flex items-center gap-2">
			{/* Theme Style Selector */}
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="outline" size="sm" className="gap-2">
						<Palette className="h-4 w-4" />
						{formatDisplayName(themeStyle)}
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					<DropdownMenuLabel>Theme Style</DropdownMenuLabel>
					<DropdownMenuSeparator />
					{THEME_STYLES.map((style) => (
						<DropdownMenuItem
							key={style.value}
							onClick={() => setThemeStyle(style.value)}
							className={themeStyle === style.value ? "bg-accent" : ""}
						>
							{style.label}
						</DropdownMenuItem>
					))}
				</DropdownMenuContent>
			</DropdownMenu>

			{/* Theme Mode Selector */}
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="outline" size="sm" className="gap-2">
						{theme === "light" && <Sun className="h-4 w-4" />}
						{theme === "dark" && <Moon className="h-4 w-4" />}
						{formatDisplayName(theme)}
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					<DropdownMenuLabel>Theme Mode</DropdownMenuLabel>
					<DropdownMenuSeparator />
					{THEME_MODES.map((mode) => (
						<DropdownMenuItem
							key={mode.value}
							onClick={() => setTheme(mode.value)}
							className={theme === mode.value ? "bg-accent" : ""}
						>
							<mode.icon className="mr-2 h-4 w-4" />
							{mode.label}
						</DropdownMenuItem>
					))}
				</DropdownMenuContent>
			</DropdownMenu>
		</div>
	);
}
