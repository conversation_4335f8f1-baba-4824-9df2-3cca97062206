import { action } from "../_generated/server";
import { v } from "convex/values";
// Handle balance command
export const handleBalanceCommand = action({
  args: {
    userId: v.id("users"),
    discordId: v.string(),
  },
  handler: async (ctx, args): Promise<{ success: boolean; message?: string; embed?: any }> => {
    try {
      // Get user's accounts
      const accounts = await ctx.runQuery("banking:getUserAccounts" as any, {
        userId: args.userId,
      });

      if (!accounts || accounts.length === 0) {
        return {
          success: true,
          message: "💰 You don't have any active accounts yet.",
        };
      }

      // Format balance information
      const balanceInfo: string = accounts
        .map((account: any) => {
          const currency = account.currency === "cash" ? "💵" : "🪙";
          return `${currency} **${account.currency.toUpperCase()}**: $${account.balance.toLocaleString()}`;
        })
        .join("\n");

      return {
        success: true,
        message: `💰 **Your Account Balances:**\n${balanceInfo}`,
        embed: {
          title: "💰 Account Balance",
          description: balanceInfo,
          color: 0x00ff00,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        message: "❌ Error retrieving balance information.",
      };
    }
  },
});

// Handle withdraw command
export const handleWithdrawCommand = action({
  args: {
    userId: v.id("users"),
    discordId: v.string(),
    amount: v.number(),
  },
  handler: async (ctx, args): Promise<{ success: boolean; message?: string; requestId?: string; embed?: any }> => {
    try {
      if (args.amount <= 0) {
        return {
          success: false,
          message: "❌ Please specify a valid amount to withdraw.",
        };
      }

      // Create withdrawal request
      const requestId = await ctx.runMutation("banking:createWithdrawalRequest" as any, {
        userId: args.userId,
        amount: args.amount,
        initiatedByDiscordId: args.discordId,
      });

      return {
        success: true,
        message: `✅ Withdrawal request created for $${args.amount.toLocaleString()}!\nRequest ID: ${requestId}\n\nYour request is pending approval.`,
        embed: {
          title: "💸 Withdrawal Request Created",
          description: `Successfully created withdrawal request for $${args.amount.toLocaleString()}`,
          color: 0x0099ff,
          fields: [
            {
              name: "Amount",
              value: `$${args.amount.toLocaleString()}`,
              inline: true,
            },
            {
              name: "Status",
              value: "Pending Approval",
              inline: true,
            },
          ],
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        message: `❌ Error creating withdrawal request: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  },
});

// Handle targets command
export const handleTargetsCommand = action({
  args: {
    userId: v.id("users"),
    filters: v.array(v.string()),
  },
  handler: async (ctx, args): Promise<{ success: boolean; message?: string; embed?: any }> => {
    try {
      // Parse filters from command arguments
      const parsedFilters: any = {};
      
      for (let i = 0; i < args.filters.length; i += 2) {
        const key = args.filters[i];
        const value = args.filters[i + 1];
        
        if (key && value) {
          switch (key.toLowerCase()) {
            case "level":
            case "lvl":
              const [min, max] = value.split("-").map(Number);
              if (!isNaN(min)) parsedFilters.levelMin = min;
              if (!isNaN(max)) parsedFilters.levelMax = max;
              break;
            case "status":
              parsedFilters.status = value.toLowerCase();
              break;
            case "faction":
              parsedFilters.faction = value;
              break;
            case "respect":
              const [minResp, maxResp] = value.split("-").map(Number);
              if (!isNaN(minResp)) parsedFilters.respectMin = minResp;
              if (!isNaN(maxResp)) parsedFilters.respectMax = maxResp;
              break;
          }
        }
      }

      // Get targets with filters
      const targets = await ctx.runQuery("targets:getTargets" as any, {
        ...parsedFilters,
        limit: 10, // Limit for Discord display
      });

      if (!targets || targets.length === 0) {
        return {
          success: true,
          message: "🎯 No targets found matching your criteria.",
        };
      }

      // Format targets for Discord
      const targetList = targets
        .slice(0, 5) // Show top 5 in message
        .map((target: any, index: number) => {
          const statusEmoji = target.status === "active" ? "🟢" : 
                             target.status === "hospitalized" ? "🏥" : 
                             target.status === "jailed" ? "🔒" : "⚫";
          
          return `${index + 1}. ${statusEmoji} **${target.username}** [${target.tornId}]\n` +
                 `   Level: ${target.level} | Respect: ${target.respect || "N/A"}`;
        })
        .join("\n\n");

      return {
        success: true,
        message: `🎯 **Top Targets** (${targets.length} found):\n\n${targetList}`,
        embed: {
          title: "🎯 Target Search Results",
          description: targetList,
          color: 0xffa500,
          footer: {
            text: `Showing ${Math.min(5, targets.length)} of ${targets.length} targets`,
          },
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        message: "❌ Error retrieving targets.",
      };
    }
  },
});

// Handle war command
export const handleWarCommand = action({
  args: {
    userId: v.id("users"),
    subcommand: v.optional(v.string()),
  },
  handler: async (ctx, args): Promise<{ success: boolean; message?: string; embed?: any }> => {
    try {
      const subcommand = args.subcommand?.toLowerCase() || "active";

      switch (subcommand) {
        case "active":
        case "current":
          const activeWars = await ctx.runQuery("wars:getActiveWars" as any, {});
          
          if (!activeWars || activeWars.length === 0) {
            return {
              success: true,
              message: "⚔️ No active wars at the moment.",
            };
          }

          const warInfo: any = activeWars
            .map((war: any) => {
              const duration = Math.floor((Date.now() - war.startTime) / (1000 * 60));
              const hours = Math.floor(duration / 60);
              const minutes = duration % 60;
              const durationText = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
              
              return `⚔️ **${war.factionName}** vs **${war.enemyFactionName}**\n` +
                     `   Score: ${war.ourScore} - ${war.enemyScore}\n` +
                     `   Duration: ${durationText}`;
            })
            .join("\n\n");

          return {
            success: true,
            message: `⚔️ **Active Wars:**\n\n${warInfo}`,
            embed: {
              title: "⚔️ Active Wars",
              description: warInfo,
              color: 0xff0000,
              timestamp: new Date().toISOString(),
            },
          };

        case "stats":
        case "statistics":
          // Get stats for the most recent active war
          if (activeWars && activeWars.length > 0) {
            const stats = await ctx.runQuery("wars:getWarStats" as any, {
              warId: activeWars[0]._id,
            });

            if (stats) {
              const statsText = `**${stats.war.factionName}** vs **${stats.war.enemyFactionName}**\n\n` +
                               `📊 **Statistics:**\n` +
                               `• Total Attacks: ${stats.totalAttacks}\n` +
                               `• Wins: ${stats.wins} (${stats.winRate}%)\n` +
                               `• Losses: ${stats.losses}\n` +
                               `• Total Respect: ${stats.totalRespect.toLocaleString()}\n` +
                               `• Avg Respect: ${stats.averageRespect}`;

              return {
                success: true,
                message: statsText,
                embed: {
                  title: "📊 War Statistics",
                  description: statsText,
                  color: 0x9932cc,
                  timestamp: new Date().toISOString(),
                },
              };
            }
          }

          return {
            success: true,
            message: "📊 No war statistics available.",
          };

        default:
          return {
            success: false,
            message: "❌ Unknown war subcommand. Use `active` or `stats`.",
          };
      }
    } catch (error) {
      return {
        success: false,
        message: "❌ Error retrieving war information.",
      };
    }
  },
});

// Handle verify command
export const handleVerifyCommand = action({
  args: {
    userId: v.id("users"),
    discordId: v.string(),
    guildId: v.optional(v.string()),
    targetDiscordId: v.optional(v.string()),
    isAdminVerification: v.optional(v.boolean()),
  },
  handler: async (ctx, args): Promise<{ success: boolean; message?: string; embed?: any }> => {
    try {
      const targetUserId = args.targetDiscordId || args.discordId;
      const isAdmin = args.isAdminVerification || false;

      // Get user information
      const user = await ctx.runQuery("users:getUserById" as any, {
        id: args.userId,
      });

      if (!user) {
        return {
          success: false,
          message: "❌ User not found.",
        };
      }

      // Check if user is in faction and get role information
      const isInFaction = Boolean(
        user.tornApiKeyVerified &&
        user.tornUserId &&
        !user.accessSuspended
      );

      // Get user's role from permissions
      const userRole = user.role || null;

      // Sync Discord roles
      if (args.guildId) {
        const botToken = process.env.DISCORD_BOT_TOKEN;
        if (!botToken) {
          return {
            success: false,
            message: "❌ Discord bot not configured properly.",
          };
        }

        const roleSync = await ctx.runAction("discord:syncDiscordRoles" as any, {
          guildId: args.guildId,
          userId: targetUserId,
          roleName: userRole,
          botToken: botToken,
          isInFaction: isInFaction,
        });

        const embed = {
          title: "✅ Verification Complete",
          description: isAdmin 
            ? `Successfully verified <@${targetUserId}>`
            : "Your account has been successfully verified!",
          color: roleSync.success ? 0x00ff00 : 0xffaa00,
          fields: [
            {
              name: "👤 User",
              value: user.name || "Unknown",
              inline: true,
            },
            {
              name: "🎭 Current Role",
              value: userRole || "No role assigned",
              inline: true,
            },
            {
              name: "🔗 Torn User ID",
              value: user.tornUserId || "Not linked",
              inline: true,
            },
            {
              name: "📊 Verification Status",
              value: user.tornApiKeyVerified ? "✅ Verified" : "❌ Not Verified",
              inline: true,
            },
            {
              name: "🎮 Discord Roles",
              value: roleSync.success ? "✅ Synced" : `⚠️ ${roleSync.message}`,
              inline: true,
            },
            {
              name: "🏛️ Faction Status",
              value: isInFaction ? "✅ Active Member" : "❌ Not in Faction",
              inline: true,
            },
          ],
          footer: {
            text: "MonkeyMenu Verification System",
          },
          timestamp: new Date().toISOString(),
        };

        return {
          success: true,
          message: roleSync.success 
            ? "✅ Verification completed successfully!" 
            : `⚠️ Verification completed with issues: ${roleSync.message}`,
          embed: embed,
        };
      } else {
        return {
          success: true,
          message: `✅ Account verified!\n👤 User: ${user.name}\n🎭 Role: ${userRole || "No role"}\n🏛️ Faction: ${isInFaction ? "Active Member" : "Not in Faction"}`,
        };
      }
    } catch (error) {
      console.error("Verify command error:", error);
      return {
        success: false,
        message: "❌ An error occurred during verification. Please try again later.",
      };
    }
  },
});

// Handle help command
export const handleHelpCommand = action({
  args: {
    userId: v.id("users"),
  },
  handler: async () => {
    const helpText = `🤖 **MonkeyMenu Bot Commands:**

**💰 Banking:**
• \`!balance\` - Check your account balances
• \`!withdraw <amount>\` - Request a withdrawal

**🎯 Targets:**
• \`!targets\` - Show available targets
• \`!targets level 1-50\` - Filter by level range
• \`!targets status active\` - Filter by status

**⚔️ Wars:**
• \`!war\` or \`!war active\` - Show active wars
• \`!war stats\` - Show war statistics  

**🔗 Account:**
• \`!link\` - Check account linking status
• \`!verify\` - Verify account and sync Discord roles
• \`!help\` - Show this help message

**Examples:**
\`!targets level 20-40 status active\`
\`!withdraw 50000\`
\`!verify\``;

    return {
      success: true,
      message: helpText,
      embed: {
        title: "🤖 MonkeyMenu Bot Help",
        description: helpText,
        color: 0x0099ff,
        timestamp: new Date().toISOString(),
      },
    };
  },
});