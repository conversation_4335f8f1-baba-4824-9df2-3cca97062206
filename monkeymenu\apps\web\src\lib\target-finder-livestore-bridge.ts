import { useStore } from "@livestore/react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useEffect, useRef } from "react";
import { events } from "../livestore/schema";
import { useLiveStoreReady } from "./livestore-ready";
import { trpc } from "./trpc-client";

// Type for chain data structure
interface ChainData {
	current: number;
	max: number;
	timeout: number;
	cooldown: number;
	end: number;
}

// Type for chain info response
interface ChainInfoResponse {
	chain?: ChainData;
}

type SyncSource = "websocket" | "manual" | "cache" | "init";

interface SyncOperation {
	id: string;
	source: SyncSource;
	timestamp: number;
}

class SyncCoordinator {
	private recentOperations = new Map<string, SyncOperation>();
	private readonly DEBOUNCE_MS = 1000; // Prevent duplicate syncs within 1 second

	shouldSync(operationId: string, source: SyncSource): boolean {
		const recent = this.recentOperations.get(operationId);
		const now = Date.now();

		if (!recent) {
			this.recentOperations.set(operationId, {
				id: operationId,
				source,
				timestamp: now,
			});
			return true;
		}

		// Allow if enough time has passed
		if (now - recent.timestamp > this.DEBOUNCE_MS) {
			this.recentOperations.set(operationId, {
				id: operationId,
				source,
				timestamp: now,
			});
			return true;
		}

		// Allow if this is a higher priority source
		const sourcePriority = { websocket: 3, manual: 2, cache: 1, init: 0 };
		if (sourcePriority[source] > sourcePriority[recent.source]) {
			this.recentOperations.set(operationId, {
				id: operationId,
				source,
				timestamp: now,
			});
			return true;
		}

		return false;
	}

	cleanup() {
		const now = Date.now();
		for (const [id, op] of this.recentOperations.entries()) {
			if (now - op.timestamp > this.DEBOUNCE_MS * 5) {
				this.recentOperations.delete(id);
			}
		}
	}
}

/**
 * Enhanced bridge hook that syncs tRPC target finder data with LiveStore
 * Maintains all existing functionality while adding real-time sync capabilities
 */
export function useTargetFinderLiveStoreSync() {
	const { store } = useStore();
	const queryClient = useQueryClient();
	const syncedTargetsRef = useRef(new Set<string>());
	const syncedListsRef = useRef(new Set<string>());
	const lastSyncedChainRef = useRef<ChainData | null>(null);
	const lastSyncedCooldownRef = useRef<number | null>(null);
	const syncCoordinatorRef = useRef(new SyncCoordinator());
	const liveStoreReady = useLiveStoreReady();

	// Sync target lists from tRPC to LiveStore
	const { data: lists } = useQuery({
		...trpc.targetFinder.getLists.queryOptions(),
	});

	// Sync chain information from tRPC to LiveStore
	const { data: chainInfo } = useQuery({
		...trpc.targetFinder.getChainInfo.queryOptions(),
		refetchInterval: 15000,
	});

	// Sync cooldown data from tRPC to LiveStore
	const { data: cooldownData } = useQuery({
		...trpc.targetFinder.getTargetFinderCooldown.queryOptions(),
	});

	// Initialize LiveStore with real data only if not already present
	useEffect(() => {
		const initializeWithRealData = async () => {
			try {
				// Initialize chain data if available
				if (chainInfo?.chain && typeof chainInfo.chain === "object") {
					const chainData = chainInfo.chain as ChainData;
					const { current, max, timeout, cooldown, end } = chainData;
					console.log(
						`[LiveStore] Initializing with real chain data: ${current}/${max}`,
					);
					store.commit(
						events.chainStatusUpdated({
							current: Number(current) || 0,
							max: Number(max) || 0,
							timeout: Number(timeout) || 0,
							cooldown: Number(cooldown) || 0,
							end: Number(end) || 0,
							updatedAt: new Date(),
						}),
					);
					lastSyncedChainRef.current = {
						current: Number(current) || 0,
						max: Number(max) || 0,
						timeout: Number(timeout) || 0,
						cooldown: Number(cooldown) || 0,
						end: Number(end) || 0,
					};
				}

				// Initialize cooldown data if available
				if (cooldownData?.remaining !== undefined) {
					console.log(
						`[LiveStore] Initializing with real cooldown data: ${cooldownData.remaining}s`,
					);
					store.commit(
						events.targetFinderCooldownUpdated({
							remaining: cooldownData.remaining,
							updatedAt: new Date(),
						}),
					);
					lastSyncedCooldownRef.current = cooldownData.remaining;
				}
			} catch (error) {
				console.error(
					"Failed to initialize target finder LiveStore with real data:",
					error,
				);
			}
		};

		// Small delay to ensure store is ready, but only init with real data
		const timeoutId = setTimeout(initializeWithRealData, 100);
		return () => clearTimeout(timeoutId);
	}, [store, chainInfo, cooldownData]);

	// Sync target lists to LiveStore when tRPC data updates
	useEffect(() => {
		if (!liveStoreReady || !lists) return;
		if (lists) {
			for (const list of lists) {
				// Only sync if we haven't already synced this list
				if (!syncedListsRef.current.has(list.id)) {
					try {
						console.log(
							`[LiveStore] Syncing target list ${list.id} from tRPC to LiveStore`,
						);

						store.commit(
							events.targetListCreated({
								id: list.id,
								name: list.name,
								userId: list.userId || undefined,
								isExternal: list.isExternal || false,
								createdAt: new Date(),
							}),
						);

						// Mark as synced to prevent duplicate attempts
						syncedListsRef.current.add(list.id);
					} catch (error) {
						console.error(
							`Failed to sync target list ${list.id} to LiveStore:`,
							error,
						);
						// Don't add to synced set if it failed, so we can retry
					}
				}
			}
		}
	}, [liveStoreReady, lists, store]);

	// Sync chain status to LiveStore when tRPC data updates
	useEffect(() => {
		if (!liveStoreReady || !chainInfo?.chain || !store) return;
		if (chainInfo?.chain && typeof chainInfo.chain === "object") {
			const chainData = chainInfo.chain as ChainData;
			const { current, max, timeout, cooldown, end } = chainData;
			const lastSynced = lastSyncedChainRef.current;

			const currentNum = Number(current) || 0;
			const maxNum = Number(max) || 0;
			const timeoutNum = Number(timeout) || 0;
			const cooldownNum = Number(cooldown) || 0;
			const endNum = Number(end) || 0;

			// Only sync if chain status has actually changed
			if (
				!lastSynced ||
				lastSynced.current !== currentNum ||
				lastSynced.max !== maxNum ||
				lastSynced.timeout !== timeoutNum ||
				lastSynced.cooldown !== cooldownNum ||
				lastSynced.end !== endNum
			) {
				try {
					console.log(
						`[LiveStore] Syncing chain status: ${currentNum}/${maxNum}`,
					);
					store.commit(
						events.chainStatusUpdated({
							current: currentNum,
							max: maxNum,
							timeout: timeoutNum,
							cooldown: cooldownNum,
							end: endNum,
							updatedAt: new Date(),
						}),
					);

					// Track the synced chain status to prevent duplicates
					lastSyncedChainRef.current = {
						current: currentNum,
						max: maxNum,
						timeout: timeoutNum,
						cooldown: cooldownNum,
						end: endNum,
					};
				} catch (error) {
					console.error("Failed to sync chain status to LiveStore:", error);
				}
			}
		}
	}, [liveStoreReady, chainInfo, store]);

	// Sync cooldown status to LiveStore when tRPC data updates
	useEffect(() => {
		if (!liveStoreReady || !cooldownData || !store) return;
		if (cooldownData?.remaining !== undefined) {
			const lastSynced = lastSyncedCooldownRef.current;

			// Only sync if cooldown has actually changed
			if (lastSynced !== cooldownData.remaining) {
				try {
					console.log(
						`[LiveStore] Syncing cooldown: ${cooldownData.remaining}s`,
					);
					store.commit(
						events.targetFinderCooldownUpdated({
							remaining: cooldownData.remaining,
							updatedAt: new Date(),
						}),
					);

					// Track the synced cooldown to prevent duplicates
					lastSyncedCooldownRef.current = cooldownData.remaining;
				} catch (error) {
					console.error("Failed to sync cooldown to LiveStore:", error);
				}
			}
		}
	}, [liveStoreReady, cooldownData, store]);

	// Listen to React Query cache updates and sync to LiveStore
	useEffect(() => {
		const handleCacheChange = () => {
			// Get current data from the cache and sync to LiveStore
			const currentLists = queryClient.getQueryData(
				trpc.targetFinder.getLists.queryKey(),
			);
			const currentChainInfo = queryClient.getQueryData(
				trpc.targetFinder.getChainInfo.queryKey(),
			) as ChainInfoResponse | undefined;
			const currentCooldown = queryClient.getQueryData(
				trpc.targetFinder.getTargetFinderCooldown.queryKey(),
			);

			// Sync lists if available
			if (currentLists && Array.isArray(currentLists)) {
				for (const list of currentLists) {
					if (!syncedListsRef.current.has(list.id)) {
						try {
							console.log(
								`[LiveStore] Syncing list from cache update: ${list.name}`,
							);
							store.commit(
								events.targetListCreated({
									id: list.id,
									name: list.name,
									userId: list.userId || undefined,
									isExternal: list.isExternal || false,
									createdAt: new Date(),
								}),
							);
							syncedListsRef.current.add(list.id);
						} catch (error) {
							console.error("Failed to sync updated list to LiveStore:", error);
						}
					}
				}
			}

			// Sync chain info if available and changed
			if (
				currentChainInfo?.chain &&
				typeof currentChainInfo.chain === "object"
			) {
				const chainData = currentChainInfo.chain as ChainData;
				const { current, max, timeout, cooldown, end } = chainData;
				const lastSynced = lastSyncedChainRef.current;

				const currentNum = Number(current) || 0;
				const maxNum = Number(max) || 0;
				const timeoutNum = Number(timeout) || 0;
				const cooldownNum = Number(cooldown) || 0;
				const endNum = Number(end) || 0;

				if (
					!lastSynced ||
					lastSynced.current !== currentNum ||
					lastSynced.max !== maxNum ||
					lastSynced.timeout !== timeoutNum ||
					lastSynced.cooldown !== cooldownNum ||
					lastSynced.end !== endNum
				) {
					try {
						console.log(
							`[LiveStore] Syncing chain from cache update: ${currentNum}/${maxNum}`,
						);
						store.commit(
							events.chainStatusUpdated({
								current: currentNum,
								max: maxNum,
								timeout: timeoutNum,
								cooldown: cooldownNum,
								end: endNum,
								updatedAt: new Date(),
							}),
						);
						lastSyncedChainRef.current = {
							current: currentNum,
							max: maxNum,
							timeout: timeoutNum,
							cooldown: cooldownNum,
							end: endNum,
						};
					} catch (error) {
						console.error("Failed to sync updated chain to LiveStore:", error);
					}
				}
			}

			// Sync cooldown if available and changed
			if (
				currentCooldown &&
				typeof currentCooldown === "object" &&
				"remaining" in currentCooldown
			) {
				const cooldown = currentCooldown as { remaining: number };
				const lastSynced = lastSyncedCooldownRef.current;
				if (lastSynced !== cooldown.remaining) {
					try {
						console.log(
							`[LiveStore] Syncing cooldown from cache update: ${cooldown.remaining}s`,
						);
						store.commit(
							events.targetFinderCooldownUpdated({
								remaining: cooldown.remaining,
								updatedAt: new Date(),
							}),
						);
						lastSyncedCooldownRef.current = cooldown.remaining;
					} catch (error) {
						console.error(
							"Failed to sync updated cooldown to LiveStore:",
							error,
						);
					}
				}
			}
		};

		// Set up a listener for query cache changes
		const unsubscribe = queryClient
			.getQueryCache()
			.subscribe(handleCacheChange);

		return () => {
			unsubscribe();
		};
	}, [queryClient, store]);

	// Cleanup sync coordinator periodically
	useEffect(() => {
		const cleanupInterval = setInterval(() => {
			syncCoordinatorRef.current.cleanup();
		}, 30000); // Clean up every 30 seconds

		return () => clearInterval(cleanupInterval);
	}, []);

	// Manual sync functions for external use
	const syncNewTarget = (
		target: {
			id: string;
			listId: string;
			name: string;
			tornId: string;
			status: string;
			profilePicture?: string;
			createdAt: Date;
		},
		source: SyncSource = "manual",
	) => {
		const operationId = `target_add_${target.id}`;
		if (!syncCoordinatorRef.current.shouldSync(operationId, source)) {
			console.log(`[LiveStore] Skipping duplicate sync: ${operationId}`);
			return { success: false, reason: "duplicate" };
		}

		try {
			console.log(`[LiveStore] Syncing new target ${target.id} from ${source}`);
			store.commit(
				events.targetAdded({
					id: target.id,
					listId: target.listId,
					name: target.name,
					tornId: target.tornId,
					status: target.status,
					profilePicture: target.profilePicture,
					createdAt: target.createdAt,
				}),
			);
			syncedTargetsRef.current.add(target.id);
			return { success: true };
		} catch (error) {
			console.error("Failed to sync new target to LiveStore:", error);
			return { success: false, reason: "error", error };
		}
	};

	const syncTargetStatusUpdate = (update: {
		id: string;
		tornId: string;
		status: string;
		profilePicture?: string;
	}) => {
		try {
			console.log(
				`[LiveStore] Manually syncing target status update ${update.id}: ${update.status}`,
			);
			store.commit(
				events.targetStatusUpdated({
					id: update.id,
					tornId: update.tornId,
					status: update.status,
					profilePicture: update.profilePicture,
					updatedAt: new Date(),
				}),
			);
		} catch (error) {
			console.error("Failed to sync target status update to LiveStore:", error);
		}
	};

	const syncTargetStatusBatch = (
		updates: Array<{
			id: string;
			tornId: string;
			status: string;
			profilePicture?: string;
		}>,
	) => {
		try {
			console.log(
				`[LiveStore] Manually syncing target status batch update: ${updates.length} targets`,
			);
			store.commit(
				events.targetStatusBatchUpdated({
					updates,
					updatedAt: new Date(),
				}),
			);
		} catch (error) {
			console.error("Failed to sync target status batch to LiveStore:", error);
		}
	};

	const syncTargetRemoval = (targetData: {
		id: string;
		listId: string;
		tornId: string;
	}) => {
		try {
			console.log(
				`[LiveStore] Manually syncing target removal ${targetData.id}`,
			);
			store.commit(
				events.targetRemoved({
					id: targetData.id,
					listId: targetData.listId,
					tornId: targetData.tornId,
				}),
			);
			syncedTargetsRef.current.delete(targetData.id);
		} catch (error) {
			console.error("Failed to sync target removal to LiveStore:", error);
		}
	};

	const syncChainUpdate = (chain: ChainData) => {
		try {
			console.log(
				`[LiveStore] Manually syncing chain update: ${chain.current}/${chain.max}`,
			);
			store.commit(
				events.chainStatusUpdated({
					current: chain.current,
					max: chain.max,
					timeout: chain.timeout,
					cooldown: chain.cooldown,
					end: chain.end,
					updatedAt: new Date(),
				}),
			);
			lastSyncedChainRef.current = chain;
		} catch (error) {
			console.error("Failed to sync chain update to LiveStore:", error);
		}
	};

	return {
		// Manual sync functions
		syncNewTarget,
		syncTargetStatusUpdate,
		syncTargetStatusBatch,
		syncTargetRemoval,
		syncChainUpdate,
		// Access to refs for debugging
		syncedTargets: syncedTargetsRef.current,
		syncedLists: syncedListsRef.current,
	};
}

/**
 * Hook to listen to WebSocket updates and sync them to LiveStore
 * This integrates with the existing target finder WebSocket system
 */
export function useTargetFinderWebSocketLiveStoreSync() {
	const {
		syncTargetStatusUpdate,
		syncTargetStatusBatch,
		syncNewTarget,
		syncTargetRemoval,
	} = useTargetFinderLiveStoreSync();

	// Note: WebSocket integration will be handled by updating the existing
	// useTargetFinderWebSocket hook to also sync real-time updates to LiveStore
	// This provides a clean separation and maintains existing functionality

	return {
		syncTargetStatusUpdate,
		syncTargetStatusBatch,
		syncNewTarget,
		syncTargetRemoval,
	};
}
