-- =============================================================================
-- GUIDES SEEDING
-- Based on guides.ts DEFAULT_GUIDES with full content
-- =============================================================================

-- Insert guides only if they don't already exist (check by title)
INSERT INTO guide (title, content, category, author_id, created_at, updated_at) 
SELECT 
    '🚀 Getting Started with MonkeyMenu', 
    'Welcome to MonkeyMenu! Here''s everything you need to know to get started:

## 🚀 Quick Overview
MonkeyMenu is the official platform for Menacing Monkeys faction members. It provides tools for banking, target finding, member communication, and faction management.

## 📍 Navigation
- **Dashboard**: Your central hub with quick access to all tools
- **Banking**: Request withdrawals and track faction finances
- **Target Finder**: Analyze and find optimal targets
- **Announcements**: Stay updated with faction news
- **Guides**: Access helpful tutorials and resources
- **Profile**: Manage your account settings

## 🔑 First Steps
1. **Complete Your Profile**: Add your preferences and update your information
2. **Read the Announcements**: Stay informed about faction activities
3. **Explore the Banking System**: Learn how to request withdrawals
4. **Check Out Target Finder**: Discover advanced target analysis tools
5. **Join Discord**: Link your Discord account for real-time notifications

## 💡 Tips
- Use the search features to quickly find what you need
- Check guides regularly for new tutorials and updates
- Contact faction leadership if you need help with anything
- Keep your API key secure and never share it with others

Need help? Contact a faction leader or check out the other guides!',
    'getting-started', 
    'system-user-monkeymenu', 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP
WHERE NOT EXISTS (
    SELECT 1 FROM guide WHERE title = '🚀 Getting Started with MonkeyMenu'
);

INSERT INTO guide (title, content, category, author_id, created_at, updated_at) 
SELECT 
    '🚀 Banking System Guide',
    'Learn how to use the MonkeyMenu banking system for faction withdrawals:

## 🏦 How Banking Works
The banking system allows faction members to request withdrawals from the faction treasury. All requests go through an approval process by faction leadership.

## 📝 Making a Withdrawal Request
1. Navigate to the **Banking** section
2. Click **"Request Withdrawal"**
3. Enter the amount you need
4. Provide a clear reason for the withdrawal
5. Submit your request

## ⏱️ Request Process
- **Submitted**: Your request is waiting for review
- **Approved**: Leadership has approved your request
- **Completed**: The money has been sent to your account
- **Denied**: Request was not approved (reason will be provided)

## 📊 Viewing Your Requests
- See all your past withdrawal requests
- Track the status of pending requests
- View approval/denial reasons
- Check transaction history

## 🔒 Important Guidelines
- Only request what you genuinely need
- Provide clear, honest reasons for withdrawals
- Don''t submit duplicate requests
- Large amounts may require additional approval time
- Abuse of the system may result in restrictions

## 💡 Pro Tips
- Check faction balance before requesting large amounts
- Be patient - approvals may take time depending on leadership availability
- Keep withdrawal reasons professional and specific
- Use the system responsibly to maintain access for all members

Questions about banking? Contact a Co-Leader or higher for assistance!',
    'banking',
    'system-user-monkeymenu', 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP
WHERE NOT EXISTS (
    SELECT 1 FROM guide WHERE title = '🚀 Banking System Guide'
);

INSERT INTO guide (title, content, category, author_id, created_at, updated_at) 
SELECT 
    '🚀 Target Finder Tutorial',
    'Master the Target Finder tool to identify optimal targets for attacks:

## 🔍 What is Target Finder?
Target Finder is an advanced analysis tool that helps you identify the best targets for attacks based on various criteria and intelligence data.

## 🚀 Getting Started
1. Go to the **Target Finder** section
2. Set your search criteria (level range, online status, etc.)
3. Use filters to narrow down results
4. Analyze target profiles and statistics
5. Make informed attack decisions

## 📊 Understanding Target Data
- **Level & Stats**: Basic combat information
- **Online Status**: When they were last seen
- **Faction Info**: Faction membership and activity
- **Battle Stats**: Estimated combat capabilities
- **Risk Assessment**: Our analysis of attack success probability

## 🎯 Search Strategies
- **Level Farming**: Target players within your range for easier wins
- **Resource Hunting**: Find players with high money/points
- **Strategic Targets**: Identify key enemy faction members
- **Training Targets**: Find appropriate opponents for skill building

## ⚡ Advanced Features
- Save frequently searched criteria
- Export target lists for faction operations
- Real-time data updates
- Integration with faction intelligence

## 🔒 Usage Guidelines
- Use intelligence responsibly and ethically
- Don''t share target data outside the faction
- Respect other players and avoid harassment
- Focus on strategic faction benefits
- Report any data inconsistencies to leadership

## 💡 Success Tips
- Cross-reference multiple data sources
- Consider timing for optimal attack windows
- Coordinate with faction members for group operations
- Keep search criteria updated based on your growth
- Use the tool regularly to stay competitive

Master these techniques and you''ll become a formidable faction asset!',
    'target-finder',
    'system-user-monkeymenu', 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP
WHERE NOT EXISTS (
    SELECT 1 FROM guide WHERE title = '🚀 Target Finder Tutorial'
);

INSERT INTO guide (title, content, category, author_id, created_at, updated_at) 
SELECT 
    '🚀 Discord Integration & Notifications',
    'Get the most out of MonkeyMenu by linking your Discord account:

## 🎮 Why Link Discord?
Linking your Discord account provides real-time notifications, role synchronization, and seamless communication with the faction.

## 🔗 How to Link Your Account
1. Go to your **Profile** settings
2. Click **"Link Discord Account"**
3. Authorize the connection through Discord
4. Your roles will automatically sync

## 📱 Notification Types
- **Banking**: Withdrawal request updates and approvals
- **Announcements**: Important faction news and updates
- **Target Updates**: New intelligence and opportunity alerts
- **System Messages**: Account and security notifications

## ⚙️ Managing Notifications
- Customize which notifications you receive
- Set quiet hours for important alerts only
- Choose notification channels (DM vs server)
- Control frequency of updates

## 🎭 Role Synchronization
Your MonkeyMenu role automatically syncs with Discord:
- **Leadership Roles**: Full access to admin channels
- **Member Roles**: Standard faction channels
- **Special Roles**: Based on your faction contributions

## 🔧 Discord Commands
Use these commands in the faction Discord server:
- `/withdraw <amount>`: Request a withdrawal directly from Discord
- `/verify`: Check your account verification status
- `/balance`: View faction treasury balance

## 🛡️ Security & Privacy
- Only necessary data is shared with Discord
- You can unlink your account at any time
- Your Torn information remains private
- All connections are encrypted and secure

## 🔄 Troubleshooting
**Connection Issues:**
- Try unlinking and relinking your account
- Check Discord permissions for the bot
- Ensure you''re in the faction Discord server

**Missing Notifications:**
- Verify notification settings in both MonkeyMenu and Discord
- Check if Discord DMs are enabled
- Confirm your Discord privacy settings

**Role Problems:**
- Contact faction leadership for role verification
- Allow up to 10 minutes for role sync after linking
- Ensure your faction membership is active

Stay connected and never miss important faction updates!',
    'getting-started',
    'system-user-monkeymenu', 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP
WHERE NOT EXISTS (
    SELECT 1 FROM guide WHERE title = '🚀 Discord Integration & Notifications'
);

INSERT INTO guide (title, content, category, author_id, created_at, updated_at) 
SELECT 
    '🚀 Account Security & Best Practices',
    'Keep your MonkeyMenu and Torn accounts secure with these essential practices:

## 🔐 API Key Security
Your Torn API key is the gateway to your account - protect it carefully:

- **Never share your API key** with anyone, even faction leadership
- **Use a Limited Access key** rather than Full Access when possible
- **Regenerate your key** if you suspect it''s been compromised
- **Store it securely** - don''t save it in plain text files
- **Monitor usage** - check for unexpected API activity in Torn

## 👤 Account Protection
- **Use strong, unique passwords** for both Torn and MonkeyMenu
- **Enable two-factor authentication** where available
- **Keep your email secure** - it''s your recovery method
- **Log out from shared computers** after using MonkeyMenu
- **Report suspicious activity** immediately to leadership

## 🚨 Red Flags to Watch For
- Unexpected withdrawal requests you didn''t make
- Changes to your profile you didn''t authorize
- Notifications about actions you didn''t perform
- Login alerts from unfamiliar locations
- Messages asking for your API key or credentials

## 📧 Phishing Prevention
Be cautious of fake communications:
- **Official emails** will always come from verified domains
- **No legitimate service** will ask for your API key via email
- **Double-check URLs** before entering credentials
- **When in doubt** - contact faction leadership directly

## 🔄 Regular Security Checkups
Monthly security review checklist:
- [ ] Review recent login activity
- [ ] Check API key usage in Torn
- [ ] Verify MonkeyMenu permissions are correct
- [ ] Update passwords if needed
- [ ] Review linked Discord account status

## 🆘 If You''re Compromised
Take immediate action:
1. **Change your Torn password** immediately
2. **Regenerate your API key** in Torn settings
3. **Update your MonkeyMenu credentials**
4. **Contact faction leadership** about the incident
5. **Review recent account activity** for unauthorized actions
6. **Disconnect and reconnect** your Discord account

## 📱 Mobile Security
When using MonkeyMenu on mobile:
- Use official browsers with security updates
- Avoid public WiFi for account access
- Enable screen locks and app security
- Keep your mobile OS updated
- Use reputable antivirus software

## 💡 Pro Tips
- **Bookmark the official MonkeyMenu URL** to avoid fake sites
- **Use a password manager** to generate and store secure passwords
- **Keep backup access methods** updated (recovery email, etc.)
- **Stay informed** about security best practices
- **Trust your instincts** - if something seems suspicious, it probably is

Your security is our security. Protect yourself and protect the faction!',
    'getting-started',
    'system-user-monkeymenu',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
WHERE NOT EXISTS (
    SELECT 1 FROM guide WHERE title = '🚀 Account Security & Best Practices'
);

-- Verification
SELECT 'Guides seeding completed!' as message;
SELECT COUNT(*) as total_guides FROM guide; 