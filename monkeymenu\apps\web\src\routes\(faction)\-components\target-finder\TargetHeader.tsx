import { HasPermission } from "@/components/permissions/PermissionGuards";
import { Button } from "@/components/ui/button";
import { PERMISSION_NAMES } from "@monkeymenu/shared";
import { RefreshCw } from "lucide-react";
import { AddTargetDialog } from "./AddTargetDialog";

interface TargetHeaderProps {
	selectedList: string;
	onAddTarget: () => void;
	onRefresh?: () => void;
	isRefreshing?: boolean;
	lastRefreshTime?: Date;
}

export function TargetHeader({
	selectedList,
	onAddTarget,
	onRefresh,
	isRefreshing = false,
	lastRefreshTime,
}: TargetHeaderProps) {
	const formatRefreshTime = (time: Date) => {
		const now = new Date();
		const diffMs = now.getTime() - time.getTime();
		const diffSecs = Math.floor(diffMs / 1000);

		if (diffSecs < 60) return `${diffSecs}s ago`;
		if (diffSecs < 3600) return `${Math.floor(diffSecs / 60)}m ago`;
		return time.toLocaleTimeString();
	};

	return (
		<div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
			<div>
				<h1 className="font-bold text-3xl text-foreground">🎯 Target Finder</h1>
				<div className="space-y-1">
					<p className="text-muted-foreground">
						Track and monitor targets across different lists with real-time
						updates
					</p>
					{lastRefreshTime && (
						<p className="text-muted-foreground text-xs">
							Last updated: {formatRefreshTime(lastRefreshTime)}
						</p>
					)}
				</div>
			</div>
			<div className="flex gap-2 self-start">
				{/* Force Refresh Button */}
				{selectedList !== "" && onRefresh && (
					<Button
						variant="outline"
						size="sm"
						onClick={onRefresh}
						disabled={isRefreshing}
						className="gap-2"
					>
						<RefreshCw
							className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
						/>
						{isRefreshing ? "Refreshing..." : "Force Refresh"}
					</Button>
				)}
				{/* Show add target button for Custom List */}
				{selectedList === "Custom List" && (
					<HasPermission permission={PERMISSION_NAMES.TARGET_FINDER_VIEW}>
						<AddTargetDialog
							selectedList={selectedList}
							onSuccess={onAddTarget}
						/>
					</HasPermission>
				)}

				{/* Show add target button for shared lists (non-custom, non-external) */}
				{selectedList !== "" &&
					selectedList !== "Custom List" &&
					!selectedList.startsWith("Baldr's") &&
					!selectedList.startsWith("Enemy Faction:") && (
						<HasPermission
							permission={PERMISSION_NAMES.TARGET_FINDER_MANAGE_SHARED_LISTS}
						>
							<AddTargetDialog
								selectedList={selectedList}
								onSuccess={onAddTarget}
							/>
						</HasPermission>
					)}
			</div>
		</div>
	);
}
