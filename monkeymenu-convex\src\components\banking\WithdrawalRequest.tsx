
import { useState } from 'react';
// Removed duplicate import
import { useQuery, useMutation } from 'convex/react'; // Removed duplicate import
import { api } from '../../../convex/_generated/api';
import { useSession } from '../../hooks/useSession';

export function WithdrawalRequest({ onCancel }: { onCancel?: () => void }) {
  const { convexUser } = useSession();
  const [amount, setAmount] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [cooldownMessage, setCooldownMessage] = useState<string | null>(null);

  // Get recent requests for cooldown logic only
  const userRequests = useQuery(
    api.banking.getUserWithdrawalRequests,
    convexUser ? { userId: convexUser._id, limit: 1 } : "skip"
  );

  // Cooldown logic: enforce a 1-minute cooldown between requests
  let isOnCooldown = false;
  let cooldownUntil: Date | null = null;
  if (userRequests && userRequests.length > 0) {
    const mostRecent = userRequests[0]; // Only need the most recent
    const lastCreated = new Date(mostRecent.createdAt);
    const now = new Date();
    const diffMs = now.getTime() - lastCreated.getTime();
    if (diffMs < 60 * 1000) {
      isOnCooldown = true;
      cooldownUntil = new Date(lastCreated.getTime() + 60 * 1000);
    }
  }

  // Create withdrawal request mutation
  const createWithdrawalRequest = useMutation(api.banking.createWithdrawalRequest);

  // Remove refetch logic since it's not needed

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);
    setCooldownMessage(null);
    if (isOnCooldown && cooldownUntil) {
      const secondsLeft = Math.ceil((cooldownUntil.getTime() - new Date().getTime()) / 1000);
      setCooldownMessage(`Please wait ${secondsLeft} more second${secondsLeft !== 1 ? 's' : ''} before submitting another withdrawal request.`);
      return;
    }
    setIsSubmitting(true);
    try {
      const numAmount = parseFloat(amount);
      if (isNaN(numAmount) || numAmount <= 0) {
        throw new Error('Please enter a valid amount');
      }
      await createWithdrawalRequest({ amount: numAmount });
      setSuccess('Withdrawal request submitted successfully!');
      setAmount('');
      if (onCancel) {
        onCancel();
      }
    } catch (err: any) {
      setError(err.message || 'Failed to submit withdrawal request');
    } finally {
      setIsSubmitting(false);
    }
  };



  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-xl font-semibold mb-6 text-gray-900">Request Withdrawal</h3>
      <form onSubmit={handleSubmit} className="space-y-4 mb-6">
        <div>
          <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
            Amount ($)
          </label>
          <input
            type="number"
            id="amount"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            placeholder="Enter amount to withdraw"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div className="flex gap-2 justify-end">
          <button
            type="submit"
            disabled={isSubmitting || isOnCooldown}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium disabled:bg-gray-400"
          >
            {isSubmitting ? 'Submitting...' : 'Submit'}
          </button>
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded text-sm font-medium"
            >
              Cancel
            </button>
          )}
        </div>
      </form>
      {error && <div className="text-red-600 mb-2">{error}</div>}
      {success && <div className="text-green-600 mb-2">{success}</div>}
      {cooldownMessage && <div className="text-yellow-600 mb-2">{cooldownMessage}</div>}
    </div>
  );
}
// ...existing code ends here. Remove all stray code below this line.