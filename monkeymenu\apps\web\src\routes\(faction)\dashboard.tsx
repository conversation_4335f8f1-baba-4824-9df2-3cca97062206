import { HasPermission } from "@/components/permissions/PermissionGuards";
import { PageContainer } from "@/components/ui/page-container";
import { PERMISSION_NAMES } from "@monkeymenu/shared";
import { createFileRoute } from "@tanstack/react-router";
import { Dashboard } from "./-components/dashboard/";

export const Route = createFileRoute("/(faction)/dashboard")({
	component: DashboardPage,
});

function DashboardPage() {
	return (
		<HasPermission permission={PERMISSION_NAMES.DASHBOARD_VIEW}>
			<PageContainer>
				<Dashboard />
			</PageContainer>
		</HasPermission>
	);
}
