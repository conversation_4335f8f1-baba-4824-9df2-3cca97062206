import { HasPermission } from "@/components/permissions/PermissionGuards";
import { But<PERSON> } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useAppForm } from "@/components/ui/tanstack-form";
import { GuideFormSchema, PERMISSION_NAMES } from "@monkeymenu/shared";
import { BookOpen } from "lucide-react";
import ReactMarkdown from "react-markdown";
import rehypeHighlight from "rehype-highlight";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";
import { GUIDE_CATEGORIES } from "./utils";

interface GuideData {
	guide: {
		id: number;
		title: string;
		content: string;
		category: string;
		createdAt: string;
		updatedAt: string;
	};
	author?: {
		name: string;
	};
}

interface CreateGuideDialogProps {
	isOpen: boolean;
	onClose: () => void;
	editingGuide: GuideData | null;
	onSubmit: (data: {
		title: string;
		content: string;
		category: string;
	}) => void;
	isSubmitting: boolean;
}

export function CreateGuideDialog({
	isOpen,
	onClose,
	editingGuide,
	onSubmit,
	isSubmitting,
}: CreateGuideDialogProps) {
	const form = useAppForm({
		defaultValues: {
			title: editingGuide?.guide.title || "",
			content: editingGuide?.guide.content || "",
			category: editingGuide?.guide.category || "general",
		},
		validators: { onChange: GuideFormSchema },
		onSubmit: ({ value }) => {
			onSubmit(value);
		},
	});

	// Reset form when dialog opens/closes or editing guide changes
	const handleOpenChange = (open: boolean) => {
		if (!open) {
			form.reset({
				title: "",
				content: "",
				category: "general",
			});
			onClose();
		}
	};

	// Update form when editing guide changes
	if (editingGuide && isOpen) {
		form.reset({
			title: editingGuide.guide.title,
			content: editingGuide.guide.content,
			category: editingGuide.guide.category,
		});
	}

	return (
		<HasPermission permission={PERMISSION_NAMES.GUIDES_MANAGE}>
			<Dialog open={isOpen} onOpenChange={handleOpenChange}>
				<DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
					<DialogHeader>
						<DialogTitle className="flex items-center gap-2">
							<BookOpen className="h-5 w-5" />
							{editingGuide ? "Edit Guide" : "Create New Guide"}
						</DialogTitle>
						<DialogDescription>
							{editingGuide
								? "Make changes to the existing guide below."
								: "Create a new guide with title, category, and markdown content."}
						</DialogDescription>
					</DialogHeader>
					<form.AppForm>
						<form
							onSubmit={(e) => {
								e.preventDefault();
								e.stopPropagation();
								void form.handleSubmit();
							}}
							className="space-y-6"
						>
							<form.AppField name="title">
								{(field) => (
									<field.FormItem>
										<field.FormLabel>Title</field.FormLabel>
										<field.FormControl>
											<Input
												placeholder="Enter guide title..."
												value={field.state.value}
												onChange={(e) => field.handleChange(e.target.value)}
												onBlur={field.handleBlur}
											/>
										</field.FormControl>
										<field.FormMessage />
									</field.FormItem>
								)}
							</form.AppField>

							<form.AppField name="category">
								{(field) => (
									<field.FormItem>
										<field.FormLabel>Category</field.FormLabel>
										<field.FormControl>
											<Select
												value={field.state.value}
												onValueChange={field.handleChange}
											>
												<SelectTrigger>
													<SelectValue placeholder="Select a category" />
												</SelectTrigger>
												<SelectContent>
													{GUIDE_CATEGORIES.map((category) => (
														<SelectItem key={category.id} value={category.id}>
															{category.emoji} {category.label}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
										</field.FormControl>
										<field.FormMessage />
									</field.FormItem>
								)}
							</form.AppField>

							<form.AppField name="content">
								{(field) => (
									<field.FormItem>
										<field.FormLabel>Content</field.FormLabel>
										<field.FormControl>
											<Tabs defaultValue="write" className="w-full">
												<TabsList className="grid w-full grid-cols-2">
													<TabsTrigger value="write">Write</TabsTrigger>
													<TabsTrigger value="preview">Preview</TabsTrigger>
												</TabsList>
												<TabsContent value="write" className="mt-4">
													<textarea
														className="flex min-h-[400px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
														placeholder="Write your guide content here... Markdown is supported"
														value={field.state.value}
														onChange={(e) => field.handleChange(e.target.value)}
														onBlur={field.handleBlur}
													/>
												</TabsContent>
												<TabsContent value="preview" className="mt-4">
													<div className="min-h-[400px] rounded-md border border-input bg-background p-4">
														<div className="prose prose-sm dark:prose-invert max-w-none">
															<ReactMarkdown
																remarkPlugins={[remarkGfm]}
																rehypePlugins={[rehypeHighlight, rehypeRaw]}
															>
																{field.state.value || "*No content to preview*"}
															</ReactMarkdown>
														</div>
													</div>
												</TabsContent>
											</Tabs>
										</field.FormControl>
										<field.FormMessage />
									</field.FormItem>
								)}
							</form.AppField>

							<div className="flex justify-between">
								<Button
									type="button"
									variant="outline"
									onClick={() => handleOpenChange(false)}
								>
									Cancel
								</Button>
								<form.Subscribe>
									{(state) => (
										<Button
											type="submit"
											disabled={
												!state.canSubmit || state.isSubmitting || isSubmitting
											}
										>
											{editingGuide ? "Update Guide" : "Create Guide"}
										</Button>
									)}
								</form.Subscribe>
							</div>
						</form>
					</form.AppForm>
				</DialogContent>
			</Dialog>
		</HasPermission>
	);
}
