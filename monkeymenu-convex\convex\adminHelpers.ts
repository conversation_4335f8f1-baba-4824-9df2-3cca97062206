import { mutation, query } from "./_generated/server";

// Get current user with full details for debugging
export const getCurrentUserDebug = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return { error: "Not authenticated" };
    }
    
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      return { error: "User not found in database" };
    }

    return {
      user: {
        _id: user._id,
        clerkId: user.clerkId,
        username: user.username,
        permissions: user.permissions,
        isActive: user.isActive,
      },
      identity: {
        subject: identity.subject,
        name: identity.name,
        email: identity.email,
      }
    };
  },
});

// Grant yourself full permissions for testing
export const grantMeFullPermissions = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }
    
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found in database");
    }

    // Grant comprehensive permissions
    const newPermissions = [
      "admin.all",
      "banking.view",
      "banking.manage", 
      "announcements.create",
      "announcements.edit",
      "announcements.delete",
      "announcements.manage",
      "targets.view",
      "targets.update",
      "targets.delete", 
      "targets.manage",
      "wars.view",
      "wars.create",
      "wars.edit",
      "wars.update",
      "wars.delete",
      "wars.manage"
    ];

    await ctx.db.patch(user._id, {
      permissions: newPermissions,
      updatedAt: Date.now(),
    });

    return {
      success: true,
      userId: user._id,
      oldPermissions: user.permissions,
      newPermissions: newPermissions
    };
  },
});

// Reset to default permissions
export const resetMyPermissions = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }
    
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found in database");
    }

    const defaultPermissions = ["banking.view"];

    await ctx.db.patch(user._id, {
      permissions: defaultPermissions,
      updatedAt: Date.now(),
    });

    return {
      success: true,
      userId: user._id,
      oldPermissions: user.permissions,
      newPermissions: defaultPermissions
    };
  },
});