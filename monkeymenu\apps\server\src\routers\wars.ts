import type {
	Torn<PERSON><PERSON><PERSON>,
	TornChainInfo,
	TornChainReport,
} from "@monkeymenu/shared";
import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import { z } from "zod";
import type { DBInstance } from "../db/index";
import { tornUser } from "../db/schema/torn";
import { decrypt, initCrypto } from "../lib/crypto";
import type { TornAPIWithSuspension } from "../lib/tornApi";
import { createTornAPIWithSuspension } from "../lib/tornApi";
import { factionProcedure, router } from "../lib/trpc";
import { WarCacheService } from "../lib/warCacheService";

// Schema definitions - use simpler validation for now
const chainReportSchema = z.object({
	id: z.number(),
	details: z.any(), // Relaxed validation
	attackers: z.array(z.any()).optional(), // Relaxed validation
});

// Constants
const MY_FACTION_ID = 53100; // Menacing Monkeys

/**
 * Helper function to get all chains within a time range with pagination
 */
async function getAllChainsInTimeRange(
	tornApi: TornAPIWithSuspension,
	warStart: number,
	warEnd: number,
): Promise<TornChainInfo[]> {
	const allChains: TornChainInfo[] = [];
	let cursor: number | undefined = warEnd;

	while (true) {
		const batch = await tornApi.getFactionChains(100, "DESC", cursor, warStart);
		if (batch.length === 0) break;

		allChains.push(...batch);

		// Continue pagination only if we got a full batch (100 items)
		// AND the oldest chain is still within our range
		if (batch.length < 100) break;

		const oldest = batch[batch.length - 1];
		if (oldest.start <= warStart) break;

		cursor = oldest.start - 1;
	}

	return allChains;
}

/**
 * Common function to validate and decrypt user API key
 */
async function getUserApiKey(
	db: DBInstance,
	userId: string,
	env: { TORN_API_KEY_ENCRYPTION_SECRET: string },
): Promise<string> {
	const userTornData = await db
		.select({
			tornApiKey: tornUser.tornApiKey,
			verified: tornUser.tornApiKeyVerified,
		})
		.from(tornUser)
		.where(eq(tornUser.id, userId))
		.get();

	if (!userTornData?.tornApiKey || !userTornData.verified) {
		throw new Error(
			"User API key is not set or not verified. Please verify your API key in settings.",
		);
	}

	initCrypto(env.TORN_API_KEY_ENCRYPTION_SECRET);

	try {
		return decrypt(userTornData.tornApiKey);
	} catch (error) {
		throw new Error("Failed to decrypt API key");
	}
}

export const warsRouter = router({
	/**
	 * Fetch the most recent ranked wars for the user's faction.
	 * Currently returns all wars provided by Torn API. Pagination can be added later.
	 */
	listRankedWars: factionProcedure
		.output(z.object({ rankedWars: z.array(z.any()) }))
		.query(async ({ ctx }) => {
			const userId = ctx.session?.userId;
			if (!userId) {
				throw new Error("User session not found");
			}

			const apiKey = await getUserApiKey(ctx.db, userId, ctx.env);

			// Call Torn API
			try {
				const tornApi = createTornAPIWithSuspension(
					apiKey,
					ctx.db,
					userId,
					ctx.env,
				);
				// Use hardcoded faction ID for now - could be made configurable
				const factionId = 53100; // Menacing Monkeys
				const rankedWars = await tornApi.getFactionRankedWars(factionId);

				return { rankedWars };
			} catch (error) {
				console.error("Failed to fetch ranked wars:", error);
				throw new Error(
					`Failed to fetch ranked wars: ${error instanceof Error ? error.message : "Unknown error"}`,
				);
			}
		}),

	getRankedWarReport: factionProcedure
		.input(z.object({ warId: z.number() }))
		.output(z.object({ warReport: z.any() }))
		.query(async ({ ctx, input }) => {
			const userId = ctx.session?.userId;
			if (!userId) {
				throw new Error("User session not found");
			}

			const apiKey = await getUserApiKey(ctx.db, userId, ctx.env);

			try {
				const tornApi = createTornAPIWithSuspension(
					apiKey,
					ctx.db,
					userId,
					ctx.env,
				);
				const warCache = new WarCacheService(ctx.db, tornApi);
				const warReport = await warCache.getWarReport(input.warId);

				return { warReport };
			} catch (error) {
				console.error("Failed to fetch war report:", error);
				throw new Error(
					`Failed to fetch war report: ${error instanceof Error ? error.message : "Unknown error"}`,
				);
			}
		}),

	getWarChainReports: factionProcedure
		.input(z.object({ warId: z.number() }))
		.output(
			z.object({
				chainReports: z.array(chainReportSchema),
				playerNames: z.record(z.string(), z.string()), // Map of player ID to name
				combinedChainStats: z.array(
					z.object({
						playerId: z.number(),
						playerName: z.string().optional(),
						totalHits: z.number(),
						totalWarHits: z.number(),
						totalAssists: z.number(),
						totalRespect: z.number(),
						averageRespect: z.number(),
						bestRespect: z.number(),
						chainCount: z.number(), // Number of chains this player participated in
					}),
				),
			}),
		)
		.query(async ({ ctx, input }) => {
			const userId = ctx.session?.userId;
			if (!userId) {
				throw new Error("User session not found");
			}

			const apiKey = await getUserApiKey(ctx.db, userId, ctx.env);

			try {
				const tornApi = createTornAPIWithSuspension(
					apiKey,
					ctx.db,
					userId,
					ctx.env,
				);

				const warCache = new WarCacheService(ctx.db, tornApi);
				const result = await warCache.getWarChainReports(input.warId);

				return result;
			} catch (error) {
				console.error("Failed to fetch war chain reports:", error);
				throw new Error(
					`Failed to fetch war chain reports: ${error instanceof Error ? error.message : "Unknown error"}`,
				);
			}
		}),

	getWarAttacks: factionProcedure
		.input(
			z.object({
				warId: z.number(),
			}),
		)
		.output(
			z.object({
				assists: z.number(),
				insideHits: z.number(),
				outsideHits: z.number(),
				totalRespect: z.number(),
				memberStats: z.array(
					z.object({
						attackerId: z.number(),
						attackerName: z.string().optional(),
						totalAttacks: z.number(),
						insideHits: z.number(),
						outsideHits: z.number(),
						assists: z.number(),
						totalRespect: z.number(),
					}),
				),
			}),
		)
		.query(async ({ ctx, input }) => {
			const userId = ctx.session?.userId;
			if (!userId) {
				throw new TRPCError({
					code: "UNAUTHORIZED",
					message: "User session not found",
				});
			}

			const apiKey = await getUserApiKey(ctx.db, userId, ctx.env);

			try {
				const tornApi = createTornAPIWithSuspension(
					apiKey,
					ctx.db,
					userId,
					ctx.env,
				);

				const warCache = new WarCacheService(ctx.db, tornApi);

				// Get war report and attacks using cache
				const warReport = await warCache.getWarReport(input.warId);
				const allAttacks = await warCache.getWarAttacks(input.warId);

				const enemyFaction = warReport.factions.find(
					(f: { id: number }) => f.id !== MY_FACTION_ID,
				);

				// Filter for non-chain attacks only (chain = 0 or null) AND attacks by YOUR faction members
				const nonChainAttacks = allAttacks.filter(
					(attack: TornAttack) =>
						(!attack.chain || attack.chain === 0) &&
						attack.attacker?.faction?.id === MY_FACTION_ID,
				);

				// Calculate summary stats (only for YOUR faction's attacks)
				const assists = nonChainAttacks.filter(
					(a: TornAttack) => a.result === "Assist",
				).length;
				const insideHits = nonChainAttacks.filter(
					(a: TornAttack) => a.defender?.faction?.id === enemyFaction?.id,
				).length;
				const outsideHits = nonChainAttacks.filter(
					(a: TornAttack) => a.defender?.faction?.id !== enemyFaction?.id,
				).length;
				const totalRespect = nonChainAttacks.reduce(
					(sum: number, a: TornAttack) =>
						sum + (Number(a.modifiers?.fair_fight) || 0),
					0,
				);

				// Calculate member statistics (already filtered to YOUR faction only)
				const memberStatsMap = new Map();

				for (const attack of nonChainAttacks) {
					const attackerId = attack.attacker?.id;
					const attackerName = attack.attacker?.name;
					if (!attackerId) continue;

					const isInside = attack.defender?.faction?.id === enemyFaction?.id;
					const isAssist = attack.result === "Assist";
					const respect = Number(attack.modifiers?.fair_fight) || 0;

					if (!memberStatsMap.has(attackerId)) {
						memberStatsMap.set(attackerId, {
							attackerId,
							attackerName,
							totalAttacks: 0,
							insideHits: 0,
							outsideHits: 0,
							assists: 0,
							totalRespect: 0,
						});
					}

					const stats = memberStatsMap.get(attackerId);
					stats.totalAttacks++;
					stats.totalRespect += respect;

					if (isAssist) {
						stats.assists++;
					} else if (isInside) {
						stats.insideHits++;
					} else {
						stats.outsideHits++;
					}
				}

				const memberStats = Array.from(memberStatsMap.values());

				return {
					assists,
					insideHits,
					outsideHits,
					totalRespect,
					memberStats,
				};
			} catch (error) {
				console.error("Failed to fetch war attacks:", error);
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to fetch war attacks",
				});
			}
		}),

	getIndividualChainDetails: factionProcedure
		.input(
			z.object({
				warId: z.number(),
				chainId: z.number(),
			}),
		)
		.output(
			z.object({
				chainReport: chainReportSchema,
				attackSequence: z.array(
					z.object({
						attackId: z.number(),
						timestamp: z.number(),
						attacker: z.object({
							id: z.number(),
							name: z.string().optional(),
						}),
						defender: z.object({
							id: z.number(),
							name: z.string().optional(),
							factionId: z.number().optional(),
						}),
						result: z.string(),
						respect: z.number(),
						chainPosition: z.number(), // Chain progression number (1-100)
						isWarTarget: z.boolean(),
						assists: z.array(
							z.object({
								attackId: z.number(),
								timestamp: z.number(),
								attacker: z.object({
									id: z.number(),
									name: z.string().optional(),
								}),
								result: z.string(),
								respect: z.number(),
							}),
						), // Assists related to this main attack
						modifiers: z.any().optional(),
					}),
				),
				chainMetrics: z.object({
					timelineBreakdown: z.array(
						z.object({
							hourBlock: z.number(), // Hour since chain start
							attackCount: z.number(),
							respectEarned: z.number(),
							uniqueAttackers: z.number(),
						}),
					),
					participationDepth: z.object({
						coreContributors: z.number(), // Players with 10+ attacks
						regularContributors: z.number(), // Players with 3-9 attacks
						casualContributors: z.number(), // Players with 1-2 attacks
					}),
					efficiencyMetrics: z.object({
						averageTimeBetweenHits: z.number(),
						peakActivity: z.object({
							hour: z.number(),
							attackCount: z.number(),
						}),
						respectPerHour: z.number(),
					}),
				}),
			}),
		)
		.query(async ({ ctx, input }) => {
			const userId = ctx.session?.userId;
			if (!userId) {
				throw new Error("User session not found");
			}

			const apiKey = await getUserApiKey(ctx.db, userId, ctx.env);

			try {
				const tornApi = createTornAPIWithSuspension(
					apiKey,
					ctx.db,
					userId,
					ctx.env,
				);

				// Get the specific chain report
				const chainReport = await tornApi.getChainReport(input.chainId);

				// Get war timeframe for filtering
				const warReport = await tornApi.getRankedWarReport(input.warId);
				const enemyFaction = warReport.factions.find(
					(f: { id: number }) => f.id !== MY_FACTION_ID,
				);

				// Fetch all attacks during chain timeframe
				const chainStart = chainReport.start;
				// Add a smart buffer to chainEnd to catch the final hit
				// Shorter chains need less buffer, longer chains might need more
				const chainLength = chainReport.details.chain;
				const bufferTime = Math.min(300, Math.max(60, chainLength * 2)); // 1-5 minutes based on chain length
				const chainEnd = chainReport.end + bufferTime;

				console.log(
					`Fetching attacks for chain ${input.chainId} from ${chainStart} to ${chainEnd}`,
				);

				const collected: TornAttack[] = [];
				let cursor: number | undefined = chainEnd;
				while (true) {
					const batch = await tornApi.getFactionAttacks(
						100,
						"DESC",
						cursor,
						chainStart,
					);
					if (batch.length === 0) break;

					// More flexible filtering - include attacks that might be part of the chain
					const within = batch.filter(
						(a) =>
							a.chain === input.chainId ||
							(a.chain && a.chain > 0) || // Any chain attack during this timeframe
							!a.chain, // Non-chain attacks during this timeframe
					);
					collected.push(...within);

					// Continue pagination only if we got a full batch (100 items)
					// AND the oldest attack is still within our range
					if (batch.length < 100) break;

					const oldest = batch[batch.length - 1];
					if (oldest.started <= chainStart) break;

					cursor = oldest.started - 1;
				}

				console.log(
					`Found ${collected.length} attacks during chain timeframe with ${bufferTime}s buffer`,
				);

				// Filter attacks to only include those from the specific chain
				// First filter by chain ID, then by attackers if needed
				let chainAttacks = collected.filter(
					(attack) => attack.chain === input.chainId,
				);

				console.log(
					`Found ${chainAttacks.length} attacks with chain ID ${input.chainId}`,
				);

				// Always extract assists from collected attacks regardless of chain ID filtering
				let assistsData: TornAttack[] = [];

				// If we still have discrepancies, also filter by known attackers as backup
				if (
					chainReport.attackers &&
					chainAttacks.length !== chainReport.details.chain
				) {
					const chainAttackerIds = new Set(
						chainReport.attackers.map((a) => a.id),
					);

					// Get chain-advancing attacks
					const chainAdvancingAttacks = collected.filter(
						(a) =>
							a.attacker?.id &&
							chainAttackerIds.has(a.attacker.id) &&
							// Only include chain-advancing attacks
							![
								"Assist",
								"Lost",
								"Escape",
								"Interrupted",
								"Defeated",
								"Timeout",
								"Stalemate",
							].includes(a.result),
					);

					// Get assists for context (but don't count toward chain progression)
					assistsData = collected.filter(
						(a) =>
							a.attacker?.id &&
							chainAttackerIds.has(a.attacker.id) &&
							a.result === "Assist",
					);

					console.log(
						`Chain-advancing attacks: ${chainAdvancingAttacks.length}/${chainReport.details.chain}, Assists: ${assistsData.length}`,
					);

					// Use whichever filtering gives us the expected count
					if (
						Math.abs(chainAttacks.length - chainReport.details.chain) >
						Math.abs(chainAdvancingAttacks.length - chainReport.details.chain)
					) {
						chainAttacks = chainAdvancingAttacks;
						console.log("Using filtered attacker-based filtering instead");
					}
				} else {
					// Even if we don't use attacker-based filtering, still get assists
					if (chainReport.attackers) {
						const chainAttackerIds = new Set(
							chainReport.attackers.map((a) => a.id),
						);
						assistsData = collected.filter(
							(a) =>
								a.attacker?.id &&
								chainAttackerIds.has(a.attacker.id) &&
								a.result === "Assist",
						);
					}
				}

				// Sort attacks chronologically and build sequence
				const sortedAttacks = chainAttacks.sort(
					(a, b) => a.started - b.started,
				);
				const sortedAssists = assistsData.sort((a, b) => a.started - b.started);

				console.log(`Attack Sequence Length: ${sortedAttacks.length}`);
				console.log(`Chain Report Total Hits: ${chainReport.details.chain}`);
				console.log(`Filtered Chain Attacks: ${chainAttacks.length}`);
				console.log(`Available Assists: ${sortedAssists.length}`);

				// Debug: Show what result types we have
				const allResults = new Set([
					...sortedAttacks.map((a) => a.result),
					...sortedAssists.map((a) => a.result),
				]);
				console.log(
					`All Attack Results: ${JSON.stringify(Array.from(allResults))}`,
				);

				const resultCounts = new Map();
				for (const a of sortedAttacks) {
					const count = resultCounts.get(a.result) || 0;
					resultCounts.set(a.result, count + 1);
				}
				console.log(
					`Result Counts: ${JSON.stringify(Object.fromEntries(resultCounts))}`,
				);

				// Build attack sequence with assists integrated into chain attacks
				const attackSequence = sortedAttacks.map((attack, index) => {
					const chainPosition = index + 1;
					const attackTime = attack.started;

					// Find assists that happened around this chain attack (within 30 seconds before/after)
					const relatedAssists = sortedAssists
						.filter(
							(assist) =>
								Math.abs(assist.started - attackTime) <= 30 &&
								assist.defender?.id === attack.defender?.id, // Same target
						)
						.map((assist) => ({
							attackId: Number(assist.id),
							timestamp: assist.started,
							attacker: {
								id: assist.attacker?.id || 0,
								name: assist.attacker?.name,
							},
							result: assist.result,
							respect: Number(assist.modifiers?.fair_fight) || 0,
						}));

					// Debug log when we find assists for an attack
					if (relatedAssists.length > 0) {
						console.log(
							`Chain hit ${chainPosition}: Found ${relatedAssists.length} assists for ${attack.attacker?.name} -> ${attack.defender?.name}`,
						);
					}

					return {
						attackId: Number(attack.id),
						timestamp: attack.started,
						attacker: {
							id: attack.attacker?.id || 0,
							name: attack.attacker?.name,
						},
						defender: {
							id: attack.defender?.id || 0,
							name: attack.defender?.name,
							factionId: attack.defender?.faction?.id,
						},
						result: attack.result,
						respect: Number(attack.modifiers?.fair_fight) || 0,
						chainPosition: chainPosition,
						isWarTarget: attack.defender?.faction?.id === enemyFaction?.id,
						assists: relatedAssists, // Assists grouped with this main attack
						modifiers: attack.modifiers,
					};
				});

				// Summary of assists integration
				const totalAssistsGrouped = attackSequence.reduce(
					(sum, attack) => sum + attack.assists.length,
					0,
				);
				console.log(
					`Total assists integrated: ${totalAssistsGrouped}/${sortedAssists.length}`,
				);

				// Calculate timeline breakdown (hourly buckets)
				const timelineBreakdown = [];
				const chainDurationHours = Math.max(
					1,
					Math.ceil((chainEnd - chainStart) / 3600),
				);

				console.log(
					`Chain duration: ${chainDurationHours} hours, attack sequence length: ${attackSequence.length}`,
				);

				for (let hour = 0; hour < chainDurationHours; hour++) {
					const hourStart = chainStart + hour * 3600;
					const hourEnd = hourStart + 3600;

					const hourAttacks = attackSequence.filter(
						(a) => a.timestamp >= hourStart && a.timestamp < hourEnd,
					);

					const uniqueAttackers = new Set(
						hourAttacks.map((a) => a.attacker.id).filter((id) => id > 0),
					);

					timelineBreakdown.push({
						hourBlock: hour,
						attackCount: hourAttacks.length,
						respectEarned: hourAttacks.reduce((sum, a) => sum + a.respect, 0),
						uniqueAttackers: uniqueAttackers.size,
					});
				}

				console.log(
					`Timeline breakdown created with ${timelineBreakdown.length} periods`,
				);

				// Calculate participation depth
				const attackerCounts = new Map();
				for (const attack of attackSequence) {
					const id = attack.attacker.id;
					if (id > 0) {
						// Only count valid attacker IDs
						attackerCounts.set(id, (attackerCounts.get(id) || 0) + 1);
					}
				}

				const contributorCounts = Array.from(attackerCounts.values());
				const participationDepth = {
					coreContributors: contributorCounts.filter((count) => count >= 10)
						.length,
					regularContributors: contributorCounts.filter(
						(count) => count >= 3 && count < 10,
					).length,
					casualContributors: contributorCounts.filter((count) => count < 3)
						.length,
				};

				console.log(
					`Participation depth: ${participationDepth.coreContributors} core, ${participationDepth.regularContributors} regular, ${participationDepth.casualContributors} casual`,
				);

				// Calculate efficiency metrics
				const timeBetweenHits = [];
				for (let i = 1; i < attackSequence.length; i++) {
					timeBetweenHits.push(
						attackSequence[i].timestamp - attackSequence[i - 1].timestamp,
					);
				}

				const averageTimeBetweenHits =
					timeBetweenHits.length > 0
						? timeBetweenHits.reduce((sum, time) => sum + time, 0) /
							timeBetweenHits.length
						: 0;

				const peakHour =
					timelineBreakdown.length > 0
						? timelineBreakdown.reduce(
								(peak, hour) =>
									hour.attackCount > peak.attackCount ? hour : peak,
								timelineBreakdown[0],
							)
						: { hourBlock: 0, attackCount: 0 };

				const totalRespect = attackSequence.reduce(
					(sum, a) => sum + a.respect,
					0,
				);
				const respectPerHour =
					chainDurationHours > 0 ? totalRespect / chainDurationHours : 0;

				const efficiencyMetrics = {
					averageTimeBetweenHits,
					peakActivity: {
						hour: peakHour.hourBlock,
						attackCount: peakHour.attackCount,
					},
					respectPerHour,
				};

				const chainMetrics = {
					timelineBreakdown,
					participationDepth,
					efficiencyMetrics,
				};

				// Build player names mapping from attack sequence data
				const playerNamesMap = new Map<number, string>();
				for (const attack of attackSequence) {
					if (attack.attacker.id && attack.attacker.name) {
						playerNamesMap.set(attack.attacker.id, attack.attacker.name);
					}
					// Also collect names from assists
					for (const assist of attack.assists) {
						if (assist.attacker.id && assist.attacker.name) {
							playerNamesMap.set(assist.attacker.id, assist.attacker.name);
						}
					}
				}

				// Enhance chain report attackers with player names
				const enhancedChainReport = {
					...chainReport,
					attackers:
						chainReport.attackers?.map((attacker) => ({
							...attacker,
							name: playerNamesMap.get(attacker.id),
						})) || [],
				};

				return {
					chainReport: enhancedChainReport,
					attackSequence,
					chainMetrics,
				};
			} catch (error) {
				console.error("Failed to fetch individual chain details:", error);
				throw new Error(
					`Failed to fetch chain details: ${error instanceof Error ? error.message : "Unknown error"}`,
				);
			}
		}),

	getAttackTimeline: factionProcedure
		.input(
			z.object({
				warId: z.number(),
				timeResolution: z.enum(["hour", "day"]).default("hour"),
			}),
		)
		.output(
			z.object({
				timeline: z.array(
					z.object({
						timestamp: z.number(),
						period: z.string(), // Human readable time period
						stats: z.object({
							totalAttacks: z.number(),
							insideHits: z.number(),
							outsideHits: z.number(),
							assists: z.number(),
							uniqueAttackers: z.number(),
							respectEarned: z.number(),
							chainAttacks: z.number(),
							nonChainAttacks: z.number(),
						}),
						topPerformers: z.array(
							z.object({
								playerId: z.number(),
								playerName: z.string().optional(),
								attacks: z.number(),
								respect: z.number(),
							}),
						),
					}),
				),
				heatmap: z.object({
					hourlyActivity: z.array(
						z.object({
							hour: z.number(), // 0-23
							intensity: z.number(), // 0-1 normalized
						}),
					),
					dailyTrends: z.array(
						z.object({
							day: z.number(), // Days since war start
							activity: z.number(),
						}),
					),
				}),
			}),
		)
		.query(async ({ ctx, input }) => {
			const userId = ctx.session?.userId;
			if (!userId) {
				throw new Error("User session not found");
			}

			const apiKey = await getUserApiKey(ctx.db, userId, ctx.env);

			try {
				const tornApi = createTornAPIWithSuspension(
					apiKey,
					ctx.db,
					userId,
					ctx.env,
				);

				// Get war timeframe
				const warReport = await tornApi.getRankedWarReport(input.warId);
				const warStart = warReport.start;
				const warEnd = warReport.end || Math.floor(Date.now() / 1000);
				const enemyFaction = warReport.factions.find(
					(f: { id: number }) => f.id !== MY_FACTION_ID,
				);

				// Fetch all attacks during war
				const collected: TornAttack[] = [];
				let cursor: number | undefined = warEnd;
				while (true) {
					const batch = await tornApi.getFactionAttacks(
						100,
						"DESC",
						cursor,
						warStart,
					);
					if (batch.length === 0) break;

					const within = batch.filter(
						(a) => a.attacker?.faction?.id === MY_FACTION_ID,
					);
					collected.push(...within);

					// Continue pagination only if we got a full batch (100 items)
					// AND the oldest attack is still within our range
					if (batch.length < 100) break;

					const oldest = batch[batch.length - 1];
					if (oldest.started <= warStart) break;

					cursor = oldest.started - 1;
				}

				// Build timeline based on resolution
				const timeline = [];
				const resolution = input.timeResolution === "hour" ? 3600 : 86400; // 1 hour or 1 day
				const warDuration = warEnd - warStart;
				const periods = Math.ceil(warDuration / resolution);

				for (let i = 0; i < periods; i++) {
					const periodStart = warStart + i * resolution;
					const periodEnd = Math.min(periodStart + resolution, warEnd);

					const periodAttacks = collected.filter(
						(a) => a.started >= periodStart && a.started < periodEnd,
					);

					// Calculate stats for this period
					const insideHits = periodAttacks.filter(
						(a) => a.defender?.faction?.id === enemyFaction?.id,
					).length;
					const assists = periodAttacks.filter(
						(a) => a.result === "Assist",
					).length;
					const chainAttacks = periodAttacks.filter(
						(a) => a.chain && a.chain > 0,
					).length;

					// Top performers for this period
					const performerMap = new Map();
					for (const attack of periodAttacks) {
						const id = attack.attacker?.id;
						const name = attack.attacker?.name;
						const respect = Number(attack.modifiers?.fair_fight) || 0;

						if (id) {
							if (!performerMap.has(id)) {
								performerMap.set(id, {
									playerId: id,
									playerName: name,
									attacks: 0,
									respect: 0,
								});
							}
							const performer = performerMap.get(id);
							performer.attacks++;
							performer.respect += respect;
						}
					}

					const topPerformers = Array.from(performerMap.values())
						.sort(
							(a: { respect: number }, b: { respect: number }) =>
								b.respect - a.respect,
						)
						.slice(0, 5);

					const uniqueAttackers = new Set(
						periodAttacks.map((a) => a.attacker?.id),
					).size;
					const respectEarned = periodAttacks.reduce(
						(sum, a) => sum + (Number(a.modifiers?.fair_fight) || 0),
						0,
					);

					timeline.push({
						timestamp: periodStart,
						period:
							input.timeResolution === "hour"
								? new Date(periodStart * 1000).toLocaleString()
								: new Date(periodStart * 1000).toLocaleDateString(),
						stats: {
							totalAttacks: periodAttacks.length,
							insideHits,
							outsideHits: periodAttacks.length - insideHits - assists,
							assists,
							uniqueAttackers,
							respectEarned,
							chainAttacks,
							nonChainAttacks: periodAttacks.length - chainAttacks,
						},
						topPerformers,
					});
				}

				// Generate heatmap data
				const hourlyActivity = Array(24)
					.fill(0)
					.map((_, hour) => ({ hour, intensity: 0 }));
				const dailyTrends = [];

				// Calculate hourly activity intensity
				for (const attack of collected) {
					const hour = new Date(attack.started * 1000).getHours();
					hourlyActivity[hour].intensity++;
				}

				// Normalize intensity (0-1)
				const maxHourlyActivity = Math.max(
					...hourlyActivity.map((h) => h.intensity),
				);
				if (maxHourlyActivity > 0) {
					for (const h of hourlyActivity) {
						h.intensity = h.intensity / maxHourlyActivity;
					}
				}

				// Calculate daily trends
				const warDays = Math.ceil(warDuration / 86400);
				for (let day = 0; day < warDays; day++) {
					const dayStart = warStart + day * 86400;
					const dayEnd = Math.min(dayStart + 86400, warEnd);
					const dayAttacks = collected.filter(
						(a) => a.started >= dayStart && a.started < dayEnd,
					).length;

					dailyTrends.push({ day, activity: dayAttacks });
				}

				return {
					timeline,
					heatmap: {
						hourlyActivity,
						dailyTrends,
					},
				};
			} catch (error) {
				console.error("Failed to fetch attack timeline:", error);
				throw new Error(
					`Failed to fetch attack timeline: ${error instanceof Error ? error.message : "Unknown error"}`,
				);
			}
		}),

	getPlayerWarPerformance: factionProcedure
		.input(
			z.object({
				warId: z.number(),
				playerId: z.number().optional(), // If not provided, returns all players
			}),
		)
		.output(
			z.object({
				playerDetails: z.array(
					z.object({
						playerId: z.number(),
						playerName: z.string().optional(),
						overallStats: z.object({
							totalAttacks: z.number(),
							chainAttacks: z.number(),
							nonChainAttacks: z.number(),
							insideHits: z.number(),
							outsideHits: z.number(),
							assists: z.number(),
							totalRespect: z.number(),
							averageRespect: z.number(),
							bestRespect: z.number(),
							activeHours: z.number(), // Hours during which player was active
							participationRate: z.number(), // Percentage of war duration player was active
						}),
						performanceTrends: z.object({
							hourlyBreakdown: z.array(
								z.object({
									hour: z.number(),
									attacks: z.number(),
									respect: z.number(),
									efficiency: z.number(), // respect per attack
								}),
							),
							dailyBreakdown: z.array(
								z.object({
									day: z.number(),
									attacks: z.number(),
									respect: z.number(),
									peakHour: z.number(),
								}),
							),
						}),
						chainContributions: z.array(
							z.object({
								chainId: z.number(),
								attacks: z.number(),
								respect: z.number(),
								warHits: z.number(),
								assists: z.number(),
								chainPosition: z.string(), // "Early", "Mid", "Late"
								performance: z.string(), // "Above Average", "Average", "Below Average"
							}),
						),
						battleEffectiveness: z.object({
							hitAccuracy: z.number(), // Success rate
							respectEfficiency: z.number(), // respect per successful hit
							targetPrioritization: z.object({
								insideTargetRatio: z.number(),
								warTargetFocus: z.number(),
							}),
							timingAnalysis: z.object({
								averageTimeBetweenAttacks: z.number(),
								peakActivityWindow: z.string(),
								consistencyScore: z.number(), // How consistent their activity was
							}),
						}),
						comparativeRanking: z.object({
							respectRank: z.number(),
							attackCountRank: z.number(),
							efficiencyRank: z.number(),
							participationRank: z.number(),
						}),
					}),
				),
			}),
		)
		.query(async ({ ctx, input }) => {
			const userId = ctx.session?.userId;
			if (!userId) {
				throw new Error("User session not found");
			}

			const apiKey = await getUserApiKey(ctx.db, userId, ctx.env);

			try {
				const tornApi = createTornAPIWithSuspension(
					apiKey,
					ctx.db,
					userId,
					ctx.env,
				);

				// Get war details and timeframe
				const warReport = await tornApi.getRankedWarReport(input.warId);
				const warStart = warReport.start;
				const warEnd = warReport.end || Math.floor(Date.now() / 1000);
				const warDurationHours = (warEnd - warStart) / 3600;
				const enemyFaction = warReport.factions.find(
					(f: { id: number }) => f.id !== MY_FACTION_ID,
				);

				// Fetch all faction attacks during war
				const collected: TornAttack[] = [];
				let cursor: number | undefined = warEnd;
				while (true) {
					const batch = await tornApi.getFactionAttacks(
						100,
						"DESC",
						cursor,
						warStart,
					);
					if (batch.length === 0) break;

					const within = batch.filter(
						(a) => a.attacker?.faction?.id === MY_FACTION_ID,
					);
					collected.push(...within);

					// Continue pagination only if we got a full batch (100 items)
					// AND the oldest attack is still within our range
					if (batch.length < 100) break;

					const oldest = batch[batch.length - 1];
					if (oldest.started <= warStart) break;

					cursor = oldest.started - 1;
				}

				// Get chain reports for chain analysis - fetch all chains in war timeframe
				const relevantChains = await getAllChainsInTimeRange(
					tornApi,
					warStart,
					warEnd,
				);

				const chainReports: TornChainReport[] = [];
				for (const chain of relevantChains) {
					try {
						const report = await tornApi.getChainReport(chain.id);
						chainReports.push(report);
					} catch (err) {
						console.error(`Failed to fetch chain report ${chain.id}:`, err);
					}
				}

				// Group attacks by player
				const playerAttacks = new Map<number, TornAttack[]>();
				for (const attack of collected) {
					const attackerId = attack.attacker?.id;
					if (attackerId) {
						if (!playerAttacks.has(attackerId)) {
							playerAttacks.set(attackerId, []);
						}
						const playerAttacksList = playerAttacks.get(attackerId);
						if (playerAttacksList) {
							playerAttacksList.push(attack);
						}
					}
				}

				// Filter to specific player if requested
				const playersToAnalyze = input.playerId
					? [input.playerId]
					: Array.from(playerAttacks.keys());

				const playerDetails = [];

				for (const playerId of playersToAnalyze) {
					const attacks = playerAttacks.get(playerId) || [];
					const playerName = attacks[0]?.attacker?.name;

					if (attacks.length === 0) continue;

					// Calculate overall stats
					const chainAttacks = attacks.filter((a) => a.chain && a.chain > 0);
					const nonChainAttacks = attacks.filter(
						(a) => !a.chain || a.chain === 0,
					);
					const insideHits = attacks.filter(
						(a) => a.defender?.faction?.id === enemyFaction?.id,
					);
					const assists = attacks.filter((a) => a.result === "Assist");
					const successfulAttacks = attacks.filter(
						(a) => a.result !== "Assist" && a.result !== "Lost",
					);

					const respectValues = attacks.map(
						(a) => Number(a.modifiers?.fair_fight) || 0,
					);
					const totalRespect = respectValues.reduce((sum, r) => sum + r, 0);
					const averageRespect =
						attacks.length > 0 ? totalRespect / attacks.length : 0;
					const bestRespect = Math.max(...respectValues, 0);

					// Calculate active hours
					const attackHours = new Set(
						attacks.map((a) => Math.floor(a.started / 3600)),
					);
					const activeHours = attackHours.size;
					const participationRate = (activeHours / warDurationHours) * 100;

					// Performance trends - hourly breakdown
					const hourlyStats = new Map<
						number,
						{ attacks: number; respect: number }
					>();
					for (const attack of attacks) {
						const hour = Math.floor((attack.started - warStart) / 3600);
						if (!hourlyStats.has(hour)) {
							hourlyStats.set(hour, { attacks: 0, respect: 0 });
						}
						const stats = hourlyStats.get(hour);
						if (stats) {
							stats.attacks++;
							stats.respect += Number(attack.modifiers?.fair_fight) || 0;
						}
					}

					const hourlyBreakdown = Array.from(hourlyStats.entries()).map(
						([hour, stats]) => ({
							hour,
							attacks: stats.attacks,
							respect: stats.respect,
							efficiency: stats.attacks > 0 ? stats.respect / stats.attacks : 0,
						}),
					);

					// Daily breakdown
					const dailyStats = new Map<
						number,
						{ attacks: number; respect: number; hours: Set<number> }
					>();
					for (const attack of attacks) {
						const day = Math.floor((attack.started - warStart) / 86400);
						const hour = new Date(attack.started * 1000).getHours();
						if (!dailyStats.has(day)) {
							dailyStats.set(day, { attacks: 0, respect: 0, hours: new Set() });
						}
						const stats = dailyStats.get(day);
						if (stats) {
							stats.attacks++;
							stats.respect += Number(attack.modifiers?.fair_fight) || 0;
							stats.hours.add(hour);
						}
					}

					const dailyBreakdown = Array.from(dailyStats.entries()).map(
						([day, stats]) => {
							const hourActivity = Array.from(stats.hours);
							const peakHour = hourActivity.reduce((peak, hour) => {
								const hourAttacks = attacks.filter((a) => {
									const attackDay = Math.floor((a.started - warStart) / 86400);
									const attackHour = new Date(a.started * 1000).getHours();
									return attackDay === day && attackHour === hour;
								}).length;
								const peakAttacks = attacks.filter((a) => {
									const attackDay = Math.floor((a.started - warStart) / 86400);
									const attackHour = new Date(a.started * 1000).getHours();
									return attackDay === day && attackHour === peak;
								}).length;
								return hourAttacks > peakAttacks ? hour : peak;
							}, hourActivity[0] || 0);

							return {
								day,
								attacks: stats.attacks,
								respect: stats.respect,
								peakHour,
							};
						},
					);

					// Chain contributions
					const chainContributions = chainReports
						.map((chainReport) => {
							const playerChainData = chainReport.attackers?.find(
								(a) => a.id === playerId,
							);
							if (!playerChainData) return null;

							const chainAttacksForPlayer = attacks.filter(
								(a) => a.chain === chainReport.id,
							);
							const warHitsInChain = chainAttacksForPlayer.filter(
								(a) => a.defender?.faction?.id === enemyFaction?.id,
							).length;
							const assistsInChain = chainAttacksForPlayer.filter(
								(a) => a.result === "Assist",
							).length;

							// Determine chain position (Early: first 25%, Mid: 25-75%, Late: 75%+)
							const totalChainHits = chainReport.details.chain;
							const firstAttackPosition = Math.min(
								...chainAttacksForPlayer.map((_, i) => i + 1),
							);
							const positionRatio = firstAttackPosition / totalChainHits;
							let chainPosition = "Mid";
							if (positionRatio <= 0.25) chainPosition = "Early";
							else if (positionRatio >= 0.75) chainPosition = "Late";

							// Performance compared to chain average
							const avgChainRespect =
								chainReport.details.respect / chainReport.details.chain;
							const playerAvgRespect = playerChainData.respect.average;
							let performance = "Average";
							if (playerAvgRespect > avgChainRespect * 1.2)
								performance = "Above Average";
							else if (playerAvgRespect < avgChainRespect * 0.8)
								performance = "Below Average";

							return {
								chainId: chainReport.id,
								attacks: playerChainData.attacks.total,
								respect: playerChainData.respect.total,
								warHits: warHitsInChain,
								assists: assistsInChain,
								chainPosition,
								performance,
							};
						})
						.filter(
							(
								contribution,
							): contribution is NonNullable<typeof contribution> =>
								contribution !== null,
						);

					// Battle effectiveness
					const hitAccuracy =
						attacks.length > 0
							? (successfulAttacks.length / attacks.length) * 100
							: 0;
					const respectEfficiency =
						successfulAttacks.length > 0
							? successfulAttacks.reduce(
									(sum, a) => sum + (Number(a.modifiers?.fair_fight) || 0),
									0,
								) / successfulAttacks.length
							: 0;

					const insideTargetRatio =
						attacks.length > 0 ? (insideHits.length / attacks.length) * 100 : 0;
					const warTargetFocus =
						nonChainAttacks.length > 0
							? (nonChainAttacks.filter(
									(a) => a.defender?.faction?.id === enemyFaction?.id,
								).length /
									nonChainAttacks.length) *
								100
							: 0;

					// Timing analysis
					const sortedAttacks = attacks.sort((a, b) => a.started - b.started);
					const timeBetweenAttacks = [];
					for (let i = 1; i < sortedAttacks.length; i++) {
						timeBetweenAttacks.push(
							sortedAttacks[i].started - sortedAttacks[i - 1].started,
						);
					}
					const averageTimeBetweenAttacks =
						timeBetweenAttacks.length > 0
							? timeBetweenAttacks.reduce((sum, time) => sum + time, 0) /
								timeBetweenAttacks.length
							: 0;

					// Peak activity window
					const hourCounts = new Map<number, number>();
					for (const attack of attacks) {
						const hour = new Date(attack.started * 1000).getHours();
						hourCounts.set(hour, (hourCounts.get(hour) || 0) + 1);
					}
					const peakHour = Array.from(hourCounts.entries()).reduce(
						(peak, [hour, count]) => (count > peak[1] ? [hour, count] : peak),
						[0, 0],
					)[0];
					const peakActivityWindow = `${peakHour}:00-${peakHour + 1}:00`;

					// Consistency score (lower variance in hourly activity = higher consistency)
					const hourlyAttackCounts = Array.from(hourCounts.values());
					const avgHourlyAttacks =
						hourlyAttackCounts.reduce((sum, count) => sum + count, 0) /
						hourlyAttackCounts.length;
					const variance =
						hourlyAttackCounts.reduce(
							(sum, count) => sum + (count - avgHourlyAttacks) ** 2,
							0,
						) / hourlyAttackCounts.length;
					const consistencyScore = Math.max(
						0,
						100 - (Math.sqrt(variance) / avgHourlyAttacks) * 100,
					);

					playerDetails.push({
						playerId,
						playerName,
						overallStats: {
							totalAttacks: attacks.length,
							chainAttacks: chainAttacks.length,
							nonChainAttacks: nonChainAttacks.length,
							insideHits: insideHits.length,
							outsideHits: attacks.length - insideHits.length - assists.length,
							assists: assists.length,
							totalRespect,
							averageRespect,
							bestRespect,
							activeHours,
							participationRate,
						},
						performanceTrends: {
							hourlyBreakdown,
							dailyBreakdown,
						},
						chainContributions,
						battleEffectiveness: {
							hitAccuracy,
							respectEfficiency,
							targetPrioritization: {
								insideTargetRatio,
								warTargetFocus,
							},
							timingAnalysis: {
								averageTimeBetweenAttacks,
								peakActivityWindow,
								consistencyScore,
							},
						},
						comparativeRanking: {
							respectRank: 0, // Will be calculated after all players are processed
							attackCountRank: 0,
							efficiencyRank: 0,
							participationRank: 0,
						},
					});
				}

				// Calculate comparative rankings
				playerDetails.sort(
					(a, b) => b.overallStats.totalRespect - a.overallStats.totalRespect,
				);
				playerDetails.forEach((player, index) => {
					player.comparativeRanking.respectRank = index + 1;
				});

				playerDetails.sort(
					(a, b) => b.overallStats.totalAttacks - a.overallStats.totalAttacks,
				);
				playerDetails.forEach((player, index) => {
					player.comparativeRanking.attackCountRank = index + 1;
				});

				playerDetails.sort(
					(a, b) =>
						b.overallStats.averageRespect - a.overallStats.averageRespect,
				);
				playerDetails.forEach((player, index) => {
					player.comparativeRanking.efficiencyRank = index + 1;
				});

				playerDetails.sort(
					(a, b) =>
						b.overallStats.participationRate - a.overallStats.participationRate,
				);
				playerDetails.forEach((player, index) => {
					player.comparativeRanking.participationRank = index + 1;
				});

				return { playerDetails };
			} catch (error) {
				console.error("Failed to fetch player war performance:", error);
				throw new Error(
					`Failed to fetch player performance: ${error instanceof Error ? error.message : "Unknown error"}`,
				);
			}
		}),

	// Admin endpoints for cache management
	clearWarCache: factionProcedure
		.input(z.object({ warId: z.number() }))
		.output(z.object({ success: z.boolean() }))
		.mutation(async ({ ctx, input }) => {
			const userId = ctx.session?.userId;
			if (!userId) {
				throw new Error("User session not found");
			}

			const apiKey = await getUserApiKey(ctx.db, userId, ctx.env);

			try {
				const tornApi = createTornAPIWithSuspension(
					apiKey,
					ctx.db,
					userId,
					ctx.env,
				);
				const warCache = new WarCacheService(ctx.db, tornApi);
				await warCache.clearWarCache(input.warId);

				return { success: true };
			} catch (error) {
				console.error("Failed to clear war cache:", error);
				throw new Error(
					`Failed to clear war cache: ${error instanceof Error ? error.message : "Unknown error"}`,
				);
			}
		}),

	getWarCacheStats: factionProcedure
		.output(
			z.object({
				cachedWarReports: z.number(),
				cachedChainReports: z.number(),
				cachedAttacks: z.number(),
				cachedStats: z.number(),
			}),
		)
		.query(async ({ ctx }) => {
			const userId = ctx.session?.userId;
			if (!userId) {
				throw new Error("User session not found");
			}

			const apiKey = await getUserApiKey(ctx.db, userId, ctx.env);

			try {
				const tornApi = createTornAPIWithSuspension(
					apiKey,
					ctx.db,
					userId,
					ctx.env,
				);
				const warCache = new WarCacheService(ctx.db, tornApi);
				const stats = await warCache.getCacheStats();

				return stats;
			} catch (error) {
				console.error("Failed to get cache stats:", error);
				throw new Error(
					`Failed to get cache stats: ${error instanceof Error ? error.message : "Unknown error"}`,
				);
			}
		}),
});
