.theme-default {
	/* Colors */
	--background: oklch(1.0 0 0);
	--foreground: oklch(0.2644 0 0);
	--card: oklch(1.0 0 0);
	--card-foreground: oklch(0.2644 0 0);
	--popover: oklch(1.0 0 0);
	--popover-foreground: oklch(0.2644 0 0);
	--primary: oklch(0.3261 0 0);
	--primary-foreground: oklch(0.9886 0 0);
	--secondary: oklch(0.9772 0 0);
	--secondary-foreground: oklch(0.3261 0 0);
	--muted: oklch(0.9772 0 0);
	--muted-foreground: oklch(0.646 0 0);
	--accent: oklch(0.9772 0 0);
	--accent-foreground: oklch(0.3261 0 0);
	--destructive: oklch(0.6201 0.2092 25.7747);
	--destructive-foreground: oklch(1.0 0 0);
	--border: oklch(0.9404 0 0);
	--input: oklch(0.9404 0 0);
	--ring: oklch(0.7716 0 0);

	/* Charts */
	--chart-1: oklch(0.8241 0.1251 84.4866);
	--chart-2: oklch(0.8006 0.1116 203.6044);
	--chart-3: oklch(0.4198 0.1693 266.7798);
	--chart-4: oklch(0.9214 0.0762 125.5777);
	--chart-5: oklch(0.9151 0.1032 116.1913);

	/* Sidebar */
	--sidebar: oklch(0.9886 0 0);
	--sidebar-foreground: oklch(0.2644 0 0);
	--sidebar-primary: oklch(0.3261 0 0);
	--sidebar-primary-foreground: oklch(0.9886 0 0);
	--sidebar-accent: oklch(0.9772 0 0);
	--sidebar-accent-foreground: oklch(0.3261 0 0);
	--sidebar-border: oklch(0.9404 0 0);
	--sidebar-ring: oklch(0.7716 0 0);

	/* Typography */
	--font-sans:
		ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
	--font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
	--font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
		"Liberation Mono", "Courier New", monospace;

	/* Layout */
	--radius: 0.625rem;

	/* Shadows */
	--shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
	--shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
	--shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px
		hsl(0 0% 0% / 0.1);
	--shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
	--shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px
		hsl(0 0% 0% / 0.1);
	--shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px
		hsl(0 0% 0% / 0.1);
	--shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px
		hsl(0 0% 0% / 0.1);
	--shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

.theme-default.dark {
	/* Colors */
	--background: oklch(0.1405 0.0044 285.8238);
	--foreground: oklch(0.9848 0 0);
	--card: oklch(0.1405 0.0044 285.8238);
	--card-foreground: oklch(0.9848 0 0);
	--popover: oklch(0.1405 0.0044 285.8238);
	--popover-foreground: oklch(0.9848 0 0);
	--primary: oklch(0.9848 0 0);
	--primary-foreground: oklch(0.2103 0.0059 285.8835);
	--secondary: oklch(0.2741 0.0055 286.0329);
	--secondary-foreground: oklch(0.9848 0 0);
	--muted: oklch(0.2741 0.0055 286.0329);
	--muted-foreground: oklch(0.7119 0.0129 286.0684);
	--accent: oklch(0.2741 0.0055 286.0329);
	--accent-foreground: oklch(0.9848 0 0);
	--destructive: oklch(0.3959 0.1331 25.7205);
	--destructive-foreground: oklch(0.971 0.0127 17.3758);
	--border: oklch(0.2741 0.0055 286.0329);
	--input: oklch(0.2741 0.0055 286.0329);
	--ring: oklch(0.8709 0.0055 286.2853);

	/* Charts */
	--chart-1: oklch(0.5292 0.1931 262.1292);
	--chart-2: oklch(0.6983 0.1337 165.4626);
	--chart-3: oklch(0.7232 0.15 60.6307);
	--chart-4: oklch(0.6192 0.2037 312.7283);
	--chart-5: oklch(0.6123 0.2093 6.3856);

	/* Sidebar */
	--sidebar: oklch(0.2103 0.0059 285.8835);
	--sidebar-foreground: oklch(0.9676 0.0013 286.3752);
	--sidebar-primary: oklch(0.4878 0.217 264.3876);
	--sidebar-primary-foreground: oklch(1.0 0 0);
	--sidebar-accent: oklch(0.2741 0.0055 286.0329);
	--sidebar-accent-foreground: oklch(0.9676 0.0013 286.3752);
	--sidebar-border: oklch(0.2741 0.0055 286.0329);
	--sidebar-ring: oklch(0.8709 0.0055 286.2853);

	/* Typography */
	--font-sans:
		ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
	--font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
	--font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
		"Liberation Mono", "Courier New", monospace;

	/* Layout */
	--radius: 0.625rem;

	/* Shadows */
	--shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
	--shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
	--shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px
		hsl(0 0% 0% / 0.1);
	--shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
	--shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px
		hsl(0 0% 0% / 0.1);
	--shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px
		hsl(0 0% 0% / 0.1);
	--shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px
		hsl(0 0% 0% / 0.1);
	--shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}
