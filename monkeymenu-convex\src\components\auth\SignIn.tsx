
import { SignIn as ClerkSignIn } from '@clerk/clerk-react'
import { Shield } from 'lucide-react'

export function SignIn() {

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-600">
            <Shield className="h-8 w-8 text-white" />
          </div>
          <h2 className="mt-6 text-3xl font-bold tracking-tight text-white">
            Welcome to MonkeyMenu
          </h2>
          <p className="mt-2 text-sm text-slate-400">
            Sign in to access your faction tools
          </p>
        </div>
        
        <div className="rounded-lg bg-slate-800/50 backdrop-blur-sm p-6 shadow-xl">
          <ClerkSignIn 
            routing="virtual"
            signUpUrl="/sign-up"
            afterSignInUrl="/"
            appearance={{
              elements: {
                rootBox: 'mx-auto',
                card: 'bg-transparent shadow-none',
                headerTitle: 'text-white',
                headerSubtitle: 'text-slate-400',
                socialButtonsBlockButton: 'bg-slate-700 hover:bg-slate-600 text-white',
                formFieldLabel: 'text-slate-300',
                formFieldInput: 'bg-slate-700 border-slate-600 text-white placeholder-slate-400',
                footerActionText: 'text-slate-400',
                footerActionLink: 'text-blue-400 hover:text-blue-300',
                identityPreviewText: 'text-white',
                identityPreviewEditButton: 'text-blue-400 hover:text-blue-300',
                alertText: 'text-red-400',
                formButtonPrimary: 'bg-blue-600 hover:bg-blue-700 text-white',
              },
            }}
          />
        </div>
      </div>
    </div>
  )
}