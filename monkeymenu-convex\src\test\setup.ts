import '@testing-library/jest-dom'
import { vi } from 'vitest'

import React from 'react'

// Mock environment variables
vi.stubEnv('VITE_CLERK_PUBLISHABLE_KEY', 'pk_test_mock_key')
vi.stubEnv('VITE_CONVEX_URL', 'https://mock-convex-url.convex.cloud')

// Mock Clerk
vi.mock('@clerk/clerk-react', () => ({
  useUser: vi.fn(() => ({
    user: {
      id: 'test-user-id',
      firstName: 'Test',
      lastName: 'User',
      emailAddresses: [{ emailAddress: '<EMAIL>' }],
    },
    isLoaded: true,
    isSignedIn: true,
  })),
  ClerkProvider: ({ children }: { children: React.ReactNode }) => children,
  SignedIn: ({ children }: { children: React.ReactNode }) => children,
  SignedOut: () => null,
  UserButton: () => React.createElement('div', { 'data-testid': 'user-button' }, 'User Button'),
  useAuth: vi.fn(() => ({
    isSignedIn: true,
    userId: 'test-user-id',
    getToken: vi.fn(() => Promise.resolve('mock-token')),
  })),
}))

// Mock Convex
vi.mock('convex/react', () => ({
  useQuery: vi.fn(),
  useMutation: vi.fn(),
  ConvexProvider: ({ children }: { children: React.ReactNode }) => children,
  ConvexReactClient: vi.fn(() => ({})),
}))

vi.mock('convex/react-clerk', () => ({
  ConvexProviderWithClerk: ({ children }: { children: React.ReactNode }) => children,
}))

// Mock Convex generated API
vi.mock('../convex/_generated/api', () => ({
  api: {
    users: {
      getCurrentUser: 'users:getCurrentUser',
      create: 'users:create',
    },
    banking: {
      getUserCashBalance: 'banking:getUserCashBalance',
      createTransaction: 'banking:createTransaction',
    },
    announcements: {
      create: 'announcements:create',
    },
    targets: {
      getTargets: 'targets:getTargets',
      create: 'targets:create',
    },
    wars: {
      create: 'wars:create',
    },
    guides: {
      create: 'guides:create',
    },
  },
}))

// Mock hooks
vi.mock('../hooks/useSession', () => ({
  useSession: vi.fn(() => ({
    user: {
      _id: 'test-user-id',
      clerkId: 'clerk-test-user',
      username: 'testuser',
      permissions: ['user', 'banking'],
    },
    isLoaded: true,
    isSignedIn: true,
  })),
}))

vi.mock('../hooks/usePermissions', () => ({
  usePermissions: vi.fn(() => ({
    hasPermission: vi.fn(() => true),
    canAccessBanking: true,
    canManageBanking: false,
    canViewTargets: vi.fn(() => true),
    canManageTargets: false,
    canViewWars: true,
    canManageWars: false,
    canViewGuides: vi.fn(() => true),
    canManageGuides: false,
    canViewAnnouncements: vi.fn(() => true),
    canManageAnnouncements: false,
    canViewAnalytics: true,
    canAccessAdmin: false,
  })),
}))

// Mock TanStack Router
vi.mock('@tanstack/react-router', () => ({
  createRootRoute: vi.fn(() => ({
    component: vi.fn(),
  })),
  createRouter: vi.fn(),
  RouterProvider: ({ children }: { children: React.ReactNode }) => children,
  Link: ({ children, to, ...props }: any) => 
    React.createElement('a', { href: to, ...props }, children),
  Outlet: () => React.createElement('div', { 'data-testid': 'outlet' }, 'Outlet'),
  useNavigate: vi.fn(() => vi.fn()),
  useRouter: vi.fn(() => ({
    navigate: vi.fn(),
  })),
}))

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Suppress console warnings in tests
const originalConsoleWarn = console.warn
console.warn = (...args: any[]) => {
  if (
    typeof args[0] === 'string' &&
    args[0].includes('Warning: ReactDOM.render is no longer supported')
  ) {
    return
  }
  originalConsoleWarn(...args)
}