#!/usr/bin/env node

/**
 * Data migration script for MonkeyMenu
 * Migrates data from the original system to Convex
 */

const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  // Source data paths (adjust these based on your original system)
  sourceDataPath: process.env.SOURCE_DATA_PATH || './migration-data',
  batchSize: 100,
  
  // Convex configuration will be loaded from environment
  convexUrl: process.env.VITE_CONVEX_URL,
  
  // Migration options
  dryRun: process.argv.includes('--dry-run'),
  verbose: process.argv.includes('--verbose'),
  force: process.argv.includes('--force'),
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  if (CONFIG.verbose || color === 'red' || color === 'green') {
    console.log(`${colors[color]}${message}${colors.reset}`);
  }
}

function error(message) {
  log(`❌ ${message}`, 'red');
}

function success(message) {
  log(`✅ ${message}`, 'green');
}

function warning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function info(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Data transformation functions
const transforms = {
  // Transform user data from original format to Convex format
  transformUser: (originalUser) => ({
    clerkId: originalUser.authId || `migrated_${originalUser.id}`,
    username: originalUser.username,
    tornId: originalUser.tornId,
    permissions: originalUser.roles || ['user'],
    discordId: originalUser.discordId || null,
    bankingEnabled: originalUser.banking?.enabled ?? true,
    createdAt: originalUser.createdAt || Date.now(),
    lastLogin: originalUser.lastLogin || Date.now(),
  }),

  // Transform banking data
  transformTransaction: (originalTransaction, userMap) => ({
    userId: userMap[originalTransaction.userId],
    type: originalTransaction.type,
    amount: originalTransaction.amount,
    status: originalTransaction.status || 'completed',
    description: originalTransaction.description || 'Migrated transaction',
    createdAt: originalTransaction.timestamp || Date.now(),
    processedAt: originalTransaction.processedAt || originalTransaction.timestamp || Date.now(),
    processedBy: userMap[originalTransaction.processedBy] || null,
  }),

  // Transform announcements
  transformAnnouncement: (originalAnnouncement, userMap) => ({
    title: originalAnnouncement.title,
    content: originalAnnouncement.content,
    priority: originalAnnouncement.priority || 'normal',
    createdBy: userMap[originalAnnouncement.authorId],
    createdAt: originalAnnouncement.createdAt || Date.now(),
    expiresAt: originalAnnouncement.expiresAt || (Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
    isActive: originalAnnouncement.active ?? true,
  }),

  // Transform targets
  transformTarget: (originalTarget) => ({
    tornId: originalTarget.tornId,
    username: originalTarget.username,
    level: originalTarget.level,
    faction: originalTarget.faction || null,
    status: originalTarget.status || 'active',
    respect: originalTarget.respect || null,
    fairFight: originalTarget.fairFight || null,
    battleStats: originalTarget.stats ? {
      strength: originalTarget.stats.strength,
      defense: originalTarget.stats.defense,
      speed: originalTarget.stats.speed,
      dexterity: originalTarget.stats.dexterity,
    } : null,
    lastUpdated: originalTarget.lastUpdated || Date.now(),
  }),

  // Transform wars
  transformWar: (originalWar, userMap) => ({
    name: originalWar.name,
    enemyFaction: originalWar.enemy,
    startTime: originalWar.startTime,
    endTime: originalWar.endTime,
    status: originalWar.status || 'active',
    ourScore: originalWar.ourScore || 0,
    theirScore: originalWar.theirScore || 0,
    attacks: (originalWar.attacks || []).map(attack => ({
      attackerId: userMap[attack.attackerId],
      defenderId: attack.defenderId,
      result: attack.result,
      respect: attack.respect || 0,
      timestamp: attack.timestamp,
    })),
    createdBy: userMap[originalWar.createdBy],
    createdAt: originalWar.createdAt || Date.now(),
  }),

  // Transform guides
  transformGuide: (originalGuide, userMap) => ({
    title: originalGuide.title,
    content: originalGuide.content,
    category: originalGuide.category || 'general',
    tags: originalGuide.tags || [],
    isPublished: originalGuide.published ?? true,
    createdBy: userMap[originalGuide.authorId],
    createdAt: originalGuide.createdAt || Date.now(),
    updatedAt: originalGuide.updatedAt || originalGuide.createdAt || Date.now(),
  }),
};

// Mock Convex client for testing
class MockConvexClient {
  async mutation(name, args) {
    log(`[DRY RUN] Would call mutation ${name} with args:`, 'cyan');
    if (CONFIG.verbose) {
      console.log(JSON.stringify(args, null, 2));
    }
    return { _id: `mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}` };
  }

  async query(name, args) {
    log(`[DRY RUN] Would call query ${name} with args:`, 'cyan');
    if (CONFIG.verbose) {
      console.log(JSON.stringify(args, null, 2));
    }
    return [];
  }
}

// Main migration class
class DataMigrator {
  constructor() {
    this.convex = CONFIG.dryRun ? new MockConvexClient() : null; // Real client would be initialized here
    this.stats = {
      users: { migrated: 0, errors: 0 },
      transactions: { migrated: 0, errors: 0 },
      announcements: { migrated: 0, errors: 0 },
      targets: { migrated: 0, errors: 0 },
      wars: { migrated: 0, errors: 0 },
      guides: { migrated: 0, errors: 0 },
    };
    this.userMap = new Map(); // Original ID -> Convex ID mapping
  }

  async loadSourceData(filename) {
    const filePath = path.join(CONFIG.sourceDataPath, filename);
    
    if (!fs.existsSync(filePath)) {
      warning(`Source file not found: ${filePath}`);
      return [];
    }

    try {
      const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      info(`Loaded ${data.length} records from ${filename}`);
      return data;
    } catch (err) {
      error(`Failed to load ${filename}: ${err.message}`);
      return [];
    }
  }

  async migrateUsers() {
    log('🔄 Migrating users...', 'bright');
    
    const users = await this.loadSourceData('users.json');
    
    for (let i = 0; i < users.length; i += CONFIG.batchSize) {
      const batch = users.slice(i, i + CONFIG.batchSize);
      
      for (const user of batch) {
        try {
          const transformedUser = transforms.transformUser(user);
          const result = await this.convex.mutation('users:create', transformedUser);
          
          this.userMap.set(user.id, result._id);
          this.stats.users.migrated++;
          
          if (CONFIG.verbose) {
            log(`Migrated user: ${user.username}`, 'green');
          }
        } catch (err) {
          error(`Failed to migrate user ${user.username}: ${err.message}`);
          this.stats.users.errors++;
        }
      }
      
      // Progress update
      log(`Users: ${Math.min(i + CONFIG.batchSize, users.length)}/${users.length}`, 'blue');
    }
    
    success(`Migrated ${this.stats.users.migrated} users (${this.stats.users.errors} errors)`);
  }

  async migrateTransactions() {
    log('🔄 Migrating transactions...', 'bright');
    
    const transactions = await this.loadSourceData('transactions.json');
    
    for (let i = 0; i < transactions.length; i += CONFIG.batchSize) {
      const batch = transactions.slice(i, i + CONFIG.batchSize);
      
      for (const transaction of batch) {
        try {
          const transformedTransaction = transforms.transformTransaction(transaction, this.userMap);
          await this.convex.mutation('banking:createTransaction', transformedTransaction);
          
          this.stats.transactions.migrated++;
        } catch (err) {
          error(`Failed to migrate transaction ${transaction.id}: ${err.message}`);
          this.stats.transactions.errors++;
        }
      }
      
      log(`Transactions: ${Math.min(i + CONFIG.batchSize, transactions.length)}/${transactions.length}`, 'blue');
    }
    
    success(`Migrated ${this.stats.transactions.migrated} transactions (${this.stats.transactions.errors} errors)`);
  }

  async migrateAnnouncements() {
    log('🔄 Migrating announcements...', 'bright');
    
    const announcements = await this.loadSourceData('announcements.json');
    
    for (const announcement of announcements) {
      try {
        const transformedAnnouncement = transforms.transformAnnouncement(announcement, this.userMap);
        await this.convex.mutation('announcements:create', transformedAnnouncement);
        
        this.stats.announcements.migrated++;
      } catch (err) {
        error(`Failed to migrate announcement ${announcement.id}: ${err.message}`);
        this.stats.announcements.errors++;
      }
    }
    
    success(`Migrated ${this.stats.announcements.migrated} announcements (${this.stats.announcements.errors} errors)`);
  }

  async migrateTargets() {
    log('🔄 Migrating targets...', 'bright');
    
    const targets = await this.loadSourceData('targets.json');
    
    for (let i = 0; i < targets.length; i += CONFIG.batchSize) {
      const batch = targets.slice(i, i + CONFIG.batchSize);
      
      for (const target of batch) {
        try {
          const transformedTarget = transforms.transformTarget(target);
          await this.convex.mutation('targets:create', transformedTarget);
          
          this.stats.targets.migrated++;
        } catch (err) {
          error(`Failed to migrate target ${target.tornId}: ${err.message}`);
          this.stats.targets.errors++;
        }
      }
      
      log(`Targets: ${Math.min(i + CONFIG.batchSize, targets.length)}/${targets.length}`, 'blue');
    }
    
    success(`Migrated ${this.stats.targets.migrated} targets (${this.stats.targets.errors} errors)`);
  }

  async migrateWars() {
    log('🔄 Migrating wars...', 'bright');
    
    const wars = await this.loadSourceData('wars.json');
    
    for (const war of wars) {
      try {
        const transformedWar = transforms.transformWar(war, this.userMap);
        await this.convex.mutation('wars:create', transformedWar);
        
        this.stats.wars.migrated++;
      } catch (err) {
        error(`Failed to migrate war ${war.id}: ${err.message}`);
        this.stats.wars.errors++;
      }
    }
    
    success(`Migrated ${this.stats.wars.migrated} wars (${this.stats.wars.errors} errors)`);
  }

  async migrateGuides() {
    log('🔄 Migrating guides...', 'bright');
    
    const guides = await this.loadSourceData('guides.json');
    
    for (const guide of guides) {
      try {
        const transformedGuide = transforms.transformGuide(guide, this.userMap);
        await this.convex.mutation('guides:create', transformedGuide);
        
        this.stats.guides.migrated++;
      } catch (err) {
        error(`Failed to migrate guide ${guide.id}: ${err.message}`);
        this.stats.guides.errors++;
      }
    }
    
    success(`Migrated ${this.stats.guides.migrated} guides (${this.stats.guides.errors} errors)`);
  }

  async run() {
    log('🚀 Starting data migration...', 'bright');
    
    if (CONFIG.dryRun) {
      warning('Running in DRY RUN mode - no data will be actually migrated');
    }
    
    if (!fs.existsSync(CONFIG.sourceDataPath)) {
      error(`Source data path does not exist: ${CONFIG.sourceDataPath}`);
      process.exit(1);
    }
    
    try {
      // Migration order is important due to dependencies
      await this.migrateUsers();
      await this.migrateTransactions();
      await this.migrateAnnouncements();
      await this.migrateTargets();
      await this.migrateWars();
      await this.migrateGuides();
      
      // Final report
      log('\n📊 Migration Summary:', 'bright');
      Object.entries(this.stats).forEach(([type, stats]) => {
        const total = stats.migrated + stats.errors;
        const successRate = total > 0 ? ((stats.migrated / total) * 100).toFixed(1) : '0';
        log(`${type}: ${stats.migrated}/${total} (${successRate}% success)`, 'cyan');
      });
      
      success('🎉 Migration completed!');
      
    } catch (err) {
      error(`Migration failed: ${err.message}`);
      process.exit(1);
    }
  }
}

// Script entry point
if (require.main === module) {
  // Parse command line arguments
  if (process.argv.includes('--help')) {
    console.log(`
Data Migration Script for MonkeyMenu

Usage: node migrate-data.js [options]

Options:
  --dry-run    Run migration without actually inserting data
  --verbose    Enable verbose logging
  --force      Force migration even if target environment is production
  --help       Show this help message

Environment Variables:
  SOURCE_DATA_PATH    Path to source data files (default: ./migration-data)
  VITE_CONVEX_URL     Convex deployment URL

Example:
  SOURCE_DATA_PATH=./old-system-export node migrate-data.js --dry-run --verbose
    `);
    process.exit(0);
  }
  
  const migrator = new DataMigrator();
  migrator.run().catch(err => {
    error(`Unhandled error: ${err.message}`);
    process.exit(1);
  });
}