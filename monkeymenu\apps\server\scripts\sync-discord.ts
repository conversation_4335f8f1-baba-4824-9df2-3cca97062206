#!/usr/bin/env tsx
import { config } from "dotenv";
import { SlashCreator } from "slash-create/web";
import {
	BalanceCommand,
	SendVerifyEmbedCommand,
	VerifyCommand,
	WithdrawCommand,
} from "../src/discord/commands";

// Load environment variables (for development)
if (process.env.NODE_ENV !== "production") {
	config({ path: ".dev.vars" });
}

async function main() {
	const applicationId = process.env.DISCORD_APPLICATION_ID;
	const botToken = process.env.DISCORD_BOT_TOKEN;
	const guildId = process.env.DISCORD_GUILD_ID; // Optional for guild-specific commands

	if (!applicationId || !botToken) {
		console.error("Missing required environment variables:");
		console.error("- DISCORD_APPLICATION_ID");
		console.error("- DISCORD_BOT_TOKEN");
		console.error("- DISCORD_GUILD_ID (optional, for guild-specific commands)");
		process.exit(1);
	}

	try {
		console.log("Syncing Discord slash commands...");

		const creator = new SlashCreator({
			applicationID: applicationId,
			token: botToken,
		});

		// Register commands
		creator.registerCommand(WithdrawCommand);
		creator.registerCommand(BalanceCommand);
		creator.registerCommand(VerifyCommand);
		creator.registerCommand(SendVerifyEmbedCommand);

		// Sync commands
		if (guildId) {
			console.log(`Syncing commands for guild: ${guildId}`);
			await creator.syncCommands();
		} else {
			console.log(
				"Syncing global commands (may take up to 1 hour to propagate)",
			);
			await creator.syncCommands();
		}

		console.log("✅ Discord commands synced successfully!");
		process.exit(0);
	} catch (error) {
		console.error("❌ Failed to sync Discord commands:", error);
		process.exit(1);
	}
}

main();
