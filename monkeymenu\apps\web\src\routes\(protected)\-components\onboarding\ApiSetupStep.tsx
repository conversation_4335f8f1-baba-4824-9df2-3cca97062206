import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { useAppForm } from "@/components/ui/tanstack-form";
import { trpc } from "@/lib/trpc-client";
import { TornApiKeySchema } from "@monkeymenu/shared";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ExternalLink } from "lucide-react";
import { useEffect, useRef } from "react";
import { toast } from "sonner";
import { EXTERNAL_URLS, ROLE_POLLING_CONFIG, TOAST_MESSAGES } from "./config";
import type { StepComponentProps } from "./types";

interface ApiSetupStepProps extends StepComponentProps {
	isDiscordLinked: boolean;
	isSubmitting: boolean;
	setIsSubmitting: (value: boolean) => void;
	invalidApiKey: boolean;
	setInvalidApiKey: (value: boolean) => void;
	isFinalizingSetup: boolean;
	setIsFinalizingSetup: (value: boolean) => void;
	pollingProgress: number;
	setPollingProgress: (value: number) => void;
}

export function ApiSetupStep({
	onPrevStep,
	isDiscordLinked,
	isSubmitting,
	setIsSubmitting,
	invalidApiKey,
	setInvalidApiKey,
	isFinalizingSetup,
	setIsFinalizingSetup,
	pollingProgress,
	setPollingProgress,
}: ApiSetupStepProps) {
	// Refs for cleanup
	const pollingIntervalRef = useRef<ReturnType<typeof setInterval> | null>(
		null,
	);
	const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
	const attemptsRef = useRef(0);

	// Get permissions query (used for polling)
	const permissionsQuery = useQuery({
		...trpc.permissions.getMyPermissions.queryOptions(),
		enabled: false,
		refetchOnWindowFocus: false,
		refetchOnMount: false,
		retry: false,
	});

	// Cleanup effect for polling timers
	useEffect(() => {
		return () => {
			if (pollingIntervalRef.current) {
				clearInterval(pollingIntervalRef.current);
			}
			if (timeoutRef.current) {
				clearTimeout(timeoutRef.current);
			}
		};
	}, []);

	const startRolePolling = () => {
		let attempts = 0;
		const maxAttempts = ROLE_POLLING_CONFIG.MAX_ATTEMPTS;

		// Set up absolute timeout
		timeoutRef.current = setTimeout(() => {
			if (pollingIntervalRef.current) {
				clearInterval(pollingIntervalRef.current);
			}
			setIsFinalizingSetup(false);
			toast.error(
				"Setup is taking longer than expected. Please refresh the page or contact support.",
			);
		}, ROLE_POLLING_CONFIG.TIMEOUT_MS);

		pollingIntervalRef.current = setInterval(async () => {
			attempts++;
			attemptsRef.current = attempts;

			// Update progress
			const progress = Math.min((attempts / maxAttempts) * 100, 95);
			setPollingProgress(progress);

			try {
				// Refetch permissions to check if role has been assigned
				const result = await permissionsQuery.refetch();

				if (result.data && result.data.roleLevel > 0) {
					// Role has been assigned successfully
					if (pollingIntervalRef.current) {
						clearInterval(pollingIntervalRef.current);
					}
					if (timeoutRef.current) {
						clearTimeout(timeoutRef.current);
					}

					setPollingProgress(100);
					setIsFinalizingSetup(false);
					toast.success("Account setup completed successfully!");

					// Simulate next step navigation - parent component should handle this
					return;
				}

				if (attempts >= maxAttempts) {
					// Max attempts reached
					if (pollingIntervalRef.current) {
						clearInterval(pollingIntervalRef.current);
					}
					if (timeoutRef.current) {
						clearTimeout(timeoutRef.current);
					}

					setIsFinalizingSetup(false);
					toast.error(
						"Role assignment is taking longer than expected. Please contact an administrator.",
					);
				}
			} catch (error) {
				console.error("Error during role polling:", error);
			}
		}, ROLE_POLLING_CONFIG.INTERVAL_MS);
	};

	// API key update mutation
	const updateTornInfoMutation = useMutation(
		trpc.user.updateTornInfo.mutationOptions({
			onSuccess: (data: { message: string; success: boolean }) => {
				setIsSubmitting(false);
				setInvalidApiKey(false);
				if (data.success) {
					toast.success(data.message);
					setIsFinalizingSetup(true);
					startRolePolling();
				} else {
					toast.error(data.message);
				}
			},
			onError: (error) => {
				setIsSubmitting(false);
				setIsFinalizingSetup(false);

				const errorMessage = error.message || "";
				if (
					errorMessage.toLowerCase().includes("invalid api key") ||
					errorMessage.toLowerCase().includes("incorrect key") ||
					errorMessage.includes("2")
				) {
					setInvalidApiKey(true);
					toast.error(TOAST_MESSAGES.API_KEY_INVALID);
				} else if (
					errorMessage.toLowerCase().includes("already linked") ||
					errorMessage.toLowerCase().includes("linked to another")
				) {
					setInvalidApiKey(true);
					toast.error(TOAST_MESSAGES.TORN_ACCOUNT_ALREADY_LINKED);
				} else {
					setInvalidApiKey(false);
					toast.error(
						errorMessage || TOAST_MESSAGES.TORN_API_KEY_UPDATE_FAILED,
					);
				}
			},
		}),
	);

	const form = useAppForm({
		defaultValues: {
			apiKey: "",
		},
		validators: {
			onChange: TornApiKeySchema,
		},
		onSubmit: async ({ value }) => {
			setIsSubmitting(true);
			setInvalidApiKey(false);
			updateTornInfoMutation.mutate({
				apiKey: value.apiKey.trim(),
			});
		},
	});

	return (
		<form.AppForm>
			<form
				onSubmit={(e) => {
					e.preventDefault();
					e.stopPropagation();
					void form.handleSubmit();
				}}
			>
				<Card>
					<CardContent className="space-y-6 p-8">
						{/* Header */}
						<div className="space-y-2 text-center">
							<h2 className="font-bold text-2xl tracking-tight">
								Connect Your Account
							</h2>
							<p className="mx-auto max-w-md text-muted-foreground leading-relaxed">
								{isDiscordLinked
									? "Enter your Torn API key to verify your faction membership."
									: "We need your Torn API key to verify your faction membership."}
							</p>
						</div>

						{/* Form Field */}
						<div className="space-y-4">
							<form.AppField name="apiKey">
								{(field) => (
									<field.FormItem>
										<field.FormLabel className="sr-only">
											Torn API Key
										</field.FormLabel>
										<field.FormControl>
											<Input
												value={field.state.value}
												onChange={(e) => field.handleChange(e.target.value)}
												onBlur={field.handleBlur}
												placeholder="Enter your 16-character API key"
												type="password"
												autoComplete="off"
												disabled={isSubmitting || isFinalizingSetup}
												className="text-center font-mono"
											/>
										</field.FormControl>
										<field.FormMessage />
									</field.FormItem>
								)}
							</form.AppField>

							{/* Help Link */}
							<div className="text-center">
								<a
									href={EXTERNAL_URLS.TORN_API_SETTINGS}
									target="_blank"
									rel="noopener noreferrer"
									className="inline-flex items-center gap-1 text-primary text-sm underline hover:text-primary/80"
								>
									Find your API key in Torn settings
									<ExternalLink className="h-3 w-3" />
								</a>
							</div>
						</div>

						{/* Error State */}
						{invalidApiKey && (
							<Alert variant="destructive" data-testid="invalid-key-alert">
								<AlertDescription>
									{TOAST_MESSAGES.API_KEY_INVALID}
								</AlertDescription>
							</Alert>
						)}

						{/* Loading State */}
						{isFinalizingSetup && (
							<div className="space-y-3 text-center">
								<div className="text-muted-foreground text-sm">
									Setting up your account...
								</div>
								<Progress value={pollingProgress} className="h-2" />
								<div className="text-muted-foreground text-xs">
									{Math.min(Math.round(pollingProgress), 100)}% complete
								</div>
							</div>
						)}
					</CardContent>

					<CardFooter className="flex gap-3 p-8 pt-0">
						<Button
							type="button"
							variant="outline"
							onClick={onPrevStep}
							disabled={isSubmitting || isFinalizingSetup}
							className="flex-1"
						>
							Back
						</Button>
						<form.Subscribe>
							{(state) => (
								<Button
									type="submit"
									className="flex-1"
									disabled={
										!state.canSubmit || isSubmitting || isFinalizingSetup
									}
								>
									{isFinalizingSetup
										? "Setting up..."
										: isSubmitting
											? "Verifying..."
											: "Continue"}
								</Button>
							)}
						</form.Subscribe>
					</CardFooter>
				</Card>
			</form>
		</form.AppForm>
	);
}
