import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Clock, ExternalLink, Swords } from "lucide-react";
import type { ChainStatusCardProps, TornChain } from "./types";
import { formatDuration, getChainStatusInfo } from "./utils";

export function ChainStatusCard({
	chainInfo,
	chainRemaining,
}: ChainStatusCardProps) {
	if (!chainInfo || chainRemaining === null || chainRemaining <= 0) {
		return null;
	}

	const statusInfo = getChainStatusInfo(chainRemaining);

	return (
		<Card
			className={cn(
				"group transition-all hover:shadow-md",
				chainRemaining <= 60
					? "border-red-300 dark:border-red-800"
					: chainRemaining <= 120
						? "border-orange-300 dark:border-orange-800"
						: "border-green-300 dark:border-green-800",
				chainRemaining <= 60 && "animate-pulse",
			)}
		>
			<CardHeader className="pb-3">
				<div className="flex items-start justify-between">
					<div className="flex items-center gap-3">
						<div
							className={cn(
								"rounded-full p-2",
								`${statusInfo.bgColor} border ${statusInfo.borderColor}`,
							)}
						>
							<Swords className={cn("h-5 w-5", statusInfo.color)} />
						</div>
						<div>
							<CardTitle className="text-base">Chain Status</CardTitle>
							<p className="font-mono text-muted-foreground text-sm">
								{(chainInfo.chain as TornChain).current}/
								{(chainInfo.chain as TornChain).max} hits
							</p>
						</div>
					</div>
					<div className="flex items-center gap-1 text-muted-foreground text-sm">
						<Clock className="h-4 w-4" />
						<span>
							<span className="hidden sm:inline">Time remaining: </span>
							{formatDuration(chainRemaining)}
						</span>
					</div>
				</div>
			</CardHeader>
			<CardFooter className="pt-0">
				<Button variant="outline" className="w-full gap-2" asChild>
					<a
						href="https://www.torn.com/factions.php?step=your#/war/chain"
						target="_blank"
						rel="noopener noreferrer"
					>
						<ExternalLink className="h-4 w-4" />
						View Chain
					</a>
				</Button>
			</CardFooter>
		</Card>
	);
}
