import React from 'react';
import { useSession } from '../../hooks/useSession';

interface HasMinimumRoleProps {
  role: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function HasMinimumRole({ role, children, fallback = null }: HasMinimumRoleProps) {
  const { convexUser } = useSession();
  
  const hasMinimumRole = convexUser?.roleLevel && 
                        getRoleLevel(role) <= convexUser.roleLevel;
  
  if (!hasMinimumRole) {
    return <>{fallback}</>;
  }
  
  return <>{children}</>;
}

// Helper function to get role level for comparison
function getRoleLevel(roleName: string): number {
  const roleLevels: Record<string, number> = {
    'recruit': 1,
    'chimpanzee': 2,
    'orangutan': 3,
    'baboon': 4,
    'primate-liaison': 5,
    'gorilla': 6,
    'monkey-mentor': 7,
    'co-leader': 8,
    'leader': 9,
    'system-administrator': 10,
  };
  
  return roleLevels[roleName.toLowerCase()] || 0;
}