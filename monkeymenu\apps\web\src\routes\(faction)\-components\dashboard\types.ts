import type { LucideIcon } from "lucide-react";

export interface FactionTool {
	title: string;
	description: string;
	icon: LucideIcon;
	href: string;
	category: string;
	priority: number;
	available: boolean;
	permission?: string;
	adminOnly?: boolean;
}

export interface SystemStats {
	totalUsers: number;
	activeUsers: number;
	totalWithdrawals: number;
	pendingWithdrawals: number;
	totalGuides: number;
	totalAnnouncements: number;
}

export interface AnnouncementData {
	announcement: {
		id: number;
		title: string;
		content: string;
		createdAt: string;
	};
	author?: {
		name: string;
	};
}

export interface WithdrawalData {
	id: number;
	amount: number;
	status: string;
	createdAt: string;
}

export interface BankingData {
	factionBalance?: {
		money: number;
	};
}

// Smart Widget System Types
export interface SmartWidgetCondition {
	type:
		| "war_active"
		| "pending_banking"
		| "chain_active"
		| "member_count"
		| "time_based";
	operator: "equals" | "greater_than" | "less_than" | "exists" | "not_exists";
	value?: unknown;
	check: () => Promise<boolean> | boolean;
}

export interface SmartWidget {
	id: string;
	title: string;
	description: string;
	icon: LucideIcon;
	priority: "critical" | "high" | "medium" | "low";
	category: "war" | "banking" | "admin" | "general";
	conditions: SmartWidgetCondition[];
	data?: unknown;
	actions?: SmartWidgetAction[];
	autoRefresh?: number; // seconds
	dismissible?: boolean;
}

export interface SmartWidgetAction {
	label: string;
	icon?: LucideIcon;
	href?: string;
	onClick?: () => void;
	variant?: "default" | "destructive" | "outline" | "secondary";
}

// Context Detection Types
export interface FactionContext {
	activeWars: WarContext[];
	pendingWithdrawals: PendingWithdrawalContext[];
	activeChains: ChainContext[];
	memberActivity: MemberActivityContext;
	systemHealth: SystemHealthContext;
}

export interface WarContext {
	warId: number;
	opponent: string;
	status: "active" | "ending_soon";
	timeRemaining?: number;
	isWinning?: boolean;
	chainCount: number;
	lastActivity?: string;
}

export interface PendingWithdrawalContext {
	count: number;
	totalAmount: number;
	oldestRequest: {
		id: string;
		age: number; // hours
		amount: number;
		requester: string;
	};
	urgentCount: number; // requests older than 12 hours
}

export interface ChainContext {
	chainId: number;
	length: number;
	timeRemaining: number;
	isWarChain: boolean;
	nextTarget?: string;
}

export interface MemberActivityContext {
	onlineCount: number;
	activeInLast24h: number;
	totalMembers: number;
	activityTrend: "increasing" | "stable" | "decreasing";
}

export interface SystemHealthContext {
	apiStatus: "healthy" | "degraded" | "down";
	dbStatus: "healthy" | "slow" | "error";
	lastUpdate: Date;
	alertCount: number;
}
