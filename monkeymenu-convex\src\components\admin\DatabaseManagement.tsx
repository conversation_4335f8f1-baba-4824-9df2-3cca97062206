import { useState } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';

export function DatabaseManagement() {
  const [selectedTable, setSelectedTable] = useState<string>('');
  const [exportFormat, setExportFormat] = useState<'json' | 'csv'>('json');
  const [isExporting, setIsExporting] = useState(false);
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importData, setImportData] = useState('');
  const [importMode, setImportMode] = useState<'append' | 'replace'>('append');
  const [activeTab, setActiveTab] = useState<'export' | 'import' | 'backup' | 'system'>('export');
  const [isInitializingLists, setIsInitializingLists] = useState(false);
  const [isCleaningDuplicates, setIsCleaningDuplicates] = useState(false);

  const databaseStats = useQuery(api.admin.getDatabaseStats);
  const tableData = useQuery(api.admin.getTableData, selectedTable ? { tableName: selectedTable } : "skip");
  const backupHistory = useQuery(api.admin.getBackupHistory);
  
  // Mutations for admin operations
  const exportTableData = useMutation(api.admin.exportTableData);
  const createBackup = useMutation(api.admin.createBackup);
  const importDataMutation = useMutation(api.admin.importData);
  const cleanupOldData = useMutation(api.admin.cleanupOldData);
  const restoreFromBackup = useMutation(api.admin.restoreFromBackup);
  
  // Target lists initialization
  const initializeSharedLists = useMutation(api.targets.initializeSharedLists);
  const cleanupDuplicateTargets = useMutation(api.targets.cleanupDuplicateTargets);


  const handleExport = async () => {
    if (!selectedTable) return;
    
    setIsExporting(true);
    try {
      const result = await exportTableData({
        tableName: selectedTable,
        format: exportFormat
      });
      
      // Create download link
      const blob = new Blob([result.data], { 
        type: exportFormat === 'json' ? 'application/json' : 'text/csv' 
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${selectedTable}_export_${new Date().toISOString().split('T')[0]}.${exportFormat}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      alert(`Successfully exported ${result.count} records`);
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  const handleBackup = async () => {
    setIsBackingUp(true);
    try {
      const result = await createBackup({});
      alert(`Backup created successfully. Backup ID: ${result.backupId}`);
    } catch (error) {
      console.error('Backup failed:', error);
      alert('Backup failed. Please try again.');
    } finally {
      setIsBackingUp(false);
    }
  };

  const handleImport = async () => {
    if (!selectedTable || !importData.trim()) return;
    
    setIsImporting(true);
    try {
      const result = await importDataMutation({
        tableName: selectedTable,
        data: importData,
        mode: importMode
      });
      
      alert(`Import completed: ${result.importedCount} records imported, ${result.errorCount} errors`);
      setImportData('');
    } catch (error) {
      console.error('Import failed:', error);
      alert('Import failed. Please check your data format and try again.');
    } finally {
      setIsImporting(false);
    }
  };

  const handleRestore = async (backupId: string) => {
    if (!confirm('Are you sure you want to restore from this backup? This will overwrite current data.')) {
      return;
    }

    try {
      const result = await restoreFromBackup({ backupId });
      alert(`Backup restored successfully. ${result.tablesRestored.length} tables restored.`);
    } catch (error) {
      console.error('Restore failed:', error);
      alert('Restore failed. Please try again.');
    }
  };

  const handleCleanup = async (table: string, days: number) => {
    if (!confirm(`Are you sure you want to delete ${table} records older than ${days} days? This action cannot be undone.`)) {
      return;
    }

    try {
      const result = await cleanupOldData({
        tableName: table,
        olderThanDays: days
      });
      alert(`Successfully deleted ${result.deletedCount} old records from ${table}`);
    } catch (error) {
      console.error('Cleanup failed:', error);
      alert('Cleanup failed. Please try again.');
    }
  };

  const handleInitializeSharedLists = async () => {
    if (!confirm('Initialize and populate shared target lists? This will:\n\n1. Create "Faction Shared Targets A" and "Faction Shared Targets B" if they don\'t exist\n2. Populate them with 50 pre-selected targets from the old app (25 in each list)\n\nProceed?')) {
      return;
    }

    setIsInitializingLists(true);
    try {
      const result = await initializeSharedLists({});
      alert(result.message + `\n\nDetails:\n- Created List A: ${result.createdA}\n- Created List B: ${result.createdB}\n- Targets added to A: ${result.targetsAddedA}\n- Targets added to B: ${result.targetsAddedB}`);
    } catch (error: any) {
      console.error('Failed to initialize shared lists:', error);
      alert('Failed to initialize shared lists: ' + error.message);
    } finally {
      setIsInitializingLists(false);
    }
  };

  const handleCleanupDuplicates = async () => {
    if (!confirm('Cleanup duplicate targets? This will:\n\n1. Find targets that appear multiple times in the same list\n2. Remove the duplicate entries (keeping the oldest)\n\nThis action cannot be undone. Proceed?')) {
      return;
    }

    setIsCleaningDuplicates(true);
    try {
      const result = await cleanupDuplicateTargets({});
      alert(result.message + `\n\nDetails:\n${result.details.map(d => `- ${d.listName}: ${d.duplicatesRemoved} duplicates removed`).join('\n')}`);
    } catch (error: any) {
      console.error('Failed to cleanup duplicates:', error);
      alert('Failed to cleanup duplicates: ' + error.message);
    } finally {
      setIsCleaningDuplicates(false);
    }
  };

  if (databaseStats === undefined) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const tables = Object.entries(databaseStats.tables || {});

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Database Management</h2>
          <p className="text-sm text-gray-600">
            Monitor database health and manage data exports
          </p>
        </div>
        
        <button
          onClick={handleBackup}
          disabled={isBackingUp}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          {isBackingUp ? 'Creating Backup...' : 'Create Backup'}
        </button>
      </div>

      {/* Database Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Records</p>
              <p className="text-2xl font-semibold text-gray-900">
                {databaseStats.totalRecords?.toLocaleString() || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Tables</p>
              <p className="text-2xl font-semibold text-gray-900">{tables.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Last Backup</p>
              <p className="text-lg font-semibold text-gray-900">
                {databaseStats.lastBackup 
                  ? new Date(databaseStats.lastBackup).toLocaleDateString()
                  : 'Never'
                }
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Tables Overview */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Tables Overview</h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Table Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Record Count
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Updated
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {tables.map(([tableName, stats]: any) => (
                <tr key={tableName} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{tableName}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{(stats as any).count?.toLocaleString() || 0}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {(stats as any).lastUpdated 
                        ? new Date((stats as any).lastUpdated).toLocaleDateString()
                        : 'N/A'
                      }
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button
                      onClick={() => setSelectedTable(tableName)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      View
                    </button>
                    <button
                      onClick={() => handleCleanup(tableName, 90)}
                      className="text-orange-600 hover:text-orange-900"
                    >
                      Cleanup (90d)
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'export', label: 'Export Data', icon: '📤' },
            { id: 'import', label: 'Import Data', icon: '📥' },
            { id: 'backup', label: 'Backup & Restore', icon: '💾' },
            { id: 'system', label: 'System Setup', icon: '🔧' },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span>{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'export' && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Data Export</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Table</label>
              <select
                value={selectedTable}
                onChange={(e) => setSelectedTable(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select a table</option>
                {tables.map(([tableName]) => (
                  <option key={tableName} value={tableName}>{tableName}</option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Format</label>
              <select
                value={exportFormat}
                onChange={(e) => setExportFormat(e.target.value as 'json' | 'csv')}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="json">JSON</option>
                <option value="csv">CSV</option>
              </select>
            </div>
            
            <div className="flex items-end">
              <button
                onClick={handleExport}
                disabled={!selectedTable || isExporting}
                className="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isExporting ? 'Exporting...' : 'Export Data'}
              </button>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'import' && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Data Import</h3>
          
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Table</label>
                <select
                  value={selectedTable}
                  onChange={(e) => setSelectedTable(e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select a table</option>
                  {['guides', 'announcements', 'targets'].map((tableName) => (
                    <option key={tableName} value={tableName}>{tableName}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Import Mode</label>
                <select
                  value={importMode}
                  onChange={(e) => setImportMode(e.target.value as 'append' | 'replace')}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="append">Append (Add to existing)</option>
                  <option value="replace">Replace (Clear and import)</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">JSON Data</label>
              <textarea
                value={importData}
                onChange={(e) => setImportData(e.target.value)}
                placeholder="Paste your JSON data here..."
                rows={12}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
              />
              <p className="text-sm text-gray-500 mt-1">
                Data must be valid JSON array format. IDs will be auto-generated.
              </p>
            </div>

            <div className="flex justify-end">
              <button
                onClick={handleImport}
                disabled={!selectedTable || !importData.trim() || isImporting}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isImporting ? 'Importing...' : 'Import Data'}
              </button>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'backup' && (
        <div className="space-y-6">
          {/* Create Backup */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Create Backup</h3>
            <div className="flex items-center gap-4">
              <button
                onClick={handleBackup}
                disabled={isBackingUp}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                {isBackingUp ? 'Creating Backup...' : 'Create Full Backup'}
              </button>
              <p className="text-sm text-gray-600">
                Creates a complete backup of all database tables
              </p>
            </div>
          </div>

          {/* Backup History */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Backup History</h3>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Backup ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Size
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {backupHistory?.map((backup: any) => (
                    <tr key={backup.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {backup.id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(backup.timestamp).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          backup.type === 'full' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                        }`}>
                          {backup.type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {backup.size}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          backup.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {backup.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => handleRestore(backup.id)}
                          className="text-blue-600 hover:text-blue-900 mr-3"
                        >
                          Restore
                        </button>
                        <button
                          onClick={() => alert('Download functionality would be implemented here')}
                          className="text-green-600 hover:text-green-900"
                        >
                          Download
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'system' && (
        <div className="space-y-6">
          {/* Target Lists Initialization */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Target Lists Setup</h3>
            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start">
                  <div className="text-blue-600 mr-3 mt-1">ℹ️</div>
                  <div>
                    <p className="text-sm text-blue-800 font-medium mb-1">
                      Shared Target Lists Initialization
                    </p>
                    <p className="text-sm text-blue-700">
                      This will create and populate the shared faction target lists that all members can use:
                    </p>
                    <ul className="text-sm text-blue-700 mt-2 list-disc list-inside">
                      <li>Faction Shared Targets A (25 pre-selected targets)</li>
                      <li>Faction Shared Targets B (25 pre-selected targets)</li>
                    </ul>
                    <p className="text-sm text-blue-700 mt-2">
                      These lists will appear at the top of the target list dropdown for all users with 50 total targets from the old app.
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-4">
                <button
                  onClick={handleInitializeSharedLists}
                  disabled={isInitializingLists}
                  className="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  {isInitializingLists ? 'Initializing...' : 'Initialize & Populate Shared Target Lists'}
                </button>
                <p className="text-sm text-gray-600">
                  Safe to run multiple times - will only create lists and add targets if they don't exist
                </p>
              </div>
            </div>
          </div>

          {/* Target Cleanup */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Target Cleanup</h3>
            <div className="space-y-4">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start">
                  <div className="text-yellow-600 mr-3 mt-1">⚠️</div>
                  <div>
                    <p className="text-sm text-yellow-800 font-medium mb-1">
                      Duplicate Target Cleanup
                    </p>
                    <p className="text-sm text-yellow-700">
                      Remove duplicate targets that appear multiple times in the same list. This can happen during data migration or if targets are accidentally added multiple times.
                    </p>
                    <p className="text-sm text-yellow-700 mt-2">
                      Only run this if you're experiencing issues with duplicate targets in your lists.
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-4">
                <button
                  onClick={handleCleanupDuplicates}
                  disabled={isCleaningDuplicates}
                  className="bg-yellow-600 text-white px-6 py-2 rounded-md hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  {isCleaningDuplicates ? 'Cleaning...' : 'Cleanup Duplicate Targets'}
                </button>
                <p className="text-sm text-gray-600">
                  Removes duplicate entries while keeping the oldest version of each target
                </p>
              </div>
            </div>
          </div>

          {/* Future System Setup Options */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Other System Setup</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Permission System</h4>
                <p className="text-sm text-gray-600 mb-3">
                  Initialize default permissions and roles
                </p>
                <button
                  disabled
                  className="bg-gray-300 text-gray-500 px-4 py-2 rounded-md cursor-not-allowed"
                >
                  Coming Soon
                </button>
              </div>
              
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Discord Integration</h4>
                <p className="text-sm text-gray-600 mb-3">
                  Set up Discord bot configurations
                </p>
                <button
                  disabled
                  className="bg-gray-300 text-gray-500 px-4 py-2 rounded-md cursor-not-allowed"
                >
                  Coming Soon
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Table Preview */}
      {selectedTable && tableData && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              {selectedTable} Preview (First 50 records)
            </h3>
          </div>
          
          <div className="overflow-x-auto">
            <pre className="p-4 text-xs text-gray-800 bg-gray-50 max-h-96 overflow-auto">
              {JSON.stringify(tableData, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}