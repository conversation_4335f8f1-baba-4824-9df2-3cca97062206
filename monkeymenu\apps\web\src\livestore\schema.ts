import {
	Events,
	Schema,
	SessionIdSymbol,
	State,
	makeSchema,
} from "@livestore/livestore";

/**
 * Type definition for LiveStore tables structure.
 *
 * This represents a collection of SQLite tables and client documents that form
 * the complete LiveStore schema. Each table is created using State.SQLite.table()
 * or State.SQLite.clientDocument() functions from the LiveStore framework.
 *
 * @remarks
 * While LiveStore doesn't export a specific type for this structure, we define
 * this custom type to improve type safety while maintaining compatibility with
 * the LiveStore internal type system. The broader `Record<string, any>` type
 * is necessary because:
 *
 * 1. LiveStore table definitions use complex internal types that aren't publicly exposed
 * 2. The State.SQLite.table() and State.SQLite.clientDocument() functions return
 *    different types that need to coexist in the same object
 * 3. LiveStore's type inference relies on the actual table definitions rather than
 *    strict typing of the container object
 */

// ------------------------
// LiveStore Table Typings
// ------------------------

/**
 * A single SQLite table created via `State.SQLite.table(...)`.
 */
type LiveStoreSQLiteTable = ReturnType<(typeof State)["SQLite"]["table"]>;

/**
 * A client-side document created via `State.SQLite.clientDocument(...)`.
 */
type LiveStoreClientDocument = ReturnType<
	(typeof State)["SQLite"]["clientDocument"]
>;

/**
 * Union of all possible table-like structures we place in the `tables` object.
 */
type LiveStoreTableLike = LiveStoreSQLiteTable | LiveStoreClientDocument;

/**
 * Map of every LiveStore table/document keyed by a readable name.
 */
export type LiveStoreTablesMap = Record<string, LiveStoreTableLike>;

// -------------------------------------------
// Actual tables definition starts below
// -------------------------------------------

// Banking state tables - mirrors your existing banking system
export const tables: LiveStoreTablesMap = {
	// Withdrawal requests with real-time status tracking
	withdrawalRequests: State.SQLite.table({
		name: "withdrawal_requests",
		columns: {
			id: State.SQLite.text({ primaryKey: true }),
			amount: State.SQLite.integer(),
			status: State.SQLite.text({ default: "PENDING" }),
			requestedById: State.SQLite.text(),
			requestedByName: State.SQLite.text(),
			requestedByTornId: State.SQLite.integer({ nullable: true }),
			processedById: State.SQLite.text({ nullable: true }),
			processedByName: State.SQLite.text({ nullable: true }),
			createdAt: State.SQLite.integer({ schema: Schema.DateFromNumber }),
			updatedAt: State.SQLite.integer({ schema: Schema.DateFromNumber }),
			processedAt: State.SQLite.integer({
				nullable: true,
				schema: Schema.DateFromNumber,
			}),
			transactionId: State.SQLite.text({ nullable: true }),
			discordMessageId: State.SQLite.text({ nullable: true }),
		},
	}),

	// Faction balance cache for real-time updates
	factionBalance: State.SQLite.table({
		name: "faction_balance",
		columns: {
			id: State.SQLite.text({ primaryKey: true }),
			money: State.SQLite.integer({ default: 0 }),
			points: State.SQLite.integer({ default: 0 }),
			lastUpdated: State.SQLite.integer({ schema: Schema.DateFromNumber }),
		},
	}),

	// Target lists for target finder
	targetLists: State.SQLite.table({
		name: "target_lists",
		columns: {
			id: State.SQLite.text({ primaryKey: true }),
			name: State.SQLite.text(),
			userId: State.SQLite.text({ nullable: true }),
			isExternal: State.SQLite.integer({ default: 0 }), // SQLite boolean as integer
			createdAt: State.SQLite.integer({ schema: Schema.DateFromNumber }),
			updatedAt: State.SQLite.integer({ schema: Schema.DateFromNumber }),
		},
	}),

	// Targets with real-time status tracking
	targets: State.SQLite.table({
		name: "targets",
		columns: {
			id: State.SQLite.text({ primaryKey: true }),
			listId: State.SQLite.text(),
			name: State.SQLite.text(),
			tornId: State.SQLite.text(),
			status: State.SQLite.text({ default: "Unknown" }),
			profilePicture: State.SQLite.text({ nullable: true }),
			createdAt: State.SQLite.integer({ schema: Schema.DateFromNumber }),
			updatedAt: State.SQLite.integer({ schema: Schema.DateFromNumber }),
		},
	}),

	// Chain status cache for real-time updates
	chainStatus: State.SQLite.table({
		name: "chain_status",
		columns: {
			id: State.SQLite.text({ primaryKey: true }),
			current: State.SQLite.integer({ default: 0 }),
			max: State.SQLite.integer({ default: 0 }),
			timeout: State.SQLite.integer({ default: 0 }),
			cooldown: State.SQLite.integer({ default: 0 }),
			end: State.SQLite.integer({ default: 0 }),
			lastUpdated: State.SQLite.integer({ schema: Schema.DateFromNumber }),
		},
	}),

	// Target finder cooldown tracking
	targetFinderCooldown: State.SQLite.table({
		name: "target_finder_cooldown",
		columns: {
			id: State.SQLite.text({ primaryKey: true }),
			remaining: State.SQLite.integer({ default: 0 }),
			lastUpdated: State.SQLite.integer({ schema: Schema.DateFromNumber }),
		},
	}),

	// Announcements with real-time updates
	announcements: State.SQLite.table({
		name: "announcements",
		columns: {
			id: State.SQLite.text({ primaryKey: true }),
			title: State.SQLite.text(),
			content: State.SQLite.text(),
			category: State.SQLite.text(),
			authorId: State.SQLite.text(),
			authorName: State.SQLite.text(),
			createdAt: State.SQLite.integer({ schema: Schema.DateFromNumber }),
			updatedAt: State.SQLite.integer({ schema: Schema.DateFromNumber }),
		},
	}),

	// Banking UI state (local only - forms, filters, etc.)
	bankingUIState: State.SQLite.clientDocument({
		name: "banking_ui_state",
		schema: Schema.Struct({
			withdrawalFormAmount: Schema.String,
			withdrawalFormErrors: Schema.Record({
				key: Schema.String,
				value: Schema.String,
			}),
			activeTab: Schema.Literal("request", "history", "admin"),
			statusFilter: Schema.Literal("all", "PENDING", "ACCEPTED", "DECLINED"),
			showWithdrawalForm: Schema.Boolean,
		}),
		default: {
			id: SessionIdSymbol,
			value: {
				withdrawalFormAmount: "",
				withdrawalFormErrors: {},
				activeTab: "request" as const,
				statusFilter: "all" as const,
				showWithdrawalForm: false,
			},
		},
	}),

	// Target finder UI state (local only - filters, search, etc.)
	targetFinderUIState: State.SQLite.clientDocument({
		name: "target_finder_ui_state",
		schema: Schema.Struct({
			selectedList: Schema.optional(Schema.String),
			searchTerm: Schema.String,
			statusFilter: Schema.Literal("all", "okay", "hospitalized", "error"),
			sortBy: Schema.Literal("smart", "name", "status", "added", "tornId"),
			sortOrder: Schema.Literal("asc", "desc"),
			viewMode: Schema.Literal("grid", "list"),
		}),
		default: {
			id: SessionIdSymbol,
			value: {
				selectedList: undefined,
				searchTerm: "",
				statusFilter: "all" as const,
				sortBy: "smart" as const,
				sortOrder: "asc" as const,
				viewMode: "grid" as const,
			},
		},
	}),

	// Announcements UI state (local only - filters, search, etc.)
	announcementsUIState: State.SQLite.clientDocument({
		name: "announcements_ui_state",
		schema: Schema.Struct({
			searchQuery: Schema.String,
			selectedCategory: Schema.String,
			viewMode: Schema.Literal("grid", "list"),
			showCreateForm: Schema.Boolean,
		}),
		default: {
			id: SessionIdSymbol,
			value: {
				searchQuery: "",
				selectedCategory: "all",
				viewMode: "grid" as const,
				showCreateForm: false,
			},
		},
	}),

	// Admin system stats cache for real-time updates
	adminSystemStats: State.SQLite.table({
		name: "admin_system_stats",
		columns: {
			id: State.SQLite.text({ primaryKey: true }),
			totalUsers: State.SQLite.integer({ default: 0 }),
			totalRoles: State.SQLite.integer({ default: 0 }),
			totalPermissions: State.SQLite.integer({ default: 0 }),
			totalGuides: State.SQLite.integer({ default: 0 }),
			lastUpdated: State.SQLite.integer({ schema: Schema.DateFromNumber }),
		},
	}),

	// Admin audit log for permission changes
	adminAuditLog: State.SQLite.table({
		name: "admin_audit_log",
		columns: {
			id: State.SQLite.text({ primaryKey: true }),
			assignedAt: State.SQLite.integer({ schema: Schema.DateFromNumber }),
			isActive: State.SQLite.integer({ default: 1 }), // SQLite boolean as integer
			assignedByUserId: State.SQLite.text({ nullable: true }),
			assignedByName: State.SQLite.text(),
			assignedToUserId: State.SQLite.text(),
			assignedToUserName: State.SQLite.text(),
			assignedToUserEmail: State.SQLite.text(),
			roleId: State.SQLite.text(),
			roleName: State.SQLite.text(),
			roleDisplayName: State.SQLite.text(),
			roleHierarchyLevel: State.SQLite.integer(),
		},
	}),

	// Admin users cache for user management
	adminUsers: State.SQLite.table({
		name: "admin_users",
		columns: {
			id: State.SQLite.text({ primaryKey: true }),
			name: State.SQLite.text(),
			email: State.SQLite.text(),
			image: State.SQLite.text({ nullable: true }),
			createdAt: State.SQLite.integer({ schema: Schema.DateFromNumber }),
			tornUserId: State.SQLite.text({ nullable: true }),
			tornFactionId: State.SQLite.text({ nullable: true }),
			verified: State.SQLite.integer({ default: 0 }), // SQLite boolean as integer
			suspended: State.SQLite.integer({ default: 0 }), // SQLite boolean as integer
			suspensionReason: State.SQLite.text({ nullable: true }),
			suspendedAt: State.SQLite.integer({
				nullable: true,
				schema: Schema.DateFromNumber,
			}),
			roleId: State.SQLite.text({ nullable: true }),
			roleName: State.SQLite.text({ nullable: true }),
			roleDisplayName: State.SQLite.text({ nullable: true }),
			roleHierarchyLevel: State.SQLite.integer({ default: 0 }),
			roleAssignedAt: State.SQLite.integer({
				nullable: true,
				schema: Schema.DateFromNumber,
			}),
			updatedAt: State.SQLite.integer({ schema: Schema.DateFromNumber }),
		},
	}),

	// Admin UI state (local only - filters, search, etc.)
	adminUIState: State.SQLite.clientDocument({
		name: "admin_ui_state",
		schema: Schema.Struct({
			userSearchQuery: Schema.String,
			userStatusFilter: Schema.Literal("all", "active", "suspended"),
			userRoleFilter: Schema.String,
			selectedUserId: Schema.optional(Schema.String),
			showUserManagement: Schema.Boolean,
			showSystemInfo: Schema.Boolean,
		}),
		default: {
			id: SessionIdSymbol,
			value: {
				userSearchQuery: "",
				userStatusFilter: "all" as const,
				userRoleFilter: "all",
				selectedUserId: undefined,
				showUserManagement: false,
				showSystemInfo: false,
			},
		},
	}),
};

// Events for banking operations - these sync across all clients
export const events = {
	// Withdrawal events
	withdrawalCreated: Events.synced({
		name: "v1.WithdrawalCreated",
		schema: Schema.Struct({
			id: Schema.String,
			amount: Schema.Number,
			requestedById: Schema.String,
			requestedByName: Schema.String,
			requestedByTornId: Schema.optional(Schema.Number),
			createdAt: Schema.Date,
		}),
	}),

	withdrawalStatusUpdated: Events.synced({
		name: "v1.WithdrawalStatusUpdated",
		schema: Schema.Struct({
			id: Schema.String,
			status: Schema.Literal(
				"PENDING",
				"ACCEPTED",
				"DECLINED",
				"COMPLETED",
				"CANCELLED",
				"EXPIRED",
			),
			processedById: Schema.optional(Schema.String),
			processedByName: Schema.optional(Schema.String),
			processedAt: Schema.optional(Schema.Date),
			transactionId: Schema.optional(Schema.String),
		}),
	}),

	// Balance events
	factionBalanceUpdated: Events.synced({
		name: "v1.FactionBalanceUpdated",
		schema: Schema.Struct({
			money: Schema.Number,
			points: Schema.Number,
			updatedAt: Schema.Date,
		}),
	}),

	// Target finder events
	targetListCreated: Events.synced({
		name: "v1.TargetListCreated",
		schema: Schema.Struct({
			id: Schema.String,
			name: Schema.String,
			userId: Schema.optional(Schema.String),
			isExternal: Schema.Boolean,
			createdAt: Schema.Date,
		}),
	}),

	targetListUpdated: Events.synced({
		name: "v1.TargetListUpdated",
		schema: Schema.Struct({
			id: Schema.String,
			name: Schema.String,
			updatedAt: Schema.Date,
		}),
	}),

	targetAdded: Events.synced({
		name: "v1.TargetAdded",
		schema: Schema.Struct({
			id: Schema.String,
			listId: Schema.String,
			name: Schema.String,
			tornId: Schema.String,
			status: Schema.String,
			profilePicture: Schema.optional(Schema.String),
			createdAt: Schema.Date,
		}),
	}),

	targetRemoved: Events.synced({
		name: "v1.TargetRemoved",
		schema: Schema.Struct({
			id: Schema.String,
			listId: Schema.String,
			tornId: Schema.String,
		}),
	}),

	targetStatusUpdated: Events.synced({
		name: "v1.TargetStatusUpdated",
		schema: Schema.Struct({
			id: Schema.String,
			tornId: Schema.String,
			status: Schema.String,
			profilePicture: Schema.optional(Schema.String),
			updatedAt: Schema.Date,
		}),
	}),

	targetStatusBatchUpdated: Events.synced({
		name: "v1.TargetStatusBatchUpdated",
		schema: Schema.Struct({
			updates: Schema.Array(
				Schema.Struct({
					id: Schema.String,
					tornId: Schema.String,
					status: Schema.String,
					profilePicture: Schema.optional(Schema.String),
				}),
			),
			updatedAt: Schema.Date,
		}),
	}),

	chainStatusUpdated: Events.synced({
		name: "v1.ChainStatusUpdated",
		schema: Schema.Struct({
			current: Schema.Number,
			max: Schema.Number,
			timeout: Schema.Number,
			cooldown: Schema.Number,
			end: Schema.Number,
			updatedAt: Schema.Date,
		}),
	}),

	targetFinderCooldownUpdated: Events.synced({
		name: "v1.TargetFinderCooldownUpdated",
		schema: Schema.Struct({
			remaining: Schema.Number,
			updatedAt: Schema.Date,
		}),
	}),

	// Announcement events
	announcementCreated: Events.synced({
		name: "v1.AnnouncementCreated",
		schema: Schema.Struct({
			id: Schema.String,
			title: Schema.String,
			content: Schema.String,
			category: Schema.String,
			authorId: Schema.String,
			authorName: Schema.String,
			createdAt: Schema.Date,
		}),
	}),

	announcementUpdated: Events.synced({
		name: "v1.AnnouncementUpdated",
		schema: Schema.Struct({
			id: Schema.String,
			title: Schema.String,
			content: Schema.String,
			category: Schema.String,
			updatedAt: Schema.Date,
		}),
	}),

	announcementDeleted: Events.synced({
		name: "v1.AnnouncementDeleted",
		schema: Schema.Struct({
			id: Schema.String,
		}),
	}),

	// Admin events
	adminSystemStatsUpdated: Events.synced({
		name: "v1.AdminSystemStatsUpdated",
		schema: Schema.Struct({
			totalUsers: Schema.Number,
			totalRoles: Schema.Number,
			totalPermissions: Schema.Number,
			totalGuides: Schema.Number,
			updatedAt: Schema.Date,
		}),
	}),

	// Temporary compatibility event for existing data
	systemStatsUpdated: Events.synced({
		name: "v1.SystemStatsUpdated",
		schema: Schema.Struct({
			totalUsers: Schema.Number,
			totalRoles: Schema.Number,
			totalPermissions: Schema.Number,
			totalGuides: Schema.Number,
			updatedAt: Schema.Date,
		}),
	}),

	adminAuditEntryCreated: Events.synced({
		name: "v1.AdminAuditEntryCreated",
		schema: Schema.Struct({
			id: Schema.String,
			assignedAt: Schema.Date,
			isActive: Schema.Boolean,
			assignedByUserId: Schema.optional(Schema.String),
			assignedByName: Schema.String,
			assignedToUserId: Schema.String,
			assignedToUserName: Schema.String,
			assignedToUserEmail: Schema.String,
			roleId: Schema.String,
			roleName: Schema.String,
			roleDisplayName: Schema.String,
			roleHierarchyLevel: Schema.Number,
		}),
	}),

	adminUserCreated: Events.synced({
		name: "v1.AdminUserCreated",
		schema: Schema.Struct({
			id: Schema.String,
			name: Schema.String,
			email: Schema.String,
			image: Schema.optional(Schema.String),
			createdAt: Schema.Date,
			tornUserId: Schema.optional(Schema.String),
			tornFactionId: Schema.optional(Schema.String),
			verified: Schema.Boolean,
			roleId: Schema.optional(Schema.String),
			roleName: Schema.optional(Schema.String),
			roleDisplayName: Schema.optional(Schema.String),
			roleHierarchyLevel: Schema.Number,
			roleAssignedAt: Schema.optional(Schema.Date),
		}),
	}),

	adminUserUpdated: Events.synced({
		name: "v1.AdminUserUpdated",
		schema: Schema.Struct({
			id: Schema.String,
			name: Schema.String,
			email: Schema.String,
			image: Schema.optional(Schema.String),
			tornUserId: Schema.optional(Schema.String),
			tornFactionId: Schema.optional(Schema.String),
			verified: Schema.Boolean,
			roleId: Schema.optional(Schema.String),
			roleName: Schema.optional(Schema.String),
			roleDisplayName: Schema.optional(Schema.String),
			roleHierarchyLevel: Schema.Number,
			roleAssignedAt: Schema.optional(Schema.Date),
			updatedAt: Schema.Date,
		}),
	}),

	adminUserSuspensionUpdated: Events.synced({
		name: "v1.AdminUserSuspensionUpdated",
		schema: Schema.Struct({
			userId: Schema.String,
			suspended: Schema.Boolean,
			reason: Schema.optional(Schema.String),
			suspendedAt: Schema.optional(Schema.Date),
			updatedAt: Schema.Date,
		}),
	}),

	adminUserDeleted: Events.synced({
		name: "v1.AdminUserDeleted",
		schema: Schema.Struct({
			userId: Schema.String,
		}),
	}),

	// UI state events (local only)
	bankingUIStateSet: tables.bankingUIState.set,
	targetFinderUIStateSet: tables.targetFinderUIState.set,
	announcementsUIStateSet: tables.announcementsUIState.set,
	adminUIStateSet: tables.adminUIState.set,
};

// Materializers map events to state changes
const materializers = State.SQLite.materializers(events, {
	"v1.WithdrawalCreated": ({
		id,
		amount,
		requestedById,
		requestedByName,
		requestedByTornId,
		createdAt,
	}) => [
		tables.withdrawalRequests.delete().where({ id }),
		tables.withdrawalRequests.insert({
			id,
			amount,
			status: "PENDING",
			requestedById,
			requestedByName,
			requestedByTornId,
			createdAt,
			updatedAt: createdAt,
		}),
	],

	"v1.WithdrawalStatusUpdated": ({
		id,
		status,
		processedById,
		processedByName,
		processedAt,
		transactionId,
	}) =>
		tables.withdrawalRequests
			.update({
				status,
				processedById,
				processedByName,
				processedAt,
				transactionId,
				updatedAt: processedAt || new Date(),
			})
			.where({ id }),

	"v1.FactionBalanceUpdated": ({ money, points, updatedAt }) => [
		tables.factionBalance.delete().where({ id: "faction_balance" }),
		tables.factionBalance.insert({
			id: "faction_balance",
			money,
			points,
			lastUpdated: updatedAt,
		}),
	],

	"v1.TargetListCreated": ({ id, name, userId, isExternal, createdAt }) => [
		tables.targetLists.delete().where({ id }),
		tables.targetLists.insert({
			id,
			name,
			userId,
			isExternal: isExternal ? 1 : 0,
			createdAt,
			updatedAt: createdAt,
		}),
	],

	"v1.TargetListUpdated": ({ id, name, updatedAt }) =>
		tables.targetLists.update({ name, updatedAt }).where({ id }),

	"v1.TargetAdded": ({
		id,
		listId,
		name,
		tornId,
		status,
		profilePicture,
		createdAt,
	}) => [
		tables.targets.delete().where({ id }),
		tables.targets.insert({
			id,
			listId,
			name,
			tornId,
			status,
			profilePicture,
			createdAt,
			updatedAt: createdAt,
		}),
	],

	"v1.TargetRemoved": ({ id }) => tables.targets.delete().where({ id }),

	"v1.TargetStatusUpdated": ({ id, status, profilePicture, updatedAt }) =>
		tables.targets
			.update({
				status,
				profilePicture,
				updatedAt,
			})
			.where({ id }),

	"v1.TargetStatusBatchUpdated": ({ updates, updatedAt }) =>
		updates.map(
			(update: { id: string; status: string; profilePicture?: string }) =>
				tables.targets
					.update({
						status: update.status,
						profilePicture: update.profilePicture,
						updatedAt,
					})
					.where({ id: update.id }),
		),

	"v1.ChainStatusUpdated": ({
		current,
		max,
		timeout,
		cooldown,
		end,
		updatedAt,
	}) => [
		tables.chainStatus.delete().where({ id: "chain_status" }),
		tables.chainStatus.insert({
			id: "chain_status",
			current,
			max,
			timeout,
			cooldown,
			end,
			lastUpdated: updatedAt,
		}),
	],

	"v1.TargetFinderCooldownUpdated": ({ remaining, updatedAt }) => [
		tables.targetFinderCooldown
			.delete()
			.where({ id: "target_finder_cooldown" }),
		tables.targetFinderCooldown.insert({
			id: "target_finder_cooldown",
			remaining,
			lastUpdated: updatedAt,
		}),
	],

	"v1.AnnouncementCreated": ({
		id,
		title,
		content,
		category,
		authorId,
		authorName,
		createdAt,
	}) => [
		tables.announcements.delete().where({ id }),
		tables.announcements.insert({
			id,
			title,
			content,
			category,
			authorId,
			authorName,
			createdAt,
			updatedAt: createdAt,
		}),
	],

	"v1.AnnouncementUpdated": ({ id, title, content, category, updatedAt }) =>
		tables.announcements
			.update({
				title,
				content,
				category,
				updatedAt,
			})
			.where({ id }),

	"v1.AnnouncementDeleted": ({ id }) =>
		tables.announcements.delete().where({ id }),

	// Shared function for system stats updates
	...(() => {
		const createSystemStatsOperations = ({
			totalUsers,
			totalRoles,
			totalPermissions,
			totalGuides,
			updatedAt,
		}: {
			totalUsers: number;
			totalRoles: number;
			totalPermissions: number;
			totalGuides: number;
			updatedAt: number;
		}) => [
			tables.adminSystemStats.delete().where({ id: "admin_system_stats" }),
			tables.adminSystemStats.insert({
				id: "admin_system_stats",
				totalUsers,
				totalRoles,
				totalPermissions,
				totalGuides,
				lastUpdated: updatedAt,
			}),
		];

		return {
			"v1.AdminSystemStatsUpdated": createSystemStatsOperations,
			// Temporary compatibility materializer for existing data
			"v1.SystemStatsUpdated": createSystemStatsOperations,
		};
	})(),

	"v1.AdminAuditEntryCreated": ({
		id,
		assignedAt,
		isActive,
		assignedByUserId,
		assignedByName,
		assignedToUserId,
		assignedToUserName,
		assignedToUserEmail,
		roleId,
		roleName,
		roleDisplayName,
		roleHierarchyLevel,
	}) => [
		tables.adminAuditLog.delete().where({ id }),
		tables.adminAuditLog.insert({
			id,
			assignedAt,
			isActive: isActive ? 1 : 0,
			assignedByUserId,
			assignedByName,
			assignedToUserId,
			assignedToUserName,
			assignedToUserEmail,
			roleId,
			roleName,
			roleDisplayName,
			roleHierarchyLevel,
		}),
	],

	"v1.AdminUserCreated": ({
		id,
		name,
		email,
		image,
		createdAt,
		tornUserId,
		tornFactionId,
		verified,
		roleId,
		roleName,
		roleDisplayName,
		roleHierarchyLevel,
		roleAssignedAt,
	}) => [
		tables.adminUsers.delete().where({ id }),
		tables.adminUsers.insert({
			id,
			name,
			email,
			image,
			createdAt,
			tornUserId,
			tornFactionId,
			verified: verified ? 1 : 0,
			suspended: 0, // Default to not suspended
			suspensionReason: null,
			suspendedAt: null,
			roleId,
			roleName,
			roleDisplayName,
			roleHierarchyLevel,
			roleAssignedAt,
			updatedAt: createdAt,
		}),
	],

	"v1.AdminUserUpdated": ({
		id,
		name,
		email,
		image,
		tornUserId,
		tornFactionId,
		verified,
		roleId,
		roleName,
		roleDisplayName,
		roleHierarchyLevel,
		roleAssignedAt,
		updatedAt,
	}) =>
		tables.adminUsers
			.update({
				name,
				email,
				image,
				tornUserId,
				tornFactionId,
				verified: verified ? 1 : 0,
				roleId,
				roleName,
				roleDisplayName,
				roleHierarchyLevel,
				roleAssignedAt,
				updatedAt,
			})
			.where({ id }),

	"v1.AdminUserSuspensionUpdated": ({
		userId,
		suspended,
		reason,
		suspendedAt,
		updatedAt,
	}) =>
		tables.adminUsers
			.update({
				suspended: suspended ? 1 : 0,
				suspensionReason: reason,
				suspendedAt,
				updatedAt,
			})
			.where({ id: userId }),

	"v1.AdminUserDeleted": ({ userId }) =>
		tables.adminUsers.delete().where({ id: userId }),
});

const state = State.SQLite.makeState({
	// biome-ignore lint/suspicious/noExplicitAny: LiveStore tables type is complex and not publicly exported
	tables: tables as any,
	// biome-ignore lint/suspicious/noExplicitAny: LiveStore materializers type is complex and not publicly exported
	materializers: materializers as any,
});

export const schema = makeSchema({
	events,
	state,
	// Version to force cache invalidation when schema changes
	version: "v2.0.0-admin-livestore",
});
