# Deployment Guide

This guide covers deploying MonkeyMenu to production environments.

## Architecture Overview

- **Frontend**: React app deployed to Cloudflare Pages
- **Backend**: Convex serverless functions
- **Authentication**: Clerk
- **Discord Bot**: Deployed separately

## Prerequisites

1. **Accounts Required:**
   - Convex account
   - Clerk account
   - Cloudflare account (for Pages hosting)
   - Discord Developer account

2. **Environment Variables:**
   - `VITE_CLERK_PUBLISHABLE_KEY`
   - `VITE_CONVEX_URL`
   - `DISCORD_TOKEN` (for bot deployment)

## Production Deployment Steps

### 1. Convex Backend Deployment

```bash
# Install Convex CLI
npm install -g convex

# Login to Convex
npx convex login

# Deploy to production
npx convex deploy --prod
```

This will:
- Deploy all Convex functions
- Set up the database schema
- Provide the production Convex URL

### 2. Environment Configuration

Create production environment variables:

```bash
# Clerk Configuration
VITE_CLERK_PUBLISHABLE_KEY=pk_live_...

# Convex Configuration  
VITE_CONVEX_URL=https://your-deployment.convex.cloud

# Discord Bot (if deploying bot)
DISCORD_TOKEN=your_bot_token
DISCORD_CLIENT_ID=your_client_id
```

### 3. Frontend Build and Deployment

```bash
# Build the application
npm run build:web

# Deploy to Cloudflare Pages
wrangler pages deploy dist --project-name=monkeymenu-production
```

### 4. Discord Worker Deployment

The Discord bot is now integrated as a Cloudflare Worker (no separate hosting required):

#### Environment Variables
Set these in your Cloudflare Worker environment:
```bash
DISCORD_APPLICATION_ID=your_discord_application_id
DISCORD_PUBLIC_KEY=your_discord_public_key
DISCORD_BOT_TOKEN=your_discord_bot_token
CONVEX_URL=your_convex_deployment_url
```

#### Deploy Discord Worker
```bash
# Deploy to staging
npm run discord:worker:deploy:staging

# Deploy to production
npm run discord:worker:deploy:prod

# Sync Discord slash commands
npm run discord:sync
```

#### Discord Application Setup
1. Go to Discord Developer Portal
2. Set Interaction Endpoint URL to:
   - Staging: `https://discord-bot-staging.monkeymenu.com/discord/interactions`
   - Production: `https://discord-bot.monkeymenu.com/discord/interactions`

#### Benefits of Worker Integration
- ✅ No separate hosting costs
- ✅ Automatic scaling
- ✅ Integrated with main infrastructure
- ✅ Better reliability and performance

## CI/CD Pipeline

The project includes GitHub Actions for automated deployment:

### Workflow Triggers
- **Staging**: Push to `develop` branch
- **Production**: Push to `main` branch
- **PR Checks**: Pull requests to `main` or `develop`

### Required Secrets

Set these in your GitHub repository settings:

```
# Convex
CONVEX_DEPLOY_KEY_STAGING
CONVEX_DEPLOY_KEY_PRODUCTION

# Cloudflare Pages
CLOUDFLARE_API_TOKEN
CLOUDFLARE_ACCOUNT_ID

# Security Scanning
SNYK_TOKEN

# Notifications
DISCORD_WEBHOOK
PRODUCTION_URL
```

## Security Considerations

### 1. Environment Variables
- Never commit secrets to the repository
- Use different keys for staging/production
- Rotate keys regularly

### 2. CORS Configuration
Ensure Convex CORS settings allow your domain:
```javascript
// convex/cors.ts
export default {
  origins: [
    "https://your-domain.com",
    "https://staging.your-domain.com"
  ]
}
```

### 3. Rate Limiting
Implement rate limiting for API endpoints:
```typescript
// In Convex functions
import { rateLimit } from './lib/rateLimit';

export const myFunction = mutation({
  handler: async (ctx, args) => {
    await rateLimit(ctx, 'user-action', 100, 3600); // 100 requests/hour
    // ... function logic
  }
});
```

## Monitoring and Logging

### 1. Application Monitoring
- Set up error tracking (Sentry, LogRocket)
- Monitor performance metrics
- Set up uptime monitoring

### 2. Discord Bot Monitoring
- Monitor bot uptime
- Track command usage
- Monitor for rate limiting

### 3. Database Monitoring
- Monitor Convex function performance
- Track database query patterns
- Set up alerts for errors

## Rollback Strategy

### 1. Frontend Rollback
```bash
# Cloudflare Pages - rollback to previous deployment
wrangler pages deployment list --project-name=monkeymenu-production
wrangler pages deployment rollback --project-name=monkeymenu-production --deployment-id=DEPLOYMENT_ID
```

### 2. Backend Rollback
```bash
# Convex - revert to previous deployment
npx convex deploy --revert-to=SNAPSHOT_ID
```

### 3. Database Migration Rollback
- Maintain migration scripts for schema changes
- Test rollback procedures in staging
- Have data backups ready

## Performance Optimization

### 1. Frontend Optimization
- Enable gzip/brotli compression
- Use CDN for static assets
- Implement proper caching headers
- Optimize images and fonts

### 2. Backend Optimization
- Optimize Convex function queries
- Use appropriate database indexes
- Implement caching where appropriate
- Monitor cold start times

## Scaling Considerations

### 1. Convex Scaling
- Convex scales automatically
- Monitor function execution times
- Optimize database queries
- Consider data partitioning for large datasets

### 2. Discord Bot Scaling
- Use Discord sharding for large servers
- Implement proper rate limiting
- Consider using Discord Gateway intents

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check TypeScript errors
   - Verify all dependencies are installed
   - Ensure environment variables are set

2. **Deployment Failures**
   - Verify API keys and permissions
   - Check network connectivity
   - Review deployment logs

3. **Runtime Errors**
   - Check application logs
   - Verify environment configuration
   - Test in staging environment first

### Support Contacts
- Development Team: [team email]
- DevOps: [devops email]
- Emergency: [emergency contact]

## Backup and Recovery

### 1. Database Backups
- Convex provides automatic backups
- Additional backups via export functions
- Test restore procedures regularly

### 2. Configuration Backups
- Store environment configurations securely
- Version control deployment scripts
- Document all external service configurations

### 3. Recovery Procedures
1. Identify the scope of the issue
2. Assess data integrity
3. Execute rollback if necessary
4. Communicate with users
5. Post-incident review