{"compilerOptions": {"target": "ES2022", "module": "ES2022", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "outDir": "./dist", "rootDir": ".", "types": ["@cloudflare/workers-types"], "lib": ["ES2022", "WebWorker"], "declaration": false, "sourceMap": false}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist", "../../convex/**/*"]}