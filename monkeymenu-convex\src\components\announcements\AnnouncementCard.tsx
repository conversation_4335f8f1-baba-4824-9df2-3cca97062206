

import { useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { usePermissions } from '../../hooks/usePermissions';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Edit, Pin, Trash2, MoreHorizontal } from 'lucide-react';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '../ui/dropdown-menu';

interface AnnouncementCardProps {
  announcement: {
    _id: Id<"announcements">;
    title: string;
    content: string;
    type: string;
    isPinned: boolean;
    isActive: boolean;
    expiresAt?: number;
    createdAt: number;
    updatedAt: number;
    author: {
      username: string;
      avatar?: string;
    } | null;
  };
  onEdit?: (announcement: any) => void;
  showActions?: boolean;
  viewMode?: 'grid' | 'list';
}

const typeConfig = {
  info: { emoji: '📢', label: 'Info', color: 'blue', variant: 'secondary' as const },
  warning: { emoji: '⚠️', label: 'Warning', color: 'yellow', variant: 'secondary' as const },
  urgent: { emoji: '🚨', label: 'Urgent', color: 'red', variant: 'destructive' as const },
  celebration: { emoji: '🎉', label: 'Celebration', color: 'green', variant: 'secondary' as const },
};

export function AnnouncementCard({ announcement, onEdit, showActions = true, viewMode = 'grid' }: AnnouncementCardProps) {
  const { canEditAnnouncements, canDeleteAnnouncements } = usePermissions();
  const deleteAnnouncement = useMutation(api.announcements.deleteAnnouncement);
  const togglePin = useMutation(api.announcements.togglePin);

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this announcement?')) return;
    
    try {
      await deleteAnnouncement({ id: announcement._id });
    } catch (error) {
      console.error('Failed to delete announcement:', error);
      alert('Failed to delete announcement');
    }
  };

  const handleTogglePin = async () => {
    try {
      await togglePin({ id: announcement._id });
    } catch (error) {
      console.error('Failed to toggle pin:', error);
      alert('Failed to toggle pin status');
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const isExpired = announcement.expiresAt && announcement.expiresAt < Date.now();
  const typeInfo = typeConfig[announcement.type as keyof typeof typeConfig] || typeConfig.info;

  // Generate preview text from markdown content
  const getPreviewText = (content: string, maxLength = 200) => {
    // Strip markdown syntax for preview
    const strippedContent = content
      .replace(/[#*_`~\[\]()]/g, '') // Remove markdown characters
      .replace(/\n+/g, ' ') // Replace line breaks with spaces
      .trim();
    
    return strippedContent.length > maxLength 
      ? strippedContent.substring(0, maxLength) + '...'
      : strippedContent;
  };

  if (viewMode === 'list') {
    // List view - horizontal, compact layout
    return (
      <Card className={`transition-all hover:shadow-md ${isExpired ? 'opacity-60' : ''}`}>
        <CardContent className="p-4">
          <div className="flex items-start gap-4">
            {/* Left side - badges and metadata */}
            <div className="flex flex-col gap-2 shrink-0">
              <Badge variant={typeInfo.variant} className="w-fit">
                {typeInfo.emoji} {typeInfo.label}
              </Badge>
              {announcement.isPinned && (
                <Badge variant="outline" className="w-fit">
                  <Pin className="h-3 w-3 mr-1" />
                  Pinned
                </Badge>
              )}
            </div>
            
            {/* Center - content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between mb-2">
                <h3 className="text-lg font-semibold leading-tight truncate pr-2">
                  {announcement.title}
                </h3>
                {showActions && (canEditAnnouncements() || canDeleteAnnouncements()) && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0 shrink-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {canEditAnnouncements() && (
                        <>
                          <DropdownMenuItem onClick={() => onEdit?.(announcement)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={handleTogglePin}>
                            <Pin className="h-4 w-4 mr-2" />
                            {announcement.isPinned ? 'Unpin' : 'Pin'}
                          </DropdownMenuItem>
                        </>
                      )}
                      {canDeleteAnnouncements() && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={handleDelete}
                            className="text-destructive focus:text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
              
              <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                {getPreviewText(announcement.content, 150)}
              </p>
              
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span>By {announcement.author?.username || 'Unknown'}</span>
                <span>•</span>
                <span>{formatDate(announcement.createdAt)}</span>
                {announcement.expiresAt && (
                  <>
                    <span>•</span>
                    <span className={isExpired ? 'text-destructive' : ''}>
                      Expires {formatDate(announcement.expiresAt)}
                    </span>
                  </>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Grid view - default vertical layout
  return (
    <Card className={`transition-all hover:shadow-md ${isExpired ? 'opacity-60' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2 flex-1">
            <Badge variant={typeInfo.variant} className="shrink-0">
              {typeInfo.emoji} {typeInfo.label}
            </Badge>
            {announcement.isPinned && (
              <Badge variant="outline" className="shrink-0">
                <Pin className="h-3 w-3 mr-1" />
                Pinned
              </Badge>
            )}
            {isExpired && (
              <Badge variant="secondary" className="shrink-0">
                Expired
              </Badge>
            )}
          </div>
          
          {showActions && (canEditAnnouncements() || canDeleteAnnouncements()) && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {canEditAnnouncements() && (
                  <>
                    <DropdownMenuItem onClick={() => onEdit?.(announcement)}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleTogglePin}>
                      <Pin className="h-4 w-4 mr-2" />
                      {announcement.isPinned ? 'Unpin' : 'Pin'}
                    </DropdownMenuItem>
                  </>
                )}
                {canDeleteAnnouncements() && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={handleDelete}
                      className="text-destructive focus:text-destructive"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
        
        <CardTitle className="text-xl leading-tight">
          {announcement.title}
        </CardTitle>
        
        <CardDescription className="flex items-center gap-2 text-sm">
          <span>By {announcement.author?.username || 'Unknown'}</span>
          <span>•</span>
          <span>{formatDate(announcement.createdAt)}</span>
          {announcement.expiresAt && (
            <>
              <span>•</span>
              <span className={isExpired ? 'text-destructive' : 'text-muted-foreground'}>
                Expires {formatDate(announcement.expiresAt)}
              </span>
            </>
          )}
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <div className="prose prose-sm max-w-none dark:prose-invert">
          <ReactMarkdown 
            remarkPlugins={[remarkGfm]}
            components={{
              // Prevent images from being too large
              img: ({ ...props }) => (
                <img {...props} className="max-w-full h-auto rounded-md" />
              ),
              // Style links
              a: ({ ...props }) => (
                <a {...props} className="text-primary hover:underline" target="_blank" rel="noopener noreferrer" />
              ),
            }}
          >
            {announcement.content}
          </ReactMarkdown>
        </div>
      </CardContent>
    </Card>
  );
}