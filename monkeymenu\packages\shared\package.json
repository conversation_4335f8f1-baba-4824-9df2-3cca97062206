{"name": "@monkeymenu/shared", "version": "0.0.1", "description": "Shared types and constants for MonkeyMenu", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "check-types": "tsc --noEmit", "format": "biome format", "lint": "biome lint", "check": "biome check"}, "dependencies": {"zod": "^3.25.46"}, "devDependencies": {"@types/node": "^20.17.57", "typescript": "^5.8.3"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}