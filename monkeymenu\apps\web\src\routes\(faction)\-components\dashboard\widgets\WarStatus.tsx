import { Badge } from "@/components/ui/badge";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { trpc } from "@/lib/trpc-client";
import { useQuery } from "@tanstack/react-query";
import {
	AlertTriangle,
	Calendar,
	Clock,
	Shield,
	Swords,
	Target,
} from "lucide-react";
import { useMemo } from "react";
import type { Faction, RankedWar } from "../../wars/types";

// Faction ID constant (matches wars component)
const MY_FACTION_ID = 53100;

function WarStatusSkeleton() {
	return (
		<Card className="min-h-[140px]">
			<CardHeader className="pb-2">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-2">
						<Skeleton className="h-4 w-4" />
						<Skeleton className="h-4 w-20" />
					</div>
					<Skeleton className="h-5 w-16" />
				</div>
			</CardHeader>
			<CardContent className="space-y-3 pt-0">
				<div className="flex items-center gap-2">
					<Skeleton className="h-4 w-4" />
					<Skeleton className="h-4 w-32" />
				</div>
				<div className="space-y-2">
					<Skeleton className="h-3 w-20" />
					<Skeleton className="h-4 w-24" />
				</div>
			</CardContent>
		</Card>
	);
}

function formatTimeRemaining(seconds: number): string {
	const days = Math.floor(seconds / 86400);
	const hours = Math.floor((seconds % 86400) / 3600);
	const minutes = Math.floor((seconds % 3600) / 60);

	if (days > 0) {
		return `${days}d ${hours}h`;
	}
	if (hours > 0) {
		return `${hours}h ${minutes}m`;
	}
	return `${minutes}m`;
}

function formatDate(timestamp: number): string {
	return new Date(timestamp * 1000).toLocaleDateString("en-US", {
		month: "short",
		day: "numeric",
		year: "numeric",
	});
}

export function WarStatus() {
	const { data, isLoading, error } = useQuery({
		...trpc.wars.listRankedWars.queryOptions(),
	});

	const warToDisplay = useMemo(() => {
		if (!data?.rankedWars || data.rankedWars.length === 0) {
			return null;
		}

		const wars = data.rankedWars as RankedWar[];
		const now = Math.floor(Date.now() / 1000);

		// First, check for current wars (started but not finished, winner is null)
		const currentWar = wars.find(
			(war) =>
				war.start <= now && war.winner === null && (!war.end || war.end > now),
		);

		if (currentWar) {
			return {
				war: currentWar,
				type: "current" as const,
			};
		}

		// Next, check for upcoming wars (start time in future)
		const upcomingWar = wars.find((war) => war.start > now);

		if (upcomingWar) {
			return {
				war: upcomingWar,
				type: "upcoming" as const,
			};
		}

		// Finally, get the most recent completed war
		const completedWars = wars.filter((war) => war.winner !== null);
		const lastWar = completedWars.sort(
			(a, b) => (b.end || b.start) - (a.end || a.start),
		)[0];

		if (lastWar) {
			return {
				war: lastWar,
				type: "last" as const,
			};
		}

		return null;
	}, [data?.rankedWars]);

	if (isLoading) {
		return <WarStatusSkeleton />;
	}

	if (error) {
		return (
			<Card>
				<CardHeader className="pb-2">
					<div className="flex items-center gap-2">
						<Shield className="h-4 w-4 text-muted-foreground" />
						<CardTitle className="text-base">War Status</CardTitle>
					</div>
				</CardHeader>
				<CardContent className="pt-0">
					<div className="flex items-center gap-2 text-red-500">
						<AlertTriangle className="h-4 w-4" />
						<p className="text-muted-foreground text-sm">
							Failed to load war information
						</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	if (!warToDisplay) {
		return (
			<Card>
				<CardHeader className="pb-2">
					<div className="flex items-center gap-2">
						<Shield className="h-4 w-4 text-muted-foreground" />
						<CardTitle className="text-base">War Status</CardTitle>
					</div>
				</CardHeader>
				<CardContent className="pt-0">
					<div className="flex items-center gap-2">
						<Shield className="h-4 w-4 text-muted-foreground/70" />
						<p className="text-muted-foreground text-sm">
							Your faction is currently at peace
						</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	const { war, type } = warToDisplay;
	const now = Math.floor(Date.now() / 1000);

	// Find our faction and opponent
	const ourFaction = war.factions.find((f: Faction) => f.id === MY_FACTION_ID);
	const opponent = war.factions.find((f: Faction) => f.id !== MY_FACTION_ID);

	// Determine status for current wars
	const isWinning = ourFaction && opponent && ourFaction.score > opponent.score;

	// Calculate time information
	let timeInfo = "";
	let TimeIconComponent = Clock;
	let timeColor = "text-orange-600";

	if (type === "current") {
		if (war.end && war.end > now) {
			timeInfo = `${formatTimeRemaining(war.end - now)} left`;
		} else {
			timeInfo = "Ongoing";
		}
	} else if (type === "upcoming") {
		timeInfo = `Starts in ${formatTimeRemaining(war.start - now)}`;
		TimeIconComponent = Calendar;
		timeColor = "text-blue-600";
	} else {
		timeInfo = `Ended ${formatDate(war.end || war.start)}`;
		TimeIconComponent = Calendar;
		timeColor = "text-muted-foreground";
	}

	// Status badge
	let statusLabel = "";
	let badgeVariant: "default" | "destructive" | "secondary" | "outline" =
		"secondary";

	if (type === "current") {
		statusLabel = isWinning ? "Winning" : "Losing";
		badgeVariant = isWinning ? "default" : "destructive";
	} else if (type === "upcoming") {
		statusLabel = "Upcoming";
		badgeVariant = "outline";
	} else {
		const isWin = war.winner === MY_FACTION_ID;
		statusLabel = isWin ? "Victory" : "Defeat";
		badgeVariant = isWin ? "default" : "destructive";
	}

	// Icon and title
	const iconProps =
		type === "current"
			? { icon: Swords, className: "h-4 w-4 text-red-500" }
			: type === "upcoming"
				? { icon: Calendar, className: "h-4 w-4 text-blue-500" }
				: { icon: Shield, className: "h-4 w-4 text-muted-foreground" };

	const IconComponent = iconProps.icon;

	return (
		<Card>
			<CardHeader className="pb-2">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-2">
						<IconComponent className={iconProps.className} />
						<CardTitle className="text-base">
							{type === "current"
								? "Current War"
								: type === "upcoming"
									? "Upcoming War"
									: "Last War"}
						</CardTitle>
					</div>
					<Badge variant={badgeVariant} className="text-xs">
						{statusLabel}
					</Badge>
				</div>
				<CardDescription className="flex items-center gap-2 text-sm">
					<span>vs {opponent?.name ?? "Unknown"}</span>
					<div className={`flex items-center gap-1 ${timeColor}`}>
						<TimeIconComponent className="h-3 w-3" />
						<span className="text-xs">{timeInfo}</span>
					</div>
				</CardDescription>
			</CardHeader>
			<CardContent className="pt-0">
				{/* Score Comparison - only show for current and completed wars */}
				{(type === "current" || type === "last") && ourFaction && opponent && (
					<div className="mb-3 space-y-2">
						<div className="flex items-center justify-between text-sm">
							<span className="font-medium">Score</span>
							<span className="text-muted-foreground text-xs">
								{ourFaction.score > opponent.score ? "+" : ""}
								{ourFaction.score - opponent.score}
							</span>
						</div>
						<div className="flex items-center gap-2">
							<div className="flex-1 text-center">
								<div
									className={`font-bold text-lg ${
										ourFaction.score > opponent.score
											? "text-green-600"
											: "text-red-600"
									}`}
								>
									{ourFaction.score.toLocaleString()}
								</div>
								<div className="text-muted-foreground text-xs">Us</div>
							</div>
							<div className="text-muted-foreground">vs</div>
							<div className="flex-1 text-center">
								<div
									className={`font-bold text-lg ${
										opponent.score > ourFaction.score
											? "text-green-600"
											: "text-red-600"
									}`}
								>
									{opponent.score.toLocaleString()}
								</div>
								<div className="text-muted-foreground text-xs">Them</div>
							</div>
						</div>
					</div>
				)}

				{/* War Info */}
				<div
					className={`space-y-3 ${(type === "current" || type === "last") && ourFaction && opponent ? "border-t pt-3" : ""}`}
				>
					{type === "upcoming" ? (
						<>
							<div className="grid grid-cols-2 gap-3">
								<div className="text-center">
									<div className="mb-1 flex items-center justify-center gap-1">
										<Calendar className="h-3 w-3 text-muted-foreground" />
										<span className="text-muted-foreground text-xs">
											Start Date
										</span>
									</div>
									<div className="font-medium text-sm">
										{formatDate(war.start)}
									</div>
								</div>
								<div className="text-center">
									<div className="mb-1 flex items-center justify-center gap-1">
										<Clock className="h-3 w-3 text-muted-foreground" />
										<span className="text-muted-foreground text-xs">
											Starts In
										</span>
									</div>
									<div className="font-medium text-sm">
										{formatTimeRemaining(war.start - now)}
									</div>
								</div>
							</div>

							{/* Faction Comparison */}
							<div className="grid grid-cols-2 gap-3">
								<div className="text-center">
									<div className="mb-1 flex items-center justify-center gap-1">
										<Shield className="h-3 w-3 text-muted-foreground" />
										<span className="text-muted-foreground text-xs">
											Our Members
										</span>
									</div>
									<div className="font-medium text-sm">
										{typeof ourFaction?.members === "number"
											? ourFaction.members
											: Array.isArray(ourFaction?.members)
												? ourFaction.members.length
												: 0}
									</div>
								</div>
								<div className="text-center">
									<div className="mb-1 flex items-center justify-center gap-1">
										<Target className="h-3 w-3 text-muted-foreground" />
										<span className="text-muted-foreground text-xs">
											Their Members
										</span>
									</div>
									<div className="font-medium text-sm">
										{typeof opponent?.members === "number"
											? opponent.members
											: Array.isArray(opponent?.members)
												? opponent.members.length
												: 0}
									</div>
								</div>
							</div>

							{/* Member Advantage */}
							<div className="space-y-2">
								<div className="flex items-center justify-between text-xs">
									<span className="text-muted-foreground">
										Member Advantage
									</span>
									<span
										className={`font-medium ${
											(
												typeof ourFaction?.members === "number"
													? ourFaction.members
													: Array.isArray(ourFaction?.members)
														? ourFaction.members.length
														: 0
											) >
											(
												typeof opponent?.members === "number"
													? opponent.members
													: Array.isArray(opponent?.members)
														? opponent.members.length
														: 0
											)
												? "text-green-600"
												: "text-red-600"
										}`}
									>
										{ourFaction && opponent
											? `${
													(typeof ourFaction.members === "number"
														? ourFaction.members
														: Array.isArray(ourFaction.members)
															? ourFaction.members.length
															: 0) -
														(typeof opponent.members === "number"
															? opponent.members
															: Array.isArray(opponent.members)
																? opponent.members.length
																: 0) >
													0
														? "+"
														: ""
												}${
													(typeof ourFaction.members === "number"
														? ourFaction.members
														: Array.isArray(ourFaction.members)
															? ourFaction.members.length
															: 0) -
													(typeof opponent.members === "number"
														? opponent.members
														: Array.isArray(opponent.members)
															? opponent.members.length
															: 0)
												}`
											: "0"}
									</span>
								</div>
								<Progress
									value={
										ourFaction && opponent
											? ((typeof ourFaction.members === "number"
													? ourFaction.members
													: Array.isArray(ourFaction.members)
														? ourFaction.members.length
														: 0) /
													Math.max(
														1,
														(typeof ourFaction.members === "number"
															? ourFaction.members
															: Array.isArray(ourFaction.members)
																? ourFaction.members.length
																: 0) +
															(typeof opponent.members === "number"
																? opponent.members
																: Array.isArray(opponent.members)
																	? opponent.members.length
																	: 0),
													)) *
												100
											: 50
									}
									className="h-2"
								/>
							</div>

							{/* War Preparation Info */}
							<div className="grid grid-cols-2 gap-3">
								<div className="text-center">
									<div className="mb-1 flex items-center justify-center gap-1">
										<Swords className="h-3 w-3 text-muted-foreground" />
										<span className="text-muted-foreground text-xs">
											War ID
										</span>
									</div>
									<div className="font-medium text-sm">#{war.id}</div>
								</div>
								<div className="text-center">
									<div className="mb-1 flex items-center justify-center gap-1">
										<AlertTriangle className="h-3 w-3 text-muted-foreground" />
										<span className="text-muted-foreground text-xs">
											Prep Time
										</span>
									</div>
									<div className="font-medium text-sm">
										{war.start - now > 3600
											? `${Math.floor((war.start - now) / 3600)}h ${Math.floor(((war.start - now) % 3600) / 60)}m`
											: `${Math.floor((war.start - now) / 60)}m`}
									</div>
								</div>
							</div>

							{/* Strategic Information */}
							<div className="space-y-2 rounded-lg bg-muted/50 p-3">
								<div className="flex items-center gap-2">
									<Target className="h-3 w-3 text-blue-500" />
									<span className="font-medium text-blue-600 text-xs">
										Preparation Tips
									</span>
								</div>
								<div className="space-y-1 text-muted-foreground text-xs">
									<div>• Ensure all members are aware of the war time</div>
									<div>• Stock up on medical items and energy</div>
									<div>• Review enemy faction member list</div>
									<div>• Coordinate attack strategies in Discord</div>
								</div>
							</div>
						</>
					) : (
						<>
							<div className="grid grid-cols-2 gap-3">
								<div className="text-center">
									<div className="mb-1 flex items-center justify-center gap-1">
										<Target className="h-3 w-3 text-muted-foreground" />
										<span className="text-muted-foreground text-xs">
											Our Attacks
										</span>
									</div>
									<div className="font-medium text-sm">
										{ourFaction?.attacks.toLocaleString() ?? "0"}
									</div>
								</div>
								<div className="text-center">
									<div className="mb-1 flex items-center justify-center gap-1">
										<Swords className="h-3 w-3 text-muted-foreground" />
										<span className="text-muted-foreground text-xs">
											Their Attacks
										</span>
									</div>
									<div className="font-medium text-sm">
										{opponent?.attacks.toLocaleString() ?? "0"}
									</div>
								</div>
							</div>
							<div className="grid grid-cols-2 gap-3">
								<div className="text-center">
									<div className="mb-1 flex items-center justify-center gap-1">
										<Shield className="h-3 w-3 text-muted-foreground" />
										<span className="text-muted-foreground text-xs">
											Our Members
										</span>
									</div>
									<div className="font-medium text-sm">
										{typeof ourFaction?.members === "number"
											? ourFaction.members
											: Array.isArray(ourFaction?.members)
												? ourFaction.members.length
												: 0}
									</div>
								</div>
								<div className="text-center">
									<div className="mb-1 flex items-center justify-center gap-1">
										<Calendar className="h-3 w-3 text-muted-foreground" />
										<span className="text-muted-foreground text-xs">
											War ID
										</span>
									</div>
									<div className="font-medium text-sm">#{war.id}</div>
								</div>
							</div>
							{type === "current" && (
								<>
									<div className="grid grid-cols-2 gap-3">
										<div className="text-center">
											<div className="mb-1 flex items-center justify-center gap-1">
												<Clock className="h-3 w-3 text-muted-foreground" />
												<span className="text-muted-foreground text-xs">
													Duration
												</span>
											</div>
											<div className="font-medium text-sm">
												{formatTimeRemaining(now - war.start)}
											</div>
										</div>
										<div className="text-center">
											<div className="mb-1 flex items-center justify-center gap-1">
												<Target className="h-3 w-3 text-muted-foreground" />
												<span className="text-muted-foreground text-xs">
													Total Attacks
												</span>
											</div>
											<div className="font-medium text-sm">
												{(
													(ourFaction?.attacks ?? 0) + (opponent?.attacks ?? 0)
												).toLocaleString()}
											</div>
										</div>
									</div>

									{/* Score Progress Bar */}
									<div className="space-y-2">
										<div className="flex items-center justify-between text-xs">
											<span className="text-muted-foreground">
												Score Progress
											</span>
											<span className="font-medium">
												{ourFaction && opponent
													? `${((ourFaction.score / (ourFaction.score + opponent.score)) * 100 || 0).toFixed(1)}%`
													: "0%"}
											</span>
										</div>
										<Progress
											value={
												ourFaction && opponent
													? (ourFaction.score /
															(ourFaction.score + opponent.score)) *
															100 || 0
													: 0
											}
											className="h-2"
										/>
									</div>

									{/* Attack Rate */}
									<div className="grid grid-cols-2 gap-3">
										<div className="text-center">
											<div className="mb-1 flex items-center justify-center gap-1">
												<Swords className="h-3 w-3 text-muted-foreground" />
												<span className="text-muted-foreground text-xs">
													Attack Rate
												</span>
											</div>
											<div className="font-medium text-sm">
												{ourFaction
													? `${(ourFaction.attacks / Math.max(1, (now - war.start) / 3600)).toFixed(1)}/hr`
													: "0/hr"}
											</div>
										</div>
										<div className="text-center">
											<div className="mb-1 flex items-center justify-center gap-1">
												<Target className="h-3 w-3 text-muted-foreground" />
												<span className="text-muted-foreground text-xs">
													Avg per Member
												</span>
											</div>
											<div className="font-medium text-sm">
												{ourFaction
													? `${(ourFaction.attacks / Math.max(1, typeof ourFaction.members === "number" ? ourFaction.members : Array.isArray(ourFaction.members) ? ourFaction.members.length : 1)).toFixed(1)}`
													: "0"}
											</div>
										</div>
									</div>
								</>
							)}

							{type === "last" && (
								<div className="grid grid-cols-2 gap-3">
									<div className="text-center">
										<div className="mb-1 flex items-center justify-center gap-1">
											<Clock className="h-3 w-3 text-muted-foreground" />
											<span className="text-muted-foreground text-xs">
												Duration
											</span>
										</div>
										<div className="font-medium text-sm">
											{war.end
												? formatTimeRemaining(war.end - war.start)
												: "Unknown"}
										</div>
									</div>
									<div className="text-center">
										<div className="mb-1 flex items-center justify-center gap-1">
											<Target className="h-3 w-3 text-muted-foreground" />
											<span className="text-muted-foreground text-xs">
												Final Margin
											</span>
										</div>
										<div className="font-medium text-sm">
											{ourFaction && opponent
												? `${Math.abs(ourFaction.score - opponent.score).toLocaleString()}`
												: "0"}
										</div>
									</div>
								</div>
							)}
						</>
					)}
				</div>
			</CardContent>
		</Card>
	);
}
