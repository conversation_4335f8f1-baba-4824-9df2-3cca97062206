---
description: 
globs: apps/server/**
alwaysApply: false
---
# Server API Development Guide

The server application is a Cloudflare Workers-based API located in [apps/server](mdc:apps/server).

## Entry Point

The main application entry point is [apps/server/src/index.ts](mdc:apps/server/src/index.ts), which sets up:
- Hono app configuration
- tRPC server setup
- Authentication middleware
- CORS handling
- Route registration

## Key Directories

### Database Layer
- [apps/server/src/db/schema](mdc:apps/server/src/db/schema) - Drizzle schema definitions
- [apps/server/src/db/migrations](mdc:apps/server/src/db/migrations) - Database migrations
- [apps/server/src/db/seeds](mdc:apps/server/src/db/seeds) - Database seeding scripts

### API Routes
- [apps/server/src/routers](mdc:apps/server/src/routers) - tRPC router definitions organized by feature
- [apps/server/src/middlewares](mdc:apps/server/src/middlewares) - Custom middleware functions

### Discord Integration
- [apps/server/src/discord](mdc:apps/server/src/discord) - Discord bot commands and interactions
- [apps/server/src/discord/commands](mdc:apps/server/src/discord/commands) - Slash commands organized by category

### Background Jobs
- [apps/server/src/cron](mdc:apps/server/src/cron) - Scheduled task handlers for Cloudflare Workers

### Utilities
- [apps/server/src/lib](mdc:apps/server/src/lib) - Shared utilities and helper functions

## Development Commands

```bash
# Start development server
pnpm dev:server

# Database operations
pnpm db:generate    # Generate migrations
pnpm db:migrate     # Apply migrations locally
pnpm db:studio      # Open Drizzle Studio

# Deployment
pnpm cf:deploy      # Deploy to Cloudflare Workers
pnpm cf:typegen     # Generate Cloudflare types
```

## Configuration Files

- [apps/server/wrangler.jsonc](mdc:apps/server/wrangler.jsonc) - Cloudflare Workers configuration
- [apps/server/drizzle.config.ts](mdc:apps/server/drizzle.config.ts) - Drizzle ORM configuration
- [apps/server/.dev.vars](mdc:apps/server/.dev.vars) - Local environment variables (generated from Doppler)

## Development Patterns

- Use tRPC for type-safe API endpoints
- Organize routers by feature/domain
- Use Drizzle ORM for database operations
- Environment variables are managed via Doppler
- Authentication is handled by better-auth
- Discord integration uses slash-create library
