{"name": "turbo-cloud", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"check": "biome check --write .", "prepare": "husky", "dev": "turbo dev", "build": "turbo build", "build:shared": "turbo -F @monkeymenu/shared build", "check-types": "turbo check-types", "dev:web": "turbo -F web dev", "dev:server": "turbo -F server dev", "dev:shared": "turbo -F @monkeymenu/shared dev", "db:push": "turbo -F server db:push", "db:studio": "turbo -F server db:studio", "db:generate": "turbo -F server db:generate", "db:migrate": "turbo -F server db:migrate", "cf:deploy": "turbo -F @monkeymenu/shared -F web build && turbo -F server cf:typegen && turbo -F server cf:deploy"}, "devDependencies": {"@biomejs/biome": "1.9.4", "husky": "^9.1.7", "lint-staged": "^15.5.2", "turbo": "^2.5.4", "typescript": "~5.7.3"}, "lint-staged": {"*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}": ["biome check --write ."]}, "packageManager": "pnpm@10.11.1", "engines": {"node": ">=18"}}