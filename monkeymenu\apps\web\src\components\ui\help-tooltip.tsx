import {
	Toolt<PERSON>,
	Too<PERSON><PERSON>Content,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	Toolt<PERSON>Trigger,
} from "@/components/ui/tooltip";
import { HelpCircle } from "lucide-react";
import type { ReactNode } from "react";

interface HelpTooltipProps {
	content: string | ReactNode;
	children?: ReactNode;
	side?: "top" | "right" | "bottom" | "left";
	className?: string;
}

export function HelpTooltip({
	content,
	children,
	side = "top",
	className = "",
}: HelpTooltipProps) {
	return (
		<TooltipProvider>
			<Tooltip>
				<TooltipTrigger asChild>
					{children || (
						<HelpCircle
							className={`h-4 w-4 cursor-help text-muted-foreground transition-colors hover:text-foreground ${className}`}
						/>
					)}
				</TooltipTrigger>
				<TooltipContent side={side} className="max-w-xs">
					{content}
				</TooltipContent>
			</Tooltip>
		</TooltipProvider>
	);
}
