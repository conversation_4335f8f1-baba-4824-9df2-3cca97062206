import { sql } from "drizzle-orm";
import type { InferSelectModel } from "drizzle-orm";
import { index, integer, sqliteTable, text } from "drizzle-orm/sqlite-core";
import { user } from "./auth";

export const guide = sqliteTable(
	"guide",
	{
		id: integer().primaryKey(),
		title: text().notNull(),
		content: text().notNull(),
		category: text().notNull().default("general"),
		authorId: text("author_id")
			.notNull()
			.references(() => user.id, { onDelete: "cascade" }),
		createdAt: text("created_at", { mode: "text" })
			.notNull()
			.default(sql`CURRENT_TIMESTAMP`),
		updatedAt: text("updated_at", { mode: "text" })
			.notNull()
			.default(sql`CURRENT_TIMESTAMP`),
	},
	(table) => [
		index("idx_guide_created_at").on(table.createdAt),
		index("idx_guide_author_id").on(table.authorId),
		index("idx_guide_category").on(table.category),
	],
);

export type Guide = InferSelectModel<typeof guide>;
