/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as AccessDeniedRouteImport } from './routes/access-denied'
import { Route as protectedLayoutRouteImport } from './routes/(protected)/layout'
import { Route as factionLayoutRouteImport } from './routes/(faction)/layout'
import { Route as authLayoutRouteImport } from './routes/(auth)/layout'
import { Route as IndexRouteImport } from './routes/index'
import { Route as protectedProfileRouteImport } from './routes/(protected)/profile'
import { Route as protectedOnboardingRouteImport } from './routes/(protected)/onboarding'
import { Route as factionWarsRouteImport } from './routes/(faction)/wars'
import { Route as factionTargetFinderRouteImport } from './routes/(faction)/target-finder'
import { Route as factionGuidesRouteImport } from './routes/(faction)/guides'
import { Route as factionDashboardRouteImport } from './routes/(faction)/dashboard'
import { Route as factionBankingRouteImport } from './routes/(faction)/banking'
import { Route as factionAnnouncementsRouteImport } from './routes/(faction)/announcements'
import { Route as factionAdminRouteImport } from './routes/(faction)/admin'
import { Route as authSignInRouteImport } from './routes/(auth)/sign-in'

const AccessDeniedRoute = AccessDeniedRouteImport.update({
  id: '/access-denied',
  path: '/access-denied',
  getParentRoute: () => rootRouteImport,
} as any)
const protectedLayoutRoute = protectedLayoutRouteImport.update({
  id: '/(protected)',
  getParentRoute: () => rootRouteImport,
} as any)
const factionLayoutRoute = factionLayoutRouteImport.update({
  id: '/(faction)',
  getParentRoute: () => rootRouteImport,
} as any)
const authLayoutRoute = authLayoutRouteImport.update({
  id: '/(auth)',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const protectedProfileRoute = protectedProfileRouteImport.update({
  id: '/profile',
  path: '/profile',
  getParentRoute: () => protectedLayoutRoute,
} as any)
const protectedOnboardingRoute = protectedOnboardingRouteImport.update({
  id: '/onboarding',
  path: '/onboarding',
  getParentRoute: () => protectedLayoutRoute,
} as any)
const factionWarsRoute = factionWarsRouteImport.update({
  id: '/wars',
  path: '/wars',
  getParentRoute: () => factionLayoutRoute,
} as any)
const factionTargetFinderRoute = factionTargetFinderRouteImport.update({
  id: '/target-finder',
  path: '/target-finder',
  getParentRoute: () => factionLayoutRoute,
} as any)
const factionGuidesRoute = factionGuidesRouteImport.update({
  id: '/guides',
  path: '/guides',
  getParentRoute: () => factionLayoutRoute,
} as any)
const factionDashboardRoute = factionDashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => factionLayoutRoute,
} as any)
const factionBankingRoute = factionBankingRouteImport.update({
  id: '/banking',
  path: '/banking',
  getParentRoute: () => factionLayoutRoute,
} as any)
const factionAnnouncementsRoute = factionAnnouncementsRouteImport.update({
  id: '/announcements',
  path: '/announcements',
  getParentRoute: () => factionLayoutRoute,
} as any)
const factionAdminRoute = factionAdminRouteImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => factionLayoutRoute,
} as any)
const authSignInRoute = authSignInRouteImport.update({
  id: '/sign-in',
  path: '/sign-in',
  getParentRoute: () => authLayoutRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof protectedLayoutRouteWithChildren
  '/access-denied': typeof AccessDeniedRoute
  '/sign-in': typeof authSignInRoute
  '/admin': typeof factionAdminRoute
  '/announcements': typeof factionAnnouncementsRoute
  '/banking': typeof factionBankingRoute
  '/dashboard': typeof factionDashboardRoute
  '/guides': typeof factionGuidesRoute
  '/target-finder': typeof factionTargetFinderRoute
  '/wars': typeof factionWarsRoute
  '/onboarding': typeof protectedOnboardingRoute
  '/profile': typeof protectedProfileRoute
}
export interface FileRoutesByTo {
  '/': typeof protectedLayoutRouteWithChildren
  '/access-denied': typeof AccessDeniedRoute
  '/sign-in': typeof authSignInRoute
  '/admin': typeof factionAdminRoute
  '/announcements': typeof factionAnnouncementsRoute
  '/banking': typeof factionBankingRoute
  '/dashboard': typeof factionDashboardRoute
  '/guides': typeof factionGuidesRoute
  '/target-finder': typeof factionTargetFinderRoute
  '/wars': typeof factionWarsRoute
  '/onboarding': typeof protectedOnboardingRoute
  '/profile': typeof protectedProfileRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/(auth)': typeof authLayoutRouteWithChildren
  '/(faction)': typeof factionLayoutRouteWithChildren
  '/(protected)': typeof protectedLayoutRouteWithChildren
  '/access-denied': typeof AccessDeniedRoute
  '/(auth)/sign-in': typeof authSignInRoute
  '/(faction)/admin': typeof factionAdminRoute
  '/(faction)/announcements': typeof factionAnnouncementsRoute
  '/(faction)/banking': typeof factionBankingRoute
  '/(faction)/dashboard': typeof factionDashboardRoute
  '/(faction)/guides': typeof factionGuidesRoute
  '/(faction)/target-finder': typeof factionTargetFinderRoute
  '/(faction)/wars': typeof factionWarsRoute
  '/(protected)/onboarding': typeof protectedOnboardingRoute
  '/(protected)/profile': typeof protectedProfileRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/access-denied'
    | '/sign-in'
    | '/admin'
    | '/announcements'
    | '/banking'
    | '/dashboard'
    | '/guides'
    | '/target-finder'
    | '/wars'
    | '/onboarding'
    | '/profile'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/access-denied'
    | '/sign-in'
    | '/admin'
    | '/announcements'
    | '/banking'
    | '/dashboard'
    | '/guides'
    | '/target-finder'
    | '/wars'
    | '/onboarding'
    | '/profile'
  id:
    | '__root__'
    | '/'
    | '/(auth)'
    | '/(faction)'
    | '/(protected)'
    | '/access-denied'
    | '/(auth)/sign-in'
    | '/(faction)/admin'
    | '/(faction)/announcements'
    | '/(faction)/banking'
    | '/(faction)/dashboard'
    | '/(faction)/guides'
    | '/(faction)/target-finder'
    | '/(faction)/wars'
    | '/(protected)/onboarding'
    | '/(protected)/profile'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  authLayoutRoute: typeof authLayoutRouteWithChildren
  factionLayoutRoute: typeof factionLayoutRouteWithChildren
  protectedLayoutRoute: typeof protectedLayoutRouteWithChildren
  AccessDeniedRoute: typeof AccessDeniedRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/access-denied': {
      id: '/access-denied'
      path: '/access-denied'
      fullPath: '/access-denied'
      preLoaderRoute: typeof AccessDeniedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(protected)': {
      id: '/(protected)'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof protectedLayoutRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(faction)': {
      id: '/(faction)'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof factionLayoutRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(auth)': {
      id: '/(auth)'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof authLayoutRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(protected)/profile': {
      id: '/(protected)/profile'
      path: '/profile'
      fullPath: '/profile'
      preLoaderRoute: typeof protectedProfileRouteImport
      parentRoute: typeof protectedLayoutRoute
    }
    '/(protected)/onboarding': {
      id: '/(protected)/onboarding'
      path: '/onboarding'
      fullPath: '/onboarding'
      preLoaderRoute: typeof protectedOnboardingRouteImport
      parentRoute: typeof protectedLayoutRoute
    }
    '/(faction)/wars': {
      id: '/(faction)/wars'
      path: '/wars'
      fullPath: '/wars'
      preLoaderRoute: typeof factionWarsRouteImport
      parentRoute: typeof factionLayoutRoute
    }
    '/(faction)/target-finder': {
      id: '/(faction)/target-finder'
      path: '/target-finder'
      fullPath: '/target-finder'
      preLoaderRoute: typeof factionTargetFinderRouteImport
      parentRoute: typeof factionLayoutRoute
    }
    '/(faction)/guides': {
      id: '/(faction)/guides'
      path: '/guides'
      fullPath: '/guides'
      preLoaderRoute: typeof factionGuidesRouteImport
      parentRoute: typeof factionLayoutRoute
    }
    '/(faction)/dashboard': {
      id: '/(faction)/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof factionDashboardRouteImport
      parentRoute: typeof factionLayoutRoute
    }
    '/(faction)/banking': {
      id: '/(faction)/banking'
      path: '/banking'
      fullPath: '/banking'
      preLoaderRoute: typeof factionBankingRouteImport
      parentRoute: typeof factionLayoutRoute
    }
    '/(faction)/announcements': {
      id: '/(faction)/announcements'
      path: '/announcements'
      fullPath: '/announcements'
      preLoaderRoute: typeof factionAnnouncementsRouteImport
      parentRoute: typeof factionLayoutRoute
    }
    '/(faction)/admin': {
      id: '/(faction)/admin'
      path: '/admin'
      fullPath: '/admin'
      preLoaderRoute: typeof factionAdminRouteImport
      parentRoute: typeof factionLayoutRoute
    }
    '/(auth)/sign-in': {
      id: '/(auth)/sign-in'
      path: '/sign-in'
      fullPath: '/sign-in'
      preLoaderRoute: typeof authSignInRouteImport
      parentRoute: typeof authLayoutRoute
    }
  }
}

interface authLayoutRouteChildren {
  authSignInRoute: typeof authSignInRoute
}

const authLayoutRouteChildren: authLayoutRouteChildren = {
  authSignInRoute: authSignInRoute,
}

const authLayoutRouteWithChildren = authLayoutRoute._addFileChildren(
  authLayoutRouteChildren,
)

interface factionLayoutRouteChildren {
  factionAdminRoute: typeof factionAdminRoute
  factionAnnouncementsRoute: typeof factionAnnouncementsRoute
  factionBankingRoute: typeof factionBankingRoute
  factionDashboardRoute: typeof factionDashboardRoute
  factionGuidesRoute: typeof factionGuidesRoute
  factionTargetFinderRoute: typeof factionTargetFinderRoute
  factionWarsRoute: typeof factionWarsRoute
}

const factionLayoutRouteChildren: factionLayoutRouteChildren = {
  factionAdminRoute: factionAdminRoute,
  factionAnnouncementsRoute: factionAnnouncementsRoute,
  factionBankingRoute: factionBankingRoute,
  factionDashboardRoute: factionDashboardRoute,
  factionGuidesRoute: factionGuidesRoute,
  factionTargetFinderRoute: factionTargetFinderRoute,
  factionWarsRoute: factionWarsRoute,
}

const factionLayoutRouteWithChildren = factionLayoutRoute._addFileChildren(
  factionLayoutRouteChildren,
)

interface protectedLayoutRouteChildren {
  protectedOnboardingRoute: typeof protectedOnboardingRoute
  protectedProfileRoute: typeof protectedProfileRoute
}

const protectedLayoutRouteChildren: protectedLayoutRouteChildren = {
  protectedOnboardingRoute: protectedOnboardingRoute,
  protectedProfileRoute: protectedProfileRoute,
}

const protectedLayoutRouteWithChildren = protectedLayoutRoute._addFileChildren(
  protectedLayoutRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  authLayoutRoute: authLayoutRouteWithChildren,
  factionLayoutRoute: factionLayoutRouteWithChildren,
  protectedLayoutRoute: protectedLayoutRouteWithChildren,
  AccessDeniedRoute: AccessDeniedRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
