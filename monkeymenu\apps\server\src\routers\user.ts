import {
	PERMISSIONS,
	TornApiKeySchema,
	UserProfileSchema,
} from "@monkeymenu/shared";
import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { user } from "../db/schema/auth";
import { factionRole, userRole } from "../db/schema/permissions";
import { tornUser } from "../db/schema/torn";
import { isEncrypted } from "../lib/crypto";
import { getDbRoleFromTornData } from "../lib/permissions";
import {
	type TornAPIResponse,
	createTornAPIWithSuspension,
} from "../lib/tornApi";
import {
	updateUserTornInfo,
	verifyAndUpdateUserTornInfo,
} from "../lib/tornUserUtils";
import type { UpdateUserTornInfoOptions } from "../lib/tornUserUtils";
import { suspendUserAccess } from "../lib/tornUserUtils";
import {
	factionPermissionProcedure,
	protectedProcedure,
	requirePermission,
	router,
} from "../lib/trpc";

export const userRouter = router({
	getProfile: protectedProcedure.query(async ({ ctx }) => {
		// Using standard protectedProcedure which already guarantees ctx.session exists
		const userId = ctx.session.userId;

		// Get user profile with torn user info using direct database access
		// This still makes sense for torn user info as it's not part of auth
		const userResult = await ctx.db
			.select()
			.from(user)
			.leftJoin(tornUser, eq(user.id, tornUser.id))
			.where(eq(user.id, userId))
			.get();

		if (!userResult) return null;

		return {
			user: userResult.user,
			tornUser: userResult.torn_user,
		};
	}),
	updateProfile: protectedProcedure
		.input(UserProfileSchema)
		.mutation(async ({ ctx, input }) => {
			return await ctx.db
				.update(user)
				.set({
					name: input.name,
					image: input.image,
					updatedAt: new Date(),
				})
				.where(eq(user.id, ctx.session.userId))
				.returning()
				.get();
		}),
	updateTornInfo: protectedProcedure
		.input(TornApiKeySchema)
		.mutation(async ({ ctx, input }) => {
			const { apiKey } = input;
			const { userId } = ctx.session;

			// Helper function to handle verification failures consistently
			async function handleVerificationFailure(message: string) {
				// Don't store API key on verification failure unless it's already encrypted
				const existingKey = await ctx.db
					.select({ key: tornUser.tornApiKey })
					.from(tornUser)
					.where(eq(tornUser.id, userId))
					.get();

				// Build update options, including the API key only if we have a non-null value
				const updateOptions: UpdateUserTornInfoOptions = {
					verified: false,
					tornUserId: null,
					tornFactionId: null,
					tornApiKeyLastCheckedAt: null, // Null timestamp indicates verification needed
				};

				// Only add the API key if we have a valid encrypted string to store
				let keyToStore: string | undefined = undefined;
				if (
					typeof existingKey?.key === "string" &&
					isEncrypted(existingKey.key)
				) {
					keyToStore = existingKey.key;
				} else if (typeof apiKey === "string" && isEncrypted(apiKey)) {
					keyToStore = apiKey;
				}
				if (keyToStore) {
					updateOptions.apiKey = keyToStore;
				}

				await updateUserTornInfo(ctx.db, userId, updateOptions);
				return { success: false as const, message };
			}

			try {
				const tornApi = createTornAPIWithSuspension(
					apiKey,
					ctx.db,
					userId,
					ctx.env,
				);
				let data: TornAPIResponse;

				try {
					data = await tornApi.getUserProfile();
				} catch (error) {
					const errorMessage =
						error instanceof Error ? error.message : "Unknown error occurred";
					return handleVerificationFailure(errorMessage);
				}

				if (data.error) {
					return handleVerificationFailure(
						`Torn API Error: ${data.error.error} (Code: ${data.error.code})`,
					);
				}

				const tornUserIdResponse = data.player_id;
				const tornFactionIdResponse = data.faction?.faction_id;
				const tornFactionPosition = data.faction?.position;

				if (!tornUserIdResponse) {
					return handleVerificationFailure(
						"Failed to retrieve User ID from Torn API. The API key might be invalid or lack permissions.",
					);
				}

				// Check if this Torn User ID is already associated with another user account
				const existingTornUser = await ctx.db
					.select({
						userId: tornUser.id,
						tornUserId: tornUser.tornUserId,
					})
					.from(tornUser)
					.where(eq(tornUser.tornUserId, String(tornUserIdResponse)))
					.get();

				if (existingTornUser && existingTornUser.userId !== userId) {
					return handleVerificationFailure(
						`This Torn account (ID: ${tornUserIdResponse}) is already linked to another MonkeyMenu user. Each Torn account can only be linked to one MonkeyMenu account.`,
					);
				}

				if (!tornFactionIdResponse || tornFactionIdResponse === 0) {
					return handleVerificationFailure(
						"API Key verified, but user is not currently in a faction.",
					);
				}

				// Verify user is in the correct faction (53100)
				const requiredFactionId = "53100";
				if (String(tornFactionIdResponse) !== requiredFactionId) {
					return handleVerificationFailure(
						`API Key verified, but you must be a member of faction #${requiredFactionId} to use this application.`,
					);
				}

				// All checks passed, update user profile
				await updateUserTornInfo(ctx.db, userId, {
					// On success, we know this is a valid API key so we can safely store it
					apiKey: apiKey,
					verified: true,
					tornUserId: tornUserIdResponse,
					tornFactionId: tornFactionIdResponse,
					tornApiKeyLastCheckedAt: new Date(),
				});

				// Assign role based on Torn faction position and admin status
				let roleAssignmentMessage = "";
				if (tornFactionPosition || tornUserIdResponse) {
					try {
						// Get the database role name with System Admin check
						const dbRoleName = getDbRoleFromTornData(
							tornFactionPosition,
							tornUserIdResponse,
							ctx.env,
						);

						// Find the role in the database
						const role = await ctx.db
							.select()
							.from(factionRole)
							.where(eq(factionRole.name, dbRoleName))
							.get();

						if (role) {
							// D1 doesn't support Drizzle transactions, use sequential operations
							// First, deactivate any existing active roles for this user
							await ctx.db
								.update(userRole)
								.set({ isActive: false })
								.where(eq(userRole.userId, userId));

							// Then insert the new active role
							await ctx.db.insert(userRole).values({
								userId: userId,
								roleId: role.id,
								assignedAt: new Date(),
								isActive: true,
							});

							roleAssignmentMessage = ` Your role has been set to ${role.displayName} based on your Torn faction position.`;
						} else {
							console.warn(
								`Role not found for Torn position: ${tornFactionPosition} (mapped to: ${dbRoleName})`,
							);
							roleAssignmentMessage =
								" Role assignment will be done manually by an admin.";
						}
					} catch (error) {
						console.error(
							"Failed to assign role based on Torn position:",
							error,
						);
						roleAssignmentMessage =
							" Role assignment will be done manually by an admin.";
					}
				}

				// Sync Discord roles if user has Discord linked and bot is configured
				let discordSyncMessage = "";
				if (ctx.env.DISCORD_BOT_TOKEN && ctx.env.DISCORD_GUILD_ID) {
					try {
						// Import the sync function from the main app
						const { syncUserDiscordRoles } = await import("../index");
						const discordResult = await syncUserDiscordRoles(
							ctx.db,
							userId,
							ctx.env.DISCORD_GUILD_ID,
							ctx.env.DISCORD_BOT_TOKEN,
							true, // User is now verified and in faction
						);

						if (
							discordResult.success &&
							discordResult.message !== "Roles already correct"
						) {
							discordSyncMessage = ` Discord roles updated: ${discordResult.message}.`;
						}
					} catch (discordError) {
						console.error(
							"Discord role sync error during API verification:",
							discordError,
						);
						// Don't fail the entire operation for Discord sync issues
					}
				}

				return {
					success: true,
					message: `Torn API key verified and information updated successfully.${roleAssignmentMessage}${discordSyncMessage}`,
				};
			} catch (error) {
				console.error("Error in updateTornInfo:", error);
				return handleVerificationFailure(
					"An unexpected error occurred while updating Torn information.",
				);
			}
		}),
	/**
	 * Suspend user access - requires moderator permissions
	 * Note: Currently allows self-suspension for emergency situations
	 * (e.g., compromised account, API key issues)
	 */
	suspendUser: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.ADMIN_SUSPEND_USERS.name))
		.input(z.string())
		.mutation(async ({ ctx, input }) => {
			const userId = ctx.session.userId;
			const reason = input;

			await suspendUserAccess(ctx.db, userId, reason);

			// Remove Discord roles if user has Discord linked and bot is configured
			let discordSyncMessage = "";
			if (ctx.env.DISCORD_BOT_TOKEN && ctx.env.DISCORD_GUILD_ID) {
				try {
					// Import the sync function from the main app
					const { syncUserDiscordRoles } = await import("../index");
					const discordResult = await syncUserDiscordRoles(
						ctx.db,
						userId,
						ctx.env.DISCORD_GUILD_ID,
						ctx.env.DISCORD_BOT_TOKEN,
						false, // User is now suspended - not in faction
					);

					if (
						discordResult.success &&
						discordResult.message !== "Roles already correct"
					) {
						discordSyncMessage = ` Discord roles updated: ${discordResult.message}.`;
					}
				} catch (discordError) {
					console.error(
						"Discord role sync error during self-suspension:",
						discordError,
					);
					// Don't fail the entire operation for Discord sync issues
				}
			}

			return {
				success: true,
				message: `User access suspended. Reason: ${reason}.${discordSyncMessage}`,
			};
		}),
	/**
	 * Restore user access - only for API error suspensions
	 * Users can only restore their own access if suspended due to API errors
	 * Admin suspensions require admin intervention
	 */
	restoreUser: protectedProcedure.mutation(async ({ ctx }) => {
		const userId = ctx.session.userId;

		// Check current suspension status
		const tornUserData = await ctx.db
			.select({
				accessSuspended: tornUser.accessSuspended,
				suspensionType: tornUser.suspensionType,
				accessSuspensionReason: tornUser.accessSuspensionReason,
			})
			.from(tornUser)
			.where(eq(tornUser.id, userId))
			.get();

		if (!tornUserData) {
			throw new TRPCError({
				code: "NOT_FOUND",
				message: "User profile not found",
			});
		}

		if (!tornUserData.accessSuspended) {
			return {
				success: true,
				message: "Access is already active",
			};
		}

		// Only allow restoration of API error suspensions
		if (tornUserData.suspensionType === "admin") {
			throw new TRPCError({
				code: "FORBIDDEN",
				message:
					"Admin suspensions require admin intervention. Please contact an administrator.",
			});
		}

		if (tornUserData.suspensionType !== "api_error") {
			throw new TRPCError({
				code: "FORBIDDEN",
				message:
					"Cannot restore access for this suspension type. Please contact an administrator.",
			});
		}

		// Attempt to verify API key before restoring access
		const userProfile = await ctx.db
			.select({
				tornApiKey: tornUser.tornApiKey,
			})
			.from(tornUser)
			.where(eq(tornUser.id, userId))
			.get();

		if (!userProfile?.tornApiKey) {
			throw new TRPCError({
				code: "BAD_REQUEST",
				message: "No API key on file. Please update your Torn API key first.",
			});
		}

		// Try to verify the API key
		try {
			const result = await verifyAndUpdateUserTornInfo(
				ctx.db,
				userId,
				userProfile.tornApiKey,
				ctx.env,
				true, // isRestoration = true
			);

			if (result.success) {
				// Verification successful, access should now be restored
				// Sync Discord roles if user has Discord linked and bot is configured
				let discordSyncMessage = "";
				if (ctx.env.DISCORD_BOT_TOKEN && ctx.env.DISCORD_GUILD_ID) {
					try {
						// Import the sync function from the main app
						const { syncUserDiscordRoles } = await import("../index");
						const discordResult = await syncUserDiscordRoles(
							ctx.db,
							userId,
							ctx.env.DISCORD_GUILD_ID,
							ctx.env.DISCORD_BOT_TOKEN,
							true, // User is now verified and in faction
						);

						if (
							discordResult.success &&
							discordResult.message !== "Roles already correct"
						) {
							discordSyncMessage = ` Discord roles updated: ${discordResult.message}.`;
						}
					} catch (discordError) {
						console.error(
							"Discord role sync error during access restoration:",
							discordError,
						);
						// Don't fail the entire operation for Discord sync issues
					}
				}

				return {
					success: true,
					message: `Access restored successfully. API key verification passed.${discordSyncMessage}`,
				};
			}
			// Verification failed, keep suspension
			return {
				success: false,
				message: `Cannot restore access: ${result.message}`,
			};
		} catch (error) {
			console.error("Error during access restoration:", error);
			return {
				success: false,
				message:
					"Failed to verify API key. Please check your Torn API settings.",
			};
		}
	}),
	checkAccessStatus: protectedProcedure.query(async ({ ctx }) => {
		const userId = ctx.session.userId;

		const tornUserData = await ctx.db
			.select()
			.from(tornUser)
			.where(eq(tornUser.id, userId))
			.get();

		if (!tornUserData) {
			return {
				suspended: false,
				reason: null,
				suspendedAt: null,
				suspensionType: null,
			};
		}

		return {
			suspended: tornUserData.accessSuspended || false,
			reason: tornUserData.accessSuspensionReason,
			suspendedAt: tornUserData.accessSuspendedAt,
			suspensionType: tornUserData.suspensionType,
			lastError: tornUserData.lastTornApiError,
			lastErrorAt: tornUserData.lastTornApiErrorAt,
		};
	}),
	getDecryptedApiKey: protectedProcedure.query(async ({ ctx }) => {
		const userId = ctx.session.userId;

		const tornUserData = await ctx.db
			.select({
				tornApiKey: tornUser.tornApiKey,
			})
			.from(tornUser)
			.where(eq(tornUser.id, userId))
			.get();

		if (!tornUserData?.tornApiKey) {
			return null;
		}

		try {
			// Import decrypt function with lazy loading
			const { decrypt } = await import("../lib/crypto");
			const decryptedKey = decrypt(tornUserData.tornApiKey);
			return decryptedKey;
		} catch (error) {
			console.error(`Failed to decrypt API key for user ${userId}:`, error);
			return null;
		}
	}),
});
