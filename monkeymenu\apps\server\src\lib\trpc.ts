import { TRPCError, initTRPC } from "@trpc/server";
import { eq } from "drizzle-orm";
import { user as userTable } from "../db/schema/auth";
import { tornUser } from "../db/schema/torn";
import { getUserPermissionContext } from "./permissions";
import type { tRPCContext } from "./types";

export const t = initTRPC.context<tRPCContext>().create();

export const router = t.router;

export const publicProcedure = t.procedure;

export const protectedProcedure = t.procedure.use(({ ctx, next }) => {
	if (!ctx.session) {
		throw new TRPCError({
			code: "UNAUTHORIZED",
			message: "Authentication required",
			cause: "No session",
		});
	}
	return next({
		ctx: {
			...ctx,
			session: ctx.session,
		},
	});
});

// Enhanced middleware that includes user permission context
export const withPermissions = t.middleware(async ({ ctx, next }) => {
	if (!ctx.session) {
		throw new TRPCError({
			code: "UNAUTHORIZED",
			message: "Authentication required",
		});
	}

	const permissionContext = await getUserPermissionContext(
		ctx.db,
		ctx.session.userId,
	);

	return next({
		ctx: {
			...ctx,
			session: ctx.session,
			permissions: permissionContext,
		},
	});
});

// Procedure with permission context
export const permissionProcedure = protectedProcedure.use(withPermissions);

// Middleware to require a specific permission
export const requirePermission = (permissionName: string) =>
	t.middleware(async ({ ctx, next }) => {
		if (!ctx.session) {
			throw new TRPCError({
				code: "UNAUTHORIZED",
				message: "Authentication required",
			});
		}

		// Reuse permissions if already present in ctx
		const permissionContext = ctx.permissions
			? ctx.permissions
			: await getUserPermissionContext(ctx.db, ctx.session.userId);

		if (!permissionContext.permissions.includes(permissionName)) {
			throw new TRPCError({
				code: "FORBIDDEN",
				message: `Missing required permission: ${permissionName}`,
			});
		}

		return next({
			ctx: {
				...ctx,
				session: ctx.session,
				permissions: permissionContext,
			},
		});
	});

// Middleware to require minimum role level
export const requireMinimumRole = (minimumLevel: number) =>
	t.middleware(async ({ ctx, next }) => {
		if (!ctx.session) {
			throw new TRPCError({
				code: "UNAUTHORIZED",
				message: "Authentication required",
			});
		}

		// Reuse permissions if already present in ctx
		const permissionContext = ctx.permissions
			? ctx.permissions
			: await getUserPermissionContext(ctx.db, ctx.session.userId);

		// Explicitly handle null/undefined roleLevel to prevent security issues
		const currentRoleLevel =
			permissionContext.roleLevel ?? Number.NEGATIVE_INFINITY;

		if (currentRoleLevel < minimumLevel) {
			throw new TRPCError({
				code: "FORBIDDEN",
				message: `Insufficient role level. Required: ${minimumLevel}, Current: ${permissionContext.roleLevel ?? "none"}`,
			});
		}

		return next({
			ctx: {
				...ctx,
				session: ctx.session,
				permissions: permissionContext,
			},
		});
	});

export const requireFactionMember = t.middleware(async ({ ctx, next }) => {
	const userId = ctx.session?.userId;
	if (!userId) {
		throw new TRPCError({
			code: "UNAUTHORIZED",
			message: "User ID missing from session.",
		});
	}

	type ResultRow = {
		user: typeof userTable.$inferSelect;
		torn_user: typeof tornUser.$inferSelect | null;
	};

	let result: ResultRow | undefined;
	try {
		result = await ctx.db
			.select()
			.from(userTable)
			.leftJoin(tornUser, eq(userTable.id, tornUser.id))
			.where(eq(userTable.id, userId))
			.get();
	} catch (err) {
		// Log the full error details server-side for debugging
		console.error("Database error in requireFactionMember middleware:", {
			userId,
			error:
				err instanceof Error ? { message: err.message, stack: err.stack } : err,
		});

		// Throw a generic error message to the client
		throw new TRPCError({
			code: "INTERNAL_SERVER_ERROR",
			message:
				"An error occurred while verifying faction membership. Please try again later.",
		});
	}

	const dbUser = result?.user;
	const dbTorn = result?.torn_user;

	if (!dbUser) {
		throw new TRPCError({ code: "UNAUTHORIZED", message: "User not found." });
	}

	if (!dbTorn) {
		throw new TRPCError({
			code: "FORBIDDEN",
			message: "No Torn profile found.",
		});
	}

	// Check for access suspension FIRST
	if (dbTorn.accessSuspended) {
		throw new TRPCError({
			code: "FORBIDDEN",
			message: `Access suspended: ${dbTorn.accessSuspensionReason || "API key issues detected"}. Please check your Torn API key settings.`,
		});
	}

	// Simple verification check - let cron job handle periodic re-verification
	if (!dbTorn.tornApiKeyVerified || !dbTorn.tornFactionId) {
		throw new TRPCError({
			code: "FORBIDDEN",
			message: "You must complete onboarding to access faction features.",
		});
	}

	if (!dbTorn.tornApiKey) {
		throw new TRPCError({
			code: "FORBIDDEN",
			message: "No Torn API key on file.",
		});
	}

	// Verify user is in the correct faction (53100)
	const requiredFactionId = "53100";
	if (String(dbTorn.tornFactionId) !== requiredFactionId) {
		throw new TRPCError({
			code: "FORBIDDEN",
			message: `Access denied. You must be a member of faction #${requiredFactionId}.`,
		});
	}

	return next({ ctx });
});

export const factionProcedure = protectedProcedure.use(requireFactionMember);

// Enhanced faction procedure with permissions
export const factionPermissionProcedure = factionProcedure.use(withPermissions);
