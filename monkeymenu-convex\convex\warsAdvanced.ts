// Advanced Wars Analytics - Convex Implementation
// Comprehensive war analysis endpoints matching original functionality

import { query, mutation, action } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";
import { requirePermission } from "./lib/permissions";
import { TornAPI, createTornAPI, TornAttack, TornChainInfo, TornChainReport, TornAPIError } from "./lib/tornApi";

const MY_FACTION_ID = 53100; // Menacing Monkeys

/**
 * Helper function to get user's API key and create TornAPI instance
 */
async function getUserTornAPI(ctx: any, user: any) {
  if (!user?.tornApiKey) {
    throw new Error("User API key is not set. Please add your API key in settings.");
  }

  // In a real implementation, you'd decrypt the API key here
  const tornApi = createTornAPI(user.tornApiKey);
  if (!tornApi) {
    throw new Error("Failed to create Torn API client");  
  }

  return tornApi;
}

/**
 * Fetch the most recent ranked wars for the user's faction
 */
export const listRankedWars = action({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.runQuery(internal.banking.getUserByClerkId, {
      clerkId: identity.subject,
    });

    if (!user) throw new Error("User not found");

    const tornApi = await getUserTornAPI(ctx, user);
    
    try {
      const rankedWars = await tornApi.getFactionRankedWars(MY_FACTION_ID);
      return { rankedWars };
    } catch (error) {
      console.error("Failed to fetch ranked wars:", error);
      throw new Error(`Failed to fetch ranked wars: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  },
});

/**
 * Get ranked war report with caching
 */
export const getRankedWarReport = action({
  args: { warId: v.number() },
  handler: async (ctx, { warId }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.runQuery(internal.banking.getUserByClerkId, {
      clerkId: identity.subject,
    });

    if (!user) throw new Error("User not found");

    const tornApi = await getUserTornAPI(ctx, user);

    try {
      const warReport = await tornApi.getRankedWarReport(warId);
      console.log(`War report fetched for war ${warId}:`, {
        warId: warReport.id,
        start: warReport.start,
        end: warReport.end,
        factions: warReport.factions?.length || 0
      });
      return { warReport };
    } catch (error) {
      console.error("Failed to fetch war report:", error);
      throw new Error(`Failed to fetch war report: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  },
});

/**
 * Get war chain reports with advanced analytics
 */
export const getWarChainReports = action({
  args: { warId: v.number() },
  handler: async (ctx, { warId }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.runQuery(internal.banking.getUserByClerkId, {
      clerkId: identity.subject,
    });

    if (!user) throw new Error("User not found");

    const tornApi = await getUserTornAPI(ctx, user);

    try {
      // Get war details
      const warReport = await tornApi.getRankedWarReport(warId);
      const warStart = warReport.start;
      const warEnd = warReport.end || Math.floor(Date.now() / 1000);
      
      // Get all chains for the war timeframe using proper pagination
      const chains = await tornApi.getAllChainsInTimeRange(warStart, warEnd);
      const chainReports = [];
      
      for (const chain of chains) {
        try {
          const report = await tornApi.getChainReport(chain.id);
          if (report && typeof report === 'object') {
            chainReports.push(report);
          } else {
            console.error(`Invalid chain report for chain ${chain.id}:`, report);
          }
        } catch (error) {
          console.error(`Failed to fetch chain ${chain.id}:`, error);
          // Don't push anything to avoid undefined values
        }
      }
      
      console.log(`War chain analysis complete for war ${warId}:`, {
        totalChainsFound: chains.length,
        validChainReports: chainReports.length,
        warTimeframe: { start: warStart, end: warEnd }
      });

      return {
        warReport,
        chains: chainReports,
        totalChains: chains.length
      };
    } catch (error) {
      console.error("Failed to fetch war chain reports:", error);
      throw new Error(`Failed to fetch war chain reports: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  },
});

/**
 * Get war attacks analytics (non-chain attacks)
 */
export const getWarAttacks = action({
  args: { warId: v.number() },
  handler: async (ctx, args) => {
    const { warId } = args;

    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.runQuery(internal.banking.getUserByClerkId, {
      clerkId: identity.subject,
    });

    if (!user) {
      throw new Error("User not found");
    }

    if (!user.tornApiKey) {
      throw new Error("API key required to fetch war attacks");
    }

    const tornApi = await getUserTornAPI(ctx, user);
    
    try {
      // Bypass cache service and call Torn API directly
      const warReport = await tornApi.getRankedWarReport(warId);
      const warStart = warReport.start;
      const warEnd = warReport.end || Math.floor(Date.now() / 1000);
      
      // Get war attacks using time range parameters
      const collected: TornAttack[] = [];
      // Add buffer to catch final attacks that complete after official end time
      const bufferedEnd = warEnd + 300; // 5 minute buffer
      let cursor: number | undefined = bufferedEnd;
      
      while (true) {
        const batch = await tornApi.getFactionAttacks(
          100,
          "DESC",
          warStart, // from (start time)
          cursor    // to (end time/cursor)
        );
        if (batch.length === 0) break;

        // Filter to actual time range (remove buffer for final results)
        const withinRange = batch.filter(
          (a) => a.started >= warStart && a.started <= warEnd
        );
        collected.push(...withinRange);

        // Continue pagination only if we got a full batch (100 items)
        // AND the oldest attack is still within our range
        if (batch.length < 100) break;

        const oldest = batch[batch.length - 1];
        if (oldest.started <= warStart) break;

        cursor = oldest.started - 1;
      }
      
      // Get enemy faction from war report
      const enemyFaction = warReport.factions.find(
        (f: { id: number }) => f.id !== MY_FACTION_ID
      );
      
      // Filter to non-chain attacks from our faction
      const nonChainAttacks = collected.filter(
        (attack: TornAttack) =>
          (!attack.chain || attack.chain === 0) &&
          attack.attacker?.faction?.id === MY_FACTION_ID
      );
      
      // Calculate war attack statistics (faction-based, not result-based)
      const insideHits = nonChainAttacks.filter(attack => 
        attack.defender?.faction?.id === enemyFaction?.id
      ).length;
      
      const outsideHits = nonChainAttacks.filter(attack => 
        attack.defender?.faction?.id !== enemyFaction?.id
      ).length;
      
      const assists = nonChainAttacks.filter(attack => 
        attack.result === 'assist'
      ).length;
      
      const totalRespect = nonChainAttacks
        .reduce((sum, attack) => sum + (Number(attack.modifiers?.fair_fight) || 0), 0);
      
      // Group attacks by attacker (using non-chain attacks only)
      const memberStatsMap = new Map<number, {
        attackerId: number;
        attackerName: string;
        totalAttacks: number;
        insideHits: number;
        outsideHits: number;
        assists: number;
        totalRespect: number;
      }>();

      nonChainAttacks.forEach(attack => {
          const attackerId = attack.attacker?.id;
          const attackerName = attack.attacker?.name || `Player #${attackerId}`;
          
          if (!attackerId) return;
          
          const existing = memberStatsMap.get(attackerId) || {
            attackerId,
            attackerName,
            totalAttacks: 0,
            insideHits: 0,
            outsideHits: 0,
            assists: 0,
            totalRespect: 0,
          };
          
          existing.totalAttacks++;
          
          // Use faction-based classification, not result-based
          if (attack.result === 'assist') {
            existing.assists++;
          } else if (attack.defender?.faction?.id === enemyFaction?.id) {
            existing.insideHits++;
          } else {
            existing.outsideHits++;
          }
          
          existing.totalRespect += Number(attack.modifiers?.fair_fight) || 0;
          
          memberStatsMap.set(attackerId, existing);
        });

      const memberStats = Array.from(memberStatsMap.values());

      console.log(`War attacks analysis complete for war ${warId}:`, {
        totalAttacks: collected.length,
        insideHits,
        outsideHits,
        assists,
        totalRespect,
        memberCount: memberStats.length,
        warTimeframe: { start: warStart, end: warEnd }
      });

      return {
        attacks: collected,
        insideHits,
        outsideHits,
        assists,
        totalRespect,
        memberStats,
      };
    } catch (error) {
      console.error("Failed to fetch war attacks:", error);
      throw new Error(
        `Failed to fetch war attacks: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  },
});

/**
 * Get individual chain details with attack sequence
 */
export const getIndividualChainDetails = query({
  args: { 
    warId: v.number(),
    chainId: v.number(),
  },
  handler: async (ctx, { warId, chainId }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) throw new Error("User not found");

    const tornApi = await getUserTornAPI(ctx, user);

    try {
      // Get the specific chain report
      const chainReport = await tornApi.getChainReport(chainId);

      // Get war timeframe for filtering
      const warReport = await tornApi.getRankedWarReport(warId);
      const enemyFaction = warReport.factions.find(
        (f: { id: number }) => f.id !== MY_FACTION_ID
      );

      // Fetch all attacks during chain timeframe
      const chainStart = chainReport.start;
      const chainEnd = chainReport.end + 300; // 5 minute buffer

      const collected: TornAttack[] = [];
      let cursor: number | undefined = chainEnd;
      
      while (true) {
        const batch = await tornApi.getFactionAttacks(100, "DESC", chainStart, cursor);
        if (batch.length === 0) break;

        const within = batch.filter(
          (a) => a.chain === chainId || (a.chain && a.chain > 0) || !a.chain
        );
        collected.push(...within);

        if (batch.length < 100) break;

        const oldest = batch[batch.length - 1];
        if (oldest.started <= chainStart) break;

        cursor = oldest.started - 1;
      }

      // Filter attacks to only include those from the specific chain
      let chainAttacks = collected.filter((attack) => attack.chain === chainId);
      let assistsData: TornAttack[] = [];

      // If we have discrepancies, also filter by known attackers as backup
      if (chainReport.attackers && chainAttacks.length !== chainReport.details.chain) {
        const chainAttackerIds = new Set(chainReport.attackers.map((a) => a.id));

        const chainAdvancingAttacks = collected.filter(
          (a) =>
            a.attacker?.id &&
            chainAttackerIds.has(a.attacker.id) &&
            !["Assist", "Lost", "Escape", "Interrupted", "Defeated", "Timeout", "Stalemate"].includes(a.result)
        );

        assistsData = collected.filter(
          (a) =>
            a.attacker?.id &&
            chainAttackerIds.has(a.attacker.id) &&
            a.result === "Assist"
        );

        if (Math.abs(chainAttacks.length - chainReport.details.chain) > 
            Math.abs(chainAdvancingAttacks.length - chainReport.details.chain)) {
          chainAttacks = chainAdvancingAttacks;
        }
      } else if (chainReport.attackers) {
        const chainAttackerIds = new Set(chainReport.attackers.map((a) => a.id));
        assistsData = collected.filter(
          (a) =>
            a.attacker?.id &&
            chainAttackerIds.has(a.attacker.id) &&
            a.result === "Assist"
        );
      }

      // Sort attacks chronologically and build sequence
      const sortedAttacks = chainAttacks.sort((a, b) => a.started - b.started);
      const sortedAssists = assistsData.sort((a, b) => a.started - b.started);

      // Build attack sequence with assists integrated into chain attacks
      const attackSequence = sortedAttacks.map((attack, index) => {
        const chainPosition = index + 1;
        const attackTime = attack.started;

        const relatedAssists = sortedAssists
          .filter(
            (assist) =>
              Math.abs(assist.started - attackTime) <= 30 &&
              assist.defender?.id === attack.defender?.id
          )
          .map((assist) => ({
            attackId: Number(assist.id),
            timestamp: assist.started,
            attacker: {
              id: assist.attacker?.id || 0,
              name: assist.attacker?.name,
            },
            result: assist.result,
            respect: Number(assist.modifiers?.fair_fight) || 0,
          }));

        return {
          attackId: Number(attack.id),
          timestamp: attack.started,
          attacker: {
            id: attack.attacker?.id || 0,
            name: attack.attacker?.name,
          },
          defender: {
            id: attack.defender?.id || 0,
            name: attack.defender?.name,
            factionId: attack.defender?.faction?.id,
          },
          result: attack.result,
          respect: Number(attack.modifiers?.fair_fight) || 0,
          chainPosition: chainPosition,
          isWarTarget: attack.defender?.faction?.id === enemyFaction?.id,
          assists: relatedAssists,
          modifiers: attack.modifiers,
        };
      });

      // Calculate timeline breakdown (hourly buckets)
      const chainDurationHours = Math.max(1, Math.ceil((chainReport.end - chainReport.start) / 3600));
      const timelineBreakdown = [];

      for (let hour = 0; hour < chainDurationHours; hour++) {
        const hourStart = chainReport.start + hour * 3600;
        const hourEnd = hourStart + 3600;

        const hourAttacks = attackSequence.filter(
          (a) => a.timestamp >= hourStart && a.timestamp < hourEnd
        );

        const uniqueAttackers = new Set(
          hourAttacks.map((a) => a.attacker.id).filter((id) => id > 0)
        );

        timelineBreakdown.push({
          hourBlock: hour,
          attackCount: hourAttacks.length,
          respectEarned: hourAttacks.reduce((sum, a) => sum + a.respect, 0),
          uniqueAttackers: uniqueAttackers.size,
        });
      }

      // Calculate participation depth
      const attackerCounts = new Map();
      for (const attack of attackSequence) {
        const id = attack.attacker.id;
        if (id > 0) {
          attackerCounts.set(id, (attackerCounts.get(id) || 0) + 1);
        }
      }

      const contributorCounts = Array.from(attackerCounts.values());
      const participationDepth = {
        coreContributors: contributorCounts.filter((count) => count >= 10).length,
        regularContributors: contributorCounts.filter((count) => count >= 3 && count < 10).length,
        casualContributors: contributorCounts.filter((count) => count < 3).length,
      };

      // Calculate efficiency metrics
      const timeBetweenHits = [];
      for (let i = 1; i < attackSequence.length; i++) {
        timeBetweenHits.push(attackSequence[i].timestamp - attackSequence[i - 1].timestamp);
      }

      const averageTimeBetweenHits = timeBetweenHits.length > 0
        ? timeBetweenHits.reduce((sum, time) => sum + time, 0) / timeBetweenHits.length
        : 0;

      const peakHour = timelineBreakdown.length > 0
        ? timelineBreakdown.reduce((peak, hour) => hour.attackCount > peak.attackCount ? hour : peak, timelineBreakdown[0])
        : { hourBlock: 0, attackCount: 0 };

      const totalRespect = attackSequence.reduce((sum, a) => sum + a.respect, 0);
      const respectPerHour = chainDurationHours > 0 ? totalRespect / chainDurationHours : 0;

      const efficiencyMetrics = {
        averageTimeBetweenHits,
        peakActivity: {
          hour: peakHour.hourBlock,
          attackCount: peakHour.attackCount,
        },
        respectPerHour,
      };

      const chainMetrics = {
        timelineBreakdown,
        participationDepth,
        efficiencyMetrics,
      };

      return {
        chainReport,
        attackSequence,
        chainMetrics,
      };
    } catch (error) {
      console.error("Failed to fetch individual chain details:", error);
      throw new Error(`Failed to fetch chain details: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  },
});

/**
 * Get attack timeline with heatmap data
 */
export const getAttackTimeline = action({
  args: {
    warId: v.number(),
    timeResolution: v.union(v.literal("hour"), v.literal("day")),
  },
  handler: async (ctx, { warId, timeResolution = "hour" }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.runQuery(internal.banking.getUserByClerkId, {
      clerkId: identity.subject,
    });

    if (!user) throw new Error("User not found");

    const tornApi = await getUserTornAPI(ctx, user);

    try {
      console.log(`Getting attack timeline for war ${warId} with resolution ${timeResolution}`);
      
      // Get war timeframe
      const warReport = await tornApi.getRankedWarReport(warId);
      if (!warReport) {
        throw new Error(`War report not found for war ${warId}`);
      }
      
      const warStart = warReport.start;
      const warEnd = warReport.end || Math.floor(Date.now() / 1000);
      const enemyFaction = warReport.factions?.find(
        (f: { id: number }) => f.id !== MY_FACTION_ID
      );
      
      console.log(`War timeframe: ${warStart} to ${warEnd}, enemy faction: ${enemyFaction?.id}`);

      // Fetch all attacks during war
      const collected: TornAttack[] = [];
      let cursor: number | undefined = warEnd;
      
      console.log(`Fetching attacks for timeline...`);
      
      while (true) {
        try {
          const batch = await tornApi.getFactionAttacks(100, "DESC", warStart, cursor);
          if (batch.length === 0) break;

          const within = batch.filter((a) => a && a.attacker?.faction?.id === MY_FACTION_ID);
          collected.push(...within);

          if (batch.length < 100) break;

          const oldest = batch[batch.length - 1];
          if (!oldest || oldest.started <= warStart) break;

          cursor = oldest.started - 1;
        } catch (attackError) {
          console.error("Error fetching attack batch:", attackError);
          break; // Stop fetching on error rather than crash
        }
      }
      
      console.log(`Collected ${collected.length} attacks for timeline`);

      // Build timeline based on resolution
      const timeline = [];
      const resolution = timeResolution === "hour" ? 3600 : 86400; // 1 hour or 1 day
      const warDuration = warEnd - warStart;
      const periods = Math.ceil(warDuration / resolution);
      
      console.log(`Building timeline: ${periods} periods of ${resolution}s each`);

      for (let i = 0; i < periods; i++) {
        const periodStart = warStart + i * resolution;
        const periodEnd = Math.min(periodStart + resolution, warEnd);

        const periodAttacks = collected.filter(
          (a) => a && a.started && a.started >= periodStart && a.started < periodEnd
        );

        // Calculate stats for this period
        const insideHits = periodAttacks.filter(
          (a) => a && a.defender?.faction?.id === enemyFaction?.id
        ).length;
        const assists = periodAttacks.filter((a) => a && a.result === "Assist").length;
        const chainAttacks = periodAttacks.filter((a) => a && a.chain && a.chain > 0).length;

        // Top performers for this period
        const performerMap = new Map();
        for (const attack of periodAttacks) {
          if (!attack || !attack.attacker) continue;
          
          const id = attack.attacker.id;
          const name = attack.attacker.name;
          const respect = Number(attack.modifiers?.fair_fight) || 0;

          if (id) {
            if (!performerMap.has(id)) {
              performerMap.set(id, {
                playerId: id,
                playerName: name || `Player #${id}`,
                attacks: 0,
                respect: 0,
              });
            }
            const performer = performerMap.get(id);
            performer.attacks++;
            performer.respect += respect;
          }
        }

        const topPerformers = Array.from(performerMap.values())
          .sort((a: any, b: any) => b.respect - a.respect)
          .slice(0, 5);

        const uniqueAttackers = new Set(
          periodAttacks
            .filter(a => a && a.attacker?.id)
            .map((a) => a.attacker.id)
        ).size;
        const respectEarned = periodAttacks.reduce(
          (sum, a) => sum + (a && a.modifiers ? (Number(a.modifiers.fair_fight) || 0) : 0),
          0
        );

        timeline.push({
          timestamp: periodStart,
          period: timeResolution === "hour"
            ? new Date(periodStart * 1000).toLocaleString('en-US', { timeZone: 'UTC' })
            : new Date(periodStart * 1000).toLocaleDateString('en-US', { timeZone: 'UTC' }),
          stats: {
            totalAttacks: periodAttacks.length,
            insideHits,
            outsideHits: periodAttacks.length - insideHits - assists,
            assists,
            uniqueAttackers,
            respectEarned,
            chainAttacks,
            nonChainAttacks: periodAttacks.length - chainAttacks,
          },
          topPerformers,
        });
      }

      // Generate heatmap data
      const hourlyActivity = Array(24).fill(0).map((_, hour) => ({ hour, intensity: 0 }));
      const dailyTrends = [];

      // Calculate hourly activity intensity
      for (const attack of collected) {
        if (!attack || !attack.started) continue;
        try {
          const hour = new Date(attack.started * 1000).getUTCHours();
          if (hour >= 0 && hour < 24) {
            hourlyActivity[hour].intensity++;
          }
        } catch (dateError) {
          console.warn("Invalid attack timestamp:", attack.started);
        }
      }

      // Normalize intensity (0-1)
      const maxHourlyActivity = Math.max(...hourlyActivity.map((h) => h.intensity));
      if (maxHourlyActivity > 0) {
        for (const h of hourlyActivity) {
          h.intensity = h.intensity / maxHourlyActivity;
        }
      }

      // Calculate daily trends
      const warDays = Math.ceil(warDuration / 86400);
      for (let day = 0; day < warDays; day++) {
        const dayStart = warStart + day * 86400;
        const dayEnd = Math.min(dayStart + 86400, warEnd);
        const dayAttacks = collected.filter(
          (a) => a && a.started && a.started >= dayStart && a.started < dayEnd
        ).length;

        dailyTrends.push({ day, activity: dayAttacks });
      }

      console.log(`Timeline generation complete: ${timeline.length} periods`);
      
      return {
        timeline,
        heatmap: {
          hourlyActivity,
          dailyTrends,
        },
      };
    } catch (error) {
      console.error("Failed to fetch attack timeline:", error);
      console.error("Error details:", error instanceof Error ? error.message : String(error));
      if (error instanceof Error) {
        console.error("Stack trace:", error.stack);
      }
      throw new Error(`Failed to fetch attack timeline: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  },
});

/**
 * Get player war performance analysis
 */
export const getPlayerWarPerformance = action({
  args: { 
    warId: v.number(),
    playerId: v.optional(v.number()), // If not provided, returns all players
  },
  handler: async (ctx, { warId, playerId }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.runQuery(internal.banking.getUserByClerkId, {
      clerkId: identity.subject,
    });

    if (!user) throw new Error("User not found");

    const tornApi = await getUserTornAPI(ctx, user);

    try {
      // Get war details and timeframe
      const warReport = await tornApi.getRankedWarReport(warId);
      const warStart = warReport.start;
      const warEnd = warReport.end || Math.floor(Date.now() / 1000);
      const warDurationHours = (warEnd - warStart) / 3600;
      const enemyFaction = warReport.factions.find(
        (f: { id: number }) => f.id !== MY_FACTION_ID
      );

      // Fetch all faction attacks during war
      const collected: TornAttack[] = [];
      let cursor: number | undefined = warEnd;
      
      while (true) {
        const batch = await tornApi.getFactionAttacks(100, "DESC", warStart, cursor);
        if (batch.length === 0) break;

        const within = batch.filter((a) => a.attacker?.faction?.id === MY_FACTION_ID);
        collected.push(...within);

        if (batch.length < 100) break;

        const oldest = batch[batch.length - 1];
        if (oldest.started <= warStart) break;

        cursor = oldest.started - 1;
      }

      // Get chain reports for chain analysis
      const allChains: TornChainInfo[] = [];
      let chainCursor: number | undefined = warEnd + 300;
      
      while (true) {
        const batch = await tornApi.getFactionChains(100, "DESC", chainCursor, warStart);
        if (batch.length === 0) break;

        allChains.push(...batch);

        if (batch.length < 100) break;

        const oldest = batch[batch.length - 1];
        if (oldest.start <= warStart) break;

        chainCursor = oldest.start - 1;
      }

      const chainReports: TornChainReport[] = [];
      for (const chain of allChains) {
        try {
          const report = await tornApi.getChainReport(chain.id);
          chainReports.push(report);
        } catch (err) {
          console.error(`Failed to fetch chain report ${chain.id}:`, err);
        }
      }

      // Group attacks by player
      const playerAttacks = new Map<number, TornAttack[]>();
      for (const attack of collected) {
        const attackerId = attack.attacker?.id;
        if (attackerId) {
          if (!playerAttacks.has(attackerId)) {
            playerAttacks.set(attackerId, []);
          }
          playerAttacks.get(attackerId)?.push(attack);
        }
      }

      // Filter to specific player if requested
      const playersToAnalyze = playerId ? [playerId] : Array.from(playerAttacks.keys());

      const playerDetails = [];

      for (const playerIdToAnalyze of playersToAnalyze) {
        const attacks = playerAttacks.get(playerIdToAnalyze) || [];
        const playerName = attacks[0]?.attacker?.name;

        if (attacks.length === 0) continue;

        // Calculate overall stats
        const chainAttacks = attacks.filter((a) => a.chain && a.chain > 0);
        const nonChainAttacks = attacks.filter((a) => !a.chain || a.chain === 0);
        const insideHits = attacks.filter((a) => a.defender?.faction?.id === enemyFaction?.id);
        const assists = attacks.filter((a) => a.result === "Assist");
        const successfulAttacks = attacks.filter(
          (a) => a.result !== "Assist" && a.result !== "Lost"
        );

        const respectValues = attacks.map((a) => Number(a.modifiers?.fair_fight) || 0);
        const totalRespect = respectValues.reduce((sum, r) => sum + r, 0);
        const averageRespect = attacks.length > 0 ? totalRespect / attacks.length : 0;
        const bestRespect = Math.max(...respectValues, 0);

        // Calculate active hours
        const attackHours = new Set(attacks.map((a) => Math.floor(a.started / 3600)));
        const activeHours = attackHours.size;
        const participationRate = (activeHours / warDurationHours) * 100;

        // Performance trends - hourly breakdown
        const hourlyStats = new Map<number, { attacks: number; respect: number }>();
        for (const attack of attacks) {
          const hour = Math.floor((attack.started - warStart) / 3600);
          if (!hourlyStats.has(hour)) {
            hourlyStats.set(hour, { attacks: 0, respect: 0 });
          }
          const stats = hourlyStats.get(hour)!;
          stats.attacks++;
          stats.respect += Number(attack.modifiers?.fair_fight) || 0;
        }

        const hourlyBreakdown = Array.from(hourlyStats.entries()).map(([hour, stats]) => ({
          hour,
          attacks: stats.attacks,
          respect: stats.respect,
          efficiency: stats.attacks > 0 ? stats.respect / stats.attacks : 0,
        }));

        // Daily breakdown
        const dailyStats = new Map<number, { attacks: number; respect: number; hours: Set<number> }>();
        for (const attack of attacks) {
          const day = Math.floor((attack.started - warStart) / 86400);
          const hour = new Date(attack.started * 1000).getHours();
          if (!dailyStats.has(day)) {
            dailyStats.set(day, { attacks: 0, respect: 0, hours: new Set() });
          }
          const stats = dailyStats.get(day)!;
          stats.attacks++;
          stats.respect += Number(attack.modifiers?.fair_fight) || 0;
          stats.hours.add(hour);
        }

        const dailyBreakdown = Array.from(dailyStats.entries()).map(([day, stats]) => {
          const hourActivity = Array.from(stats.hours);
          const peakHour = hourActivity.reduce((peak, hour) => {
            const hourAttacks = attacks.filter((a) => {
              const attackDay = Math.floor((a.started - warStart) / 86400);
              const attackHour = new Date(a.started * 1000).getHours();
              return attackDay === day && attackHour === hour;
            }).length;
            const peakAttacks = attacks.filter((a) => {
              const attackDay = Math.floor((a.started - warStart) / 86400);
              const attackHour = new Date(a.started * 1000).getHours();
              return attackDay === day && attackHour === peak;
            }).length;
            return hourAttacks > peakAttacks ? hour : peak;
          }, hourActivity[0] || 0);

          return {
            day,
            attacks: stats.attacks,
            respect: stats.respect,
            peakHour,
          };
        });

        // Chain contributions
        const chainContributions = chainReports
          .map((chainReport) => {
            const playerChainData = chainReport.attackers?.find(
              (a) => a.id === playerIdToAnalyze
            );
            if (!playerChainData) return null;

            const chainAttacksForPlayer = attacks.filter(
              (a) => a.chain === chainReport.id
            );
            const warHitsInChain = chainAttacksForPlayer.filter(
              (a) => a.defender?.faction?.id === enemyFaction?.id
            ).length;
            const assistsInChain = chainAttacksForPlayer.filter(
              (a) => a.result === "Assist"
            ).length;

            // Determine chain position (Early: first 25%, Mid: 25-75%, Late: 75%+)
            const totalChainHits = chainReport.details.chain;
            const firstAttackPosition = Math.min(...chainAttacksForPlayer.map((_, i) => i + 1));
            const positionRatio = firstAttackPosition / totalChainHits;
            let chainPosition = "Mid";
            if (positionRatio <= 0.25) chainPosition = "Early";
            else if (positionRatio >= 0.75) chainPosition = "Late";

            // Performance compared to chain average
            const avgChainRespect = chainReport.details.respect / chainReport.details.chain;
            const playerAvgRespect = playerChainData.respect.average;
            let performance = "Average";
            if (playerAvgRespect > avgChainRespect * 1.2) performance = "Above Average";
            else if (playerAvgRespect < avgChainRespect * 0.8) performance = "Below Average";

            return {
              chainId: chainReport.id,
              attacks: playerChainData.attacks.total,
              respect: playerChainData.respect.total,
              warHits: warHitsInChain,
              assists: assistsInChain,
              chainPosition,
              performance,
            };
          })
          .filter((contribution): contribution is NonNullable<typeof contribution> => contribution !== null);

        // Battle effectiveness
        const hitAccuracy = attacks.length > 0
          ? (successfulAttacks.length / attacks.length) * 100
          : 0;
        const respectEfficiency = successfulAttacks.length > 0
          ? successfulAttacks.reduce((sum, a) => sum + (Number(a.modifiers?.fair_fight) || 0), 0) / successfulAttacks.length
          : 0;

        const insideTargetRatio = attacks.length > 0 ? (insideHits.length / attacks.length) * 100 : 0;
        const warTargetFocus = nonChainAttacks.length > 0
          ? (nonChainAttacks.filter((a) => a.defender?.faction?.id === enemyFaction?.id).length / nonChainAttacks.length) * 100
          : 0;

        // Timing analysis
        const sortedAttacks = attacks.sort((a, b) => a.started - b.started);
        const timeBetweenAttacks = [];
        for (let i = 1; i < sortedAttacks.length; i++) {
          timeBetweenAttacks.push(sortedAttacks[i].started - sortedAttacks[i - 1].started);
        }
        const averageTimeBetweenAttacks = timeBetweenAttacks.length > 0
          ? timeBetweenAttacks.reduce((sum, time) => sum + time, 0) / timeBetweenAttacks.length
          : 0;

        // Peak activity window
        const hourCounts = new Map<number, number>();
        for (const attack of attacks) {
          const hour = new Date(attack.started * 1000).getHours();
          hourCounts.set(hour, (hourCounts.get(hour) || 0) + 1);
        }
        const peakHour = Array.from(hourCounts.entries()).reduce(
          (peak, [hour, count]) => (count > peak[1] ? [hour, count] : peak),
          [0, 0]
        )[0];
        const peakActivityWindow = `${peakHour}:00-${peakHour + 1}:00`;

        // Consistency score (lower variance in hourly activity = higher consistency)
        const hourlyAttackCounts = Array.from(hourCounts.values());
        const avgHourlyAttacks = hourlyAttackCounts.reduce((sum, count) => sum + count, 0) / hourlyAttackCounts.length;
        const variance = hourlyAttackCounts.reduce((sum, count) => sum + (count - avgHourlyAttacks) ** 2, 0) / hourlyAttackCounts.length;
        const consistencyScore = Math.max(0, 100 - (Math.sqrt(variance) / avgHourlyAttacks) * 100);

        playerDetails.push({
          playerId: playerIdToAnalyze,
          playerName,
          overallStats: {
            totalAttacks: attacks.length,
            chainAttacks: chainAttacks.length,
            nonChainAttacks: nonChainAttacks.length,
            insideHits: insideHits.length,
            outsideHits: attacks.length - insideHits.length - assists.length,
            assists: assists.length,
            totalRespect,
            averageRespect,
            bestRespect,
            activeHours,
            participationRate,
          },
          performanceTrends: {
            hourlyBreakdown,
            dailyBreakdown,
          },
          chainContributions,
          battleEffectiveness: {
            hitAccuracy,
            respectEfficiency,
            targetPrioritization: {
              insideTargetRatio,
              warTargetFocus,
            },
            timingAnalysis: {
              averageTimeBetweenAttacks,
              peakActivityWindow,
              consistencyScore,
            },
          },
          comparativeRanking: {
            respectRank: 0, // Will be calculated after all players are processed
            attackCountRank: 0,
            efficiencyRank: 0,
            participationRank: 0,
          },
        });
      }

      // Calculate comparative rankings
      playerDetails.sort((a, b) => b.overallStats.totalRespect - a.overallStats.totalRespect);
      playerDetails.forEach((player, index) => {
        player.comparativeRanking.respectRank = index + 1;
      });

      playerDetails.sort((a, b) => b.overallStats.totalAttacks - a.overallStats.totalAttacks);
      playerDetails.forEach((player, index) => {
        player.comparativeRanking.attackCountRank = index + 1;
      });

      playerDetails.sort((a, b) => b.overallStats.averageRespect - a.overallStats.averageRespect);
      playerDetails.forEach((player, index) => {
        player.comparativeRanking.efficiencyRank = index + 1;
      });

      playerDetails.sort((a, b) => b.overallStats.participationRate - a.overallStats.participationRate);
      playerDetails.forEach((player, index) => {
        player.comparativeRanking.participationRank = index + 1;
      });

      console.log(`Player performance analysis complete for war ${warId}:`, {
        totalPlayers: playerDetails.length,
        warTimeframe: { start: warStart, end: warEnd }
      });

      return { playerDetails };
    } catch (error) {
      console.error("Failed to fetch player war performance:", error);
      throw new Error("Failed to fetch player performance");
    }
  },
});

