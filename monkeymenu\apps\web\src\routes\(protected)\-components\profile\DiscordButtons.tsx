import { Button } from "@/components/ui/button";
import { SiDiscord } from "@icons-pack/react-simple-icons";
import { EXTERNAL_URLS } from "@monkeymenu/shared";
import { useEffect, useState } from "react";
import { toast } from "sonner";

export function DiscordConnectionButton() {
	const [isDiscordLinked, setIsDiscordLinked] = useState(false);
	const [isLinking, setIsLinking] = useState(false);

	// Import and use auth client to check linked accounts
	useEffect(() => {
		async function getLinkedAccounts() {
			try {
				const { authClient } = await import("@/lib/auth-client");
				const accounts = await authClient.listAccounts();

				// Check if Discord is among the linked accounts
				// Better Auth returns accounts data wrapped in a Data object
				if ("data" in accounts && accounts.data) {
					const discordAccount = accounts.data.find(
						(account: { provider: string }) => account.provider === "discord",
					);
					setIsDiscordLinked(!!discordAccount);
				}
			} catch (error) {
				console.error("Error fetching linked accounts:", error);
			}
		}

		getLinkedAccounts();
	}, []);

	const handleConnectDiscord = async () => {
		try {
			// Import the auth client
			const { authClient } = await import("@/lib/auth-client");

			if (isDiscordLinked) {
				// If Discord is already linked, offer to unlink
				if (confirm("Are you sure you want to unlink your Discord account?")) {
					await authClient.unlinkAccount({
						providerId: "discord",
					});
					toast.success("Discord account unlinked successfully");

					// Refresh linked accounts status
					const accounts = await authClient.listAccounts();
					if ("data" in accounts && accounts.data) {
						const discordAccount = accounts.data.find(
							(account: { provider: string }) => account.provider === "discord",
						);
						setIsDiscordLinked(!!discordAccount);
					}
				}
				return;
			}

			setIsLinking(true);

			// Use direct account linking
			toast.info("Redirecting to Discord for account linking...");

			// Create a callback URL with success status parameter
			const callbackURL = new URL("/profile", window.location.origin);
			callbackURL.searchParams.append("linkStatus", "success");

			// Use the direct linkSocial method
			await authClient.linkSocial({
				provider: "discord",
				callbackURL: callbackURL.toString(), // Redirect back to profile page after linking
			});

			// Note: The above will redirect the user away from the page, so the code below won't execute
			// until they're redirected back after the linking process
		} catch (error) {
			console.error("Error during Discord linking flow:", error);
			toast.error("Failed to start Discord linking flow");
			setIsLinking(false);
		}
	};

	return (
		<Button
			className="flex items-center gap-2"
			variant={isDiscordLinked ? "outline" : "default"}
			size="sm"
			onClick={handleConnectDiscord}
			disabled={isLinking}
		>
			{isLinking ? (
				<>
					<span className="mr-1 animate-spin">⟳</span>
					Connecting...
				</>
			) : (
				<>
					<SiDiscord className="h-4 w-4" />
					{isDiscordLinked ? "Unlink Discord" : "Connect Discord"}
				</>
			)}
		</Button>
	);
}

export function DiscordJoinButton() {
	const handleJoinServer = () => {
		// Open Discord server invite in new tab
		window.open(EXTERNAL_URLS.DISCORD_INVITE, "_blank", "noopener,noreferrer");
	};

	return (
		<Button
			variant="secondary"
			size="sm"
			onClick={handleJoinServer}
			className="flex items-center gap-2"
		>
			<SiDiscord className="h-4 w-4" />
			Join Discord Server
		</Button>
	);
}
