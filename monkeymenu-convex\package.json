{"name": "monkeymenu-convex", "version": "1.0.0", "description": "MonkeyMenu Convex Migration - Modern real-time app with Convex backend", "type": "module", "private": true, "scripts": {"dev": "concurrently \"convex dev --typecheck=disable\" \"bun run dev:web\"", "dev:web": "vite --port 5173 --host localhost", "dev:convex": "convex dev", "discord:worker:dev": "cd workers/discord && wrangler dev", "discord:worker:deploy": "cd workers/discord && wrangler deploy", "discord:worker:deploy:staging": "cd workers/discord && wrangler deploy --env staging", "discord:worker:deploy:prod": "cd workers/discord && wrangler deploy --env production", "discord:sync": "cd workers/discord && tsx sync-commands.ts", "build": "npm run build:web && npm run build:convex", "build:web": "vite build", "build:convex": "convex deploy", "preview": "vite preview", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit"}, "dependencies": {"@clerk/clerk-react": "^5.7.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-router": "^1.58.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "convex": "^1.16.1", "lucide-react": "^0.436.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "slash-create": "^6.5.0", "tailwind-merge": "^2.5.2", "terser": "^5.43.1", "zod": "^3.23.8"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250803.0", "@tanstack/react-router-devtools": "^1.130.8", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.19", "concurrently": "^9.2.0", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "jsdom": "^26.1.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "typescript": "^5.2.2", "vite": "^5.2.0", "vitest": "^3.2.4"}}