import { HasPermission } from "@/components/permissions/PermissionGuards";
import { But<PERSON> } from "@/components/ui/button";
import { PERMISSION_NAMES } from "@monkeymenu/shared";
import { Plus } from "lucide-react";

interface AnnouncementHeaderProps {
	onCreateNew: () => void;
}

export function AnnouncementHeader({ onCreateNew }: AnnouncementHeaderProps) {
	return (
		<div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
			<div>
				<h1 className="font-bold text-3xl text-foreground">
					📢 Faction Announcements
				</h1>
				<p className="text-muted-foreground">
					Important news and updates for faction members
				</p>
			</div>
			<div className="flex gap-2">
				<HasPermission permission={PERMISSION_NAMES.ANNOUNCEMENTS_MANAGE}>
					<Button onClick={onCreateNew} className="gap-2">
						<Plus className="h-4 w-4" />
						Create Announcement
					</Button>
				</HasPermission>
			</div>
		</div>
	);
}
