import { PageContainer } from "@/components/ui/page-container";
import { createFileRoute } from "@tanstack/react-router";
import { Onboarding } from "./-components/onboarding/";

// This route no longer handles onboarding redirect logic. Onboarding redirect is now only handled in (faction) routes.
export const Route = createFileRoute("/(protected)/onboarding")({
	component: OnboardingComponent,
});

function OnboardingComponent() {
	return (
		<PageContainer variant="narrow">
			<Onboarding />
		</PageContainer>
	);
}
