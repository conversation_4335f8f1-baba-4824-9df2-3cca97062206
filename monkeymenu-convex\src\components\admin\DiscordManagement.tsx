import { useState } from 'react';
import { usePermissions } from '../../hooks/usePermissions';

export function DiscordManagement() {
  const perms = usePermissions();
  const canViewAdmin = () => perms.hasPermission && perms.hasPermission('admin');
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [bulkMessage, setBulkMessage] = useState('');
  const [isSending, setIsSending] = useState(false);

  const stats = null as any;
  const allUsers = null as any;

  // Use useAction for actions
  // import { useAction } from 'convex/react';
  // const sendBulkNotification = useAction(api.discord.notifications.bulkNotifyUsers);

  if (!canViewAdmin()) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Restricted</h2>
          <p className="text-gray-600">You don't have permission to manage Discord settings.</p>
        </div>
      </div>
    );
  }

  const handleBulkNotify = async () => {
    if (!bulkMessage.trim() || selectedUsers.length === 0) return;

    setIsSending(true);
    try {
      // TODO: Implement sendBulkNotification function
      console.log('Sending to users:', selectedUsers);
      console.log('Message:', bulkMessage);
      
      alert('Bulk notification feature not yet implemented');
      setBulkMessage('');
      setSelectedUsers([]);
    } catch (error) {
      console.error('Failed to send bulk notifications:', error);
      alert('Failed to send notifications');
    } finally {
      setIsSending(false);
    }
  };

  const toggleUserSelection = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const selectAllUsers = () => {
    if (!allUsers || !Array.isArray(allUsers)) return;
    const allUserIds = allUsers.map((user: { _id: string }) => user._id);
    setSelectedUsers(allUserIds);
  };

  const clearSelection = () => {
    setSelectedUsers([]);
  };

  const linkedUsers = allUsers && Array.isArray(allUsers) ? allUsers : (allUsers ? [allUsers] : []);

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">🤖 Discord Management</h1>
        <p className="text-gray-600">Manage Discord bot integration and notifications</p>
      </div>

      {/* Statistics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <span className="text-2xl">👥</span>
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-gray-600">Total Users</h3>
                <p className="text-2xl font-bold text-gray-900">{stats.totalUsers}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <span className="text-2xl">🔗</span>
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-gray-600">Linked Accounts</h3>
                <p className="text-2xl font-bold text-gray-900">{stats.linkedUsers}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <span className="text-2xl">📊</span>
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-gray-600">Linkage Rate</h3>
                <p className="text-2xl font-bold text-gray-900">{stats.linkageRate}%</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Bot Commands Info */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">🤖 Available Bot Commands</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-medium text-gray-900 mb-2">💰 Banking Commands</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li><code className="bg-gray-100 px-1 rounded">!balance</code> - Check account balances</li>
              <li><code className="bg-gray-100 px-1 rounded">!withdraw &lt;amount&gt;</code> - Request withdrawal</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium text-gray-900 mb-2">🎯 Target Commands</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li><code className="bg-gray-100 px-1 rounded">!targets</code> - Show available targets</li>
              <li><code className="bg-gray-100 px-1 rounded">!targets level 1-50</code> - Filter by level</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium text-gray-900 mb-2">⚔️ War Commands</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li><code className="bg-gray-100 px-1 rounded">!war</code> - Show active wars</li>
              <li><code className="bg-gray-100 px-1 rounded">!war stats</code> - Show war statistics</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium text-gray-900 mb-2">🔗 Utility Commands</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li><code className="bg-gray-100 px-1 rounded">!help</code> - Show help message</li>
              <li><code className="bg-gray-100 px-1 rounded">!link</code> - Check linking status</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Bulk Notifications */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">📢 Bulk Notifications</h2>
        
        {/* User Selection */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-700">Select Users</h3>
            <div className="space-x-2">
              <button
                onClick={selectAllUsers}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                Select All ({Array.isArray(allUsers) ? allUsers.length : (allUsers ? 1 : 0)})
              </button>
              <button
                onClick={clearSelection}
                className="text-sm text-gray-600 hover:text-gray-800"
              >
                Clear Selection
              </button>
            </div>
          </div>
          
          <div className="max-h-40 overflow-y-auto border border-gray-200 rounded-md p-2">
            {linkedUsers.map((user: any) => (
              <label key={user._id} className="flex items-center space-x-2 p-1 hover:bg-gray-50 rounded cursor-pointer">
                <input
                  type="checkbox"
                  checked={selectedUsers.includes(user._id)}
                  onChange={() => toggleUserSelection(user._id)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-900">{user.username}</span>
                <span className="text-xs text-gray-500">({user.tornId})</span>
              </label>
            ))}
          </div>
          
          <div className="text-sm text-gray-600 mt-2">
            {selectedUsers.length} user{selectedUsers.length !== 1 ? 's' : ''} selected
          </div>
        </div>

        {/* Message Input */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Notification Message
          </label>
          <textarea
            value={bulkMessage}
            onChange={(e) => setBulkMessage(e.target.value)}
            placeholder="Enter your notification message..."
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Send Button */}
        <div className="flex justify-end">
          <button
            onClick={handleBulkNotify}
            disabled={isSending || !bulkMessage.trim() || selectedUsers.length === 0}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSending ? 'Sending...' : `Send to ${selectedUsers.length} user${selectedUsers.length !== 1 ? 's' : ''}`}
          </button>
        </div>
      </div>

      {/* Bot Setup Instructions */}
      <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h2 className="text-lg font-semibold text-yellow-800 mb-3">🚀 Bot Setup Instructions</h2>
        <div className="text-sm text-yellow-700 space-y-2">
          <p><strong>1. Environment Variables:</strong> Set <code>DISCORD_BOT_TOKEN</code> and <code>CONVEX_URL</code></p>
          <p><strong>2. Install Dependencies:</strong> <code>npm install discord.js</code></p>
          <p><strong>3. Start Bot:</strong> <code>npm run discord:bot</code> or <code>node src/discord/bot.js</code></p>
          <p><strong>4. Invite Bot:</strong> Use Discord Developer Portal to invite bot to your server</p>
          <p><strong>5. Required Permissions:</strong> Send Messages, Embed Links, Read Message History</p>
        </div>
      </div>
    </div>
  );
}