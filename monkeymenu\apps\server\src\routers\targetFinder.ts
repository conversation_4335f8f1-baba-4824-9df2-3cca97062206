import { PERMISSIONS } from "@monkeymenu/shared";
import { TRPCError } from "@trpc/server";
import { and, eq } from "drizzle-orm";
import { z } from "zod";
import { target, targetList } from "../db/schema/targetFinder";
import { tornUser } from "../db/schema/torn";
import { decrypt, initCrypto } from "../lib/crypto";
import { pushLiveStoreEvents } from "../lib/livestore-events";
import {
	factionPermissionProcedure,
	requirePermission,
	router,
} from "../lib/trpc";
import { WebSocketService } from "../lib/websocket-service";

// Type guard for Torn API user response
function isTornUserResponse(obj: unknown): obj is {
	status?: { state?: string; until?: number };
	error?: { code?: string; error?: string };
	name?: string;
	level?: number;
	player_id?: number;
	profile_image?: string;
} {
	return typeof obj === "object" && obj !== null;
}

// Type definitions for war API responses (internal to this module)
type TornWarResponse = {
	pacts: unknown[];
	wars: {
		ranked?: {
			war_id: number;
			start: number;
			end: number | null;
			target: number;
			winner: number | null;
			factions: Array<{
				id: number;
				name: string;
				score: number;
				chain: number;
			}>;
		};
		raids: unknown[];
		territory: unknown[];
	};
	error?: { code: string; error: string };
};

// Type definitions for faction members API response (internal to this module)
type TornFactionMembersResponse = {
	members: Array<{
		id: number;
		name: string;
		level: number;
		days_in_faction: number;
		last_action: {
			status: string;
			timestamp: number;
			relative: string;
		};
		status: {
			description: string;
			details: string;
			state: string;
			until: number;
		};
		revive_setting: string;
		position: string;
		is_revivable: boolean;
		is_on_wall: boolean;
		is_in_oc: boolean;
		has_early_discharge: boolean;
	}>;
	error?: { code: string; error: string };
};

// Type guards for API responses
function isTornWarResponse(obj: unknown): obj is TornWarResponse {
	return typeof obj === "object" && obj !== null && "wars" in obj;
}

function isTornFactionMembersResponse(
	obj: unknown,
): obj is TornFactionMembersResponse {
	return typeof obj === "object" && obj !== null && "members" in obj;
}

// Type for a target with status
export type TargetWithStatus = {
	id: string;
	listId: string;
	name: string;
	tornId: string;
	status: string;
	profilePicture?: string;
	createdAt: Date;
	updatedAt: Date;
};

// Fresh-First Strategy: Always fetch fresh data and broadcast to all users

export const targetFinderRouter = router({
	getLists: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.TARGET_FINDER_VIEW.name))
		.query(async ({ ctx }) => {
			const userId = ctx.session.userId;

			// Fetch all lists from the database, getting id, name, and userId
			const allListsFromDb = await ctx.db
				.select({
					id: targetList.id,
					name: targetList.name,
					userId: targetList.userId,
				})
				.from(targetList);

			// Prepare the list to be returned
			const resultLists: Array<{
				id: string;
				name: string;
				userId: string | null;
				isExternal?: boolean;
			}> = [];

			// Add all global lists (userId is null) from database
			for (const list of allListsFromDb) {
				if (list.userId === null) {
					resultLists.push(list);
				}
			}

			// Find or create a placeholder for the user's custom list
			const userCustomList = allListsFromDb.find((l) => l.userId === userId);

			if (userCustomList) {
				resultLists.push(userCustomList);
			} else {
				// If no custom list exists in DB, add the placeholder.
				// The actual creation and DB ID assignment happens in `createOrGetCustomList`.
				resultLists.push({
					id: "custom-list-placeholder", // Special non-DB ID for client-side identification
					name: "Custom List",
					userId: userId, // Conceptually belongs to the current user
				});
			}

			return resultLists;
		}),

	getTargets: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.TARGET_FINDER_VIEW.name))
		.input(z.object({ listName: z.string() }))
		.query(async ({ input, ctx }) => {
			const userId = ctx.session.userId;

			console.log(
				`[Fresh-First] ${userId} requesting fresh data for ${input.listName}`,
			);

			// --- COOLDOWN LOGIC ---
			// Note: Client-side cache is set to 1 minute stale time with 2-minute background refetch
			// This server-side 30-second cooldown prevents API spam while allowing fresh combat data
			const TARGET_FINDER_COOLDOWN_SECONDS = 30;

			// Get last fetch time
			const userResult = await ctx.db
				.select({ lastTargetFinderFetch: tornUser.lastTargetFinderFetch })
				.from(tornUser)
				.where(eq(tornUser.id, userId))
				.limit(1);

			const currentTime = new Date();
			const lastFetch = userResult[0]?.lastTargetFinderFetch;

			if (lastFetch) {
				const secondsSince =
					(currentTime.getTime() - lastFetch.getTime()) / 1000;
				if (secondsSince < TARGET_FINDER_COOLDOWN_SECONDS) {
					throw new TRPCError({
						code: "TOO_MANY_REQUESTS",
						message: "Please wait before fetching again.",
						cause: {
							remaining: Math.ceil(
								TARGET_FINDER_COOLDOWN_SECONDS - secondsSince,
							),
						},
					});
				}
			}

			// Update last fetch time
			await ctx.db
				.update(tornUser)
				.set({ lastTargetFinderFetch: currentTime })
				.where(eq(tornUser.id, userId));

			// Get user's Torn API key
			const tornUserData = await ctx.db
				.select({
					tornApiKey: tornUser.tornApiKey,
					tornApiKeyVerified: tornUser.tornApiKeyVerified,
				})
				.from(tornUser)
				.where(eq(tornUser.id, userId))
				.limit(1);

			if (
				!tornUserData[0]?.tornApiKey ||
				!tornUserData[0]?.tornApiKeyVerified
			) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Torn API key not configured or not verified",
				});
			}

			// Initialize crypto to decrypt API key
			initCrypto(ctx.env.TORN_API_KEY_ENCRYPTION_SECRET);

			// Decrypt the Torn API key
			let apiKey: string;
			try {
				apiKey = decrypt(tornUserData[0].tornApiKey);
			} catch (error) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to decrypt API key",
				});
			}

			// Prepare targets array
			let targets: Array<{
				id: string;
				listId: string;
				name: string;
				tornId: string;
				createdAt: Date;
				updatedAt: Date;
			}> = [];

			// Handle database (shared & personal) lists
			let listResult = await ctx.db
				.select()
				.from(targetList)
				.where(eq(targetList.name, input.listName))
				.limit(1);

			if (input.listName === "Custom List") {
				// Try to find the user's custom list
				listResult = await ctx.db
					.select()
					.from(targetList)
					.where(eq(targetList.userId, userId))
					.limit(1);
			}

			if (!listResult.length) {
				throw new TRPCError({ code: "NOT_FOUND", message: "List not found" });
			}

			const listRow = listResult[0];
			if (!listRow) {
				throw new TRPCError({ code: "NOT_FOUND", message: "List not found" });
			}

			const listId = listRow.id;

			// Fetch all targets for this list
			const dbTargets = await ctx.db
				.select()
				.from(target)
				.where(eq(target.listId, listId));

			targets = dbTargets;

			// Fetch current status for each target with limited concurrency
			const CONCURRENCY_LIMIT = 10;
			const usersWithStatus: Array<
				typeof target.$inferSelect & { status: string; profilePicture?: string }
			> = [];

			let idx = 0;
			async function worker() {
				while (true) {
					const cur = idx++;
					if (cur >= targets.length) break;
					const row = targets[cur];
					const result = await fetchSingleStatus(row);
					usersWithStatus.push(result);
				}
			}

			async function fetchSingleStatus(row: typeof target.$inferSelect) {
				try {
					const resp = await fetch(
						`https://api.torn.com/user/${row.tornId}?selections=basic,profile&key=${apiKey}`,
					);
					const data: unknown = await resp.json();
					let status = "Unknown";
					let profilePicture: string | undefined;

					if (isTornUserResponse(data)) {
						if (!data.error) {
							const st = data.status;
							if (st?.state === "Hospital") {
								const remaining = (st.until ?? 0) - Date.now() / 1000;
								status =
									remaining > 0
										? `Hospitalized (${Math.floor(remaining / 60)}m ${Math.floor(remaining % 60)}s)`
										: "Okay";
							} else {
								status = "Okay";
							}
							if (
								typeof data === "object" &&
								data !== null &&
								"profile_image" in data &&
								typeof (data as { profile_image?: unknown }).profile_image ===
									"string"
							) {
								profilePicture = (data as { profile_image: string })
									.profile_image;
							}
						} else {
							const errObj = data.error;
							status = `Error (${errObj.code ?? errObj.error ?? "Unknown"})`;
						}
					}

					return { ...row, status, profilePicture };
				} catch {
					return { ...row, status: "Fetch Error", profilePicture: undefined };
				}
			}

			await Promise.all(Array.from({ length: CONCURRENCY_LIMIT }, worker));

			// Sort so 'Okay' appears first, then hospital by remaining time
			const sortedUsersWithStatus = usersWithStatus.sort(
				(
					a: typeof target.$inferSelect & { status: string },
					b: typeof target.$inferSelect & { status: string },
				) => {
					const aOkay = a.status === "Okay";
					const bOkay = b.status === "Okay";
					if (aOkay && !bOkay) return -1;
					if (!aOkay && bOkay) return 1;

					const parseTime = (st?: string): number => {
						if (!st) return Number.POSITIVE_INFINITY;
						const m = st.match(/(\d+)m/);
						const s = st.match(/(\d+)s/);
						if (!m || !s || !m[1] || !s[1]) return Number.POSITIVE_INFINITY;
						return Number.parseInt(m[1], 10) * 60 + Number.parseInt(s[1], 10);
					};
					return parseTime(a.status) - parseTime(b.status);
				},
			);

			// Fresh data obtained - now broadcast to all users

			// Broadcast only for shared/external lists (skip personal custom lists)
			if (listRow.userId === null) {
				// --- LiveStore push ---
				try {
					await pushLiveStoreEvents(ctx.env, [
						{
							name: "v1.TargetStatusBatchUpdated",
							data: {
								updates: sortedUsersWithStatus.map((t) => ({
									id: t.id,
									tornId: t.tornId,
									status: t.status,
									profilePicture: t.profilePicture,
								})),
								updatedAt: new Date(),
							},
						},
					]);
				} catch (error) {
					console.error("Failed to push LiveStore status batch:", error);
				}

				// --- Legacy WebSocket broadcast (to be removed after full migration) ---
				try {
					const webSocketService = new WebSocketService();
					await webSocketService.broadcastTargetStatusBatch({
						listName: input.listName,
						statuses: sortedUsersWithStatus.map((t) => ({
							tornId: t.tornId,
							name: t.name,
							status: t.status,
						})),
					});
					console.log(
						`[Fresh Data] Updated cache and broadcast for ${input.listName}`,
					);
				} catch (error) {
					console.error("Failed to broadcast target status batch:", error);
					// Don't fail the query if WebSocket broadcast fails
				}
			}

			return sortedUsersWithStatus;
		}),

	getSingleTargetStatus: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.TARGET_FINDER_VIEW.name))
		.input(z.object({ tornId: z.string() }))
		.query(async ({ input, ctx }) => {
			const userId = ctx.session.userId;

			// Get user's Torn API key
			const tornUserData = await ctx.db
				.select({
					tornApiKey: tornUser.tornApiKey,
					tornApiKeyVerified: tornUser.tornApiKeyVerified,
				})
				.from(tornUser)
				.where(eq(tornUser.id, userId))
				.limit(1);

			if (
				!tornUserData[0]?.tornApiKey ||
				!tornUserData[0]?.tornApiKeyVerified
			) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Torn API key not configured or not verified",
				});
			}

			// Initialize crypto to decrypt API key
			initCrypto(ctx.env.TORN_API_KEY_ENCRYPTION_SECRET);

			// Decrypt the Torn API key
			let apiKey: string;
			try {
				apiKey = decrypt(tornUserData[0].tornApiKey);
			} catch (error) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to decrypt API key",
				});
			}

			try {
				// Fetch single target status - no cooldown needed for individual targets
				const resp = await fetch(
					`https://api.torn.com/user/${input.tornId}?selections=basic,profile&key=${apiKey}`,
				);
				const data: unknown = await resp.json();
				let status = "Unknown";
				let profilePicture: string | undefined;

				if (isTornUserResponse(data)) {
					if (!data.error) {
						const st = data.status;
						if (st?.state === "Hospital") {
							const remaining = (st.until ?? 0) - Date.now() / 1000;
							status =
								remaining > 0
									? `Hospitalized (${Math.floor(remaining / 60)}m ${Math.floor(remaining % 60)}s)`
									: "Okay";
						} else {
							status = "Okay";
						}
						if (
							typeof data === "object" &&
							data !== null &&
							"profile_image" in data &&
							typeof (data as { profile_image?: unknown }).profile_image ===
								"string"
						) {
							profilePicture = (data as { profile_image: string })
								.profile_image;
						}
					} else {
						const errObj = data.error;
						status = `Error (${errObj.code ?? errObj.error ?? "Unknown"})`;
					}
				}

				return {
					tornId: input.tornId,
					status,
					profilePicture,
					timestamp: new Date().toISOString(),
				};
			} catch (error) {
				return {
					tornId: input.tornId,
					status: "Fetch Error",
					profilePicture: undefined,
					timestamp: new Date().toISOString(),
				};
			}
		}),

	createOrGetCustomList: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.TARGET_FINDER_VIEW.name))
		.mutation(async ({ ctx }) => {
			const userId = ctx.session.userId;

			// Check if the user already has a custom list
			const list = await ctx.db
				.select()
				.from(targetList)
				.where(eq(targetList.userId, userId))
				.limit(1);

			if (list && list.length > 0 && list[0])
				return list[0] as typeof targetList.$inferSelect;

			// Create a new custom list for the user
			const createdArr = await ctx.db
				.insert(targetList)
				.values({
					id: crypto.randomUUID(),
					name: "Custom List",
					userId,
					createdAt: new Date(),
					updatedAt: new Date(),
				})
				.returning();

			const created = createdArr?.[0];
			if (!created)
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to create custom list",
				});

			return created as typeof targetList.$inferSelect;
		}),

	createSharedList: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.TARGET_FINDER_MANAGE_SHARED_LISTS.name))
		.input(
			z.object({
				name: z.string().min(1).max(50),
			}),
		)
		.mutation(async ({ input, ctx }) => {
			// TODO: Add permission check for creating shared lists
			// For now, any authenticated user can create shared lists

			// Check if a shared list with this name already exists
			const existingList = await ctx.db
				.select()
				.from(targetList)
				.where(eq(targetList.name, input.name))
				.limit(1);

			if (existingList.length > 0) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "A list with this name already exists",
				});
			}

			// Create a new shared list (userId = null for shared lists)
			const createdArr = await ctx.db
				.insert(targetList)
				.values({
					id: crypto.randomUUID(),
					name: input.name,
					userId: null, // null means it's a shared list
					createdAt: new Date(),
					updatedAt: new Date(),
				})
				.returning();

			const created = createdArr?.[0];
			if (!created)
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to create shared list",
				});

			// LiveStore event for new shared list
			try {
				await pushLiveStoreEvents(ctx.env, [
					{
						name: "v1.TargetListCreated",
						data: {
							id: created.id,
							name: created.name,
							userId: undefined,
							isExternal: false,
							createdAt: created.createdAt,
						},
					},
				]);
			} catch (error) {
				console.error("Failed to push LiveStore list created:", error);
			}

			// Legacy WebSocket update for shared list (will be removed later)
			try {
				const webSocketService = new WebSocketService();
				await webSocketService.broadcastTargetListUpdated({
					listId: created.id,
					listName: created.name,
					action: "created",
				});
			} catch (error) {
				console.error("Failed to broadcast list created:", error);
				// Don't fail the mutation if WebSocket broadcast fails
			}

			return created as typeof targetList.$inferSelect;
		}),

	addTargetToList: factionPermissionProcedure
		.input(
			z.object({
				tornId: z.string(),
				listName: z.string(),
			}),
		)
		.mutation(async ({ input, ctx }) => {
			const userId = ctx.session.userId;

			let list: (typeof targetList.$inferSelect)[];

			if (input.listName === "Custom List") {
				// Require permission to use personal lists
				if (
					!ctx.permissions.permissions.includes(
						PERMISSIONS.TARGET_FINDER_VIEW.name,
					)
				) {
					throw new TRPCError({
						code: "FORBIDDEN",
						message:
							"You don't have permission to manage personal target lists.",
					});
				}

				// Find or create the user's custom list
				list = await ctx.db
					.select()
					.from(targetList)
					.where(eq(targetList.userId, userId))
					.limit(1);

				if (!list || list.length === 0 || !list[0]) {
					const createdArr = await ctx.db
						.insert(targetList)
						.values({
							id: crypto.randomUUID(),
							name: "Custom List",
							userId,
							createdAt: new Date(),
							updatedAt: new Date(),
						})
						.returning();
					list = createdArr;
				}
			} else {
				// Require permission to manage shared lists
				if (
					!ctx.permissions.permissions.includes(
						PERMISSIONS.TARGET_FINDER_MANAGE_SHARED_LISTS.name,
					)
				) {
					throw new TRPCError({
						code: "FORBIDDEN",
						message:
							"You don't have permission to add targets to shared lists.",
					});
				}

				// Find the shared list by name
				list = await ctx.db
					.select()
					.from(targetList)
					.where(eq(targetList.name, input.listName))
					.limit(1);

				if (!list || list.length === 0 || !list[0]) {
					throw new TRPCError({
						code: "NOT_FOUND",
						message: "Shared list not found",
					});
				}
			}

			const listId = list[0]?.id;
			if (!listId)
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Custom list not found",
				});

			// ENFORCE MAX TARGETS PER LIST
			const targetCount = await ctx.db
				.select()
				.from(target)
				.where(eq(target.listId, listId));

			if (targetCount.length >= 25) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "This list already has the maximum of 25 targets.",
				});
			}

			// Get user's Torn API key
			const tornUserData = await ctx.db
				.select({
					tornApiKey: tornUser.tornApiKey,
					tornApiKeyVerified: tornUser.tornApiKeyVerified,
				})
				.from(tornUser)
				.where(eq(tornUser.id, userId))
				.limit(1);

			if (
				!tornUserData[0]?.tornApiKey ||
				!tornUserData[0]?.tornApiKeyVerified
			) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Torn API key not configured or not verified",
				});
			}

			// Decrypt the API key
			let apiKey: string;
			try {
				apiKey = decrypt(tornUserData[0].tornApiKey);
			} catch (error) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to decrypt API key",
				});
			}

			let name = "";
			try {
				// Fetch the target's basic info from Torn API
				const resp = await fetch(
					`https://api.torn.com/user/${input.tornId}?selections=basic&key=${apiKey}`,
				);
				const data: unknown = await resp.json();

				if (isTornUserResponse(data)) {
					if (data.error) {
						throw new TRPCError({
							code: "BAD_REQUEST",
							message:
								data.error.error || data.error.code || "Unknown Torn API error",
						});
					}
					name = data.name ?? "";
				} else {
					throw new TRPCError({
						code: "BAD_REQUEST",
						message: "Unexpected response from Torn API",
					});
				}
			} catch (error) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message:
						error instanceof Error
							? error.message
							: "Failed to fetch player info from Torn API",
				});
			}

			// Add the target (store name and tornId)
			const createdTargetArr = await ctx.db
				.insert(target)
				.values({
					id: crypto.randomUUID(),
					listId,
					name,
					tornId: input.tornId,
					createdAt: new Date(),
					updatedAt: new Date(),
				})
				.returning();

			if (!createdTargetArr || !createdTargetArr[0])
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to add target",
				});

			const createdTarget = createdTargetArr[0];

			// Broadcast WebSocket updates only for shared/external lists
			const listName = list[0]?.name || "Custom List";

			if (list[0]?.userId === null) {
				// LiveStore event for target added
				try {
					await pushLiveStoreEvents(ctx.env, [
						{
							name: "v1.TargetAdded",
							data: {
								id: createdTarget.id,
								listId: listId,
								name: createdTarget.name,
								tornId: createdTarget.tornId,
								status: "Unknown",
								profilePicture: undefined,
								createdAt: createdTarget.createdAt,
							},
						},
					]);
				} catch (error) {
					console.error("Failed to push LiveStore target added:", error);
				}

				try {
					const webSocketService = new WebSocketService();
					await webSocketService.broadcastTargetAdded({
						listId: listId,
						listName: listName,
						targetId: createdTarget.id,
						tornId: createdTarget.tornId,
						name: createdTarget.name,
						addedBy: userId,
					});
				} catch (error) {
					console.error("Failed to broadcast target added:", error);
					// Don't fail the mutation if WebSocket broadcast fails
				}
			}

			return createdTarget as typeof target.$inferSelect;
		}),

	// Legacy mutation for backward compatibility
	addTargetToCustomList: factionPermissionProcedure
		.input(
			z.object({
				tornId: z.string(),
			}),
		)
		.mutation(async ({ input, ctx }) => {
			const userId = ctx.session.userId;

			// Find or create the user's custom list
			let list = await ctx.db
				.select()
				.from(targetList)
				.where(eq(targetList.userId, userId))
				.limit(1);

			if (!list || list.length === 0 || !list[0]) {
				const createdArr = await ctx.db
					.insert(targetList)
					.values({
						id: crypto.randomUUID(),
						name: "Custom List",
						userId,
						createdAt: new Date(),
						updatedAt: new Date(),
					})
					.returning();
				list = createdArr;
			}

			const listId = list[0]?.id;
			if (!listId)
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Custom list not found",
				});

			// ENFORCE MAX TARGETS PER LIST
			const targetCount = await ctx.db
				.select()
				.from(target)
				.where(eq(target.listId, listId));

			if (targetCount.length >= 25) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "This list already has the maximum of 25 targets.",
				});
			}

			// Get user's Torn API key
			const tornUserData = await ctx.db
				.select({
					tornApiKey: tornUser.tornApiKey,
					tornApiKeyVerified: tornUser.tornApiKeyVerified,
				})
				.from(tornUser)
				.where(eq(tornUser.id, userId))
				.limit(1);

			if (
				!tornUserData[0]?.tornApiKey ||
				!tornUserData[0]?.tornApiKeyVerified
			) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Torn API key not configured or not verified",
				});
			}

			// Decrypt the API key
			let apiKey: string;
			try {
				apiKey = decrypt(tornUserData[0].tornApiKey);
			} catch (error) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to decrypt API key",
				});
			}

			let name = "";
			try {
				// Fetch the target's basic info from Torn API
				const resp = await fetch(
					`https://api.torn.com/user/${input.tornId}?selections=basic&key=${apiKey}`,
				);
				const data: unknown = await resp.json();

				if (isTornUserResponse(data)) {
					if (data.error) {
						throw new TRPCError({
							code: "BAD_REQUEST",
							message:
								data.error.error || data.error.code || "Unknown Torn API error",
						});
					}
					name = data.name ?? "";
				} else {
					throw new TRPCError({
						code: "BAD_REQUEST",
						message: "Unexpected response from Torn API",
					});
				}
			} catch (error) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message:
						error instanceof Error
							? error.message
							: "Failed to fetch player info from Torn API",
				});
			}

			// Add the target (store name and tornId)
			const createdTargetArr = await ctx.db
				.insert(target)
				.values({
					id: crypto.randomUUID(),
					listId,
					name,
					tornId: input.tornId,
					createdAt: new Date(),
					updatedAt: new Date(),
				})
				.returning();

			if (!createdTargetArr || !createdTargetArr[0])
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to add target",
				});

			const createdTarget = createdTargetArr[0];

			// No WebSocket broadcast for personal custom lists

			return createdTarget as typeof target.$inferSelect;
		}),

	removeTargetFromList: factionPermissionProcedure
		.input(
			z.object({
				tornId: z.string(),
				listName: z.string(),
			}),
		)
		.mutation(async ({ input, ctx }) => {
			const userId = ctx.session.userId;
			let listId: string | undefined;

			if (input.listName === "Custom List") {
				// Require permission to use personal lists
				if (
					!ctx.permissions.permissions.includes(
						PERMISSIONS.TARGET_FINDER_VIEW.name,
					)
				) {
					throw new TRPCError({
						code: "FORBIDDEN",
						message:
							"You don't have permission to manage personal target lists.",
					});
				}
				const customList = await ctx.db
					.select({ id: targetList.id })
					.from(targetList)
					.where(eq(targetList.userId, userId))
					.limit(1);
				listId = customList[0]?.id;
			} else {
				// Require permission to manage shared lists
				if (
					!ctx.permissions.permissions.includes(
						PERMISSIONS.TARGET_FINDER_MANAGE_SHARED_LISTS.name,
					)
				) {
					throw new TRPCError({
						code: "FORBIDDEN",
						message: "You don't have permission to manage shared target lists.",
					});
				}
				const sharedList = await ctx.db
					.select({ id: targetList.id })
					.from(targetList)
					.where(eq(targetList.name, input.listName))
					.limit(1);
				listId = sharedList[0]?.id;
			}

			if (!listId) {
				throw new TRPCError({ code: "NOT_FOUND", message: "List not found" });
			}

			// Fetch the target record first to obtain its unique ID (not just tornId)
			const targetRecord = await ctx.db
				.select({ id: target.id })
				.from(target)
				.where(and(eq(target.listId, listId), eq(target.tornId, input.tornId)))
				.limit(1);
			const removedTargetId = targetRecord[0]?.id ?? input.tornId;

			const deleteResult = await ctx.db
				.delete(target)
				.where(and(eq(target.listId, listId), eq(target.tornId, input.tornId)))
				.run(); // For D1, .run() returns D1Result

			// Broadcast WebSocket updates only for shared/external lists
			if (deleteResult.meta?.changes > 0 && input.listName !== "Custom List") {
				// LiveStore event for target removed
				try {
					await pushLiveStoreEvents(ctx.env, [
						{
							name: "v1.TargetRemoved",
							data: {
								id: removedTargetId,
								listId: listId,
								tornId: input.tornId,
							},
						},
					]);
				} catch (error) {
					console.error("Failed to push LiveStore target removed:", error);
				}

				try {
					const webSocketService = new WebSocketService();
					await webSocketService.broadcastTargetRemoved({
						listId: listId,
						listName: input.listName,
						targetId: removedTargetId,
						tornId: input.tornId,
						name: input.tornId,
					});
				} catch (error) {
					console.error("Failed to broadcast target removed:", error);
					// Don't fail the mutation if WebSocket broadcast fails
				}
			}

			return { success: true };
		}),

	// Legacy mutation for backward compatibility
	removeTargetFromCustomList: factionPermissionProcedure
		.input(
			z.object({
				tornId: z.string(),
			}),
		)
		.mutation(async ({ input, ctx }) => {
			const userId = ctx.session.userId;

			// Find the user's custom list
			const list = await ctx.db
				.select()
				.from(targetList)
				.where(eq(targetList.userId, userId))
				.limit(1);

			if (!list || list.length === 0 || !list[0])
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Custom list not found",
				});

			const listId = list[0]?.id;
			if (!listId)
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Custom list not found",
				});

			// Remove the target
			await ctx.db
				.delete(target)
				.where(and(eq(target.listId, listId), eq(target.tornId, input.tornId)));

			// No WebSocket broadcast for personal custom lists

			return { success: true };
		}),

	getTargetFinderCooldown: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.TARGET_FINDER_VIEW.name))
		.query(async ({ ctx }) => {
			const userId = ctx.session.userId;
			const TARGET_FINDER_COOLDOWN_SECONDS = 30;

			const userResult = await ctx.db
				.select({ lastTargetFinderFetch: tornUser.lastTargetFinderFetch })
				.from(tornUser)
				.where(eq(tornUser.id, userId))
				.limit(1);

			const now = new Date();
			const lastFetch = userResult[0]?.lastTargetFinderFetch;
			let remaining = 0;

			if (lastFetch) {
				const secondsSince = (now.getTime() - lastFetch.getTime()) / 1000;
				if (secondsSince < TARGET_FINDER_COOLDOWN_SECONDS) {
					remaining = Math.ceil(TARGET_FINDER_COOLDOWN_SECONDS - secondsSince);
				}
			}

			return { remaining };
		}),

	getWarInfo: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.TARGET_FINDER_VIEW.name))
		.query(async ({ ctx }) => {
			const userId = ctx.session.userId;

			// Get user's Torn API key
			const tornUserData = await ctx.db
				.select({
					tornApiKey: tornUser.tornApiKey,
					tornApiKeyVerified: tornUser.tornApiKeyVerified,
				})
				.from(tornUser)
				.where(eq(tornUser.id, userId))
				.limit(1);

			if (
				!tornUserData[0]?.tornApiKey ||
				!tornUserData[0]?.tornApiKeyVerified
			) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Torn API key not configured or not verified",
				});
			}

			// Initialize crypto to decrypt API key
			initCrypto(ctx.env.TORN_API_KEY_ENCRYPTION_SECRET);

			// Decrypt the Torn API key
			let apiKey: string;
			try {
				apiKey = decrypt(tornUserData[0].tornApiKey);
			} catch (error) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to decrypt API key",
				});
			}

			try {
				// Fetch faction wars information
				const response = await fetch(
					`https://api.torn.com/v2/faction/wars?key=${apiKey}`,
				);
				const warData: unknown = await response.json();

				if (!isTornWarResponse(warData)) {
					throw new TRPCError({
						code: "INTERNAL_SERVER_ERROR",
						message: "Invalid response format from Torn API",
					});
				}

				if (warData.error) {
					throw new TRPCError({
						code: "BAD_REQUEST",
						message: `Torn API Error: ${warData.error.error || warData.error.code}`,
					});
				}

				return warData;
			} catch (error) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to fetch war information",
				});
			}
		}),

	getEnemyFactionMembers: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.TARGET_FINDER_VIEW.name))
		.input(z.object({ factionId: z.string() }))
		.query(async ({ input, ctx }) => {
			const userId = ctx.session.userId;

			// Get user's Torn API key
			const tornUserData = await ctx.db
				.select({
					tornApiKey: tornUser.tornApiKey,
					tornApiKeyVerified: tornUser.tornApiKeyVerified,
				})
				.from(tornUser)
				.where(eq(tornUser.id, userId))
				.limit(1);

			if (
				!tornUserData[0]?.tornApiKey ||
				!tornUserData[0]?.tornApiKeyVerified
			) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Torn API key not configured or not verified",
				});
			}

			// Initialize crypto to decrypt API key
			initCrypto(ctx.env.TORN_API_KEY_ENCRYPTION_SECRET);

			// Decrypt the Torn API key
			let apiKey: string;
			try {
				apiKey = decrypt(tornUserData[0].tornApiKey);
			} catch (error) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to decrypt API key",
				});
			}

			try {
				// Fetch faction members information
				const response = await fetch(
					`https://api.torn.com/v2/faction/${input.factionId}/members?striptags=true&key=${apiKey}`,
				);
				const memberData: unknown = await response.json();

				if (!isTornFactionMembersResponse(memberData)) {
					throw new TRPCError({
						code: "INTERNAL_SERVER_ERROR",
						message: "Invalid response format from Torn API",
					});
				}

				if (memberData.error) {
					throw new TRPCError({
						code: "BAD_REQUEST",
						message: `Torn API Error: ${memberData.error.error || memberData.error.code}`,
					});
				}

				// Transform member data to match our target format
				const members = memberData.members || [];
				return members.map((member) => {
					let status = "Okay";

					// Parse hospital status with time remaining
					if (member.status?.state === "Hospital") {
						const hospitalUntil = member.status.until;
						const now = Math.floor(Date.now() / 1000);
						const remainingSeconds = hospitalUntil - now;

						if (remainingSeconds > 0) {
							const minutes = Math.floor(remainingSeconds / 60);
							const seconds = remainingSeconds % 60;
							status = `Hospitalized (${minutes}m ${seconds}s)`;
						} else {
							status = "Okay"; // Hospital time has expired
						}
					}

					return {
						id: `enemy-${member.id}`,
						listId: `enemy-faction-${input.factionId}`,
						name: member.name,
						tornId: String(member.id),
						status,
						profilePicture: undefined,
						createdAt: new Date().toISOString(),
						updatedAt: new Date().toISOString(),
						level: member.level,
						lastAction: member.last_action,
						position: member.position,
						isRevivable: member.is_revivable,
						isOnWall: member.is_on_wall,
						isInOc: member.is_in_oc,
						hasEarlyDischarge: member.has_early_discharge,
					};
				});
			} catch (error) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to fetch enemy faction members",
				});
			}
		}),

	getChainInfo: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.TARGET_FINDER_VIEW.name))
		.query(async ({ ctx }) => {
			const userId = ctx.session.userId;

			// Get user's Torn API key
			const tornUserData = await ctx.db
				.select({
					tornApiKey: tornUser.tornApiKey,
					tornApiKeyVerified: tornUser.tornApiKeyVerified,
				})
				.from(tornUser)
				.where(eq(tornUser.id, userId))
				.limit(1);

			if (
				!tornUserData[0]?.tornApiKey ||
				!tornUserData[0]?.tornApiKeyVerified
			) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Torn API key not configured or not verified",
				});
			}

			// Initialize crypto and decrypt key
			initCrypto(ctx.env.TORN_API_KEY_ENCRYPTION_SECRET);
			let apiKey: string;
			try {
				apiKey = decrypt(tornUserData[0].tornApiKey);
			} catch {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to decrypt API key",
				});
			}

			// Fetch chain info
			try {
				const resp = await fetch(
					`https://api.torn.com/v2/faction/chain?key=${apiKey}`,
				);
				const data: unknown = await resp.json();
				if (typeof data !== "object" || data === null || !("chain" in data)) {
					throw new Error("Invalid response format from Torn API");
				}
				return data as { chain: unknown };
			} catch (error) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to fetch chain information",
				});
			}
		}),
});
