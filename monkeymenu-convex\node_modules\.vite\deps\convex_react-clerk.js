import {
  ConvexProviderWithAuth
} from "./chunk-ATYAE7JB.js";
import "./chunk-E7JIGTN5.js";
import {
  require_react
} from "./chunk-RLJ2RCJQ.js";
import {
  __toESM
} from "./chunk-DC5AMYBS.js";

// node_modules/convex/dist/esm/react-clerk/ConvexProviderWithClerk.js
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
function ConvexProviderWithClerk({
  children,
  client,
  useAuth
}) {
  const useAuthFromClerk = useUseAuthFromClerk(useAuth);
  return import_react.default.createElement(ConvexProviderWithAuth, { client, useAuth: useAuthFromClerk }, children);
}
function useUseAuthFromClerk(useAuth) {
  return (0, import_react2.useMemo)(
    () => function useAuthFromClerk() {
      const { isLoaded, isSignedIn, getToken, orgId, orgRole } = useAuth();
      const fetchAccessToken = (0, import_react2.useCallback)(
        async ({ forceRefreshToken }) => {
          try {
            return getToken({
              template: "convex",
              skipCache: forceRefreshToken
            });
          } catch {
            return null;
          }
        },
        // Build a new fetchAccessToken to trigger setAuth() whenever these change.
        // Anything else from the JWT Clerk wants to be reactive goes here too.
        // Clerk's Expo useAuth hook is not memoized so we don't include getToken.
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [orgId, orgRole]
      );
      return (0, import_react2.useMemo)(
        () => ({
          isLoading: !isLoaded,
          isAuthenticated: isSignedIn ?? false,
          fetchAccessToken
        }),
        [isLoaded, isSignedIn, fetchAccessToken]
      );
    },
    [useAuth]
  );
}
export {
  ConvexProviderWithClerk
};
//# sourceMappingURL=convex_react-clerk.js.map
