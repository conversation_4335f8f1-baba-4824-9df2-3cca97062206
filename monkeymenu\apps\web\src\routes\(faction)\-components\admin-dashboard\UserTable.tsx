import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import {
	MoreHorizontal,
	RefreshCw,
	Trash2,
	User<PERSON>heck,
	UserX,
	Users,
} from "lucide-react";
import { useMemo } from "react";
import { Skeleton } from "./Skeleton";
import type { UserRoleAssignment, UserTableProps } from "./types";
import { isUserSuspended } from "./utils";

export function UserTable({
	userRoles,
	isLoading,
	searchQuery,
	onSuspendUser,
	onRestoreUser,
	onDeleteUser,
	onRecheck<PERSON><PERSON><PERSON><PERSON>,
	isRech<PERSON>ing<PERSON><PERSON><PERSON>ey,
	isRestoring,
}: UserTableProps) {
	// Filter users based on search
	const filteredUserRoles = useMemo(() => {
		if (!userRoles || !searchQuery) return userRoles;
		return userRoles.filter(
			(assignment) =>
				assignment.user.name
					?.toLowerCase()
					.includes(searchQuery.toLowerCase()) ||
				assignment.user.email
					?.toLowerCase()
					.includes(searchQuery.toLowerCase()) ||
				assignment.role.displayName
					?.toLowerCase()
					.includes(searchQuery.toLowerCase()),
		);
	}, [userRoles, searchQuery]);

	if (isLoading) {
		return (
			<div className="space-y-3">
				{Array.from({ length: 5 }, () => (
					<div
						key={crypto.randomUUID()}
						className="flex items-center gap-4 p-3"
					>
						<Skeleton className="h-8 w-8 rounded-full" />
						<div className="flex-1 space-y-2">
							<Skeleton className="h-4 w-32" />
							<Skeleton className="h-3 w-24" />
						</div>
						<Skeleton className="h-8 w-20" />
					</div>
				))}
			</div>
		);
	}

	if (filteredUserRoles?.length === 0) {
		return (
			<div className="py-8 text-center">
				<Users className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
				<p className="text-muted-foreground">No users found.</p>
			</div>
		);
	}

	return (
		<>
			{/* Desktop Table View */}
			<div className="hidden lg:block">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>User</TableHead>
							<TableHead>Role</TableHead>
							<TableHead>Status</TableHead>
							<TableHead>Actions</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{filteredUserRoles?.map((assignment: UserRoleAssignment) => {
							const isSuspended = isUserSuspended(assignment);

							return (
								<TableRow key={assignment.user.id}>
									<TableCell>
										<div className="space-y-1">
											<div className="font-medium">
												{assignment.user.name || "Unknown"}
											</div>
											<div className="text-muted-foreground text-sm">
												{assignment.user.email}
											</div>
										</div>
									</TableCell>
									<TableCell>
										<Badge variant="secondary">
											{assignment.role.displayName}
										</Badge>
									</TableCell>
									<TableCell>
										{isSuspended ? (
											<Badge variant="destructive">Suspended</Badge>
										) : (
											<Badge variant="default">Active</Badge>
										)}
									</TableCell>
									<TableCell>
										<div className="flex items-center gap-1">
											<Button
												size="sm"
												variant="ghost"
												onClick={() => onRecheckApiKey(assignment.user.id)}
												disabled={isRecheckingApiKey}
												title="Recheck user's API key"
											>
												<RefreshCw
													className={`h-3 w-3 ${
														isRecheckingApiKey ? "animate-spin" : ""
													}`}
												/>
											</Button>
											<DropdownMenu>
												<DropdownMenuTrigger asChild>
													<Button variant="ghost" size="sm">
														<MoreHorizontal className="h-3 w-3" />
													</Button>
												</DropdownMenuTrigger>
												<DropdownMenuContent align="end">
													<DropdownMenuLabel>User Actions</DropdownMenuLabel>
													{isSuspended ? (
														<DropdownMenuItem
															onClick={() => onRestoreUser(assignment.user.id)}
															disabled={isRestoring}
														>
															<UserCheck className="mr-2 h-4 w-4" />
															Restore Access
														</DropdownMenuItem>
													) : (
														<DropdownMenuItem
															onClick={() => onSuspendUser(assignment.user.id)}
														>
															<UserX className="mr-2 h-4 w-4" />
															Suspend Access
														</DropdownMenuItem>
													)}
													<DropdownMenuSeparator />
													<DropdownMenuItem
														onClick={() => onDeleteUser(assignment.user.id)}
														className="text-destructive focus:text-destructive"
													>
														<Trash2 className="mr-2 h-4 w-4" />
														Delete User
													</DropdownMenuItem>
												</DropdownMenuContent>
											</DropdownMenu>
										</div>
									</TableCell>
								</TableRow>
							);
						})}
					</TableBody>
				</Table>
			</div>

			{/* Mobile Card View */}
			<div className="space-y-3 lg:hidden">
				{filteredUserRoles?.map((assignment: UserRoleAssignment) => {
					const isSuspended = isUserSuspended(assignment);

					return (
						<Card key={assignment.user.id} className="w-full p-4">
							<div className="min-w-0 space-y-3">
								{/* User Info Header */}
								<div className="flex items-start justify-between">
									<div className="min-w-0 flex-1">
										<div className="truncate font-medium">
											{assignment.user.name || "Unknown"}
										</div>
										<div className="truncate text-muted-foreground text-sm">
											{assignment.user.email}
										</div>
									</div>
									<div className="ml-2 flex flex-col items-end gap-2">
										<Badge variant="secondary" className="text-xs">
											{assignment.role.displayName}
										</Badge>
										{isSuspended ? (
											<Badge variant="destructive" className="text-xs">
												Suspended
											</Badge>
										) : (
											<Badge variant="default" className="text-xs">
												Active
											</Badge>
										)}
									</div>
								</div>

								{/* Action Buttons */}
								<div className="flex items-center gap-2 border-t pt-2">
									<Button
										size="sm"
										variant="outline"
										className="min-w-0 flex-1"
										onClick={() => onRecheckApiKey(assignment.user.id)}
										disabled={isRecheckingApiKey}
									>
										<RefreshCw
											className={`mr-1 h-3 w-3 ${
												isRecheckingApiKey ? "animate-spin" : ""
											}`}
										/>
										<span className="truncate">API Check</span>
									</Button>
									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<Button variant="outline" size="sm" className="shrink-0">
												<MoreHorizontal className="h-4 w-4" />
											</Button>
										</DropdownMenuTrigger>
										<DropdownMenuContent align="end" className="w-48">
											<DropdownMenuLabel>User Actions</DropdownMenuLabel>
											{isSuspended ? (
												<DropdownMenuItem
													onClick={() => onRestoreUser(assignment.user.id)}
													disabled={isRestoring}
												>
													<UserCheck className="mr-2 h-4 w-4" />
													Restore Access
												</DropdownMenuItem>
											) : (
												<DropdownMenuItem
													onClick={() => onSuspendUser(assignment.user.id)}
												>
													<UserX className="mr-2 h-4 w-4" />
													Suspend Access
												</DropdownMenuItem>
											)}
											<DropdownMenuSeparator />
											<DropdownMenuItem
												onClick={() => onDeleteUser(assignment.user.id)}
												className="text-destructive focus:text-destructive"
											>
												<Trash2 className="mr-2 h-4 w-4" />
												Delete User
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</div>
							</div>
						</Card>
					);
				})}
			</div>
		</>
	);
}
