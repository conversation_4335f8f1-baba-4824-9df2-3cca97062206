import { usePermissions } from "../../hooks/usePermissions";
import {
	BookOpen,
	CreditCard,
	Search,
	Shield,
	Swords,
	Zap,
} from "lucide-react";
import type { FactionTool } from "./types";
import { ActivityFeed, FactionTools, QuickActionsPanel } from "./widgets";

export function Dashboard() {
	const permissions = usePermissions();
	const isAdmin = permissions.isAdmin();

	// Check individual permissions for tool availability
	const canViewBanking = permissions.canAccessBanking();
	const canViewTargetFinder = permissions.canViewTargets();
	const canViewAnnouncements = true; // Most users can view announcements
	const canViewWars = permissions.canViewWars();
	const canViewGuides = true; // Most users can view guides

	const factionTools: FactionTool[] = [
		{
			title: "Faction Banking",
			description: "Manage withdrawals and view transaction history",
			icon: CreditCard,
			href: "/banking",
			category: "Financial",
			priority: 1,
			available: canViewBanking,
		},
		{
			title: "Target Finder",
			description: "Find and analyze potential targets",
			icon: Search,
			href: "/targets",
			category: "Combat",
			priority: 2,
			available: canViewTargetFinder,
		},
		{
			title: "Announcements",
			description: "Important faction news and updates",
			icon: Zap,
			href: "/announcements",
			category: "Information",
			priority: 3,
			available: canViewAnnouncements,
		},
		{
			title: "War Reports",
			description: "Analyze war performance and chain statistics",
			icon: Swords,
			href: "/wars",
			category: "Combat",
			priority: 4,
			available: canViewWars,
		},
		{
			title: "Faction Guides",
			description: "Access comprehensive faction guides and tutorials",
			icon: BookOpen,
			href: "/guides",
			category: "Information",
			priority: 5,
			available: canViewGuides,
		},
		{
			title: "Admin Dashboard",
			description: "Manage faction settings, permissions, and members",
			icon: Shield,
			href: "/admin",
			category: "Administration",
			priority: 6,
			available: isAdmin,
			adminOnly: true,
		},
	];

	return (
		<>
			{/* Welcome Section */}
			<div className="mb-6 text-center">
				<h1 className="mb-2 font-bold text-4xl tracking-tight">
					Welcome to MonkeyMenu
				</h1>
				<p className="text-lg text-muted-foreground">
					Your Faction Mission Control
				</p>
			</div>

			{/* Dashboard Layout */}
			<div className="space-y-6">
				{/* Top Row: Quick Actions and Recent Activity */}
				<div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
					<QuickActionsPanel />
					<ActivityFeed />
				</div>

				{/* Main Tools Section */}
				<div id="faction-tools-section">
					<FactionTools factionTools={factionTools} />
				</div>
			</div>
		</>
	);
}