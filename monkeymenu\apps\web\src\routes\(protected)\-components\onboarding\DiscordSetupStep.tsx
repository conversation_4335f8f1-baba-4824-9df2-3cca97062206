import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { SiDiscord } from "@icons-pack/react-simple-icons";
import { ArrowRight, CheckCircle, ExternalLink } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { EXTERNAL_URLS } from "./config";
import type { StepComponentProps } from "./types";

interface DiscordSetupStepProps extends StepComponentProps {
	isDiscordLinked: boolean;
	currentStep: string;
}

export function DiscordSetupStep({
	onNextStep,
	onPrevStep,
	isDiscordLinked,
	currentStep,
}: DiscordSetupStepProps) {
	const [isLinkingDiscord, setIsLinkingDiscord] = useState(false);

	const handleConnectDiscord = async () => {
		try {
			setIsLinkingDiscord(true);

			// Import the auth client
			const { authClient } = await import("@/lib/auth-client");

			// Save current step to session storage
			sessionStorage.setItem("onboarding-step", currentStep);

			toast.info("Redirecting to Discord for account linking...");

			// Create a callback URL with success status parameter
			const callbackURL = new URL("/onboarding", window.location.origin);
			callbackURL.searchParams.append("linkStatus", "success");

			// Use the direct linkSocial method
			await authClient.linkSocial({
				provider: "discord",
				callbackURL: callbackURL.toString(),
			});
		} catch (error) {
			console.error("Discord linking error:", error);
			toast.error("Failed to link Discord account. Please try again.");
			setIsLinkingDiscord(false);
		}
	};

	return (
		<Card>
			<CardContent className="space-y-6 p-8">
				{/* Header */}
				<div className="space-y-2 text-center">
					<h2 className="font-bold text-2xl tracking-tight">Connect Discord</h2>
					<p className="mx-auto max-w-md text-muted-foreground leading-relaxed">
						Link your account for notifications and community access. This step
						is optional.
					</p>
				</div>

				{/* Discord Actions */}
				<div className="space-y-4">
					{/* Link Account */}
					{isDiscordLinked ? (
						<div className="flex items-center justify-center gap-2 rounded-lg bg-green-50 p-4 text-green-600 dark:bg-green-950/20 dark:text-green-400">
							<CheckCircle className="h-5 w-5" />
							<span className="font-medium">Discord account linked!</span>
						</div>
					) : (
						<Button
							onClick={handleConnectDiscord}
							disabled={isLinkingDiscord}
							className="w-full"
							variant="outline"
						>
							{isLinkingDiscord ? (
								<>
									<span className="mr-2 animate-spin">⟳</span>
									Connecting...
								</>
							) : (
								<>
									<SiDiscord className="mr-2 h-4 w-4" />
									Link Discord Account
								</>
							)}
						</Button>
					)}

					{/* Join Server */}
					<Button asChild className="w-full bg-[#5865F2] hover:bg-[#4752C4]">
						<a
							href={EXTERNAL_URLS.DISCORD_INVITE}
							target="_blank"
							rel="noopener noreferrer"
							className="inline-flex items-center gap-2"
						>
							<SiDiscord className="h-4 w-4" />
							Join Discord Server
							<ExternalLink className="h-3 w-3" />
						</a>
					</Button>
				</div>
			</CardContent>

			<CardFooter className="flex gap-3 p-8 pt-0">
				<Button
					type="button"
					variant="outline"
					onClick={onPrevStep}
					className="flex-1"
				>
					Back
				</Button>
				<Button onClick={onNextStep} className="flex-1">
					{isDiscordLinked ? "Continue" : "Skip for Now"}
					<ArrowRight className="ml-2 h-4 w-4" />
				</Button>
			</CardFooter>
		</Card>
	);
}
