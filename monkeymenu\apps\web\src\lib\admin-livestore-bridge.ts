import { useStore } from "@livestore/react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useEffect, useRef } from "react";
import { events } from "../livestore/schema";
import { trpc } from "./trpc-client";

/**
 * Enhanced bridge hook that syncs tRPC admin data with LiveStore
 * Maintains all existing functionality while adding real-time sync capabilities
 */
export function useAdminLiveStoreSync(enabled = true) {
	const { store } = useStore();
	const queryClient = useQueryClient();
	const syncedUsersRef = useRef(new Set<string>());
	const syncedAuditEntriesRef = useRef(new Set<string>());
	const lastSyncedStatsRef = useRef<{
		totalUsers: number;
		totalRoles: number;
		totalPermissions: number;
		totalGuides: number;
	} | null>(null);

	// Safety check: don't sync if store is not available or enabled is false
	if (!enabled || !store) {
		return {
			syncUserUpdate: () => {},
			syncUserSuspension: () => {},
			syncUserDeletion: () => {},
			syncStatsUpdate: () => {},
		};
	}

	// Sync system stats from tRPC to LiveStore
	const { data: systemStats } = useQuery({
		...trpc.admin.getSystemStats.queryOptions(),
		enabled,
		staleTime: 60000, // 1 minute - stats don't change frequently
	});

	// Sync permission audit log from tRPC to LiveStore
	const { data: auditLog } = useQuery({
		...trpc.admin.getPermissionAuditLog.queryOptions(),
		enabled,
		staleTime: 30000, // 30 seconds - audit log changes more frequently
	});

	// Sync all users from tRPC to LiveStore
	const { data: allUsers } = useQuery({
		...trpc.admin.getAllUsers.queryOptions({}),
		enabled,
		staleTime: 60000, // 1 minute - user list doesn't change frequently
	});

	// Initialize LiveStore with real system stats data only if not already present
	useEffect(() => {
		if (!enabled || !store) return;

		const initializeWithRealData = async () => {
			try {
				// Only initialize if we have real stats data from tRPC
				if (systemStats) {
					const { totalUsers, totalRoles, totalPermissions, totalGuides } =
						systemStats;
					console.log(
						`[LiveStore] Initializing with real system stats: ${totalUsers} users, ${totalRoles} roles`,
					);

					store.commit(
						events.adminSystemStatsUpdated({
							totalUsers,
							totalRoles,
							totalPermissions,
							totalGuides,
							updatedAt: new Date(),
						}),
					);

					// Track the synced stats to prevent duplicates
					lastSyncedStatsRef.current = {
						totalUsers,
						totalRoles,
						totalPermissions,
						totalGuides,
					};
				}
			} catch (error) {
				console.error("Failed to initialize LiveStore with real data:", error);
			}
		};

		// Small delay to ensure store is ready, but only init with real data
		const timeoutId = setTimeout(initializeWithRealData, 100);
		return () => clearTimeout(timeoutId);
	}, [enabled, store, systemStats]);

	// Reset sync tracking when data changes
	useEffect(() => {
		if (!enabled || !store) return;

		if (systemStats || auditLog || allUsers) {
			console.log(
				"[LiveStore] Admin data updated, clearing sync tracking for fresh sync",
			);
			syncedUsersRef.current.clear();
			syncedAuditEntriesRef.current.clear();
		}
	}, [enabled, store, systemStats, auditLog, allUsers]);

	// Sync system stats to LiveStore when tRPC data updates
	useEffect(() => {
		if (!enabled || !store || !systemStats) return;

		if (systemStats) {
			const { totalUsers, totalRoles, totalPermissions, totalGuides } =
				systemStats;
			const lastSynced = lastSyncedStatsRef.current;

			// Only sync if stats have actually changed
			if (
				!lastSynced ||
				lastSynced.totalUsers !== totalUsers ||
				lastSynced.totalRoles !== totalRoles ||
				lastSynced.totalPermissions !== totalPermissions ||
				lastSynced.totalGuides !== totalGuides
			) {
				try {
					console.log(
						`[LiveStore] Syncing system stats: ${totalUsers} users, ${totalRoles} roles`,
					);

					store.commit(
						events.adminSystemStatsUpdated({
							totalUsers,
							totalRoles,
							totalPermissions,
							totalGuides,
							updatedAt: new Date(),
						}),
					);

					// Track the synced stats to prevent duplicates
					lastSyncedStatsRef.current = {
						totalUsers,
						totalRoles,
						totalPermissions,
						totalGuides,
					};
				} catch (error) {
					console.error("Failed to sync system stats to LiveStore:", error);
				}
			}
		}
	}, [enabled, systemStats, store]);

	// Sync audit log to LiveStore when tRPC data updates
	useEffect(() => {
		if (!enabled || !store || !auditLog) return;

		if (auditLog) {
			for (const entry of auditLog) {
				// Only sync if we haven't already synced this audit entry
				if (!syncedAuditEntriesRef.current.has(entry.id.toString())) {
					try {
						console.log(
							`[LiveStore] Syncing audit entry ${entry.id} from tRPC to LiveStore`,
						);

						// Create audit entry in LiveStore
						store.commit(
							events.adminAuditEntryCreated({
								id: entry.id.toString(),
								assignedAt: new Date(entry.assignedAt),
								isActive: entry.isActive,
								assignedByUserId: entry.assignedByUserId || undefined,
								assignedToUserId: entry.assignedToUser.id,
								assignedToUserName: entry.assignedToUser.name,
								assignedToUserEmail: entry.assignedToUser.email,
								roleId: entry.role.id.toString(),
								roleName: entry.role.name,
								roleDisplayName: entry.role.displayName,
								roleHierarchyLevel: entry.role.hierarchyLevel,
								assignedByName: entry.assignedBy?.name || "System",
							}),
						);

						// Mark as synced to prevent duplicate attempts
						syncedAuditEntriesRef.current.add(entry.id.toString());
					} catch (error) {
						console.error(
							`Failed to sync audit entry ${entry.id} to LiveStore:`,
							error,
						);
						// Don't add to synced set if it failed, so we can retry
					}
				}
			}
		}
	}, [enabled, auditLog, store]);

	// Sync all users to LiveStore when tRPC data updates
	useEffect(() => {
		if (!enabled || !store || !allUsers?.users) return;

		if (allUsers?.users) {
			for (const userEntry of allUsers.users) {
				const userId = userEntry.user.id;
				// Only sync if we haven't already synced this user
				if (!syncedUsersRef.current.has(userId)) {
					try {
						console.log(
							`[LiveStore] Syncing user ${userId} from tRPC to LiveStore`,
						);

						// Create user in LiveStore
						store.commit(
							events.adminUserCreated({
								id: userId,
								name: userEntry.user.name,
								email: userEntry.user.email,
								image: userEntry.user.image || undefined,
								createdAt: new Date(userEntry.user.createdAt),
								tornUserId: userEntry.tornUser?.tornUserId
									? userEntry.tornUser.tornUserId.toString()
									: undefined,
								tornFactionId: userEntry.tornUser?.tornFactionId
									? userEntry.tornUser.tornFactionId.toString()
									: undefined,
								verified: userEntry.tornUser?.verified || false,
								roleId: userEntry.role?.role.id
									? userEntry.role.role.id.toString()
									: undefined,
								roleName: userEntry.role?.role.name || undefined,
								roleDisplayName: userEntry.role?.role.displayName || undefined,
								roleHierarchyLevel: userEntry.role?.role.hierarchyLevel || 0,
								roleAssignedAt: userEntry.role?.assignedAt
									? new Date(userEntry.role.assignedAt)
									: undefined,
							}),
						);

						// Mark as synced to prevent duplicate attempts
						syncedUsersRef.current.add(userId);
					} catch (error) {
						console.error(`Failed to sync user ${userId} to LiveStore:`, error);
						// Don't add to synced set if it failed, so we can retry
					}
				}
			}
		}
	}, [enabled, allUsers, store]);

	// Listen to React Query cache updates and sync to LiveStore
	useEffect(() => {
		if (!enabled || !store) return;

		const handleCacheChange = () => {
			// Re-sync data when React Query cache is invalidated
			// This handles WebSocket updates that invalidate the cache
			const systemStatsQuery = queryClient.getQueryData([
				["admin", "getSystemStats"],
			]);

			// Sync updated system stats
			if (systemStatsQuery && typeof systemStatsQuery === "object") {
				const stats = systemStatsQuery as {
					totalUsers: number;
					totalRoles: number;
					totalPermissions: number;
					totalGuides: number;
				};

				const lastSynced = lastSyncedStatsRef.current;

				// Only sync if stats have actually changed
				if (
					!lastSynced ||
					lastSynced.totalUsers !== stats.totalUsers ||
					lastSynced.totalRoles !== stats.totalRoles ||
					lastSynced.totalPermissions !== stats.totalPermissions ||
					lastSynced.totalGuides !== stats.totalGuides
				) {
					try {
						console.log(
							`[LiveStore] Syncing system stats from cache update: ${stats.totalUsers} users`,
						);
						store.commit(
							events.adminSystemStatsUpdated({
								totalUsers: stats.totalUsers,
								totalRoles: stats.totalRoles,
								totalPermissions: stats.totalPermissions,
								totalGuides: stats.totalGuides,
								updatedAt: new Date(),
							}),
						);

						// Track the synced stats to prevent duplicates
						lastSyncedStatsRef.current = {
							totalUsers: stats.totalUsers,
							totalRoles: stats.totalRoles,
							totalPermissions: stats.totalPermissions,
							totalGuides: stats.totalGuides,
						};
					} catch (error) {
						console.error(
							"Failed to sync updated system stats to LiveStore:",
							error,
						);
					}
				}
			}
		};

		// Set up cache listener for real-time updates
		const unsubscribe = queryClient
			.getQueryCache()
			.subscribe(handleCacheChange);
		return unsubscribe;
	}, [enabled, queryClient, store]);

	// Utility functions for manual sync (useful for immediate updates after mutations)
	const syncUserUpdate = (user: {
		id: string;
		name: string;
		email: string;
		image?: string | null;
		tornUserId?: string | null;
		tornFactionId?: string | null;
		verified?: boolean;
		roleId?: string | null;
		roleName?: string | null;
		roleDisplayName?: string | null;
		roleHierarchyLevel?: number;
		roleAssignedAt?: Date | null;
	}) => {
		if (!enabled || !store) return;

		try {
			console.log(`[LiveStore] Manually syncing user update ${user.id}`);
			store.commit(
				events.adminUserUpdated({
					id: user.id,
					name: user.name,
					email: user.email,
					image: user.image || undefined,
					tornUserId: user.tornUserId || undefined,
					tornFactionId: user.tornFactionId || undefined,
					verified: user.verified || false,
					roleId: user.roleId || undefined,
					roleName: user.roleName || undefined,
					roleDisplayName: user.roleDisplayName || undefined,
					roleHierarchyLevel: user.roleHierarchyLevel || 0,
					roleAssignedAt: user.roleAssignedAt || undefined,
					updatedAt: new Date(),
				}),
			);
		} catch (error) {
			console.error("Failed to sync user update to LiveStore:", error);
		}
	};

	const syncUserSuspension = (suspension: {
		userId: string;
		suspended: boolean;
		reason?: string;
		suspendedAt?: Date;
	}) => {
		if (!enabled || !store) return;

		try {
			console.log(
				`[LiveStore] Manually syncing user suspension ${suspension.userId}: ${suspension.suspended}`,
			);
			store.commit(
				events.adminUserSuspensionUpdated({
					userId: suspension.userId,
					suspended: suspension.suspended,
					reason: suspension.reason,
					suspendedAt: suspension.suspendedAt,
					updatedAt: new Date(),
				}),
			);
		} catch (error) {
			console.error("Failed to sync user suspension to LiveStore:", error);
		}
	};

	const syncUserDeletion = (userId: string) => {
		if (!enabled || !store) return;

		try {
			console.log(`[LiveStore] Manually syncing user deletion ${userId}`);
			store.commit(
				events.adminUserDeleted({
					userId,
				}),
			);
			syncedUsersRef.current.delete(userId);
		} catch (error) {
			console.error("Failed to sync user deletion to LiveStore:", error);
		}
	};

	const syncStatsUpdate = (stats: {
		totalUsers: number;
		totalRoles: number;
		totalPermissions: number;
		totalGuides: number;
	}) => {
		if (!enabled || !store) return;

		const lastSynced = lastSyncedStatsRef.current;

		// Only sync if stats have actually changed
		if (
			!lastSynced ||
			lastSynced.totalUsers !== stats.totalUsers ||
			lastSynced.totalRoles !== stats.totalRoles ||
			lastSynced.totalPermissions !== stats.totalPermissions ||
			lastSynced.totalGuides !== stats.totalGuides
		) {
			try {
				console.log(
					`[LiveStore] Manually syncing stats update: ${stats.totalUsers} users`,
				);
				store.commit(
					events.adminSystemStatsUpdated({
						totalUsers: stats.totalUsers,
						totalRoles: stats.totalRoles,
						totalPermissions: stats.totalPermissions,
						totalGuides: stats.totalGuides,
						updatedAt: new Date(),
					}),
				);

				// Track the synced stats to prevent duplicates
				lastSyncedStatsRef.current = {
					totalUsers: stats.totalUsers,
					totalRoles: stats.totalRoles,
					totalPermissions: stats.totalPermissions,
					totalGuides: stats.totalGuides,
				};
			} catch (error) {
				console.error("Failed to sync stats update to LiveStore:", error);
			}
		}
	};

	return {
		syncUserUpdate,
		syncUserSuspension,
		syncUserDeletion,
		syncStatsUpdate,
	};
}

/**
 * Hook to listen to WebSocket updates and sync them to LiveStore
 */
export function useAdminWebSocketLiveStoreSync() {
	useEffect(() => {
		// Note: WebSocket integration would go here
		// This would integrate with any existing admin WebSocket hook
		// to sync real-time updates to LiveStore

		return () => {
			// Cleanup if needed
		};
	}, []);
}
