import { AlertCircle, CheckCircle, Heart, Shield } from "lucide-react";
import type { ChainStatusInfo, StatusInfo } from "./types";

// Enhanced status types with more visual feedback
export const getStatusInfo = (status: string): StatusInfo => {
	if (status === "Okay") {
		return {
			variant: "success" as const,
			icon: CheckCircle,
			color: "text-green-600",
			bgColor: "bg-green-50 dark:bg-green-950/20",
			borderColor: "border-green-200 dark:border-green-800",
		};
	}
	if (status.includes("Hospitalized")) {
		return {
			variant: "destructive" as const,
			icon: Heart,
			color: "text-red-600",
			bgColor: "bg-red-50 dark:bg-red-950/20",
			borderColor: "border-red-200 dark:border-red-800",
		};
	}
	if (status.includes("Error") || status === "Fetch Error") {
		return {
			variant: "secondary" as const,
			icon: AlertCircle,
			color: "text-orange-600",
			bgColor: "bg-orange-50 dark:bg-orange-950/20",
			borderColor: "border-orange-200 dark:border-orange-800",
		};
	}
	return {
		variant: "outline" as const,
		icon: Shield,
		color: "text-gray-600",
		bgColor: "bg-gray-50 dark:bg-gray-950/20",
		borderColor: "border-gray-200 dark:border-gray-800",
	};
};

// Helper function to extract hospital time in seconds for sorting
export const getHospitalTimeInSeconds = (status: string): number => {
	if (!status.includes("Hospitalized")) return 0;

	const match = status.match(/(\d+)m (\d+)s/);
	if (match?.[1] && match[2]) {
		const minutes = Number.parseInt(match[1], 10);
		const seconds = Number.parseInt(match[2], 10);
		return minutes * 60 + seconds;
	}
	return 0;
};

// Helper function to get status priority for smart sorting
export const getStatusPriority = (status: string): number => {
	if (status === "Okay") return 1; // Highest priority
	if (status.includes("Hospitalized")) return 2; // Medium priority
	if (status.includes("Error") || status === "Fetch Error") return 4; // Lowest priority
	return 3; // Unknown status
};

// Add helper to format duration
export const formatDuration = (totalSeconds: number): string => {
	const minutes = Math.floor(totalSeconds / 60);
	const seconds = totalSeconds % 60;
	return `${minutes}m ${seconds.toString().padStart(2, "0")}s`;
};

// Simplify the chain status info to use consistent colors for both border and icon
export const getChainStatusInfo = (
	remainingSeconds: number,
): ChainStatusInfo => {
	if (remainingSeconds <= 60) {
		return {
			color: "text-red-600",
			bgColor: "bg-red-50 dark:bg-red-950/20",
			borderColor: "border-red-300 dark:border-red-800",
		};
	}
	if (remainingSeconds <= 120) {
		return {
			color: "text-orange-600",
			bgColor: "bg-orange-50 dark:bg-orange-950/20",
			borderColor: "border-orange-300 dark:border-orange-800",
		};
	}
	return {
		color: "text-green-600",
		bgColor: "bg-green-50 dark:bg-green-950/20",
		borderColor: "border-green-300 dark:border-green-800",
	};
};

// Sound alert for critical chain
export const playCriticalSound = () => {
	try {
		interface WindowWithWebkit extends Window {
			AudioContext?: typeof AudioContext;
			webkitAudioContext?: typeof AudioContext;
		}
		const win = window as WindowWithWebkit;
		const AudioCtx = win.AudioContext || win.webkitAudioContext;
		if (!AudioCtx) return;
		const ctx = new AudioCtx();
		const osc = ctx.createOscillator();
		const gain = ctx.createGain();
		osc.frequency.value = 880;
		osc.type = "square";
		osc.connect(gain);
		gain.connect(ctx.destination);
		osc.start();
		gain.gain.exponentialRampToValueAtTime(0.0001, ctx.currentTime + 0.5);
		osc.stop(ctx.currentTime + 0.5);
	} catch (e) {
		// Ignore if autoplay blocked
	}
};
