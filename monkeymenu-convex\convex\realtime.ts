import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Real-time events for target finder
export const broadcastTargetUpdate = mutation({
  args: {
    listId: v.id("targetLists"),
    targetId: v.id("targets"),
    eventType: v.string(), // "added", "removed", "status_updated"
    data: v.any(),
  },
  handler: async (ctx, args) => {
    // Store the event for real-time updates
    const now = Date.now();
    
    // For now, we'll just log the event
    // In a full implementation, this would broadcast to connected clients
    console.log(`[RealTime] ${args.eventType} event for target ${args.targetId} in list ${args.listId}`, args.data);
    
    // You could store these events in a table for debugging/auditing
    // Or implement WebSocket-style broadcasting
    
    return { success: true, timestamp: now };
  },
});

// Get recent target events (for debugging)
export const getRecentTargetEvents = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    // This would query stored events
    // For now, return empty array as we're not storing events in the database
    return [];
  },
});

// Broadcast target list changes
export const broadcastListUpdate = mutation({
  args: {
    listId: v.id("targetLists"),
    eventType: v.string(), // "created", "deleted", "updated"
    data: v.any(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    console.log(`[RealTime] List ${args.eventType} event for list ${args.listId}`, args.data);
    
    return { success: true, timestamp: now };
  },
});

// Broadcast status batch updates (when multiple targets are updated)
export const broadcastStatusBatchUpdate = mutation({
  args: {
    listId: v.id("targetLists"),
    targets: v.array(v.object({
      id: v.id("targets"),
      tornId: v.number(),
      status: v.string(),
      profilePicture: v.optional(v.string()),
    })),
    updatedAt: v.number(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    console.log(`[RealTime] Batch status update for ${args.targets.length} targets in list ${args.listId}`);
    
    // In a full implementation, this would:
    // 1. Broadcast to all connected clients viewing this list
    // 2. Update any caches
    // 3. Trigger notifications if needed
    
    return { success: true, timestamp: now };
  },
});