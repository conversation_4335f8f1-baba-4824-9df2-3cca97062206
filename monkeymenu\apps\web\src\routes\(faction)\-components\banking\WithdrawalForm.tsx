import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { trpc } from "@/lib/trpc-client";
import { useStore } from "@livestore/react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AlertCircle, CheckCircle, Plus } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { events } from "../../../../livestore/schema";
import type { BankingFormProps } from "./types";
import {
	MAX_WITHDRAWAL_AMOUNT,
	formatCurrency,
	formatNumberWithCommas,
	processAmountInput,
} from "./utils";

export function WithdrawalForm({ balance, onSubmitSuccess }: BankingFormProps) {
	const queryClient = useQueryClient();
	const [amount, setAmount] = useState("");
	const [formError, setFormError] = useState("");
	const [successMessage, setSuccessMessage] = useState("");
	const { store } = useStore();

	const createWithdrawal = useMutation(
		trpc.banking.createWithdrawalRequest.mutationOptions({
			onError: (err) => {
				setFormError(err.message);
				setSuccessMessage("");
				toast.error(`Failed to create withdrawal: ${err.message}`);
			},
			onSuccess: (data) => {
				setAmount("");
				setFormError("");
				setSuccessMessage(
					`Withdrawal request for ${formatCurrency(data.amount)} submitted successfully!`,
				);
				toast.success("Withdrawal request submitted successfully!");

				// Invalidate relevant queries
				queryClient.invalidateQueries({
					predicate: (query) => {
						const queryKey = query.queryKey;
						return (
							Array.isArray(queryKey) &&
							Array.isArray(queryKey[0]) &&
							queryKey[0][0] === "banking"
						);
					},
				});

				// Commit to LiveStore for real-time sync
				try {
					store.commit(
						events.withdrawalCreated({
							id: data.id,
							amount: data.amount,
							requestedById: data.requestedById,
							requestedByName: "You",
							requestedByTornId: undefined,
							createdAt: new Date(data.createdAt),
						}),
					);
				} catch (err) {
					console.error("[LiveStore] failed to commit created withdrawal", err);
				}

				setTimeout(() => setSuccessMessage(""), 5000);
				onSubmitSuccess();
			},
		}),
	);

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		setFormError("");
		setSuccessMessage("");
		const numAmount = Number.parseInt(amount, 10);

		if (Number.isNaN(numAmount) || numAmount <= 0) {
			setFormError("Please enter a valid amount");
			return;
		}

		if (numAmount > MAX_WITHDRAWAL_AMOUNT) {
			setFormError(
				`Amount cannot exceed ${formatCurrency(MAX_WITHDRAWAL_AMOUNT)}.`,
			);
			return;
		}

		if (balance?.factionBalance && numAmount > balance.factionBalance.money) {
			setFormError("Amount exceeds available balance");
			return;
		}

		createWithdrawal.mutate({ amount: numAmount });
	};

	const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const rawValue = e.target.value;
		const processedValue = processAmountInput(rawValue);

		setAmount(processedValue);
		setFormError("");
		setSuccessMessage("");
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Plus className="h-5 w-5" />
					Request Withdrawal
				</CardTitle>
			</CardHeader>
			<CardContent>
				<form onSubmit={handleSubmit} className="space-y-4">
					<div className="space-y-2">
						<Label htmlFor="amount">
							Amount (you can use k, m, b suffixes)
						</Label>
						<Input
							id="amount"
							type="text"
							placeholder="Enter amount (e.g., 1000000 or 1m)"
							value={amount ? formatNumberWithCommas(amount) : ""}
							onChange={handleAmountChange}
							disabled={createWithdrawal.isPending}
						/>
					</div>

					{formError && (
						<Alert variant="destructive">
							<AlertCircle className="h-4 w-4" />
							<AlertDescription>{formError}</AlertDescription>
						</Alert>
					)}

					{successMessage && (
						<Alert className="border-green-500 bg-green-50 text-green-800 dark:bg-green-950 dark:text-green-200">
							<CheckCircle className="h-4 w-4" />
							<AlertDescription>{successMessage}</AlertDescription>
						</Alert>
					)}

					<Button
						type="submit"
						disabled={createWithdrawal.isPending}
						className="w-full"
					>
						{createWithdrawal.isPending ? "Submitting..." : "Submit Request"}
					</Button>
				</form>
			</CardContent>
		</Card>
	);
}
