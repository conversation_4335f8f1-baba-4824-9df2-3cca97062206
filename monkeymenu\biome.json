{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "ignore": ["dist", ".turbo", ".vscode", ".wrangler", "routeTree.gen.ts", "worker-configuration.d.ts"]}, "formatter": {"enabled": true, "indentStyle": "tab", "indentWidth": 2, "formatWithErrors": true}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "nursery": {"useSortedClasses": {"level": "warn", "fix": "safe", "options": {"functions": ["clsx", "cva", "cn"]}}}}}, "javascript": {"formatter": {"quoteStyle": "double", "trailingCommas": "all", "semicolons": "always"}}}