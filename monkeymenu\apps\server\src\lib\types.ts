/// <reference path="../../worker-configuration.d.ts" />

import type { UserPermissionContext } from "@monkeymenu/shared";
import type { Session, User } from "better-auth";
import type { DBInstance } from "../db";
import type { BetterAuthInstance } from "./auth";

// Hono bindings
export interface AppBindings {
	Bindings: {
		ASSETS: Fetcher;
		DB: D1Database;
		LIVESTORE_WEBSOCKET_SERVER: DurableObjectNamespace;
		// SESSION_KV: KVNamespace;
		CORS_ORIGIN: string;
		ENVIRONMENT: string;
		CLOUDFLARE_ACCOUNT_ID: string;
		CLOUDFLARE_DATABASE_ID: string;
		CLOUDFLARE_TOKEN: string;
		BETTER_AUTH_SECRET: string;
		BETTER_AUTH_URL: string;
		GOOGLE_CLIENT_ID: string;
		GOOGLE_CLIENT_SECRET: string;
		GOOGLE_GENERATIVE_API_KEY: string;
		GITHUB_CLIENT_ID: string;
		GITHUB_CLIENT_SECRET: string;
		DISCORD_CLIENT_ID: string;
		DISCORD_CLIENT_SECRET: string;
		DISCORD_BOT_TOKEN: string;
		DISCORD_PUBLIC_KEY: string;
		DISCORD_APPLICATION_ID: string;
		DISCORD_GUILD_ID: string;
		RESEND_API_KEY: string;
		RESEND_FROM_EMAIL: string;
		APP_NAME: string;
		FRONTEND_URL: string;
		TORN_API_KEY_ENCRYPTION_SECRET: string;
		SYSTEM_ADMIN_TORN_IDS: string;
		BANKING_NOTIFICATION_CHANNEL_ID: string;
		APP_URL: string;
		ANNOUNCEMENTS_CHANNEL_ID: string;
		FACTION_ROLE_ID: string;
		OVERDOSE_NOTIFICATION_CHANNEL_ID: string;
	};
	Variables: {
		user: User | null;
		session: Session | null;
		db: DBInstance;
		auth: BetterAuthInstance;
	};
}

// tRPC context reflects what we inject from Hono: env, db, and session
export interface tRPCContext {
	env: AppBindings["Bindings"];
	db: DBInstance;
	session: Session | null;
	permissions?: UserPermissionContext; // Optional, for composable permission middleware
}

// Enhanced tRPC context with permissions
export interface tRPCContextWithPermissions extends tRPCContext {
	session: Session;
	permissions: UserPermissionContext;
}
