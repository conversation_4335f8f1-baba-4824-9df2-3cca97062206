import {
	GuideFormSchema,
	GuideUpdateSchema,
	PERMISSIONS,
} from "@monkeymenu/shared";
import { desc, eq } from "drizzle-orm";
import { z } from "zod";
import { user } from "../db/schema/auth";
import { guide } from "../db/schema/guides";
import {
	factionPermissionProcedure,
	requirePermission,
	router,
} from "../lib/trpc";

export const guidesRouter = router({
	// Get all guides (requires view permission)
	getAll: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.GUIDES_VIEW.name))
		.query(async ({ ctx }) => {
			return await ctx.db
				.select({
					guide: guide,
					author: {
						id: user.id,
						name: user.name,
						image: user.image,
					},
				})
				.from(guide)
				.innerJoin(user, eq(guide.authorId, user.id))
				.orderBy(desc(guide.createdAt));
		}),

	// Get a single guide by ID (requires view permission)
	getById: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.GUIDES_VIEW.name))
		.input(z.object({ id: z.number() }))
		.query(async ({ ctx, input }) => {
			return await ctx.db
				.select({
					guide: guide,
					author: {
						id: user.id,
						name: user.name,
						image: user.image,
					},
				})
				.from(guide)
				.innerJoin(user, eq(guide.authorId, user.id))
				.where(eq(guide.id, input.id))
				.get();
		}),

	// Create a new guide (requires manage permission)
	create: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.GUIDES_MANAGE.name))
		.input(GuideFormSchema)
		.mutation(async ({ ctx, input }) => {
			return await ctx.db
				.insert(guide)
				.values({
					title: input.title,
					content: input.content,
					category: input.category,
					authorId: ctx.session.userId,
				})
				.returning()
				.get();
		}),

	// Update an existing guide (requires manage permission)
	update: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.GUIDES_MANAGE.name))
		.input(GuideUpdateSchema)
		.mutation(async ({ ctx, input }) => {
			// Get the existing guide
			const existingGuide = await ctx.db
				.select()
				.from(guide)
				.where(eq(guide.id, input.id))
				.get();

			if (!existingGuide) {
				throw new Error("Guide not found");
			}

			// Permission is now handled by .use(requirePermission(PERMISSIONS.GUIDES_MANAGE.name))

			return await ctx.db
				.update(guide)
				.set({
					title: input.title,
					content: input.content,
					category: input.category,
					updatedAt: new Date().toISOString(),
				})
				.where(eq(guide.id, input.id))
				.returning()
				.get();
		}),

	// Delete a guide (requires manage permission)
	delete: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.GUIDES_MANAGE.name))
		.input(z.object({ id: z.number() }))
		.mutation(async ({ ctx, input }) => {
			// Get the existing guide
			const existingGuide = await ctx.db
				.select()
				.from(guide)
				.where(eq(guide.id, input.id))
				.get();

			if (!existingGuide) {
				throw new Error("Guide not found");
			}

			// Permission is now handled by .use(requirePermission(PERMISSIONS.GUIDES_MANAGE.name))

			await ctx.db.delete(guide).where(eq(guide.id, input.id)).run();
			return { success: true };
		}),
});
