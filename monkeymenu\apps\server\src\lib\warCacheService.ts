import type {
	Torn<PERSON><PERSON><PERSON>,
	TornChainInfo,
	TornChainReport,
} from "@monkeymenu/shared";
import { and, desc, eq, isNull } from "drizzle-orm";
import type { DBInstance } from "../db";
import {
	cachedChainReport,
	cachedWarAttacks,
	cachedWarReport,
	cachedWarStats,
} from "../db/schema/wars";
import type { TornAPI } from "./tornApi";

export class WarCacheService {
	constructor(
		private db: DBInstance,
		private tornApi: TornAPI,
	) {}

	/**
	 * Get war report with intelligent caching
	 * - Returns cached data for completed wars
	 * - Fetches fresh data for ongoing wars
	 */
	async getWarReport(warId: number) {
		const cached = await this.db
			.select()
			.from(cachedWarReport)
			.where(eq(cachedWarReport.warId, warId))
			.get();

		// If we have cached data and the war is completed, return it
		if (cached?.isCompleted) {
			return JSON.parse(cached.reportData);
		}

		// Fetch fresh data from API
		const freshReport = await this.tornApi.getRankedWarReport(warId);

		// Cache the data
		await this.cacheWarReport(warId, freshReport);

		return freshReport;
	}

	/**
	 * Get chain reports for a war with intelligent caching
	 */
	async getWarChainReports(warId: number) {
		const warReport = await this.getWarReport(warId);
		const isWarCompleted = warReport.end !== null;

		// Get any cached chain reports
		const cachedChains = await this.db
			.select()
			.from(cachedChainReport)
			.where(eq(cachedChainReport.warId, warId))
			.all();

		// For completed wars, we can trust all cached data
		if (isWarCompleted && cachedChains.length > 0) {
			// Check if we need to fetch any missing chains
			const warStart = warReport.start;
			const warEnd = warReport.end;

			// Get faction chains from API to see if we're missing any
			const chains = await this.getAllChainsInTimeRange(warStart, warEnd);
			const relevantChains = chains;

			const cachedChainIds = new Set(cachedChains.map((c) => c.chainId));
			const missingChains = relevantChains.filter(
				(ch) => !cachedChainIds.has(ch.id),
			);

			// Fetch and cache missing chains
			for (const chain of missingChains) {
				try {
					const chainReport = await this.tornApi.getChainReport(chain.id);
					await this.cacheChainReport(warId, chainReport);
					cachedChains.push({
						chainId: chain.id,
						warId,
						reportData: JSON.stringify(chainReport),
						startTime: chain.start,
						endTime: chain.end,
						chainLength: chainReport.details.chain,
						totalRespect: chainReport.details.respect,
						attackerCount: chainReport.attackers?.length || 0,
						cachedAt: new Date(),
					});
				} catch (err) {
					console.error(`Failed to fetch chain report ${chain.id}:`, err);
				}
			}
		}

		// If war is ongoing or we have no cached data, fetch fresh
		if (!isWarCompleted || cachedChains.length === 0) {
			return await this.fetchAndCacheChainReports(warId);
		}

		// Return cached chain reports
		const chainReports = cachedChains.map((cached) =>
			JSON.parse(cached.reportData),
		);

		// Build player names mapping from cached attacks
		const playerNames = await this.getPlayerNamesFromCache(warId);

		// Calculate combined stats from cached data
		const combinedChainStats = this.calculateCombinedChainStats(
			chainReports,
			playerNames,
		);

		return { chainReports, playerNames, combinedChainStats };
	}

	/**
	 * Get war attacks with intelligent caching
	 */
	async getWarAttacks(warId: number, fetchLatest = false) {
		const warReport = await this.getWarReport(warId);
		const isWarCompleted = warReport.end !== null;
		const warStart = warReport.start;
		const warEnd = warReport.end || Math.floor(Date.now() / 1000);

		// For completed wars, use cached data if available
		if (isWarCompleted && !fetchLatest) {
			const cachedAttacks = await this.db
				.select()
				.from(cachedWarAttacks)
				.where(eq(cachedWarAttacks.warId, warId))
				.orderBy(desc(cachedWarAttacks.timestamp))
				.all();

			if (cachedAttacks.length > 0) {
				return cachedAttacks.map((cached) => JSON.parse(cached.attackData));
			}
		}

		// Fetch fresh attacks from API using time range parameters
		const collected: TornAttack[] = [];
		// Add buffer to catch final attacks that complete after official end time
		const bufferedEnd = warEnd + 300; // 5 minute buffer
		let cursor: number | undefined = bufferedEnd;
		while (true) {
			const batch = await this.tornApi.getFactionAttacks(
				100,
				"DESC",
				cursor,
				warStart,
			);
			if (batch.length === 0) break;

			// Filter to actual time range (remove buffer for final results)
			const withinRange = batch.filter(
				(a) => a.started >= warStart && a.started <= warEnd,
			);
			collected.push(...withinRange);

			// Continue pagination only if we got a full batch (100 items)
			// AND the oldest attack is still within our range
			if (batch.length < 100) break;

			const oldest = batch[batch.length - 1];
			if (oldest.started <= warStart) break;

			cursor = oldest.started - 1;
		}

		// Cache attacks if war is completed
		if (isWarCompleted) {
			await this.cacheWarAttacks(warId, collected);
		}

		return collected;
	}

	/**
	 * Get cached stats or calculate and cache them
	 */
	async getWarStats(
		warId: number,
		factionId: number,
		statsType: string,
		timeframe?: string,
	) {
		const cached = await this.db
			.select()
			.from(cachedWarStats)
			.where(
				and(
					eq(cachedWarStats.warId, warId),
					eq(cachedWarStats.factionId, factionId),
					eq(cachedWarStats.statsType, statsType),
					timeframe
						? eq(cachedWarStats.timeframe, timeframe)
						: isNull(cachedWarStats.timeframe),
				),
			)
			.get();

		const warReport = await this.getWarReport(warId);
		const isWarCompleted = warReport.end !== null;

		// For completed wars, trust cached stats
		if (cached && isWarCompleted) {
			return JSON.parse(cached.statsData);
		}

		// Calculate fresh stats (implement specific logic based on statsType)
		// This would contain the actual calculation logic based on statsType
		// For now, return null to indicate fresh calculation is needed
		return null;
	}

	/**
	 * Cache a war report
	 */
	private async cacheWarReport(
		warId: number,
		report: {
			factions: Array<{ id: number }>;
			end: number | null;
			winner: number | null;
			start: number;
		},
	) {
		const factionIds = report.factions.map((f) => f.id).join(",");
		const isCompleted = report.end !== null;

		await this.db
			.insert(cachedWarReport)
			.values({
				warId,
				reportData: JSON.stringify(report),
				factionIds,
				startTime: report.start,
				endTime: report.end,
				winner: report.winner,
				isCompleted,
			})
			.onConflictDoUpdate({
				target: cachedWarReport.warId,
				set: {
					reportData: JSON.stringify(report),
					endTime: report.end,
					winner: report.winner,
					isCompleted,
					lastUpdated: new Date(),
				},
			});
	}

	/**
	 * Cache a chain report
	 */
	private async cacheChainReport(warId: number, chainReport: TornChainReport) {
		await this.db
			.insert(cachedChainReport)
			.values({
				chainId: chainReport.id,
				warId,
				reportData: JSON.stringify(chainReport),
				startTime: chainReport.start,
				endTime: chainReport.end,
				chainLength: chainReport.details.chain,
				totalRespect: chainReport.details.respect,
				attackerCount: chainReport.attackers?.length || 0,
			})
			.onConflictDoNothing();
	}

	/**
	 * Cache war attacks
	 */
	private async cacheWarAttacks(warId: number, attacks: TornAttack[]) {
		// Clear existing cache for this war first to avoid conflicts
		await this.db
			.delete(cachedWarAttacks)
			.where(eq(cachedWarAttacks.warId, warId));

		const attackRecords = attacks.map((attack) => ({
			id: String(attack.id),
			warId,
			attackData: JSON.stringify(attack),
			attackerId: attack.attacker?.id || 0,
			defenderId: attack.defender?.id || 0,
			attackerFactionId: attack.attacker?.faction?.id || null,
			defenderFactionId: attack.defender?.faction?.id || null,
			timestamp: attack.started,
			chainId: attack.chain || null,
			result: attack.result,
			respect: Number(attack.modifiers?.fair_fight) || 0,
		}));

		// Insert in very small batches to avoid SQLite variable limits
		// SQLite has a limit of 999 variables per statement
		// With 12 columns per record and the complexity of the INSERT statement,
		// we need to be very conservative with batch sizes
		const batchSize = 25;
		for (let i = 0; i < attackRecords.length; i += batchSize) {
			const batch = attackRecords.slice(i, i + batchSize);
			try {
				await this.db.insert(cachedWarAttacks).values(batch);
			} catch (error) {
				console.error(
					`Failed to insert batch ${i}-${i + batch.length}:`,
					error,
				);
				// Try inserting one by one if batch fails
				for (const record of batch) {
					try {
						await this.db.insert(cachedWarAttacks).values(record);
					} catch (singleError) {
						console.error(
							`Failed to insert single record ${record.id}:`,
							singleError,
						);
					}
				}
			}
		}
	}

	/**
	 * Fetch and cache chain reports (fallback method)
	 */
	private async fetchAndCacheChainReports(warId: number) {
		// This is the original logic from the wars router
		const warReport = await this.getWarReport(warId);
		const warStart = warReport.start;
		const warEnd = warReport.end;

		const relevantChains = await this.getAllChainsInTimeRange(warStart, warEnd);

		const chainReports: TornChainReport[] = [];
		const playerNamesMap = new Map<string, string>();

		// Fetch all attacks during the war period to build player name mapping
		const collected: TornAttack[] = [];
		// Add buffer to catch final attacks that complete after official end time
		const bufferedEnd = warEnd + 300; // 5 minute buffer
		let cursor: number | undefined = bufferedEnd;
		while (true) {
			const batch = await this.tornApi.getFactionAttacks(
				100,
				"DESC",
				cursor,
				warStart,
			);
			if (batch.length === 0) break;

			// Filter to actual time range (remove buffer for final results)
			const withinRange = batch.filter(
				(a) => a.started >= warStart && a.started <= warEnd,
			);
			collected.push(...withinRange);

			// Continue pagination only if we got a full batch (100 items)
			// AND the oldest attack is still within our range
			if (batch.length < 100) break;

			const oldest = batch[batch.length - 1];
			if (oldest.started <= warStart) break;

			cursor = oldest.started - 1;
		}

		// Build player name mapping from attacks
		for (const attack of collected) {
			if (attack.attacker?.id && attack.attacker?.name) {
				playerNamesMap.set(attack.attacker.id.toString(), attack.attacker.name);
			}
		}

		// Fetch chain reports
		for (const chain of relevantChains) {
			try {
				const report = await this.tornApi.getChainReport(chain.id);
				chainReports.push(report);

				// Cache the chain report
				await this.cacheChainReport(warId, report);
			} catch (err) {
				console.error(`Failed to fetch chain report ${chain.id}:`, err);
			}
		}

		const playerNames = Object.fromEntries(playerNamesMap);
		const combinedChainStats = this.calculateCombinedChainStats(
			chainReports,
			playerNames,
		);

		return { chainReports, playerNames, combinedChainStats };
	}

	/**
	 * Get player names from cached attack data
	 */
	private async getPlayerNamesFromCache(
		warId: number,
	): Promise<Record<string, string>> {
		const cachedAttacks = await this.db
			.select({ attackData: cachedWarAttacks.attackData })
			.from(cachedWarAttacks)
			.where(eq(cachedWarAttacks.warId, warId))
			.all();

		const playerNames: Record<string, string> = {};
		for (const cached of cachedAttacks) {
			const attack = JSON.parse(cached.attackData);
			if (attack.attacker?.id && attack.attacker?.name) {
				playerNames[attack.attacker.id.toString()] = attack.attacker.name;
			}
		}

		return playerNames;
	}

	/**
	 * Calculate combined chain statistics
	 */
	private calculateCombinedChainStats(
		chainReports: TornChainReport[],
		playerNames: Record<string, string>,
	) {
		const combinedStatsMap = new Map();

		for (const chainReport of chainReports) {
			if (chainReport.attackers) {
				for (const attacker of chainReport.attackers) {
					const playerId = attacker.id;
					const playerName = playerNames[playerId.toString()];

					if (!combinedStatsMap.has(playerId)) {
						combinedStatsMap.set(playerId, {
							playerId,
							playerName,
							totalHits: 0,
							totalWarHits: 0,
							totalAssists: 0,
							totalRespect: 0,
							bestRespect: 0,
							chainCount: 0,
							respectValues: [],
						});
					}

					const stats = combinedStatsMap.get(playerId);
					stats.totalHits += attacker.attacks.total;
					stats.totalWarHits += attacker.attacks.war;
					stats.totalAssists += attacker.attacks.assists;
					stats.totalRespect += attacker.respect.total;
					stats.bestRespect = Math.max(
						stats.bestRespect,
						attacker.respect.best,
					);
					stats.chainCount++;
					stats.respectValues.push(attacker.respect.total);
				}
			}
		}

		return Array.from(combinedStatsMap.values()).map(
			(stats: {
				playerId: number;
				playerName?: string;
				totalHits: number;
				totalWarHits: number;
				totalAssists: number;
				totalRespect: number;
				bestRespect: number;
				chainCount: number;
			}) => ({
				playerId: stats.playerId,
				playerName: stats.playerName,
				totalHits: stats.totalHits,
				totalWarHits: stats.totalWarHits,
				totalAssists: stats.totalAssists,
				totalRespect: stats.totalRespect,
				averageRespect:
					stats.totalHits > 0 ? stats.totalRespect / stats.totalHits : 0,
				bestRespect: stats.bestRespect,
				chainCount: stats.chainCount,
			}),
		);
	}

	/**
	 * Clear cache for a specific war (useful for debugging or data updates)
	 */
	async clearWarCache(warId: number) {
		await Promise.all([
			this.db.delete(cachedWarReport).where(eq(cachedWarReport.warId, warId)),
			this.db
				.delete(cachedChainReport)
				.where(eq(cachedChainReport.warId, warId)),
			this.db.delete(cachedWarAttacks).where(eq(cachedWarAttacks.warId, warId)),
			this.db.delete(cachedWarStats).where(eq(cachedWarStats.warId, warId)),
		]);
	}

	/**
	 * Get all chains within a time range with pagination
	 */
	private async getAllChainsInTimeRange(warStart: number, warEnd: number) {
		const allChains: TornChainInfo[] = [];
		// Add buffer to ensure we don't miss chain end times
		const bufferedEnd = warEnd + 300; // 5 minute buffer
		let cursor: number | undefined = bufferedEnd;

		while (true) {
			const batch = await this.tornApi.getFactionChains(
				100,
				"DESC",
				cursor,
				warStart,
			);
			if (batch.length === 0) break;

			allChains.push(...batch);

			// Continue pagination only if we got a full batch (100 items)
			// AND the oldest chain is still within our range
			if (batch.length < 100) break;

			const oldest = batch[batch.length - 1];
			if (oldest.start <= warStart) break;

			cursor = oldest.start - 1;
		}

		return allChains;
	}

	/**
	 * Get cache statistics for monitoring
	 */
	async getCacheStats() {
		const [warReports, chainReports, attacks, stats] = await Promise.all([
			this.db
				.select({ count: cachedWarReport.warId })
				.from(cachedWarReport)
				.all(),
			this.db
				.select({ count: cachedChainReport.chainId })
				.from(cachedChainReport)
				.all(),
			this.db
				.select({ count: cachedWarAttacks.id })
				.from(cachedWarAttacks)
				.all(),
			this.db.select({ count: cachedWarStats.id }).from(cachedWarStats).all(),
		]);

		return {
			cachedWarReports: warReports.length,
			cachedChainReports: chainReports.length,
			cachedAttacks: attacks.length,
			cachedStats: stats.length,
		};
	}
}
