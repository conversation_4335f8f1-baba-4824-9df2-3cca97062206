import { HasPermission } from "@/components/permissions/PermissionGuards";
import { PERMISSION_NAMES } from "@monkeymenu/shared";
import { WithdrawalForm } from "./WithdrawalForm";
import type { BankingFormProps } from "./types";

export function DashboardTab(props: BankingFormProps) {
	return (
		<div className="space-y-6">
			<HasPermission permission={PERMISSION_NAMES.BANKING_REQUEST}>
				<WithdrawalForm {...props} />
			</HasPermission>
		</div>
	);
}
