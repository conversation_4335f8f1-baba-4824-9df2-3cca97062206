import { cn } from "@/lib/utils";
import type { ReactNode } from "react";

interface PageContainerProps {
	children: ReactNode;
	variant?: "wide" | "narrow";
	className?: string;
}

export function PageContainer({
	children,
	variant = "wide",
	className,
}: PageContainerProps) {
	const baseClasses = "container mx-auto w-full min-w-0";

	const variantClasses = {
		wide: "max-w-[95vw] px-4 py-6 sm:max-w-4xl sm:px-6 md:max-w-6xl lg:max-w-7xl",
		narrow: "max-w-[90vw] px-3 py-6 sm:max-w-2xl sm:px-4 md:max-w-3xl",
	};

	return (
		<div className={cn(baseClasses, variantClasses[variant], className)}>
			{children}
		</div>
	);
}
