import { useState, useEffect } from 'react';
import { useQuery, useAction } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { AttackTimelineDialog } from './AttackTimelineDialog';
import { ChainDetailsDialog } from './ChainDetailsDialog';
import { PlayerPerformanceDialog } from './PlayerPerformanceDialog';

interface WarDialogContentProps {
  war: {
    _id: Id<"wars">;
    factionName: string;
    enemyFactionName: string;
    startTime: number;
    endTime?: number;
    status: string;
    ourScore: number;
    enemyScore: number;
  };
  warId: number; // Torn war ID for analytics
  onClose: () => void;
}

export function WarDialogContent({ war, warId, onClose }: WarDialogContentProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'chains' | 'attacks' | 'players'>('overview');
  const [selectedChainId, setSelectedChainId] = useState<number | null>(null);
  const [selectedPlayerId, setSelectedPlayerId] = useState<number | null>(null);
  const [showTimelineDialog, setShowTimelineDialog] = useState(false);
  
  // State for action results
  const [warReport, setWarReport] = useState<any>(null);
  const [chainReports, setChainReports] = useState<any>(null);
  const [warAttacks, setWarAttacks] = useState<any>(null);
  const [playerPerformance, setPlayerPerformance] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Actions
  const getRankedWarReport = useAction(api.warsAdvanced.getRankedWarReport);
  const getWarChainReports = useAction(api.warsAdvanced.getWarChainReports);
  const getWarAttacks = useAction(api.warsAdvanced.getWarAttacks);
  const getPlayerWarPerformance = useAction(api.warsAdvanced.getPlayerWarPerformance);

  // Fetch data when component mounts
  useEffect(() => {
    const fetchWarData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        console.log('Fetching war analytics for war ID:', warId);
        
        // Fetch all war analytics in parallel
        const [reportResult, chainsResult, attacksResult, performanceResult] = await Promise.allSettled([
          getRankedWarReport({ warId }),
          getWarChainReports({ warId }),
          getWarAttacks({ warId }),
          getPlayerWarPerformance({ warId })
        ]);
        
        // Handle results
        if (reportResult.status === 'fulfilled') {
          console.log('War report result:', reportResult.value);
          setWarReport(reportResult.value?.warReport || reportResult.value);
        } else {
          console.error('Failed to fetch war report:', reportResult.reason);
          const errorMessage = reportResult.reason?.message || 'Unknown error';
          
          if (errorMessage.includes('not found in faction\'s ranked wars')) {
            setError(`War ${warId} is not accessible through the Torn API. This war may be too old (only recent wars are available) or archived. Please try analytics on more recent wars.`);
            return;
          } else if (errorMessage.includes('direct access failed')) {
            setError(`War ${warId} could not be accessed through any available API endpoint. This war may be archived or removed from Torn's systems.`);
            return;
          }
          
          setError(`Failed to load war data: ${errorMessage}`);
        }
        
        if (chainsResult.status === 'fulfilled') {
          console.log('Chain reports result:', chainsResult.value);
          console.log('First chain structure:', chainsResult.value?.chains?.[0]);
          setChainReports(chainsResult.value);
        } else {
          console.error('Failed to fetch chain reports:', chainsResult.reason);
        }
        
        if (attacksResult.status === 'fulfilled') {
          console.log('War attacks result:', attacksResult.value);
          setWarAttacks(attacksResult.value);
        } else {
          console.error('Failed to fetch war attacks:', attacksResult.reason);
        }
        
        if (performanceResult.status === 'fulfilled') {
          console.log('Player performance result:', performanceResult.value);
          setPlayerPerformance(performanceResult.value);
        } else {
          console.error('Failed to fetch player performance:', performanceResult.reason);
        }
        
      } catch (error) {
        console.error('Error fetching war analytics:', error);
        setError(error instanceof Error ? error.message : 'Failed to load war analytics');
      } finally {
        setLoading(false);
      }
    };

    fetchWarData();
  }, [warId, getRankedWarReport, getWarChainReports, getWarAttacks, getPlayerWarPerformance]);

  const formatDateTime = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDuration = (start: number, end?: number) => {
    const endTime = end || Date.now() / 1000;
    const duration = endTime - start;
    const hours = Math.floor(duration / 3600);
    const minutes = Math.floor((duration % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const getWarResult = () => {
    if (war.status !== 'ended') return null;
    
    if (war.ourScore > war.enemyScore) return 'won';
    if (war.ourScore < war.enemyScore) return 'lost';
    return 'tied';
  };

  const warResult = getWarResult();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-7xl w-full max-h-[95vh] overflow-y-auto">
        <div className="sticky top-0 bg-white border-b border-gray-200 p-6 z-10">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                ⚔️ {war.factionName} vs {war.enemyFactionName}
              </h2>
              <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
                <span>Started: {formatDateTime(war.startTime)}</span>
                {war.endTime && <span>Ended: {formatDateTime(war.endTime)}</span>}
                <span>Duration: {formatDuration(war.startTime, war.endTime)}</span>
                {warResult && (
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    warResult === 'won' ? 'bg-green-100 text-green-800' :
                    warResult === 'lost' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {warResult === 'won' ? '🎉 Victory!' : 
                     warResult === 'lost' ? '💀 Defeat' : 
                     '🤝 Tied'}
                  </span>
                )}
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl font-bold"
            >
              ×
            </button>
          </div>

          {/* Tab Navigation */}
          <div className="flex space-x-1 mt-6">
            {[
              { id: 'overview', label: '📊 Overview', icon: '📊' },
              { id: 'chains', label: '🔗 Chains', icon: '🔗' },
              { id: 'attacks', label: '⚔️ Attacks', icon: '⚔️' },
              { id: 'players', label: '👥 Players', icon: '👥' },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-100 text-blue-800 border border-blue-200'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span className="hidden sm:inline">{tab.label}</span>
                <span className="sm:hidden">{tab.icon}</span>
              </button>
            ))}
          </div>
        </div>

        <div className="p-6">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Loading war analytics...</span>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="text-red-600 text-lg mb-2">❌ Error Loading Analytics</div>
                <div className="text-gray-600 text-sm">{error}</div>
              </div>
            </div>
          ) : (
            <>
              {/* Overview Tab */}
              {activeTab === 'overview' && (
                <div className="space-y-6">
                  {/* Score Display */}
                  <div className="bg-gradient-to-r from-blue-50 to-red-50 rounded-lg p-6">
                    <div className="flex items-center justify-center space-x-8">
                      <div className="text-center">
                        <div className="text-4xl font-bold text-blue-600">{war.ourScore}</div>
                        <div className="text-gray-600 font-medium">{war.factionName}</div>
                      </div>
                      
                      <div className="text-3xl text-gray-400">vs</div>
                      
                      <div className="text-center">
                        <div className="text-4xl font-bold text-red-600">{war.enemyScore}</div>
                        <div className="text-gray-600 font-medium">{war.enemyFactionName}</div>
                      </div>
                    </div>
                  </div>

                  {/* Quick Stats Grid */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {chainReports?.chains?.length || 0}
                      </div>
                      <div className="text-sm text-gray-600">Total Chains</div>
                    </div>
                    <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {warAttacks?.totalRespect?.toLocaleString() || '0'}
                      </div>
                      <div className="text-sm text-gray-600">Total Respect</div>
                    </div>
                    <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {warAttacks?.insideHits || 0}
                      </div>
                      <div className="text-sm text-gray-600">Inside Hits</div>
                    </div>
                    <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-orange-600">
                        {playerPerformance?.playerDetails?.length || 0}
                      </div>
                      <div className="text-sm text-gray-600">Active Members</div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-wrap gap-3">
                    <button
                      onClick={() => setShowTimelineDialog(true)}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                      📈 View Timeline
                    </button>
                    <button
                      onClick={() => setActiveTab('chains')}
                      className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                    >
                      🔗 Analyze Chains
                    </button>
                    <button
                      onClick={() => setActiveTab('players')}
                      className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
                    >
                      👥 Player Performance
                    </button>
                  </div>

                  {/* Top Performers */}
                  {warAttacks?.memberStats && warAttacks.memberStats.length > 0 && (
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">🏆 Top Performers</h3>
                      <div className="space-y-3">
                        {warAttacks.memberStats
                          .sort((a: any, b: any) => b.totalRespect - a.totalRespect)
                          .slice(0, 10)
                          .map((member: any, index: number) => (
                            <div key={member.attackerId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                              <div className="flex items-center space-x-3">
                                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm ${
                                  index < 3 
                                    ? index === 0 ? 'bg-yellow-500' : index === 1 ? 'bg-gray-400' : 'bg-orange-500'
                                    : 'bg-blue-500'
                                }`}>
                                  {index + 1}
                                </div>
                                <div>
                                  <div className="font-medium">{member.attackerName || `Player #${member.attackerId}`}</div>
                                  <div className="text-sm text-gray-600">
                                    {member.totalAttacks} attacks • {member.insideHits} inside hits
                                  </div>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="font-bold text-lg">{member.totalRespect.toLocaleString()}</div>
                                <div className="text-sm text-gray-600">respect</div>
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Chains Tab */}
              {activeTab === 'chains' && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-xl font-bold text-gray-900">🔗 Chain Analysis</h3>
                    <div className="text-sm text-gray-600">
                      {chainReports?.chains?.length || 0} chains analyzed
                    </div>
                  </div>

                  {chainReports?.chains && chainReports.chains.length > 0 ? (
                    <div className="grid gap-4">
                      {(() => {
                        console.log('All chains data:', chainReports.chains);
                        console.log('Chains with IDs:', chainReports.chains.filter((chain: any) => chain && chain.id));
                        
                        const validChains = chainReports.chains
                          .filter((chain: any) => {
                            const hasId = chain && (chain.id || chain.chainreport?.id);
                            if (!hasId) {
                              console.log('Chain filtered out (no ID):', chain);
                            }
                            return hasId;
                          });
                        
                        console.log('Valid chains count:', validChains.length);
                        
                        return validChains
                          .sort((a: any, b: any) => {
                            // Safe sorting with fallback values - check both structures
                            const aRespect = a.details?.respect ?? a.respect ?? a.chainreport?.details?.respect ?? a.chainreport?.respect ?? 0;
                            const bRespect = b.details?.respect ?? b.respect ?? b.chainreport?.details?.respect ?? b.chainreport?.respect ?? 0;
                            return bRespect - aRespect;
                          })
                          .map((chain: any) => {
                          // Extract data safely with fallbacks - prioritize chainreport structure
                          const chainData = chain.chainreport || chain;
                          const chainId = chainData.id || chain.id;
                          const chainHits = chainData.details?.chain ?? chainData.chain ?? 0;
                          const respect = chainData.details?.respect ?? chainData.respect ?? 0;
                          const warHits = chainData.details?.war ?? chainData.war ?? 0;
                          const attackerCount = chainData.attackers?.length ?? 0;
                          const startTime = chainData.start;
                          const endTime = chainData.end;
                          
                          return (
                            <div key={chainId} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                              <div className="flex items-center justify-between mb-4">
                                <div>
                                  <h4 className="text-lg font-semibold">Chain #{chainId}</h4>
                                  <div className="text-sm text-gray-600">
                                    {startTime ? formatDateTime(startTime) : 'Unknown'} - {endTime ? formatDateTime(endTime) : 'Unknown'}
                                  </div>
                                </div>
                                <button
                                  onClick={() => setSelectedChainId(chainId)}
                                  className="px-3 py-1 bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200 transition-colors text-sm"
                                >
                                  View Details
                                </button>
                              </div>

                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div className="text-center">
                                  <div className="text-2xl font-bold text-blue-600">{chainHits}</div>
                                  <div className="text-xs text-gray-600">Chain Hits</div>
                                </div>
                                <div className="text-center">
                                  <div className="text-2xl font-bold text-green-600">{respect.toFixed(1)}</div>
                                  <div className="text-xs text-gray-600">Total Respect</div>
                                </div>
                                <div className="text-center">
                                  <div className="text-2xl font-bold text-purple-600">{warHits}</div>
                                  <div className="text-xs text-gray-600">War Hits</div>
                                </div>
                                <div className="text-center">
                                  <div className="text-2xl font-bold text-orange-600">{attackerCount}</div>
                                  <div className="text-xs text-gray-600">Attackers</div>
                                </div>
                              </div>
                            </div>
                          );
                        });
                      })()}
                    </div>
                  ) : (
                    <div className="text-center py-12 text-gray-500">
                      <div className="mb-2">No chain data available for this war.</div>
                      <div className="text-sm text-gray-400">
                        {chainReports ? `Found ${chainReports.totalChains || 0} chains but no valid chain reports` : 'Chain data not loaded'}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Attacks Tab */}
              {activeTab === 'attacks' && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-xl font-bold text-gray-900">⚔️ Attack Analysis</h3>
                    <div className="text-sm text-gray-600">
                      Non-chain attacks only
                    </div>
                  </div>

                  {warAttacks ? (
                    <>
                      {/* Attack Summary */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold text-red-600">{warAttacks.insideHits}</div>
                          <div className="text-sm text-gray-600">Inside Hits</div>
                        </div>
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold text-blue-600">{warAttacks.outsideHits}</div>
                          <div className="text-sm text-gray-600">Outside Hits</div>
                        </div>
                        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold text-green-600">{warAttacks.assists}</div>
                          <div className="text-sm text-gray-600">Assists</div>
                        </div>
                        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold text-purple-600">{warAttacks.totalRespect.toLocaleString()}</div>
                          <div className="text-sm text-gray-600">Total Respect</div>
                        </div>
                      </div>

                      {/* Member Performance Table */}
                      {warAttacks.memberStats && warAttacks.memberStats.length > 0 && (
                        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                          <div className="px-6 py-4 border-b border-gray-200">
                            <h4 className="text-lg font-semibold text-gray-900">Member Performance</h4>
                          </div>
                          <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Player</th>
                                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Total</th>
                                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Inside</th>
                                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Outside</th>
                                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Assists</th>
                                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Respect</th>
                                </tr>
                              </thead>
                              <tbody className="bg-white divide-y divide-gray-200">
                                {warAttacks.memberStats
                                  .sort((a: any, b: any) => b.totalRespect - a.totalRespect)
                                  .map((member: any) => (
                                    <tr key={member.attackerId} className="hover:bg-gray-50">
                                      <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="font-medium text-gray-900">
                                          {member.attackerName || `Player #${member.attackerId}`}
                                        </div>
                                      </td>
                                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {member.totalAttacks}
                                      </td>
                                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {member.insideHits}
                                      </td>
                                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {member.outsideHits}
                                      </td>
                                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {member.assists}
                                      </td>
                                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {member.totalRespect.toLocaleString()}
                                      </td>
                                    </tr>
                                  ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="text-center py-12 text-gray-500">
                      <div className="mb-2">No attack data available for this war.</div>
                      <div className="text-sm text-gray-400">Attack analytics could not be loaded</div>
                    </div>
                  )}
                </div>
              )}

              {/* Players Tab */}
              {activeTab === 'players' && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-xl font-bold text-gray-900">👥 Player Performance</h3>
                    <button
                      onClick={() => setSelectedPlayerId(0)} // 0 means show all players
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                      📊 Detailed Analysis
                    </button>
                  </div>

                  {playerPerformance?.playerDetails && playerPerformance.playerDetails.length > 0 ? (
                    <div className="grid gap-4">
                      {playerPerformance.playerDetails
                        .sort((a: any, b: any) => b.overallStats.totalRespect - a.overallStats.totalRespect)
                        .slice(0, 20) // Show top 20 players
                        .map((player: any) => (
                          <div key={player.playerId} className="bg-white border border-gray-200 rounded-lg p-6">
                            <div className="flex items-center justify-between mb-4">
                              <div>
                                <h4 className="text-lg font-semibold">
                                  {player.playerName || `Player #${player.playerId}`}
                                </h4>
                                <div className="text-sm text-gray-600">
                                  Rank #{player.comparativeRanking.respectRank} • {player.overallStats.participationRate.toFixed(1)}% participation
                                </div>
                              </div>
                              <button
                                onClick={() => setSelectedPlayerId(player.playerId)}
                                className="px-3 py-1 bg-purple-100 text-purple-800 rounded-md hover:bg-purple-200 transition-colors text-sm"
                              >
                                View Details
                              </button>
                            </div>

                            <div className="grid grid-cols-3 md:grid-cols-6 gap-4 text-center">
                              <div>
                                <div className="text-xl font-bold text-blue-600">{player.overallStats.totalAttacks}</div>
                                <div className="text-xs text-gray-600">Attacks</div>
                              </div>
                              <div>
                                <div className="text-xl font-bold text-green-600">{player.overallStats.totalRespect.toFixed(0)}</div>
                                <div className="text-xs text-gray-600">Respect</div>
                              </div>
                              <div>
                                <div className="text-xl font-bold text-purple-600">{player.overallStats.averageRespect.toFixed(1)}</div>
                                <div className="text-xs text-gray-600">Avg</div>
                              </div>
                              <div>
                                <div className="text-xl font-bold text-red-600">{player.overallStats.insideHits}</div>
                                <div className="text-xs text-gray-600">Inside</div>
                              </div>
                              <div>
                                <div className="text-xl font-bold text-orange-600">{player.overallStats.chainAttacks}</div>
                                <div className="text-xs text-gray-600">Chain</div>
                              </div>
                              <div>
                                <div className="text-xl font-bold text-gray-600">{player.overallStats.assists}</div>
                                <div className="text-xs text-gray-600">Assists</div>
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  ) : (
                    <div className="text-center py-12 text-gray-500">
                      No player performance data available.
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Sub-dialogs */}
      {showTimelineDialog && (
        <AttackTimelineDialog
          isOpen={showTimelineDialog}
          onClose={() => setShowTimelineDialog(false)}
          warId={warId}
        />
      )}

      {selectedChainId && (
        <ChainDetailsDialog
          isOpen={!!selectedChainId}
          onClose={() => setSelectedChainId(null)}
          warId={warId}
          chainId={selectedChainId}
        />
      )}

      {selectedPlayerId !== null && (
        <PlayerPerformanceDialog
          isOpen={selectedPlayerId !== null}
          onClose={() => setSelectedPlayerId(null)}
          warId={warId}
          playerId={selectedPlayerId === 0 ? undefined : selectedPlayerId}
        />
      )}
    </div>
  );
}