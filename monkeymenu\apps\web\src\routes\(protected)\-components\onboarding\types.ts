export type OnboardingStep =
	| "welcome"
	| "theme-setup"
	| "api-setup"
	| "feature-tour"
	| "discord-setup"
	| "completion";

export interface OnboardingStepInfo {
	id: OnboardingStep;
	title: string;
	description: string;
}

export interface PlatformFeature {
	icon: React.ComponentType<{ className?: string }>;
	title: string;
	description: string;
	category: string;
	adminOnly?: boolean;
}

export interface OnboardingProps {
	currentStep: OnboardingStep;
	onNextStep: () => void;
	onPrevStep: () => void;
	progress: number;
	currentStepIndex: number;
	totalSteps: number;
}

export interface StepComponentProps {
	onNextStep: () => void;
	onPrevStep: () => void;
}
