---
description: This rule is helpful for tasks involving monorepo management, build orchestration, package management, deployment workflows, environment configuration, code quality setup, dependency management across workspaces, Turbo configuration issues, pnpm workspace commands, database operations, and Cloudflare deployment processes.
globs: 
alwaysApply: false
---
# Monorepo Workflow Guide

This project uses a modern monorepo setup with pnpm workspaces and Turbo for efficient development and builds.

## Package Management

The project uses **pnpm** as the package manager with workspaces configured in [pnpm-workspace.yaml](mdc:pnpm-workspace.yaml).

### Workspace Structure
- `apps/*` - Applications (server, web)
- `packages/*` - Shared packages (shared utilities)

### Key Commands

```bash
# Install dependencies for all workspaces
pnpm install

# Run commands in specific workspaces
pnpm -F server <command>    # Run in server app
pnpm -F web <command>       # Run in web app  
pnpm -F @monkeymenu/shared <command>  # Run in shared package
```

## Turbo Configuration

Build orchestration is handled by <PERSON>, configured in [turbo.json](mdc:turbo.json).

### Global Commands

```bash
# Development
pnpm dev           # Start all services
pnpm dev:server    # Start only server
pnpm dev:web       # Start only web
pnpm dev:shared    # Start shared package in watch mode

# Building
pnpm build         # Build all packages
pnpm build:shared  # Build only shared package

# Code Quality
pnpm check         # Run Biome checks on all packages
pnpm check-types   # Type check all packages
```

## Database Operations

Database operations are centralized through the server app:

```bash
# Local Development
pnpm db:generate   # Generate migration files
pnpm db:migrate    # Apply migrations locally
pnpm db:studio     # Open Drizzle Studio locally

# Production
pnpm db:migrate:prod   # Apply migrations to production
pnpm db:studio:prod    # Open Drizzle Studio for production
```

## Deployment

```bash
# Build and deploy to Cloudflare
pnpm cf:deploy
```

This command:
1. Builds shared package and web app
2. Generates Cloudflare types for server
3. Deploys server to Cloudflare Workers

## Environment Management

Environment variables are managed through **Doppler**:
- Local: `.dev.vars` (server) and `.env.development` (web)
- Production: Automatically synced during deployment

## Code Quality

### Biome Configuration
- Configured in [biome.json](mdc:biome.json)
- Handles linting, formatting, and import sorting
- Runs on pre-commit hooks via [.husky](mdc:.husky)

### TypeScript
- Base configuration in [tsconfig.base.json](mdc:tsconfig.base.json)
- Each package extends the base config
- Strict type checking enabled

### Pre-commit Hooks
- Configured with Husky and lint-staged
- Automatically runs Biome checks before commits
- Configured in [package.json](mdc:package.json) lint-staged section

## Development Workflow

1. **Start Development**: `pnpm dev`
2. **Make Changes**: Edit code in respective apps/packages
3. **Run Checks**: `pnpm check` for linting/formatting
4. **Type Check**: `pnpm check-types`
5. **Commit**: Pre-commit hooks run automatically
6. **Deploy**: `pnpm cf:deploy` for production

## Dependencies

### Shared Dependencies
- Located in [packages/shared](mdc:packages/shared)
- Referenced as `@monkeymenu/shared` in apps
- Built before dependent packages

### Adding Dependencies
```bash
# Add to specific workspace
pnpm -F server add <package>
pnpm -F web add <package>
pnpm -F @monkeymenu/shared add <package>

# Add to root (dev dependencies)
pnpm add -D <package>
```
