import type { UserPermissionContext } from "@monkeymenu/shared";
import { and, eq } from "drizzle-orm";
import type { DBInstance } from "../db";
import { account as accountTable } from "../db/schema/auth";
import { user as userTable } from "../db/schema/auth";
import { getUserPermissionContext } from "./permissions";

export interface DiscordPermissionCheckResult {
	hasPermission: boolean;
	isLinked: boolean;
	userId?: string;
	permissionContext?: UserPermissionContext;
	error?: string;
}

/**
 * Check if a Discord user has a specific permission
 * @param db Database instance
 * @param discordUserId Discord user ID
 * @param permissionName Permission to check (e.g., PERMISSIONS.BANKING_REQUEST.name)
 * @returns Permission check result
 */
export async function checkDiscordUserPermission(
	db: DBInstance,
	discordUserId: string,
	permissionName: string,
): Promise<DiscordPermissionCheckResult> {
	try {
		// Find user by Discord ID
		const userAccount = await db
			.select({
				userId: userTable.id,
			})
			.from(accountTable)
			.innerJoin(userTable, eq(accountTable.userId, userTable.id))
			.where(
				and(
					eq(accountTable.providerId, "discord"),
					eq(accountTable.accountId, discordUserId),
				),
			)
			.get();

		if (!userAccount) {
			return {
				hasPermission: false,
				isLinked: false,
				error: "Discord account not linked to MonkeyMenu",
			};
		}

		// Get user's permission context
		const permissionContext = await getUserPermissionContext(
			db,
			userAccount.userId,
		);

		// Check if user has the specific permission
		const hasPermission =
			permissionContext?.permissions.includes(permissionName) ?? false;

		return {
			hasPermission,
			isLinked: true,
			userId: userAccount.userId,
			permissionContext,
		};
	} catch (error) {
		console.error("Error checking Discord user permission:", error);
		return {
			hasPermission: false,
			isLinked: false,
			error: error instanceof Error ? error.message : "Permission check failed",
		};
	}
}

/**
 * Create a permission error embed for Discord responses
 */
export function createPermissionErrorEmbed(
	discordUserTag: string,
	permissionName: string,
	requiredRole?: string,
) {
	return {
		color: 0xff0000, // Red
		title: "🚫 Insufficient Permissions",
		description: `You don't have permission to use this command.`,
		fields: [
			{
				name: "👤 User",
				value: discordUserTag,
				inline: true,
			},
			{
				name: "🔒 Required Permission",
				value: `\`${permissionName}\``,
				inline: true,
			},
			{
				name: "📋 Required Role",
				value: requiredRole || "Member or higher",
				inline: true,
			},
			{
				name: "💡 How to get access",
				value:
					"Contact a faction leader or administrator to review your role permissions.",
				inline: false,
			},
		],
		footer: {
			text: "MonkeyMenu Banking System",
		},
		timestamp: new Date().toISOString(),
	};
}

/**
 * Create an account not linked error embed
 */
export function createAccountNotLinkedEmbed(discordUserTag: string) {
	return {
		color: 0xff0000, // Red
		title: "❌ Account Not Linked",
		description: "Your Discord account is not linked to MonkeyMenu.",
		fields: [
			{
				name: "👤 User",
				value: discordUserTag,
				inline: true,
			},
			{
				name: "❌ Status",
				value: "Account Not Found",
				inline: true,
			},
			{
				name: "📋 Next Steps",
				value:
					"1. Visit [MonkeyMenu](https://monkeymenu.app) and sign in\n" +
					"2. Go to your profile/settings page\n" +
					"3. Click 'Connect Discord' to link your account\n" +
					"4. Try the command again",
				inline: false,
			},
		],
		footer: {
			text: "MonkeyMenu Banking System",
		},
		timestamp: new Date().toISOString(),
	};
}
