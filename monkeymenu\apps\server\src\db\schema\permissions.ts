import { sql } from "drizzle-orm";
import {
	integer,
	sqliteTable,
	text,
	unique,
	uniqueIndex,
} from "drizzle-orm/sqlite-core";
import { user } from "./auth";

// Faction roles table
export const factionRole = sqliteTable("faction_role", {
	id: integer("id").primaryKey({ autoIncrement: true }),
	name: text("name").notNull().unique(),
	displayName: text("display_name").notNull(),
	hierarchyLevel: integer("hierarchy_level").notNull(), // Higher number = higher rank
	description: text("description"),
	createdAt: integer("created_at", { mode: "timestamp" })
		.notNull()
		.$defaultFn(() => new Date()),
	updatedAt: integer("updated_at", { mode: "timestamp" })
		.notNull()
		.$defaultFn(() => new Date())
		.$onUpdateFn(() => new Date()),
});

// Permissions table
export const permission = sqliteTable("permission", {
	id: integer("id").primary<PERSON>ey({ autoIncrement: true }),
	name: text("name").notNull().unique(),
	displayName: text("display_name").notNull(),
	description: text("description"),
	category: text("category").notNull(), // e.g., "guides", "administration", "moderation"
	createdAt: integer("created_at", { mode: "timestamp" })
		.notNull()
		.$defaultFn(() => new Date()),
});

// Role permissions junction table
export const rolePermission = sqliteTable(
	"role_permission",
	{
		id: integer("id").primaryKey({ autoIncrement: true }),
		roleId: integer("role_id")
			.notNull()
			.references(() => factionRole.id, { onDelete: "cascade" }),
		permissionId: integer("permission_id")
			.notNull()
			.references(() => permission.id, { onDelete: "cascade" }),
		createdAt: integer("created_at", { mode: "timestamp" })
			.notNull()
			.$defaultFn(() => new Date()),
	},
	(table) => [unique().on(table.roleId, table.permissionId)],
);

// User roles table - tracks which role each user has
export const userRole = sqliteTable(
	"user_role",
	{
		id: integer("id").primaryKey({ autoIncrement: true }),
		userId: text("user_id")
			.notNull()
			.references(() => user.id, { onDelete: "cascade" }),
		roleId: integer("role_id")
			.notNull()
			.references(() => factionRole.id, { onDelete: "cascade" }),
		assignedBy: text("assigned_by").references(() => user.id),
		assignedAt: integer("assigned_at", { mode: "timestamp" })
			.notNull()
			.$defaultFn(() => new Date()),
		isActive: integer("is_active", { mode: "boolean" }).notNull().default(true),
	},
	(table) => [
		// Partial unique index: only one active role per user
		// This prevents concurrent transactions from creating multiple active roles
		uniqueIndex("user_role_active_user_unique")
			.on(table.userId)
			.where(sql`${table.isActive} = 1`),
	],
);

export type FactionRole = typeof factionRole.$inferSelect;
export type NewFactionRole = typeof factionRole.$inferInsert;
export type Permission = typeof permission.$inferSelect;
export type NewPermission = typeof permission.$inferInsert;
export type RolePermission = typeof rolePermission.$inferSelect;
export type NewRolePermission = typeof rolePermission.$inferInsert;
export type UserRole = typeof userRole.$inferSelect;
export type NewUserRole = typeof userRole.$inferInsert;
