import { trpcServer } from "@hono/trpc-server";
import { and, isNotNull } from "drizzle-orm";
import { eq, gt } from "drizzle-orm";
import { Hono } from "hono";
import { logger } from "hono/logger";
import { prettyJSON } from "hono/pretty-json";
import { runOverdoseDetection } from "./cron/overdoseDetection";
import { initDb } from "./db";
import { user as userTable } from "./db/schema/auth";
import { account as accountTable } from "./db/schema/auth";
import { tornUser } from "./db/schema/torn";
import { createDiscordBot } from "./discord";
import { initCrypto } from "./lib/crypto";
import { getUserPermissionContext } from "./lib/permissions";
import { verifyAndUpdateUserTornInfo } from "./lib/tornUserUtils";
import { runTransactionVerification } from "./lib/transactionVerification";
import type { AppBindings } from "./lib/types";
import { runWithdrawalExpiration } from "./lib/withdrawalExpiration";
import authMiddleware from "./middlewares/authMiddleware";
import corsMiddleware from "./middlewares/corsMiddleware";
import sessionMiddleware from "./middlewares/sessionMiddleware";
import { appRouter } from "./routers";

const app = new Hono<AppBindings>({ strict: false });

// Shared promise to ensure concurrency-safe initialization
let permissionsInitPromise: Promise<void> | null = null;
let isInitialized = false;

const initializePermissions = async (
	env: AppBindings["Bindings"],
): Promise<void> => {
	// If already initialized, return immediately
	if (isInitialized) return;

	// If currently initializing, wait for the existing promise
	if (permissionsInitPromise) return permissionsInitPromise;

	permissionsInitPromise = (async () => {
		try {
			// Initialize crypto for startup tasks
			initCrypto(env.TORN_API_KEY_ENCRYPTION_SECRET);

			// Initialize database (connection needed for later tasks)
			initDb(env);

			console.log("[server] Permission system initialized successfully");
			isInitialized = true;
		} catch (error) {
			console.error("[server] Failed to initialize permission system:", error);
			// Clear the promise so future calls can retry if needed
			permissionsInitPromise = null;
			throw error;
		}
	})();
	return permissionsInitPromise;
};

app.use("*", logger());
app.use("*", prettyJSON());
app.use("*", authMiddleware);
app.use("*", corsMiddleware);
app.use("*", sessionMiddleware);

app.on(["POST", "GET"], "/api/auth/**", async (c) => {
	// Initialize permissions on first request (lazy initialization, concurrency-safe)
	await initializePermissions(c.env);

	const authInstance = c.get("auth");
	if (!authInstance) {
		throw new Error("Auth instance not found");
	}
	const response = await authInstance.handler(c.req.raw);
	return response;
});

console.log(
	"[server] Initializing appRouter with keys:",
	Object.keys(appRouter._def.record),
);

app.use("/trpc/*", async (c, next) => {
	// Initialize permissions on first tRPC request (lazy initialization, concurrency-safe)
	await initializePermissions(c.env);

	const url = c.req.url;
	const method = c.req.method;
	console.log(`[server] Incoming request: ${method} ${url}`);
	return await next();
});

app.use(
	"/trpc/*",
	trpcServer({
		router: appRouter,
		createContext: (_opts, c) => ({
			session: c.get("session"),
			db: c.get("db"),
			env: c.env,
		}),
	}),
);

// Discord bot API endpoints for approve/reject buttons
app.get("/api/bot/approve-withdrawal", async (c) => {
	const { requestId, requesterTornId, amount } = c.req.query();

	if (!requestId || !amount) {
		return c.text("Missing required parameters", 400);
	}

	// Redirect to banking page with action and parameters
	const redirectUrl = `${c.env.FRONTEND_URL}/banking?action=approve&requestId=${requestId}&amount=${amount}&requesterTornId=${requesterTornId || ""}`;

	return c.redirect(redirectUrl);
});

app.get("/api/bot/reject-withdrawal", async (c) => {
	const { requestId } = c.req.query();

	if (!requestId) {
		return c.text("Missing required parameters", 400);
	}

	// Redirect to banking page with action and parameters
	const redirectUrl = `${c.env.FRONTEND_URL}/banking?action=reject&requestId=${requestId}`;

	return c.redirect(redirectUrl);
});

// Discord bot verification endpoint
app.get("/api/bot/verify-account", async (c) => {
	// Redirect to profile page with verification trigger
	const redirectUrl = `${c.env.FRONTEND_URL}/profile?verify=true`;

	return c.redirect(redirectUrl);
});

app.get("*", (c) => c.env.ASSETS.fetch(c.req.raw)); // Catch-All API route for static assets

// Export the Durable Object classes
export { LiveStoreWebSocketServer } from "./lib/livestore-sync";

export type AppType = typeof app;

// Create a combined handler that includes both the main app and Discord bot
export default {
	async fetch(
		request: Request,
		env: AppBindings["Bindings"],
		ctx: ExecutionContext,
	): Promise<Response> {
		const url = new URL(request.url);

		// Initialize permissions (no seeding) for all requests
		await initializePermissions(env);

		// Handle LiveStore sync requests using Durable Object
		if (url.pathname.startsWith("/api/livestore")) {
			try {
				// Get the Durable Object instance
				const durableObjectId =
					env.LIVESTORE_WEBSOCKET_SERVER.idFromName("livestore-sync");
				const durableObject =
					env.LIVESTORE_WEBSOCKET_SERVER.get(durableObjectId);

				// Forward the request to the Durable Object
				return await durableObject.fetch(request);
			} catch (error) {
				console.error("[LiveStore] Durable Object fetch failed:", error);

				// Return appropriate error response based on error type
				if (error instanceof Error) {
					// Check for specific error types that might indicate different issues
					if (
						error.message.includes("timeout") ||
						error.message.includes("Timeout")
					) {
						return new Response(
							"LiveStore service temporarily unavailable - timeout",
							{
								status: 504,
								headers: { "Content-Type": "text/plain" },
							},
						);
					}

					if (
						error.message.includes("network") ||
						error.message.includes("Network")
					) {
						return new Response(
							"LiveStore service temporarily unavailable - network error",
							{
								status: 503,
								headers: { "Content-Type": "text/plain" },
							},
						);
					}
				}

				// Generic server error for unknown issues
				return new Response("LiveStore service temporarily unavailable", {
					status: 500,
					headers: { "Content-Type": "text/plain" },
				});
			}
		}

		// Handle Discord interactions using CloudflareWorkerServer
		if (url.pathname === "/discord/interactions" && request.method === "POST") {
			const { server } = createDiscordBot(env);
			return server.fetch(request, env, ctx);
		}

		// Handle all other requests with the main Hono app
		return app.fetch(request, env, ctx);
	},

	async scheduled(
		event: ScheduledEvent,
		env: AppBindings["Bindings"],
	): Promise<void> {
		const scheduledTime = new Date(event.scheduledTime);
		const currentMinute = scheduledTime.getUTCMinutes();

		console.log(
			`[Scheduler] Starting scheduled tasks at ${scheduledTime.toISOString()}`,
		);

		// Initialize permissions system for scheduled tasks (includes crypto initialization)
		try {
			await initializePermissions(env);
		} catch (error) {
			console.error(
				"[Scheduler] Failed to initialize permissions system in scheduled task:",
				error,
			);
			return;
		}

		const db = initDb(env);
		const overallStartTime = Date.now();

		// Task 1: User verification and role updates (every hour)
		// Run at the top of every hour (when minute is 0)
		if (currentMinute === 0) {
			await runUserVerification(db, env);

			// Task 1.5: Discord role audit - clean up unverified users with faction roles
			if (env.DISCORD_BOT_TOKEN && env.DISCORD_GUILD_ID) {
				await runDiscordRoleAudit(db, env);
			}
		}

		// Task 2: Transaction verification (every 10 minutes)
		await runTransactionVerification(db, env);

		// Task 3: Withdrawal expiration (every hour)
		if (currentMinute === 0) {
			await runWithdrawalExpiration(db, env);
		}

		// Task 4: Overdose detection (every hour)
		if (currentMinute === 0) {
			await runOverdoseDetection(db, env);
		}

		const totalTime = (Date.now() - overallStartTime) / 1000;
		console.log(
			`[Scheduler] All scheduled tasks completed in ${totalTime.toFixed(2)}s`,
		);
	},
};

// Cloudflare Worker scheduled event is now part of the default export above

// Discord role mappings (same as in verify command)
const DISCORD_ROLE_MAPPING = {
	"system-admin": "1376990512560476202",
	leader: "1325385710945046679",
	"co-leader": "1330265362033541192",
	"monkey-mentor": "1369953001719988234",
	gorilla: "1325381427595841556",
	"primate-liaison": "1369953069017600052",
	baboon: "1325381512421445642",
	orangutan: "1325381581749354516",
	chimpanzee: "1325381637969674261",
	recruit: "1376990392725147688",
} as const;

const BASIC_FACTION_ROLE_ID = "1324623178806591599";

/**
 * Audit Discord roles - remove faction roles from users who aren't verified on the website
 * This catches users who have Discord roles but:
 * - Haven't signed up on the website
 * - Haven't completed API verification
 * - Left the faction but still have roles
 */
async function runDiscordRoleAudit(
	db: ReturnType<typeof initDb>,
	env: AppBindings["Bindings"],
): Promise<void> {
	if (!env.DISCORD_BOT_TOKEN || !env.DISCORD_GUILD_ID) {
		console.log("[DiscordRoleAudit] Skipping - missing Discord configuration");
		return;
	}

	console.log("[DiscordRoleAudit] Starting Discord role audit...");
	const startTime = Date.now();

	try {
		// Get all faction role IDs that we manage
		const allFactionRoleIds = [
			...Object.values(DISCORD_ROLE_MAPPING),
			BASIC_FACTION_ROLE_ID,
		] as string[];

		let totalAudited = 0;
		let totalRolesRemoved = 0;
		let totalErrors = 0;

		// Get all Discord server members
		let after = "0";
		const BATCH_SIZE = 1000;
		let batchCount = 0;

		while (true) {
			batchCount++;
			console.log(
				`[DiscordRoleAudit] Fetching batch ${batchCount} (after: ${after})`,
			);

			const membersResponse = await fetch(
				`https://discord.com/api/v10/guilds/${env.DISCORD_GUILD_ID}/members?limit=${BATCH_SIZE}&after=${after}`,
				{
					headers: {
						Authorization: `Bot ${env.DISCORD_BOT_TOKEN}`,
						"Content-Type": "application/json",
					},
				},
			);

			if (!membersResponse.ok) {
				const errorText = await membersResponse.text();
				console.error(
					`[DiscordRoleAudit] Failed to fetch Discord members: ${membersResponse.status} - ${errorText}`,
				);
				break;
			}

			const members = (await membersResponse.json()) as Array<{
				user: { id: string; username: string };
				roles: string[];
			}>;

			console.log(
				`[DiscordRoleAudit] Fetched ${members.length} members in batch ${batchCount}`,
			);

			if (members.length === 0) {
				console.log("[DiscordRoleAudit] No more members to process");
				break;
			}

			// Update the after parameter for pagination
			after = members[members.length - 1].user.id;

			// Process each member
			let membersWithRoles = 0;
			for (const member of members) {
				const memberFactionRoles = member.roles.filter((roleId) =>
					allFactionRoleIds.includes(roleId),
				);

				// Skip members without any faction roles
				if (memberFactionRoles.length === 0) continue;

				membersWithRoles++;
				totalAudited++;

				try {
					// Check if this Discord user is linked to a verified website account
					const linkedAccount = await db
						.select({
							userId: accountTable.userId,
							tornApiKeyVerified: tornUser.tornApiKeyVerified,
							accessSuspended: tornUser.accessSuspended,
							tornFactionId: tornUser.tornFactionId,
						})
						.from(accountTable)
						.leftJoin(tornUser, eq(accountTable.userId, tornUser.id))
						.where(
							and(
								eq(accountTable.providerId, "discord"),
								eq(accountTable.accountId, member.user.id),
							),
						)
						.get();

					const shouldHaveRoles = Boolean(
						linkedAccount?.tornApiKeyVerified &&
							!linkedAccount?.accessSuspended &&
							linkedAccount?.tornFactionId === "53100", // Required faction ID
					);

					// Remove roles if user shouldn't have them
					if (!shouldHaveRoles) {
						for (const roleId of memberFactionRoles) {
							try {
								const removeResponse = await fetch(
									`https://discord.com/api/v10/guilds/${env.DISCORD_GUILD_ID}/members/${member.user.id}/roles/${roleId}`,
									{
										method: "DELETE",
										headers: {
											Authorization: `Bot ${env.DISCORD_BOT_TOKEN}`,
											"Content-Type": "application/json",
										},
									},
								);

								if (removeResponse.ok) {
									totalRolesRemoved++;
								} else {
									console.warn(
										`[DiscordRoleAudit] Failed to remove role ${roleId} from ${member.user.username}: ${removeResponse.status}`,
									);
								}

								// Rate limit protection
								await new Promise((resolve) => setTimeout(resolve, 100));
							} catch (error) {
								console.error(
									`[DiscordRoleAudit] Error removing role from ${member.user.username}:`,
									error,
								);
								totalErrors++;
							}
						}

						if (memberFactionRoles.length > 0) {
							console.log(
								`[DiscordRoleAudit] Removed ${memberFactionRoles.length} role(s) from unverified user: ${member.user.username}`,
							);
						}
					}
				} catch (error) {
					console.error(
						`[DiscordRoleAudit] Error processing member ${member.user.username}:`,
						error,
					);
					totalErrors++;
				}
			}

			// If we got less than the batch size, we've reached the end
			if (members.length < BATCH_SIZE) break;

			// Rate limit protection between batches
			await new Promise((resolve) => setTimeout(resolve, 1000));
		}

		const processingTime = (Date.now() - startTime) / 1000;
		console.log(`[DiscordRoleAudit] Discord role audit complete:
- Time taken: ${processingTime.toFixed(2)}s
- Users audited: ${totalAudited}
- Roles removed: ${totalRolesRemoved}
- Errors: ${totalErrors}`);
	} catch (error) {
		console.error("[DiscordRoleAudit] Discord role audit failed:", error);
	}
}

/**
 * Sync Discord roles for a user based on their MonkeyMenu role
 */
export async function syncUserDiscordRoles(
	db: ReturnType<typeof initDb>,
	userId: string,
	guildId: string,
	botToken: string,
	isInFaction: boolean,
): Promise<{ success: boolean; message: string }> {
	try {
		// Get user's Discord account
		const discordAccount = await db
			.select({
				accountId: accountTable.accountId,
			})
			.from(accountTable)
			.where(
				and(
					eq(accountTable.userId, userId),
					eq(accountTable.providerId, "discord"),
				),
			)
			.get();

		if (!discordAccount) {
			return { success: false, message: "No Discord account linked" };
		}

		// Get user's current role
		const permissionContext = await getUserPermissionContext(db, userId);

		// Get current Discord roles
		const memberResponse = await fetch(
			`https://discord.com/api/v10/guilds/${guildId}/members/${discordAccount.accountId}`,
			{
				headers: {
					Authorization: `Bot ${botToken}`,
					"Content-Type": "application/json",
				},
			},
		);

		if (!memberResponse.ok) {
			if (memberResponse.status === 404) {
				return { success: false, message: "User not in Discord server" };
			}
			return {
				success: false,
				message: `Discord API error: ${memberResponse.status}`,
			};
		}

		const memberData = (await memberResponse.json()) as { roles: string[] };
		const currentRoles = memberData.roles;

		// Get all faction role IDs for comparison
		const allFactionRoleIds = Object.values(DISCORD_ROLE_MAPPING) as string[];
		const currentFactionRoles = currentRoles.filter((roleId) =>
			allFactionRoleIds.includes(roleId),
		);
		const hasBasicFactionRole = currentRoles.includes(BASIC_FACTION_ROLE_ID);

		// Determine what the user should have
		const shouldHaveSpecificRole =
			isInFaction && permissionContext.roleName
				? DISCORD_ROLE_MAPPING[
						permissionContext.roleName as keyof typeof DISCORD_ROLE_MAPPING
					]
				: null;

		const actions: string[] = [];

		// Handle users NOT in faction
		if (!isInFaction) {
			// Remove all faction roles if they have any
			for (const roleId of [...currentFactionRoles, BASIC_FACTION_ROLE_ID]) {
				if (currentRoles.includes(roleId)) {
					await fetch(
						`https://discord.com/api/v10/guilds/${guildId}/members/${discordAccount.accountId}/roles/${roleId}`,
						{
							method: "DELETE",
							headers: {
								Authorization: `Bot ${botToken}`,
								"Content-Type": "application/json",
							},
						},
					);
					actions.push("Removed faction role");
				}
			}

			return {
				success: true,
				message:
					actions.length > 0
						? actions.join(", ")
						: "No faction roles to remove",
			};
		}

		// Handle users IN faction
		// Remove incorrect specific faction roles (but not basic faction role)
		for (const roleId of currentFactionRoles) {
			if (roleId !== shouldHaveSpecificRole) {
				await fetch(
					`https://discord.com/api/v10/guilds/${guildId}/members/${discordAccount.accountId}/roles/${roleId}`,
					{
						method: "DELETE",
						headers: {
							Authorization: `Bot ${botToken}`,
							"Content-Type": "application/json",
						},
					},
				);
				actions.push("Removed incorrect role");
			}
		}

		// Add basic faction role if missing
		if (!hasBasicFactionRole) {
			const basicRoleResponse = await fetch(
				`https://discord.com/api/v10/guilds/${guildId}/members/${discordAccount.accountId}/roles/${BASIC_FACTION_ROLE_ID}`,
				{
					method: "PUT",
					headers: {
						Authorization: `Bot ${botToken}`,
						"Content-Type": "application/json",
					},
				},
			);

			if (basicRoleResponse.ok) {
				actions.push("Added faction role");
			}
		}

		// Add specific role if needed and mapped
		if (
			shouldHaveSpecificRole &&
			!currentRoles.includes(shouldHaveSpecificRole)
		) {
			const response = await fetch(
				`https://discord.com/api/v10/guilds/${guildId}/members/${discordAccount.accountId}/roles/${shouldHaveSpecificRole}`,
				{
					method: "PUT",
					headers: {
						Authorization: `Bot ${botToken}`,
						"Content-Type": "application/json",
					},
				},
			);

			if (response.ok) {
				actions.push(`Added ${permissionContext.roleName} role`);
			}
		}

		return {
			success: true,
			message:
				actions.length > 0 ? actions.join(", ") : "Roles already correct",
		};
	} catch (error) {
		console.error("Discord role sync error:", error);
		return {
			success: false,
			message: error instanceof Error ? error.message : "Unknown error",
		};
	}
}

/**
 * Run user verification and role updates
 */
async function runUserVerification(
	db: ReturnType<typeof initDb>,
	env: AppBindings["Bindings"],
): Promise<void> {
	console.log(
		"[UserVerification] Starting user verification and role updates...",
	);

	const PAGE_SIZE = 25;
	let lastId: string | null = null;
	let totalProcessed = 0;
	let totalFailed = 0;
	let totalRetries = 0;
	let totalDiscordSynced = 0;
	let totalDiscordFailed = 0;
	const MAX_TIMEOUT_MS = 8 * 60 * 1000; // 8 minutes (leave 2 minutes for other tasks)
	const startTime = Date.now();
	let timedOut = false;

	while (true) {
		if (Date.now() - startTime > MAX_TIMEOUT_MS) {
			console.error("[UserVerification] User verification timed out.");
			timedOut = true;
			break;
		}

		// Use keyset pagination with proper filter conditions
		let users: {
			user: { id: string };
			torn_user: {
				id: string;
				tornApiKey: string | null;
				tornApiKeyVerified: boolean | null;
				accessSuspended: boolean | null;
			} | null;
		}[];

		if (lastId !== null) {
			users = await db
				.select({
					user: { id: userTable.id },
					torn_user: {
						id: tornUser.id,
						tornApiKey: tornUser.tornApiKey,
						tornApiKeyVerified: tornUser.tornApiKeyVerified,
						accessSuspended: tornUser.accessSuspended,
					},
				})
				.from(userTable)
				.leftJoin(tornUser, eq(userTable.id, tornUser.id))
				.where(and(isNotNull(tornUser.tornApiKey), gt(userTable.id, lastId)))
				.orderBy(userTable.id)
				.limit(PAGE_SIZE)
				.all();
		} else {
			users = await db
				.select({
					user: { id: userTable.id },
					torn_user: {
						id: tornUser.id,
						tornApiKey: tornUser.tornApiKey,
						tornApiKeyVerified: tornUser.tornApiKeyVerified,
						accessSuspended: tornUser.accessSuspended,
					},
				})
				.from(userTable)
				.leftJoin(tornUser, eq(userTable.id, tornUser.id))
				.where(and(isNotNull(tornUser.tornApiKey)))
				.orderBy(userTable.id)
				.limit(PAGE_SIZE)
				.all();
		}

		if (users.length === 0) break;

		// Update lastId for next iteration
		if (users.length > 0) {
			const lastRow = users[users.length - 1];
			lastId = lastRow.user.id;
		}

		for (const row of users) {
			const u = row.torn_user;
			if (!u || !u.tornApiKey) continue;

			let retries = 0;
			const MAX_RETRIES = 3;
			let verificationResult: { success: boolean; message: string } | null =
				null;

			// Step 1: Verify and update MonkeyMenu roles
			while (retries < MAX_RETRIES) {
				try {
					verificationResult = await verifyAndUpdateUserTornInfo(
						db,
						u.id,
						u.tornApiKey,
						env,
					);
					totalProcessed++;

					if (verificationResult.success) {
						console.log(
							`[UserVerification] Successfully verified user ${u.id} on attempt ${retries + 1}: ${verificationResult.message}`,
						);
					} else {
						console.log(
							`[UserVerification] Verification failed for user ${u.id} on attempt ${retries + 1}: ${verificationResult.message}`,
						);
					}
					break;
				} catch (err) {
					retries++;
					totalRetries++;
					const errorMsg =
						err instanceof Error ? err.message : JSON.stringify(err);
					console.warn(
						`[UserVerification] Retry ${retries}/${MAX_RETRIES} for user ${u.id}: ${errorMsg}`,
					);
					if (retries >= MAX_RETRIES) {
						totalFailed++;
						console.error(
							`[UserVerification] Failed to recheck user ${u.id} after ${MAX_RETRIES} attempts:`,
							err,
						);
					}
					const backoffMs = 250 * 2 ** retries;
					await new Promise((res) => setTimeout(res, backoffMs));
				}
			}

			// Step 2: Sync Discord roles (only if we have Discord bot token and guild ID)
			if (verificationResult && env.DISCORD_BOT_TOKEN && env.DISCORD_GUILD_ID) {
				try {
					// Check if user is in faction (verified API key, has Torn user ID, not suspended)
					const isInFaction = Boolean(
						u.tornApiKeyVerified &&
							verificationResult.success &&
							!u.accessSuspended,
					);

					const discordResult = await syncUserDiscordRoles(
						db,
						u.id,
						env.DISCORD_GUILD_ID,
						env.DISCORD_BOT_TOKEN,
						isInFaction,
					);

					if (discordResult.success) {
						totalDiscordSynced++;
						if (discordResult.message !== "Roles already correct") {
							console.log(
								`[UserVerification] Discord sync for user ${u.id}: ${discordResult.message}`,
							);
						}
					} else {
						totalDiscordFailed++;
						if (discordResult.message !== "No Discord account linked") {
							console.warn(
								`[UserVerification] Discord sync failed for user ${u.id}: ${discordResult.message}`,
							);
						}
					}
				} catch (err) {
					totalDiscordFailed++;
					console.error(
						`[UserVerification] Discord sync error for user ${u.id}:`,
						err,
					);
				}
			}
		}
	}

	const processingTime = (Date.now() - startTime) / 1000;
	const averageRetries =
		totalProcessed > 0 ? (totalRetries / totalProcessed).toFixed(2) : "0.00";
	const successRate =
		totalProcessed > 0
			? (((totalProcessed - totalFailed) / totalProcessed) * 100).toFixed(1)
			: "0.0";
	const discordSyncRate =
		totalDiscordSynced + totalDiscordFailed > 0
			? (
					(totalDiscordSynced / (totalDiscordSynced + totalDiscordFailed)) *
					100
				).toFixed(1)
			: "0.0";

	console.log(`[UserVerification] User verification complete:
- Time taken: ${processingTime.toFixed(2)}s
- Users processed: ${totalProcessed}
- Failed: ${totalFailed}
- Total retries: ${totalRetries}
- Average retries per user: ${averageRetries}
- Success rate: ${successRate}%
- Discord synced: ${totalDiscordSynced}
- Discord failed: ${totalDiscordFailed}
- Discord sync rate: ${discordSyncRate}%`);

	if (timedOut) {
		console.error("[UserVerification] User verification ended due to timeout.");
	}
}
