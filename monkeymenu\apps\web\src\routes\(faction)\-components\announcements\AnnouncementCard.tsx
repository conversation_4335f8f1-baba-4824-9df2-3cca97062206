import { HasPermission } from "@/components/permissions/PermissionGuards";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
	<PERSON>,
	CardContent,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { PERMISSION_NAMES } from "@monkeymenu/shared";
import {
	Calendar,
	Clock,
	Edit,
	Eye,
	Megaphone,
	Trash2,
	User,
} from "lucide-react";
import type { AnnouncementData } from "./types";
import { getCategoryInfo, getPreviewText } from "./utils";

interface AnnouncementCardProps {
	announcement: AnnouncementData;
	viewMode: "grid" | "list";
	onView: (announcement: AnnouncementData) => void;
	onEdit: (announcement: AnnouncementData) => void;
	onDelete: (announcement: AnnouncementData) => void;
}

export function AnnouncementCard({
	announcement,
	viewMode,
	onView,
	onEdit,
	onDelete,
}: AnnouncementCardProps) {
	const categoryInfo = getCategoryInfo(announcement.announcement.category);

	const handleDelete = () => {
		if (confirm("Are you sure you want to delete this announcement?")) {
			onDelete(announcement);
		}
	};

	return (
		<Card className="group transition-shadow hover:shadow-md">
			<CardHeader className="pb-3">
				<div className="flex items-start justify-between gap-2">
					<div className="min-w-0 flex-1">
						<div className="mb-2 flex items-center gap-2">
							<Badge variant="secondary" className="text-xs">
								{categoryInfo.emoji} {categoryInfo.label}
							</Badge>
						</div>
						<CardTitle className="line-clamp-2 text-lg leading-tight">
							{announcement.announcement.title}
						</CardTitle>
					</div>
					<div className="flex gap-1 opacity-0 transition-opacity group-hover:opacity-100">
						<Button
							variant="ghost"
							size="sm"
							onClick={() => onView(announcement)}
							className="h-8 w-8 p-0"
						>
							<Eye className="h-4 w-4" />
						</Button>
						<HasPermission permission={PERMISSION_NAMES.ANNOUNCEMENTS_MANAGE}>
							<Button
								variant="ghost"
								size="sm"
								onClick={() => onEdit(announcement)}
								className="h-8 w-8 p-0"
							>
								<Edit className="h-4 w-4" />
							</Button>
						</HasPermission>
						<HasPermission permission={PERMISSION_NAMES.ANNOUNCEMENTS_MANAGE}>
							<Button
								variant="ghost"
								size="sm"
								onClick={handleDelete}
								className="h-8 w-8 p-0 text-destructive hover:text-destructive"
							>
								<Trash2 className="h-4 w-4" />
							</Button>
						</HasPermission>
					</div>
				</div>
				<div className="flex items-center gap-4 text-muted-foreground text-xs">
					<div className="flex items-center gap-1">
						<User className="h-3 w-3" />
						{announcement.author?.name || "Unknown"}
					</div>
					<div className="flex items-center gap-1">
						<Calendar className="h-3 w-3" />
						{new Date(announcement.announcement.createdAt).toLocaleDateString()}
					</div>
					{announcement.announcement.updatedAt !==
						announcement.announcement.createdAt && (
						<div className="flex items-center gap-1">
							<Clock className="h-3 w-3" />
							Updated
						</div>
					)}
				</div>
			</CardHeader>
			{viewMode === "grid" && (
				<CardContent className="pt-0">
					<p className="line-clamp-3 text-muted-foreground text-sm">
						{getPreviewText(announcement.announcement.content)}
					</p>
				</CardContent>
			)}
			<CardFooter className="pt-0">
				<Button
					variant="outline"
					className="w-full gap-2"
					onClick={() => onView(announcement)}
				>
					<Megaphone className="h-4 w-4" />
					Read Announcement
				</Button>
			</CardFooter>
		</Card>
	);
}
