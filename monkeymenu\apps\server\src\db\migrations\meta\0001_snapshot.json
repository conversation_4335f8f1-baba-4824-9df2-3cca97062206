{"version": "6", "dialect": "sqlite", "id": "3ae9df95-7d8d-43fd-b51d-e5d7e03644da", "prevId": "ddd1bd15-9366-498b-84e4-06352511b0ed", "tables": {"announcement": {"name": "announcement", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'general'"}, "author_id": {"name": "author_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"idx_announcement_created_at": {"name": "idx_announcement_created_at", "columns": ["created_at"], "isUnique": false}, "idx_announcement_author_id": {"name": "idx_announcement_author_id", "columns": ["author_id"], "isUnique": false}, "idx_announcement_category": {"name": "idx_announcement_category", "columns": ["category"], "isUnique": false}}, "foreignKeys": {"announcement_author_id_user_id_fk": {"name": "announcement_author_id_user_id_fk", "tableFrom": "announcement", "tableTo": "user", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "account": {"name": "account", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"idx_account_user_id": {"name": "idx_account_user_id", "columns": ["user_id"], "isUnique": false}, "idx_account_provider_account": {"name": "idx_account_provider_account", "columns": ["provider_id", "account_id"], "isUnique": false}}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "session": {"name": "session", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"session_token_unique": {"name": "session_token_unique", "columns": ["token"], "isUnique": true}, "idx_session_user_id": {"name": "idx_session_user_id", "columns": ["user_id"], "isUnique": false}}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user": {"name": "user", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email_verified": {"name": "email_verified", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"user_email_unique": {"name": "user_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "verification": {"name": "verification", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"idx_verification_identifier": {"name": "idx_verification_identifier", "columns": ["identifier"], "isUnique": false}, "idx_verification_expires_at": {"name": "idx_verification_expires_at", "columns": ["expires_at"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "withdrawal_request": {"name": "withdrawal_request", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'PENDING'"}, "discord_message_id": {"name": "discord_message_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "processed_at": {"name": "processed_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "transaction_id": {"name": "transaction_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "processed_by_bot_discord_id": {"name": "processed_by_bot_discord_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "processed_by_bot_discord_tag": {"name": "processed_by_bot_discord_tag", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "initiated_by_discord_id": {"name": "initiated_by_discord_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "initiated_by_discord_tag": {"name": "initiated_by_discord_tag", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "requested_by_id": {"name": "requested_by_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "processed_by_id": {"name": "processed_by_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "guide": {"name": "guide", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'general'"}, "author_id": {"name": "author_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"idx_guide_created_at": {"name": "idx_guide_created_at", "columns": ["created_at"], "isUnique": false}, "idx_guide_author_id": {"name": "idx_guide_author_id", "columns": ["author_id"], "isUnique": false}, "idx_guide_category": {"name": "idx_guide_category", "columns": ["category"], "isUnique": false}}, "foreignKeys": {"guide_author_id_user_id_fk": {"name": "guide_author_id_user_id_fk", "tableFrom": "guide", "tableTo": "user", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "faction_role": {"name": "faction_role", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "hierarchy_level": {"name": "hierarchy_level", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"faction_role_name_unique": {"name": "faction_role_name_unique", "columns": ["name"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "permission": {"name": "permission", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"permission_name_unique": {"name": "permission_name_unique", "columns": ["name"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "role_permission": {"name": "role_permission", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "role_id": {"name": "role_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "permission_id": {"name": "permission_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"role_permission_role_id_permission_id_unique": {"name": "role_permission_role_id_permission_id_unique", "columns": ["role_id", "permission_id"], "isUnique": true}}, "foreignKeys": {"role_permission_role_id_faction_role_id_fk": {"name": "role_permission_role_id_faction_role_id_fk", "tableFrom": "role_permission", "tableTo": "faction_role", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "role_permission_permission_id_permission_id_fk": {"name": "role_permission_permission_id_permission_id_fk", "tableFrom": "role_permission", "tableTo": "permission", "columnsFrom": ["permission_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_role": {"name": "user_role", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role_id": {"name": "role_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "assigned_by": {"name": "assigned_by", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "assigned_at": {"name": "assigned_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}}, "indexes": {"user_role_active_user_unique": {"name": "user_role_active_user_unique", "columns": ["user_id"], "isUnique": true, "where": "\"user_role\".\"is_active\" = 1"}}, "foreignKeys": {"user_role_user_id_user_id_fk": {"name": "user_role_user_id_user_id_fk", "tableFrom": "user_role", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_role_role_id_faction_role_id_fk": {"name": "user_role_role_id_faction_role_id_fk", "tableFrom": "user_role", "tableTo": "faction_role", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_role_assigned_by_user_id_fk": {"name": "user_role_assigned_by_user_id_fk", "tableFrom": "user_role", "tableTo": "user", "columnsFrom": ["assigned_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "target": {"name": "target", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "list_id": {"name": "list_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "torn_id": {"name": "torn_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"idx_target_list_id": {"name": "idx_target_list_id", "columns": ["list_id"], "isUnique": false}, "idx_target_torn_id": {"name": "idx_target_torn_id", "columns": ["torn_id"], "isUnique": false}}, "foreignKeys": {"target_list_id_target_list_id_fk": {"name": "target_list_id_target_list_id_fk", "tableFrom": "target", "tableTo": "target_list", "columnsFrom": ["list_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "target_list": {"name": "target_list", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"target_list_name_idx": {"name": "target_list_name_idx", "columns": ["name"], "isUnique": true}, "idx_target_list_user_id": {"name": "idx_target_list_user_id", "columns": ["user_id"], "isUnique": false}}, "foreignKeys": {"target_list_user_id_user_id_fk": {"name": "target_list_user_id_user_id_fk", "tableFrom": "target_list", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "torn_user": {"name": "torn_user", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "torn_api_key": {"name": "torn_api_key", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "torn_api_key_verified": {"name": "torn_api_key_verified", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "torn_user_id": {"name": "torn_user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "torn_faction_id": {"name": "torn_faction_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "torn_api_key_last_checked_at": {"name": "torn_api_key_last_checked_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "access_suspended": {"name": "access_suspended", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "access_suspension_reason": {"name": "access_suspension_reason", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "access_suspended_at": {"name": "access_suspended_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "suspension_type": {"name": "suspension_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_torn_api_error": {"name": "last_torn_api_error", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_torn_api_error_at": {"name": "last_torn_api_error_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_target_finder_fetch": {"name": "last_target_finder_fetch", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_overdose_event_timestamp": {"name": "last_overdose_event_timestamp", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"torn_user_id_user_id_fk": {"name": "torn_user_id_user_id_fk", "tableFrom": "torn_user", "tableTo": "user", "columnsFrom": ["id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "cached_chain_report": {"name": "cached_chain_report", "columns": {"chain_id": {"name": "chain_id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "war_id": {"name": "war_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "report_data": {"name": "report_data", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "start_time": {"name": "start_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "end_time": {"name": "end_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "chain_length": {"name": "chain_length", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "total_respect": {"name": "total_respect", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "attacker_count": {"name": "attacker_count", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "cached_at": {"name": "cached_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"idx_chain_war_id": {"name": "idx_chain_war_id", "columns": ["war_id"], "isUnique": false}, "idx_chain_start_time": {"name": "idx_chain_start_time", "columns": ["start_time"], "isUnique": false}, "idx_chain_end_time": {"name": "idx_chain_end_time", "columns": ["end_time"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "cached_war_attacks": {"name": "cached_war_attacks", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "war_id": {"name": "war_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "attack_data": {"name": "attack_data", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "attacker_id": {"name": "attacker_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "defender_id": {"name": "defender_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "attacker_faction_id": {"name": "attacker_faction_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "defender_faction_id": {"name": "defender_faction_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "timestamp": {"name": "timestamp", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "chain_id": {"name": "chain_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "result": {"name": "result", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "respect": {"name": "respect", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "cached_at": {"name": "cached_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"idx_attack_war_id": {"name": "idx_attack_war_id", "columns": ["war_id"], "isUnique": false}, "idx_attack_timestamp": {"name": "idx_attack_timestamp", "columns": ["timestamp"], "isUnique": false}, "idx_attack_attacker_id": {"name": "idx_attack_attacker_id", "columns": ["attacker_id"], "isUnique": false}, "idx_attack_defender_id": {"name": "idx_attack_defender_id", "columns": ["defender_id"], "isUnique": false}, "idx_attack_chain_id": {"name": "idx_attack_chain_id", "columns": ["chain_id"], "isUnique": false}, "idx_attack_result": {"name": "idx_attack_result", "columns": ["result"], "isUnique": false}, "idx_attack_war_chain": {"name": "idx_attack_war_chain", "columns": ["war_id", "chain_id"], "isUnique": false}, "idx_attack_war_attacker": {"name": "idx_attack_war_attacker", "columns": ["war_id", "attacker_id"], "isUnique": false}, "idx_attack_war_timestamp": {"name": "idx_attack_war_timestamp", "columns": ["war_id", "timestamp"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "cached_war_report": {"name": "cached_war_report", "columns": {"war_id": {"name": "war_id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "report_data": {"name": "report_data", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "faction_ids": {"name": "faction_ids", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "start_time": {"name": "start_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "end_time": {"name": "end_time", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "winner": {"name": "winner", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_completed": {"name": "is_completed", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "cached_at": {"name": "cached_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "last_updated": {"name": "last_updated", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"idx_war_start_time": {"name": "idx_war_start_time", "columns": ["start_time"], "isUnique": false}, "idx_war_end_time": {"name": "idx_war_end_time", "columns": ["end_time"], "isUnique": false}, "idx_war_is_completed": {"name": "idx_war_is_completed", "columns": ["is_completed"], "isUnique": false}, "idx_war_faction_ids": {"name": "idx_war_faction_ids", "columns": ["faction_ids"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "cached_war_stats": {"name": "cached_war_stats", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "war_id": {"name": "war_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "faction_id": {"name": "faction_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "stats_type": {"name": "stats_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "timeframe": {"name": "timeframe", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "stats_data": {"name": "stats_data", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "cached_at": {"name": "cached_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"idx_war_stats_unique": {"name": "idx_war_stats_unique", "columns": ["war_id", "faction_id", "stats_type", "timeframe"], "isUnique": true}, "idx_war_stats_war_id": {"name": "idx_war_stats_war_id", "columns": ["war_id"], "isUnique": false}, "idx_war_stats_faction_id": {"name": "idx_war_stats_faction_id", "columns": ["faction_id"], "isUnique": false}, "idx_war_stats_type": {"name": "idx_war_stats_type", "columns": ["stats_type"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}