import { HasPermission } from "@/components/permissions/PermissionGuards";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAppForm } from "@/components/ui/tanstack-form";
import { PERMISSION_NAMES } from "@monkeymenu/shared";
import { Megaphone, MessageSquare } from "lucide-react";
import ReactMarkdown from "react-markdown";
import rehypeHighlight from "rehype-highlight";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";
import { useAnnouncementMutations } from "./hooks";
import type { AnnouncementData } from "./types";
import { ANNOUNCEMENT_CATEGORIES } from "./utils";

interface CreateAnnouncementDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	editingAnnouncement?: AnnouncementData | null;
}

export function CreateAnnouncementDialog({
	open,
	onOpenChange,
	editingAnnouncement,
}: CreateAnnouncementDialogProps) {
	const { createMutation, updateMutation } = useAnnouncementMutations();

	const form = useAppForm({
		defaultValues: {
			title: editingAnnouncement?.announcement.title ?? "",
			content: editingAnnouncement?.announcement.content ?? "",
			category: editingAnnouncement?.announcement.category ?? "general",
			isUrgent: false,
		},
		onSubmit: ({ value }) => {
			if (editingAnnouncement) {
				updateMutation.mutate({
					id: editingAnnouncement.announcement.id,
					title: value.title,
					content: value.content,
					category: value.category,
				});
			} else {
				createMutation.mutate(value);
			}
		},
	});

	const handleOpenChange = (open: boolean) => {
		if (!open) {
			form.reset();
		}
		onOpenChange(open);
	};

	const isEditing = !!editingAnnouncement;

	return (
		<HasPermission permission={PERMISSION_NAMES.ANNOUNCEMENTS_MANAGE}>
			<Dialog open={open} onOpenChange={handleOpenChange}>
				<DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
					<DialogHeader>
						<DialogTitle className="flex items-center gap-2">
							<Megaphone className="h-5 w-5" />
							{isEditing ? "Edit Announcement" : "Create New Announcement"}
						</DialogTitle>
						<DialogDescription>
							{isEditing
								? "Make changes to the existing announcement below."
								: "Create a new announcement with title, category, and markdown content."}
						</DialogDescription>
					</DialogHeader>
					<form.AppForm>
						<form
							onSubmit={(e) => {
								e.preventDefault();
								e.stopPropagation();
								void form.handleSubmit();
							}}
							className="space-y-6"
						>
							<form.AppField name="title">
								{(field) => (
									<field.FormItem>
										<field.FormLabel>Title</field.FormLabel>
										<field.FormControl>
											<Input
												placeholder="Enter announcement title..."
												value={field.state.value}
												onChange={(e) => field.handleChange(e.target.value)}
												onBlur={field.handleBlur}
											/>
										</field.FormControl>
										<field.FormMessage />
									</field.FormItem>
								)}
							</form.AppField>

							<form.AppField name="category">
								{(field) => (
									<field.FormItem>
										<field.FormLabel>Category</field.FormLabel>
										<field.FormControl>
											<Select
												value={field.state.value}
												onValueChange={field.handleChange}
											>
												<SelectTrigger>
													<SelectValue placeholder="Select a category" />
												</SelectTrigger>
												<SelectContent>
													{ANNOUNCEMENT_CATEGORIES.map((category) => (
														<SelectItem key={category.id} value={category.id}>
															<div className="flex items-center gap-2">
																<span>{category.emoji}</span>
																<span>{category.label}</span>
															</div>
														</SelectItem>
													))}
												</SelectContent>
											</Select>
										</field.FormControl>
										<field.FormMessage />
									</field.FormItem>
								)}
							</form.AppField>

							<form.AppField name="content">
								{(field) => (
									<field.FormItem>
										<field.FormLabel>Content</field.FormLabel>
										<field.FormControl>
											<Tabs defaultValue="write" className="w-full">
												<TabsList className="grid w-full grid-cols-2">
													<TabsTrigger value="write">Write</TabsTrigger>
													<TabsTrigger value="preview">Preview</TabsTrigger>
												</TabsList>
												<TabsContent value="write" className="mt-4">
													<textarea
														className="flex min-h-[400px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
														placeholder="Write your announcement content here... Markdown is supported"
														value={field.state.value}
														onChange={(e) => field.handleChange(e.target.value)}
														onBlur={field.handleBlur}
													/>
												</TabsContent>
												<TabsContent value="preview" className="mt-4">
													<div className="min-h-[400px] rounded-md border border-input bg-background p-4">
														<div className="prose prose-sm dark:prose-invert max-w-none">
															<ReactMarkdown
																remarkPlugins={[remarkGfm]}
																rehypePlugins={[rehypeHighlight, rehypeRaw]}
															>
																{field.state.value || "*No content to preview*"}
															</ReactMarkdown>
														</div>
													</div>
												</TabsContent>
											</Tabs>
										</field.FormControl>
										<field.FormMessage />
									</field.FormItem>
								)}
							</form.AppField>

							{!isEditing && (
								<form.AppField name="isUrgent">
									{(field) => (
										<field.FormItem className="rounded-md border p-4">
											<div className="flex items-center justify-between">
												<div className="space-y-1">
													<field.FormLabel className="flex items-center gap-2 font-medium text-sm">
														<MessageSquare className="h-4 w-4" />
														Urgent Announcement
													</field.FormLabel>
													<p className="text-muted-foreground text-sm">
														Mark as urgent to ping the faction role in Discord
													</p>
												</div>
												<field.FormControl>
													<Button
														type="button"
														variant={
															field.state.value ? "destructive" : "outline"
														}
														size="sm"
														onClick={() =>
															field.handleChange(!field.state.value)
														}
														className="ml-4"
													>
														{field.state.value ? "🚨 Urgent" : "Normal"}
													</Button>
												</field.FormControl>
											</div>
										</field.FormItem>
									)}
								</form.AppField>
							)}

							<div className="flex justify-between">
								<Button
									type="button"
									variant="outline"
									onClick={() => handleOpenChange(false)}
								>
									Cancel
								</Button>
								<form.Subscribe>
									{(state) => (
										<Button
											type="submit"
											disabled={
												!state.canSubmit ||
												state.isSubmitting ||
												createMutation.isPending ||
												updateMutation.isPending
											}
										>
											{isEditing
												? "Update Announcement"
												: "Create Announcement"}
										</Button>
									)}
								</form.Subscribe>
							</div>
						</form>
					</form.AppForm>
				</DialogContent>
			</Dialog>
		</HasPermission>
	);
}
