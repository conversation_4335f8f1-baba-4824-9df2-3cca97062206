import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { getUserByClerkIdInternal } from "./users";

export const createGuide = mutation({
  args: {
    title: v.string(),
    content: v.string(),
    category: v.string(),
    tags: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user) {
      throw new Error("User not found");
    }

    return await ctx.db.insert("guides", {
      title: args.title,
      content: args.content,
      category: args.category,
      authorId: user._id,
      isPublished: false,
      viewCount: 0,
      tags: args.tags,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
  },
});

export const updateGuide = mutation({
  args: {
    id: v.id("guides"),
    title: v.optional(v.string()),
    content: v.optional(v.string()),
    category: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    isPublished: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user) {
      throw new Error("User not found");
    }

    const guide = await ctx.db.get(args.id);
    if (!guide) {
      throw new Error("Guide not found");
    }

    // Check if user is author or has admin permissions
    if (guide.authorId !== user._id && !user.permissions.includes("admin")) {
      throw new Error("Not authorized to edit this guide");
    }

    const updates: any = {
      updatedAt: Date.now(),
    };

    if (args.title !== undefined) updates.title = args.title;
    if (args.content !== undefined) updates.content = args.content;
    if (args.category !== undefined) updates.category = args.category;
    if (args.tags !== undefined) updates.tags = args.tags;
    if (args.isPublished !== undefined) updates.isPublished = args.isPublished;

    await ctx.db.patch(args.id, updates);
  },
});

export const deleteGuide = mutation({
  args: {
    id: v.id("guides"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user) {
      throw new Error("User not found");
    }

    const guide = await ctx.db.get(args.id);
    if (!guide) {
      throw new Error("Guide not found");
    }

    // Check if user is author or has admin permissions
    if (guide.authorId !== user._id && !user.permissions.includes("admin")) {
      throw new Error("Not authorized to delete this guide");
    }

    await ctx.db.delete(args.id);
  },
});

export const getGuides = query({
  args: {
    category: v.optional(v.string()),
    publishedOnly: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    let guidesQuery = ctx.db.query("guides");

    if (args.category) {
      guidesQuery = guidesQuery.filter((q) => q.eq(q.field("category"), args.category));
    }

    if (args.publishedOnly) {
      guidesQuery = guidesQuery.filter((q) => q.eq(q.field("isPublished"), true));
    }

    const guides = await guidesQuery.order("desc").take(100);

    // Get authors for each guide
    const guidesWithAuthors = await Promise.all(
      guides.map(async (guide) => {
        const author = await ctx.db.get(guide.authorId);
        return {
          ...guide,
          author: author ? { username: author.username } : null,
        };
      })
    );

    return guidesWithAuthors;
  },
});

export const getGuideById = query({
  args: {
    id: v.id("guides"),
  },
  handler: async (ctx, args) => {
    const guide = await ctx.db.get(args.id);
    if (!guide) {
      return null;
    }

    const author = await ctx.db.get(guide.authorId);
    
    return {
      ...guide,
      author: author ? { username: author.username } : null,
    };
  },
});

export const incrementViewCount = mutation({
  args: {
    id: v.id("guides"),
  },
  handler: async (ctx, args) => {
    const guide = await ctx.db.get(args.id);
    if (!guide) {
      throw new Error("Guide not found");
    }

    await ctx.db.patch(args.id, {
      viewCount: guide.viewCount + 1,
    });
  },
});

export const getCategories = query({
  handler: async (ctx) => {
    const guides = await ctx.db.query("guides").collect();
    const categories = [...new Set(guides.map(guide => guide.category))];
    return categories.sort();
  },
});

export const searchGuides = query({
  args: {
    searchTerm: v.string(),
    category: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    let guidesQuery = ctx.db.query("guides");

    if (args.category) {
      guidesQuery = guidesQuery.filter((q) => q.eq(q.field("category"), args.category));
    }

    const guides = await guidesQuery.collect();

    // Simple text search in title and content
    const searchTerm = args.searchTerm.toLowerCase();
    const filteredGuides = guides.filter(guide => 
      guide.title.toLowerCase().includes(searchTerm) ||
      guide.content.toLowerCase().includes(searchTerm) ||
      guide.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    );

    // Get authors for each guide
    const guidesWithAuthors = await Promise.all(
      filteredGuides.map(async (guide) => {
        const author = await ctx.db.get(guide.authorId);
        return {
          ...guide,
          author: author ? { username: author.username } : null,
        };
      })
    );

    return guidesWithAuthors.sort((a, b) => b.createdAt - a.createdAt);
  },
});

export const getUserGuides = query({
  args: {
    userId: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    let targetUserId = args.userId;
    let actualUserId = targetUserId;
    if (!actualUserId) {
      const user = await getUserByClerkIdInternal(ctx, identity.subject);
      if (!user) {
        throw new Error("User not found");
      }
      actualUserId = user._id;
    }
    if (!actualUserId) {
      throw new Error("No userId available");
    }
    const guides = await ctx.db
      .query("guides")
      .filter((q) => q.eq(q.field("authorId"), actualUserId))
      .order("desc")
      .collect();
    const author = await ctx.db.get(actualUserId);
    return guides.map(guide => ({
      ...guide,
      author: author ? { username: author.username } : null,
    }));
  },
});

// Get recent guides for activity feed
export const getRecentGuides = query({
  args: {},
  handler: async (ctx) => {
    const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
    
    return await ctx.db
      .query("guides")
      .withIndex("by_published", (q) => q.eq("isPublished", true))
      .filter((q) => q.gte(q.field("createdAt"), sevenDaysAgo))
      .order("desc")
      .take(10);
  },
});