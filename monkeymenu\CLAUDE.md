# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
```bash
# Start all services
pnpm dev

# Start individual services
pnpm dev:server    # Server only (port 3000)
pnpm dev:web       # Web only (port 3001)
pnpm dev:shared    # Shared package in watch mode

# Build
pnpm build         # Build all packages
pnpm build:shared  # Build shared package only
```

### Database Operations
```bash
# Local development
pnpm db:generate   # Generate migrations after schema changes
pnpm db:migrate    # Apply migrations locally
pnpm db:studio     # Open Drizzle Studio

# Production
pnpm db:migrate:prod   # Apply migrations to production
pnpm db:studio:prod    # Open Drizzle Studio for production
```

### Code Quality
```bash
pnpm check         # Run Biome linting and formatting
pnpm check-types   # TypeScript type checking across all packages
```

### Deployment
```bash
pnpm cf:deploy     # Build and deploy to Cloudflare Workers
```

## Architecture

### Monorepo Structure
This is a pnpm workspace monorepo with Turbo build orchestration:
- `apps/server/` - Hono API on Cloudflare Workers with tRPC, Drizzle ORM, Discord bot
- `apps/web/` - React frontend with TanStack Router/Query, Tailwind CSS, Radix UI
- `packages/shared/` - Common utilities and types

### Server Architecture
- **Framework**: Hono on Cloudflare Workers with lazy initialization
- **API**: tRPC with layered middleware system for auth/permissions
- **Database**: Drizzle ORM with Cloudflare D1 (SQLite)
- **Authentication**: better-auth with Discord OAuth, session-based
- **Real-time**: LiveStore-powered synchronization using a Cloudflare Durable Object for all modules (banking, target finder, announcements, etc.)
- **Background Jobs**: Scheduled tasks for user verification, role sync

### Key tRPC Procedures
- `publicProcedure` - No auth required
- `protectedProcedure` - Valid session required
- `factionProcedure` - Faction membership required (ID: 53100)
- `permissionProcedure` - Includes user permissions context
- `factionPermissionProcedure` - Combined faction + permissions

### Permission System
Uses `requirePermission(name)` and `requireMinimumRole(level)` middleware for granular access control.

### Web Architecture
- **Routing**: TanStack Router with file-based routing, type-safe
- **State**: TanStack Query for server state, React Context for local state
- **Styling**: Tailwind CSS with Radix UI components
- **Themes**: Multiple theme support with CSS custom properties

### Database Patterns
- Schema files in `apps/server/src/db/schema/` organized by feature
- Always run `pnpm db:generate` after schema changes
- Use proper indexing for performance
- Drizzle ORM with typed queries and relations

### External Integrations
- **Torn API**: User verification, faction status, game data sync
- **Discord**: OAuth, bot commands, role management, approval workflows
- **LiveStore**: Real-time data synchronization

## Development Patterns

### Adding New Features
1. Define database schema in `apps/server/src/db/schema/`
2. Create tRPC router in `apps/server/src/routers/`
3. Add router to main router in `apps/server/src/routers/index.ts`
4. Create React components in `apps/web/src/routes/` or `apps/web/src/components/`
5. Use TanStack Query hooks for server state management

### Authentication Flow
1. Discord OAuth via better-auth
2. Session management with automatic expiration
3. Faction membership verification via Torn API
4. Permission-based access control throughout the app

### Code Style
- Uses Biome for linting/formatting with tabs, double quotes, trailing commas
- Pre-commit hooks enforce code quality
- TypeScript strict mode enabled
- Import organization via Biome

### Environment Management
- Doppler for secret management
- Local: `.dev.vars` (server), `.env.development` (web)
- Production: Auto-synced during deployment