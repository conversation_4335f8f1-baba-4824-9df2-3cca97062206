#!/bin/bash

# Production deployment script for MonkeyMenu
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-production}

echo -e "${GREEN}🚀 Starting deployment to $ENVIRONMENT...${NC}"

# Check prerequisites
echo -e "${YELLOW}📋 Checking prerequisites...${NC}"

if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ npm is required but not installed${NC}"
    exit 1
fi

if ! command -v git &> /dev/null; then
    echo -e "${RED}❌ git is required but not installed${NC}"
    exit 1
fi

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo -e "${RED}❌ Not in a git repository${NC}"
    exit 1
fi

# Check for uncommitted changes
if [[ -n $(git status --porcelain) ]]; then
    echo -e "${YELLOW}⚠️  Warning: You have uncommitted changes${NC}"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Install dependencies
echo -e "${YELLOW}📦 Installing dependencies...${NC}"
npm ci

# Run linting
echo -e "${YELLOW}🔍 Running linter...${NC}"
npm run lint

# Run type checking
echo -e "${YELLOW}🔧 Running type check...${NC}"
npm run type-check

# Run tests
echo -e "${YELLOW}🧪 Running tests...${NC}"
npm run test:run

# Build the application
echo -e "${YELLOW}🏗️  Building application...${NC}"
npm run build:web

# Deploy Convex backend
echo -e "${YELLOW}☁️  Deploying Convex backend...${NC}"
if [[ $ENVIRONMENT == "production" ]]; then
    npx convex deploy --prod
else
    npx convex deploy
fi

# Get Convex URL for frontend deployment
CONVEX_URL=$(npx convex env get CONVEX_URL)
echo "Using Convex URL: $CONVEX_URL"

# Create production environment file
echo -e "${YELLOW}⚙️  Creating environment configuration...${NC}"
cat > .env.production << EOF
VITE_CONVEX_URL=$CONVEX_URL
VITE_CLERK_PUBLISHABLE_KEY=$VITE_CLERK_PUBLISHABLE_KEY
EOF

# Deploy to Cloudflare Pages
if command -v wrangler &> /dev/null; then
    echo -e "${YELLOW}🌐 Deploying to Cloudflare Pages...${NC}"
    if [[ $ENVIRONMENT == "production" ]]; then
        wrangler pages deploy dist --project-name=monkeymenu-production
    else
        wrangler pages deploy dist --project-name=monkeymenu-staging
    fi
else
    echo -e "${YELLOW}ℹ️  Wrangler CLI not found. Please deploy manually or install Wrangler CLI${NC}"
    echo -e "${GREEN}✅ Build artifacts are ready in ./dist directory${NC}"
    echo "   Install Wrangler: npm install -g wrangler"
    echo "   Then run: wrangler pages deploy dist --project-name=your-project"
fi

# Deploy Discord Worker (if configured)
if [[ -n "$DISCORD_BOT_TOKEN" ]]; then
    echo -e "${YELLOW}🤖 Deploying Discord Worker...${NC}"

    # Sync Discord commands first
    echo -e "${YELLOW}📡 Syncing Discord slash commands...${NC}"
    if [[ $ENVIRONMENT == "production" ]]; then
        npm run discord:sync
    else
        npm run discord:sync
    fi

    # Deploy Discord Worker
    if [[ $ENVIRONMENT == "production" ]]; then
        npm run discord:worker:deploy:prod
    else
        npm run discord:worker:deploy:staging
    fi

    echo -e "${GREEN}✅ Discord Worker deployed successfully!${NC}"
else
    echo -e "${YELLOW}ℹ️  Discord Worker deployment skipped (DISCORD_BOT_TOKEN not set)${NC}"
fi

# Success message
echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"

if [[ $ENVIRONMENT == "production" ]]; then
    echo -e "${GREEN}🌟 Your application is now live in production!${NC}"
else
    echo -e "${GREEN}🚧 Your staging deployment is ready for testing!${NC}"
fi

# Post-deployment checks
echo -e "${YELLOW}🔍 Running post-deployment checks...${NC}"

# Check if Convex functions are responding
echo "Testing Convex connection..."
if curl -f "$CONVEX_URL" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Convex backend is responding${NC}"
else
    echo -e "${YELLOW}⚠️  Could not reach Convex backend${NC}"
fi

# Cleanup
rm -f .env.production

echo -e "${GREEN}🏁 Deployment process complete!${NC}"