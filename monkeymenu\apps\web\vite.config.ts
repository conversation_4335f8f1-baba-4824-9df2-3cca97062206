import path from "node:path";
import { livestoreDevtoolsPlugin } from "@livestore/devtools-vite";
import tailwindcss from "@tailwindcss/vite";
import { TanStackRouterVite } from "@tanstack/router-plugin/vite";
import react from "@vitejs/plugin-react";
import { defineConfig } from "vite";

// https://vite.dev/config/
export default defineConfig({
	plugins: [
		TanStackRouterVite({
			autoCodeSplitting: true,
			routeToken: "layout",
		}),
		react(),
		tailwindcss(),
		livestoreDevtoolsPlugin({ schemaPath: "./src/livestore/schema.ts" }),
	],
	worker: { format: "es" },
	resolve: {
		alias: {
			"@": path.resolve(__dirname, "./src/"),
		},
	},
	build: {
		// Optimize bundle splitting
		rollupOptions: {
			output: {
				manualChunks: {
					// Separate vendor libraries into their own chunks
					"react-vendor": ["react", "react-dom"],
					"tanstack-vendor": [
						"@tanstack/react-query",
						"@tanstack/react-router",
						"@tanstack/react-form",
						"@tanstack/react-store",
					],
					"trpc-vendor": ["@trpc/client", "@trpc/tanstack-react-query"],
					"radix-vendor": [
						"@radix-ui/react-avatar",
						"@radix-ui/react-checkbox",
						"@radix-ui/react-dialog",
						"@radix-ui/react-dropdown-menu",
						"@radix-ui/react-label",
						"@radix-ui/react-select",
						"@radix-ui/react-separator",
						"@radix-ui/react-slot",
						"@radix-ui/react-switch",
						"@radix-ui/react-tabs",
						"@radix-ui/react-tooltip",
					],
					"ui-vendor": [
						"lucide-react",
						"@icons-pack/react-simple-icons",
						"sonner",
						"input-otp",
					],
					"utils-vendor": [
						"clsx",
						"tailwind-merge",
						"class-variance-authority",
						"zod",
					],
				},
			},
		},
		// Increase chunk size warning limit since we're now splitting properly
		chunkSizeWarningLimit: 1000,
		// Enable source maps for better debugging (optional)
		sourcemap: false,
	},
	server: {
		proxy: {
			"/trpc": {
				target: "http://127.0.0.1:3000",
				changeOrigin: true,
			},
			"/api": {
				target: "http://127.0.0.1:3000",
				changeOrigin: true,
				ws: true, // Enable WebSocket proxying
			},
		},
	},
});
