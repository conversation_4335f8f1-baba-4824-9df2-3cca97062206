import { query, mutation, action } from "./_generated/server";
import { v } from "convex/values";
import { ConvexError } from "convex/values";
import { internal } from "./_generated/api";
import { requirePermission } from "./lib/permissions";

// Get wars cooldown status
export const getWarsCooldown = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) throw new Error("User not found");

    // Check for recent war data refresh within last 60 seconds
    const sixtySecondsAgo = Date.now() - 60000;
    const recentWarUpdate = await ctx.db
      .query("wars")
      .filter((q) => q.gte(q.field("updatedAt"), sixtySecondsAgo))
      .first();

    if (recentWarUpdate) {
      const cooldownUntil = recentWarUpdate.updatedAt + 60000;
      return {
        cooldownUntil,
        cooldownDuration: 60000,
        remaining: Math.max(0, Math.ceil((cooldownUntil - Date.now()) / 1000))
      };
    }

    return {
      cooldownUntil: null,
      cooldownDuration: 60000,
      remaining: 0
    };
  },
});

// Get active wars
export const getActiveWars = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query("wars")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("desc")
      .collect();
  },
});

// Get all wars with filtering
export const getWars = query({
  args: {
    status: v.optional(v.string()),
    factionId: v.optional(v.number()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let baseQuery = ctx.db.query("wars")
      .filter((q: any) => q.eq(q.field("isActive"), true));

    if (args.status) {
      return await ctx.db.query("wars")
        .withIndex("by_status", (q: any) => q.eq("status", args.status))
        .filter((q: any) => q.eq(q.field("isActive"), true))
        .order("desc")
        .take(args.limit ?? 50);
    }

    if (args.factionId) {
      return await ctx.db.query("wars")
        .withIndex("by_faction", (q: any) => q.eq("factionId", args.factionId))
        .filter((q: any) => q.eq(q.field("isActive"), true))
        .order("desc")
        .take(args.limit ?? 50);
    }

    return await baseQuery
      .order("desc")
      .take(args.limit ?? 50);
  },
});

// Get war by ID with attacks
export const getWarWithAttacks = query({
  args: { warId: v.id("wars") },
  handler: async (ctx, args) => {
    const war = await ctx.db.get(args.warId);
    if (!war) return null;

    const attacks = await ctx.db
      .query("warAttacks")
      .withIndex("by_war", (q) => q.eq("warId", args.warId))
      .order("desc")
      .collect();

    return { war, attacks };
  },
});

// Get war statistics
export const getWarStats = query({
  args: { warId: v.id("wars") },
  handler: async (ctx, args) => {
    const war = await ctx.db.get(args.warId);
    if (!war) return null;

    const attacks = await ctx.db
      .query("warAttacks")
      .withIndex("by_war", (q) => q.eq("warId", args.warId))
      .collect();

    // Calculate statistics
    const totalAttacks = attacks.length;
    const wins = attacks.filter(a => a.result === 'win').length;
    const losses = attacks.filter(a => a.result === 'loss').length;
    const timeouts = attacks.filter(a => a.result === 'timeout').length;
    const escapes = attacks.filter(a => a.result === 'escape').length;

    const totalRespect = attacks.reduce((sum, a) => sum + a.respect, 0);
    const averageRespect = totalAttacks > 0 ? Math.round(totalRespect / totalAttacks) : 0;

    // Attacker performance
    const attackerStats = new Map<number, {
      name: string;
      attacks: number;
      wins: number;
      respect: number;
    }>();

    attacks.forEach(attack => {
      const existing = attackerStats.get(attack.attackerId) || {
        name: attack.attackerName,
        attacks: 0,
        wins: 0,
        respect: 0,
      };
      
      existing.attacks++;
      if (attack.result === 'win') existing.wins++;
      existing.respect += attack.respect;
      
      attackerStats.set(attack.attackerId, existing);
    });

    const topAttackers = Array.from(attackerStats.entries())
      .map(([id, stats]) => ({ id, ...stats }))
      .sort((a, b) => b.respect - a.respect)
      .slice(0, 10);

    return {
      war,
      totalAttacks,
      wins,
      losses,
      timeouts,
      escapes,
      winRate: totalAttacks > 0 ? Math.round((wins / totalAttacks) * 100) : 0,
      totalRespect,
      averageRespect,
      topAttackers,
    };
  },
});

// Get recent war activity
export const getRecentWarActivity = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("warAttacks")
      .order("desc")
      .take(args.limit ?? 20);
  },
});

// Create new war
export const createWar = mutation({
  args: {
    factionId: v.number(),
    factionName: v.string(),
    enemyFactionId: v.number(),
    enemyFactionName: v.string(),
    startTime: v.number(),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "wars.create");

    const now = Date.now();
    return await ctx.db.insert("wars", {
      factionId: args.factionId,
      factionName: args.factionName,
      enemyFactionId: args.enemyFactionId,
      enemyFactionName: args.enemyFactionName,
      startTime: args.startTime,
      status: "active",
      ourScore: 0,
      enemyScore: 0,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Update war status and scores
export const updateWar = mutation({
  args: {
    warId: v.id("wars"),
    status: v.optional(v.string()),
    ourScore: v.optional(v.number()),
    enemyScore: v.optional(v.number()),
    endTime: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "wars.edit");

    const { warId, ...updates } = args;
    return await ctx.db.patch(warId, {
      ...updates,
      updatedAt: Date.now(),
    });
  },
});

// Add war attack
export const addWarAttack = mutation({
  args: {
    warId: v.id("wars"),
    attackerId: v.number(),
    attackerName: v.string(),
    defenderId: v.number(),
    defenderName: v.string(),
    result: v.string(),
    respect: v.number(),
    chain: v.optional(v.number()),
    timestamp: v.number(),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "wars.update");

    const now = Date.now();
    const attackId = await ctx.db.insert("warAttacks", {
      warId: args.warId,
      attackerId: args.attackerId,
      attackerName: args.attackerName,
      defenderId: args.defenderId,
      defenderName: args.defenderName,
      result: args.result,
      respect: args.respect,
      chain: args.chain,
      timestamp: args.timestamp,
      createdAt: now,
    });

    // Update war score if it's a win
    if (args.result === 'win') {
      const war = await ctx.db.get(args.warId);
      if (war) {
        await ctx.db.patch(args.warId, {
          ourScore: war.ourScore + 1,
          updatedAt: now,
        });
      }
    }

    return attackId;
  },
});

// Batch import war attacks
export const batchImportWarAttacks = mutation({
  args: {
    warId: v.id("wars"),
    attacks: v.array(v.object({
      attackerId: v.number(),
      attackerName: v.string(),
      defenderId: v.number(),
      defenderName: v.string(),
      result: v.string(),
      respect: v.number(),
      chain: v.optional(v.number()),
      timestamp: v.number(),
    })),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "wars.manage");

    const results = [];
    const now = Date.now();
    let ourWins = 0;

    for (const attack of args.attacks) {
      // Check if attack already exists
      const existing = await ctx.db
        .query("warAttacks")
        .withIndex("by_war", (q) => q.eq("warId", args.warId))
        .filter((q) => 
          q.and(
            q.eq(q.field("attackerId"), attack.attackerId),
            q.eq(q.field("defenderId"), attack.defenderId),
            q.eq(q.field("timestamp"), attack.timestamp)
          )
        )
        .first();

      if (!existing) {
        const attackId = await ctx.db.insert("warAttacks", {
          warId: args.warId,
          attackerId: attack.attackerId,
          attackerName: attack.attackerName,
          defenderId: attack.defenderId,
          defenderName: attack.defenderName,
          result: attack.result,
          respect: attack.respect,
          chain: attack.chain,
          timestamp: attack.timestamp,
          createdAt: now,
        });

        if (attack.result === 'win') {
          ourWins++;
        }

        results.push({ action: 'created', id: attackId });
      } else {
        results.push({ action: 'skipped', reason: 'duplicate' });
      }
    }

    // Update war score
    if (ourWins > 0) {
      const war = await ctx.db.get(args.warId);
      if (war) {
        await ctx.db.patch(args.warId, {
          ourScore: war.ourScore + ourWins,
          updatedAt: now,
        });
      }
    }

    return { processed: results.length, created: results.filter(r => r.action === 'created').length, results };
  },
});

// End war
export const endWar = mutation({
  args: { 
    warId: v.id("wars"),
    endTime: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "wars.edit");

    return await ctx.db.patch(args.warId, {
      status: "ended",
      endTime: args.endTime || Date.now(),
      updatedAt: Date.now(),
    });
  },
});

// Delete war (soft delete)
export const deleteWar = mutation({
  args: { warId: v.id("wars") },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "wars.delete");

    return await ctx.db.patch(args.warId, {
      isActive: false,
      updatedAt: Date.now(),
    });
  },
});

// Get faction war history
export const getFactionWarHistory = query({
  args: { 
    factionId: v.number(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("wars")
      .withIndex("by_faction", (q) => q.eq("factionId", args.factionId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("desc")
      .take(args.limit ?? 20);
  },
});

// Get all wars (alias for dashboard compatibility)
export const listWars = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query("wars")
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("desc")
      .collect();
  },
});

// Action to fetch active wars from Torn API
export const fetchWarsFromTornAPI = action({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("Not authenticated");
    }

    // Get user to retrieve their API key
    const user = await ctx.runQuery(internal.banking.getUserByClerkId, {
      clerkId: identity.subject,
    });

    if (!user) {
      throw new ConvexError("User not found");
    }

    if (!user.tornApiKey) {
      throw new ConvexError("API key required to fetch wars");
    }

    // Only fetch wars for our faction (53100)
    const factionId = 53100;

    try {
      // Fetch faction data including wars
      console.log("Fetching wars for faction", factionId);
      const response = await fetch(
        `https://api.torn.com/faction/${factionId}?selections=basic,rankedwars&key=${user.tornApiKey}`
      );

      if (!response.ok) {
        throw new ConvexError("Failed to fetch faction data from Torn API");
      }

      const data = await response.json();
      console.log("Torn API faction response:", JSON.stringify(data, null, 2));

      if (data.error) {
        throw new ConvexError("Torn API error: " + data.error.error);
      }

      // Import wars
      let imported = 0;
      let skipped = 0;

      if (data.rankedwars && Object.keys(data.rankedwars).length > 0) {
        console.log("Processing rankedwars:", Object.keys(data.rankedwars).length, "wars found");
        
        for (const [warId, warData] of Object.entries(data.rankedwars as Record<string, any>)) {
          console.log(`Processing war ${warId}:`, JSON.stringify(warData, null, 2));
          
          // Check if war already exists
          const existingWar = await ctx.runQuery(internal.wars.getWarByTornId, {
            tornWarId: parseInt(warId),
          });

          // Extract war data with correct field mapping
          // Find enemy faction (the one that's not us - faction 53100)
          let enemyFactionId = 0;
          let enemyFactionName = "Unknown";
          let ourScore = 0;
          let enemyScore = 0;
          
          if (warData.factions) {
            for (const [factionIdStr, factionData] of Object.entries(warData.factions)) {
              const factionIdNum = parseInt(factionIdStr);
              if (factionIdNum === 53100) {
                // This is our faction
                ourScore = factionData.score || 0;
              } else {
                // This is the enemy faction
                enemyFactionId = factionIdNum;
                enemyFactionName = factionData.name || "Unknown";
                enemyScore = factionData.score || 0;
              }
            }
          }
          
          const startTime = warData.war?.start ? warData.war.start * 1000 : Date.now(); // Convert to milliseconds
          const endTime = warData.war?.end ? warData.war.end * 1000 : undefined;
          
          // Debug timestamp conversion
          console.log("Timestamp conversion debug:", {
            rawStart: warData.war?.start,
            rawEnd: warData.war?.end,
            convertedStart: startTime,
            convertedEnd: endTime,
            startDate: new Date(startTime).toISOString(),
            endDate: endTime ? new Date(endTime).toISOString() : null,
            durationDays: endTime ? Math.floor((endTime - startTime) / (1000 * 60 * 60 * 24)) : null
          });
          const status = endTime ? "ended" : "active";

          console.log("Parsed war data:", {
            tornWarId: parseInt(warId),
            enemyFactionId,
            enemyFactionName,
            startTime,
            endTime,
            ourScore,
            enemyScore,
            status
          });

          if (!existingWar) {
            // Import new war
            await ctx.runMutation(internal.wars.importWar, {
              tornWarId: parseInt(warId),
              factionId: factionId,
              factionName: data.name,
              enemyFactionId,
              enemyFactionName,
              startTime,
              endTime,
              status,
              ourScore,
              enemyScore,
            });
            imported++;
          } else {
            // Update existing war
            await ctx.runMutation(internal.wars.updateExistingWar, {
              warId: existingWar._id,
              startTime,
              endTime,
              status,
              ourScore,
              enemyScore,
              enemyFactionName,
            });
            skipped++;
          }
        }
      } else {
        console.log("No rankedwars found in API response");
      }

      return {
        success: true,
        message: `Wars fetch completed. Imported: ${imported}, Updated: ${skipped}`,
        imported,
        updated: skipped,
      };
    } catch (error) {
      console.error("Failed to fetch wars:", error);
      throw new ConvexError(
        "Failed to fetch wars: " + (error instanceof Error ? error.message : "Unknown error")
      );
    }
  },
});

// Internal query to get war by Torn ID
export const getWarByTornId = query({
  args: { tornWarId: v.number() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("wars")
      .filter((q) => q.eq(q.field("tornWarId"), args.tornWarId))
      .first();
  },
});

// Internal mutation to import war
export const importWar = mutation({
  args: {
    tornWarId: v.number(),
    factionId: v.number(),
    factionName: v.string(),
    enemyFactionId: v.number(),
    enemyFactionName: v.string(),
    startTime: v.number(),
    endTime: v.optional(v.number()),
    status: v.string(),
    ourScore: v.number(),
    enemyScore: v.number(),
  },
  handler: async (ctx, args) => {
    console.log("importWar mutation received:", {
      tornWarId: args.tornWarId,
      enemyFactionName: args.enemyFactionName,
      enemyFactionId: args.enemyFactionId,
      ourScore: args.ourScore,
      enemyScore: args.enemyScore
    });
    
    const now = Date.now();
    const warId = await ctx.db.insert("wars", {
      tornWarId: args.tornWarId,
      factionId: args.factionId,
      factionName: args.factionName,
      enemyFactionId: args.enemyFactionId,
      enemyFactionName: args.enemyFactionName,
      startTime: args.startTime,
      endTime: args.endTime,
      status: args.status,
      ourScore: args.ourScore,
      enemyScore: args.enemyScore,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });
    
    console.log("War inserted successfully with ID:", warId);
    return warId;
  },
});

// Internal mutation to update existing war
export const updateExistingWar = mutation({
  args: {
    warId: v.id("wars"),
    startTime: v.number(),
    endTime: v.optional(v.number()),
    status: v.string(),
    ourScore: v.number(),
    enemyScore: v.number(),
    enemyFactionName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    console.log("updateExistingWar mutation received:", {
      warId: args.warId,
      startTime: args.startTime,
      endTime: args.endTime,
      ourScore: args.ourScore,
      enemyScore: args.enemyScore,
      enemyFactionName: args.enemyFactionName
    });
    
    const { warId, ...updates } = args;
    return await ctx.db.patch(warId, {
      ...updates,
      updatedAt: Date.now(),
    });
  },
});

// Import wars from Torn API (admin function)
export const importWarFromTornAPI = mutation({
  args: { 
    tornWarId: v.number(),
    factionId: v.number(),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "wars.manage");

    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    // This would integrate with the TornAPI to fetch and import a war
    // For now, return a placeholder response
    return { 
      success: true, 
      message: "War import functionality requires Torn API integration",
      warId: args.tornWarId 
    };
  },
});