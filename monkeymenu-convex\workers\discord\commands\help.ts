import { CommandContext, BaseSlashCreator } from "slash-create";
import { BaseCommand } from "./base";

export class HelpCommand extends BaseCommand {
  constructor(creator: BaseSlashCreator, convexUrl: string) {
    super(creator, convexUrl, {
      name: "help",
      description: "Show available commands and help information",
      options: [],
    });
  }

  async run(ctx: CommandContext) {
    const discordId = ctx.user.id;

    try {
      // Handle the help command through Convex
      const result = await this.handleBotCommand(discordId, "help") as any;

      if (result?.success) {
        if (result.embed) {
          return this.createEmbedResponse(result.embed);
        } else {
          return this.createSuccessResponse(result.message || "Help information retrieved successfully");
        }
      } else {
        return this.createErrorResponse(result?.message || "❌ Failed to retrieve help information");
      }
    } catch (error) {
      console.error("Help command error:", error);
      return this.createErrorResponse("❌ An error occurred while retrieving help information.");
    }
  }
}
