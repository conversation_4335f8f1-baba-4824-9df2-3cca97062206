import { HasPermission } from "@/components/permissions/PermissionGuards";
import { PageContainer } from "@/components/ui/page-container";
import { PERMISSION_NAMES } from "@monkeymenu/shared";
import { createFileRoute } from "@tanstack/react-router";
import { Banking } from "./-components/banking/";

export const Route = createFileRoute("/(faction)/banking")({
	component: BankingPage,
});

function BankingPage() {
	return (
		<HasPermission permission={PERMISSION_NAMES.BANKING_VIEW}>
			<PageContainer>
				<Banking />
			</PageContainer>
		</HasPermission>
	);
}
