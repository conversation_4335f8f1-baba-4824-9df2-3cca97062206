// Database entity types that are shared between frontend and backend

export interface Guide {
	id: number;
	title: string;
	content: string;
	authorId: string;
	createdAt: string;
	updatedAt: string;
}

export interface GuideWithAuthor {
	guide: Guide;
	author: {
		id: string;
		name: string | null;
		image: string | null;
	};
}

export interface UserProfile {
	id: string;
	name: string | null;
	email: string;
	emailVerified: boolean;
	image: string | null;
	createdAt: string | Date;
	updatedAt: string | Date;
}

export interface TornUser {
	id: string;
	tornApiKey: string | null;
	tornApiKeyVerified: boolean;
	tornUserId: string | null;
	tornFactionId: string | null;
	tornApiKeyLastCheckedAt: Date | null;
	// Access suspension tracking
	accessSuspended: boolean;
	accessSuspensionReason: string | null;
	accessSuspendedAt: Date | null;
	lastTornApiError: number | null;
	lastTornApiErrorAt: Date | null;
}

export interface UserWithTornInfo {
	user: UserProfile;
	tornUser: TornUser | null;
}

// API Response types
export interface APIResponse<T = unknown> {
	success: boolean;
	message: string;
	data?: T;
}

export interface PaginatedResponse<T> {
	items: T[];
	total: number;
	page: number;
	limit: number;
	hasMore: boolean;
}

// Common error types
export interface APIError {
	code: string;
	message: string;
	details?: Record<string, unknown>;
}

// Torn API types
export interface TornAPIResponse {
	player_id?: number;
	faction?: {
		faction_id?: number;
		position?: string;
	};
	error?: {
		code: number;
		error: string;
	};
}

// Auth session types (compatible with better-auth)
export interface AuthSession {
	userId: string;
	expiresAt: Date;
	token: string;
	ipAddress?: string;
	userAgent?: string;
}

export interface AuthUser {
	id: string;
	name: string;
	email: string;
	emailVerified: boolean;
	image?: string;
	createdAt: Date;
	updatedAt: Date;
}
