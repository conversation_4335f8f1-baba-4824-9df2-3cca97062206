{"name": "web", "private": true, "type": "module", "scripts": {"dev": "pnpm env:sync && vite --port 3001", "build": "pnpm env:sync:prod && tsc -b && vite build", "env:sync": "doppler secrets download --no-file --format env > .env.development", "env:sync:prod": "doppler secrets download --no-file --format env --config prd > .env.production", "check-types": "tsc --noEmit", "format": "biome format", "lint": "biome lint", "check": "biome check"}, "dependencies": {"@icons-pack/react-simple-icons": "^12.9.0", "@livestore/adapter-web": "^0.3.1", "@livestore/devtools-vite": "^0.3.1", "@livestore/livestore": "^0.3.1", "@livestore/peer-deps": "^0.3.1", "@livestore/react": "^0.3.1", "@livestore/sync-cf": "^0.3.1", "@livestore/wa-sqlite": "1.0.5-dev.2", "@monkeymenu/shared": "workspace:*", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-form": "^1.12.0", "@tanstack/react-query": "^5.79.0", "@tanstack/react-router": "^1.120.13", "@tanstack/react-store": "^0.7.1", "@trpc/client": "^11.3.1", "@trpc/server": "^11.3.1", "@trpc/tanstack-react-query": "^11.3.1", "better-auth": "^1.2.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "highlight.js": "^11.11.1", "input-otp": "^1.4.2", "lucide-react": "^0.476.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.4", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.3", "zod": "^3.25.46"}, "devDependencies": {"@tanstack/react-query-devtools": "^5.79.0", "@tanstack/react-router-devtools": "^1.120.13", "@tanstack/router-plugin": "^1.120.13", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.5.0", "rollup-plugin-visualizer": "^6.0.1", "typescript": "^5.8.3", "vite": "^6.3.5", "wrangler": "^4.18.0"}}