import { But<PERSON> } from "../../ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  <PERSON><PERSON>T<PERSON>le,
  DialogTrigger,
} from "../../ui/dialog";
import { Input } from "../../ui/input";
import { Label } from "../../ui/label";
import { useState } from "react";
// import { useMutation } from "convex/react";
// import { api } from "../../../../convex/_generated/api";

interface WithdrawalDialogTriggerProps {
  children: React.ReactNode;
}

export function WithdrawalDialogTrigger({ children }: WithdrawalDialogTriggerProps) {
  const [open, setOpen] = useState(false);
  const [amount, setAmount] = useState("");
  const [reason, setReason] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // TODO: Add Convex mutation for withdrawal requests
  // const createWithdrawalRequest = useMutation(api.banking.createWithdrawalRequest);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!amount || isNaN(Number(amount))) {
      alert("Please enter a valid amount");
      return;
    }

    setIsSubmitting(true);
    
    try {
      // TODO: Replace with actual Convex mutation
      // await createWithdrawalRequest({
      //   amount: Number(amount),
      //   reason: reason.trim() || undefined,
      // });
      
      // Mock success for now
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert(`Withdrawal request for $${Number(amount).toLocaleString()} submitted successfully!`);
      setAmount("");
      setReason("");
      setOpen(false);
    } catch (error) {
      console.error("Failed to submit withdrawal request:", error);
      alert("Failed to submit withdrawal request. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Request Withdrawal</DialogTitle>
          <DialogDescription>
            Submit a withdrawal request from faction funds. Requests require approval from leadership.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="amount" className="text-right">
                Amount
              </Label>
              <Input
                id="amount"
                type="number"
                placeholder="50000"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="col-span-3"
                min="1"
                max="10000000"
                step="1"
                required
                disabled={isSubmitting}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="reason" className="text-right">
                Reason
              </Label>
              <Input
                id="reason"
                placeholder="Drug money, armor, etc. (optional)"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="col-span-3"
                maxLength={200}
                disabled={isSubmitting}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Submitting..." : "Submit Request"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}