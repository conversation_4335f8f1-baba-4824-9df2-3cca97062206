import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { usePermissions, useRoles } from "@/hooks/usePermissions";
import { Settings, Shield } from "lucide-react";
import { RoleHierarchy } from "./RoleHierarchy";
import type { SystemInfoDialogProps } from "./types";

export function SystemInfoDialog({ isOpen, onClose }: SystemInfoDialogProps) {
	const permissions = usePermissions();
	const roles = useRoles();

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-h-[90vh] w-full max-w-6xl overflow-y-auto px-4 sm:px-6">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<Settings className="h-5 w-5" />
						System Information
					</DialogTitle>
					<DialogDescription>
						Complete overview of system roles, permissions, and configuration
					</DialogDescription>
				</DialogHeader>

				<Tabs defaultValue="roles" className="w-full">
					<TabsList>
						<TabsTrigger value="roles" className="flex items-center gap-2">
							<Shield className="h-4 w-4" />
							Roles & Permissions
						</TabsTrigger>
					</TabsList>

					<TabsContent value="roles" className="space-y-4">
						<RoleHierarchy
							roles={roles.data}
							isLoading={roles.isLoading}
							currentUserRoleName={permissions.data?.roleName}
						/>
					</TabsContent>
				</Tabs>
			</DialogContent>
		</Dialog>
	);
}
