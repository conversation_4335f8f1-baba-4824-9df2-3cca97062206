import { FACTION_ROLES, PERMISSIONS } from "@monkeymenu/shared";
import { and, eq } from "drizzle-orm";
import {
	type CommandContext,
	CommandOptionType,
	SlashCommand,
	type SlashCreator,
} from "slash-create/web";
import { initDb } from "../../db";
import { user as userTable } from "../../db/schema/auth";
import { account as accountTable } from "../../db/schema/auth";
import { tornUser } from "../../db/schema/torn";
import { initCrypto } from "../../lib/crypto";
import { checkDiscordUserPermission } from "../../lib/discord-permissions";
import { getUserPermissionContext } from "../../lib/permissions";
import { verifyAndUpdateUserTornInfo } from "../../lib/tornUserUtils";
import type { AppBindings } from "../../lib/types";
import { syncDiscordRoles } from "../lib/role-sync";

export default class VerifyCommand extends SlashCommand {
	constructor(creator: <PERSON><PERSON><PERSON><PERSON>) {
		super(creator, {
			name: "verify",
			description: "Verify your Discord account and sync roles",
			options: [
				{
					type: CommandOptionType.USER,
					name: "user",
					description: "User to verify (admin only)",
					required: false,
				},
			],
		});
	}

	async run(ctx: CommandContext) {
		const discordUserId = ctx.user.id;
		const discordUserTag = `${ctx.user.username}#${ctx.user.discriminator}`;
		const targetUser = ctx.options.user;
		const isAdminVerification = !!targetUser;

		await ctx.defer(true); // Ephemeral response

		try {
			// Get environment from creator options
			const env = (this.creator.options as { env?: AppBindings["Bindings"] })
				.env;
			if (!env) {
				throw new Error("Environment not available in creator options");
			}
			const db = initDb(env);

			// For admin verification, check if user has permission to verify others
			if (isAdminVerification) {
				const permissionCheck = await checkDiscordUserPermission(
					db,
					discordUserId,
					PERMISSIONS.ADMIN_VIEW.name,
				);

				if (!permissionCheck.isLinked) {
					return {
						content:
							"❌ Your Discord account is not linked to MonkeyMenu. Please link your account first.",
						ephemeral: true,
					};
				}

				if (!permissionCheck.hasPermission) {
					return {
						content:
							"❌ You do not have permission to verify other users. (Requires: Role Management permission)",
						ephemeral: true,
					};
				}
			}

			// Determine which Discord user to verify
			const targetDiscordUserId = isAdminVerification
				? targetUser.id
				: discordUserId;
			const targetDiscordUserTag = isAdminVerification
				? `${targetUser.username}#${targetUser.discriminator}`
				: discordUserTag;

			// Check if target user has linked Discord account
			const userAccount = await db
				.select({
					userId: userTable.id,
					userName: userTable.name,
					tornUserId: tornUser.tornUserId,
					tornApiKey: tornUser.tornApiKey,
					tornApiKeyVerified: tornUser.tornApiKeyVerified,
					accessSuspended: tornUser.accessSuspended,
				})
				.from(accountTable)
				.innerJoin(userTable, eq(accountTable.userId, userTable.id))
				.leftJoin(tornUser, eq(userTable.id, tornUser.id))
				.where(
					and(
						eq(accountTable.providerId, "discord"),
						eq(accountTable.accountId, targetDiscordUserId),
					),
				)
				.get();

			if (!userAccount) {
				return {
					content: isAdminVerification
						? `❌ ${targetDiscordUserTag} has not linked their Discord account to MonkeyMenu.`
						: "❌ Your Discord account is not linked to MonkeyMenu. Please visit https://monkeymenu.app/settings to link your account.",
					ephemeral: true,
				};
			}

			// Check if user has access suspension
			if (userAccount.accessSuspended) {
				return {
					content: isAdminVerification
						? `❌ ${targetDiscordUserTag} has suspended access and cannot be verified.`
						: "❌ Your access is currently suspended. Please contact an administrator.",
					ephemeral: true,
				};
			}

			// Initialize processing response
			const processingEmbed = {
				color: 0xffa500, // Orange
				title: "🔄 Processing Verification",
				description: isAdminVerification
					? `Verifying ${targetDiscordUserTag} and syncing their roles...`
					: "Verifying your account and syncing your roles...",
				fields: [
					{
						name: "👤 Target User",
						value: targetDiscordUserTag,
						inline: true,
					},
					{
						name: "⚙️ Actions",
						value:
							"• Checking Torn API\n• Updating roles\n• Syncing Discord roles",
						inline: true,
					},
				],
				footer: {
					text: "MonkeyMenu Verification System",
				},
				timestamp: new Date().toISOString(),
			};

			await ctx.editOriginal({
				embeds: [processingEmbed],
			});

			// Get current role and permission context (for logging purposes)

			// If user has API key, re-verify and update roles
			let verificationResult = null;
			if (userAccount.tornApiKey && userAccount.tornApiKeyVerified) {
				try {
					initCrypto(env.TORN_API_KEY_ENCRYPTION_SECRET);
					verificationResult = await verifyAndUpdateUserTornInfo(
						db,
						userAccount.userId,
						userAccount.tornApiKey,
						env,
					);
				} catch (error) {
					console.error(
						`Error re-verifying user ${userAccount.userId}:`,
						error,
					);
				}
			}

			// Get updated role information
			const updatedPermissions = await getUserPermissionContext(
				db,
				userAccount.userId,
			);

			// Check if user is in the faction (has verified API key and in correct faction)
			const isInFaction = Boolean(
				userAccount.tornApiKeyVerified &&
					userAccount.tornUserId &&
					!userAccount.accessSuspended &&
					(verificationResult ? verificationResult.success : true),
			);

			// Sync Discord roles
			const roleSync = await syncDiscordRoles(
				ctx.guildID || "",
				targetDiscordUserId,
				updatedPermissions.roleName,
				env.DISCORD_BOT_TOKEN,
				isInFaction,
			);

			console.info(
				`[VERIFICATION] ${isAdminVerification ? "Admin" : "Self"} verification completed for ${targetDiscordUserTag} (Discord: ${targetDiscordUserId}, User: ${userAccount.userId}). Role: ${updatedPermissions.roleName || "None"}. Discord sync: ${roleSync.success ? "Success" : "Failed"}`,
			);

			// Create success response
			const successEmbed = {
				color: roleSync.success ? 0x00ff00 : 0xffaa00, // Green if success, yellow if partial
				title: "✅ Verification Complete",
				description: isAdminVerification
					? `Successfully verified ${targetDiscordUserTag}`
					: "Your account has been successfully verified!",
				fields: [
					{
						name: "👤 User",
						value: `${userAccount.userName || targetDiscordUserTag}`,
						inline: true,
					},
					{
						name: "🎭 Current Role",
						value: updatedPermissions.roleName
							? FACTION_ROLES[
									updatedPermissions.roleName as keyof typeof FACTION_ROLES
								]?.displayName || updatedPermissions.roleName
							: "No role assigned",
						inline: true,
					},
					{
						name: "🔗 Torn User ID",
						value: userAccount.tornUserId || "Not linked",
						inline: true,
					},
					{
						name: "📊 Verification Status",
						value: verificationResult
							? verificationResult.success
								? "✅ API Re-verified"
								: "⚠️ API Check Failed"
							: userAccount.tornApiKeyVerified
								? "✅ Previously Verified"
								: "❌ Not Verified",
						inline: true,
					},
					{
						name: "🎮 Discord Roles",
						value: roleSync.success ? "✅ Synced" : `⚠️ ${roleSync.message}`,
						inline: true,
					},
				],
				footer: {
					text: "MonkeyMenu Verification System",
				},
				timestamp: new Date().toISOString(),
			};

			// Add additional info if verification failed
			if (verificationResult && !verificationResult.success) {
				successEmbed.fields.push({
					name: "⚠️ API Verification Issue",
					value: verificationResult.message,
					inline: false,
				});
			}

			return await ctx.editOriginal({
				embeds: [successEmbed],
			});
		} catch (error) {
			console.error(
				`[ERROR] Verification failed. Initiator: ${discordUserTag} (${discordUserId}), Target: ${isAdminVerification ? `${targetUser.username}#${targetUser.discriminator} (${targetUser.id})` : "self"}`,
				error,
			);

			// Specific error handling
			if (error instanceof Error) {
				if (error.message.includes("Environment not available")) {
					return {
						content:
							"❌ System configuration error. Please contact an administrator.",
						ephemeral: true,
					};
				}
				if (error.message.includes("Permission check failed")) {
					return {
						content: "❌ Unable to verify permissions. Please try again later.",
						ephemeral: true,
					};
				}
			}

			return {
				content:
					"❌ An error occurred during verification. Please try again later.",
				ephemeral: true,
			};
		}
	}
}
