import { Loader } from "@/components/navbar/loader";
import { authClient } from "@/lib/auth-client";
import { trpc } from "@/lib/trpc-client";
import { useQuery } from "@tanstack/react-query";
import {
	Navigate,
	Outlet,
	createFileRoute,
	useRouterState,
} from "@tanstack/react-router";

export const Route = createFileRoute("/(protected)")({
	component: Layout,
});

function Layout() {
	const { data: session, isPending } = authClient.useSession();
	const { isLoading: isProfileLoading, error: profileError } = useQuery({
		...trpc.user.getProfile.queryOptions(),
		enabled: !!session?.user,
	});
	const routerState = useRouterState();

	if (isPending || (session?.user && isProfileLoading)) {
		return <Loader />;
	}

	if (!session?.user) {
		return (
			<Navigate
				to="/sign-in"
				search={{ redirect: routerState.location.pathname }}
			/>
		);
	}

	if (profileError) {
		console.error("Failed to load user profile:", profileError);
		return (
			<div className="p-4 text-red-500">
				Failed to load user profile. Please try again later.
			</div>
		);
	}

	return <Outlet />;
}
