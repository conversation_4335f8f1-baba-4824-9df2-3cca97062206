import type { AnnouncementCategory } from "./types";

export const ANNOUNCEMENT_CATEGORIES: AnnouncementCategory[] = [
	{ id: "general", label: "General", emoji: "📋" },
	{ id: "war", label: "War", emoji: "⚔️" },
	{ id: "crimes", label: "Crimes", emoji: "🔫" },
];

export function getCategoryInfo(categoryId: string): AnnouncementCategory {
	return (
		ANNOUNCEMENT_CATEGORIES.find((cat) => cat.id === categoryId) ||
		ANNOUNCEMENT_CATEGORIES[0]
	);
}

export function getPreviewText(content: string, maxLength = 150): string {
	const plainText = content
		.replace(/#{1,6}\s+/g, "")
		.replace(/\*\*(.*?)\*\*/g, "$1")
		.replace(/\*(.*?)\*/g, "$1")
		.replace(/`(.*?)`/g, "$1")
		.replace(/\[(.*?)\]\(.*?\)/g, "$1")
		.replace(/\n+/g, " ")
		.trim();

	return plainText.length > maxLength
		? `${plainText.substring(0, maxLength)}...`
		: plainText;
}
