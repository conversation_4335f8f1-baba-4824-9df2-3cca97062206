import { Server, ServerRequestHandler } from '../server';
/**
 * A server for Azure Function integration
 * @see https://docs.microsoft.com/en-us/azure/azure-functions/
 * @deprecated Use `AzureFunctionV4Server` as that will supercede this server in the future.
 */
export declare class AzureFunctionServer extends Server {
    private _handler?;
    constructor(moduleExports: any, target?: string);
    private _onRequest;
    /** @private */
    createEndpoint(path: string, handler: ServerRequestHandler): void;
}
/**
 * A server for Azure Function V4 integration
 * @see https://docs.microsoft.com/en-us/azure/azure-functions/
 */
export declare class AzureFunctionV4Server extends Server {
    private _handler?;
    constructor(app: any, target?: string);
    private _onRequest;
    /** @private */
    createEndpoint(path: string, handler: Server<PERSON>equestHandler): void;
}
