# Discord Integration

This directory contains the Discord bot integration for MonkeyMenu using [slash-create](https://github.com/Snazzah/slash-create) and Cloudflare Workers.

## Features

- **Slash Commands**: `/withdraw`, `/verify`, `/sendverifyembed`
- **Button Interactions**: Verification button handling (coming soon)
- **Database Integration**: Direct access to MonkeyMenu database
- **Serverless**: Runs on Cloudflare Workers alongside the main app

## Setup

### 1. Discord Application Setup

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create a new application or use existing one
3. Go to "Bot" section and create a bot
4. Copy the following values:
   - **Application ID** (from General Information)
   - **Public Key** (from General Information)
   - **Bot Token** (from Bot section)

### 2. Environment Variables

Add these to your `.dev.vars` file:

```env
DISCORD_APPLICATION_ID=your_discord_application_id
DISCORD_PUBLIC_KEY=your_discord_public_key
DISCORD_BOT_TOKEN=your_discord_bot_token
DISCORD_GUILD_ID=your_discord_guild_id_for_testing
```

### 3. Set Interactions Endpoint URL

In the Discord Developer Portal, go to General Information and set:
- **Interactions Endpoint URL**: `https://your-worker-domain.com/discord/interactions`

### 4. Register Commands

Run the command sync script:

```bash
pnpm discord:sync
```

This will register your slash commands with Discord.

## Commands

### `/withdraw <amount>`
- Allows users to request withdrawals from the faction bank
- Requires linked Discord account and verified Torn API key
- Creates a withdrawal request in the database

### `/verify [user]`
- Placeholder for verification functionality
- Will sync Discord roles with app permissions

### `/sendverifyembed`
- Admin-only command to send verification embed
- Creates an embed with verification instructions and button

## Architecture

The integration follows the [slash-create-worker](https://github.com/Snazzah/slash-create-worker) template pattern:

1. **SlashCreator** handles Discord interactions
2. **CloudflareWorkerServer** processes requests in the worker
3. Commands are registered as classes extending **SlashCommand**
4. Environment variables are passed through creator options

## Development

- Commands are in `./commands/` directory
- Main integration is in `./index.ts`
- Sync script is in `./sync.ts`
- Use `pnpm discord:sync` to register commands during development

## Migration from Fly.io Bot

This replaces the following functionality from your existing Fly.io bot:
- ✅ Withdrawal commands
- ✅ Verification embeds
- ⏳ Role verification (needs implementation)
- ❌ Cron jobs (keep on Fly.io)
- ❌ Complex event handling (keep on Fly.io)

## Next Steps

1. **Implement role verification logic** in `commands/verify.ts`
2. **Add button interaction handling** for verification
3. **Test with your Discord server**
4. **Migrate users from old bot commands**
5. **Update Discord bot permissions** as needed

## Troubleshooting

- **Commands not appearing**: Wait up to 1 hour for global commands, or use guild-specific commands for testing
- **Signature verification failed**: Check your public key is correct
- **Database errors**: Ensure user has linked Discord account on website 