import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ExternalLink, ArrowRight, AlertCircle } from "lucide-react";
import { useState } from "react";
import { EXTERNAL_URLS } from "./config";
import type { StepComponentProps } from "./types";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface TornSetupStepProps extends StepComponentProps {
  onApiKeySubmit?: (apiKey: string) => Promise<void>;
}

export function TornSetupStep({ onNextStep, onPrevStep, onApiKeySubmit }: TornSetupStepProps) {
  const [apiKey, setApiKey] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError("");
    
    if (!apiKey.trim()) {
      setError("Please provide your API key");
      return;
    }

    if (apiKey.length !== 16) {
      setError("API key must be 16 characters long");
      return;
    }

    setIsSubmitting(true);
    
    try {
      if (onApiKeySubmit) {
        await onApiKeySubmit(apiKey.trim());
      }
      onNextStep();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to verify Torn account");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardContent className="space-y-6 p-8">
        <div className="space-y-2 text-center">
          <h2 className="font-bold text-2xl tracking-tight">
            Connect Your Torn Account
          </h2>
          <p className="mx-auto max-w-md text-muted-foreground leading-relaxed">
            We need your API key to verify your faction membership
            and set up the right permissions.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="apiKey">API Key</Label>
            <Input
              id="apiKey"
              type="password"
              placeholder="Enter your 16-character API key"
              value={apiKey}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setApiKey(e.target.value)}
              disabled={isSubmitting}
              className="font-mono"
              maxLength={16}
            />
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="text-center">
            <a
              href={EXTERNAL_URLS.TORN_API_SETTINGS}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-1 text-primary text-sm underline hover:text-primary/80"
            >
              Find your API key in Torn settings
              <ExternalLink className="h-3 w-3" />
            </a>
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onPrevStep}
              disabled={isSubmitting}
              className="flex-1"
            >
              Back
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !apiKey.trim()}
              className="flex-1"
            >
              {isSubmitting ? "Verifying..." : "Continue"}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}