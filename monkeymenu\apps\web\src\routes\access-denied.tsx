import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { PageContainer } from "@/components/ui/page-container";
import { Link, createFileRoute } from "@tanstack/react-router";
import { <PERSON><PERSON><PERSON>riangle, ArrowLeft, Home, Shield } from "lucide-react";

export const Route = createFileRoute("/access-denied")({
	component: AccessDeniedComponent,
});

function AccessDeniedComponent() {
	return (
		<PageContainer
			variant="narrow"
			className="flex min-h-[calc(100vh-4rem)] items-center justify-center"
		>
			<Card className="w-full max-w-md">
				<CardHeader className="text-center">
					<div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-destructive/10">
						<Shield className="h-8 w-8 text-destructive" />
					</div>
					<CardTitle className="flex items-center justify-center gap-2 text-2xl">
						<AlertTriangle className="h-6 w-6 text-destructive" />
						Access Denied
					</CardTitle>
					<CardDescription className="text-base">
						You don't have permission to view this page
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4 text-center">
					<p className="text-muted-foreground text-sm">
						This page requires special permissions or faction membership. If you
						believe this is an error, please contact an administrator.
					</p>

					<div className="space-y-3 pt-4">
						<Button asChild className="w-full">
							<Link to="/">
								<Home className="mr-2 h-4 w-4" />
								Go to Homepage
							</Link>
						</Button>

						<Button asChild variant="outline" className="w-full">
							<Link to="/dashboard">
								<ArrowLeft className="mr-2 h-4 w-4" />
								Back to Dashboard
							</Link>
						</Button>
					</div>

					<div className="pt-4 text-center">
						<p className="text-muted-foreground text-xs">
							Need help? Contact a faction leader or check our{" "}
							<Link to="/guides" className="underline hover:text-foreground">
								guides
							</Link>
						</p>
					</div>
				</CardContent>
			</Card>
		</PageContainer>
	);
}
