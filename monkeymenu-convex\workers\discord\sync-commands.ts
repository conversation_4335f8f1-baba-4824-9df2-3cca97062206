import { <PERSON><PERSON><PERSON><PERSON> } from "slash-create";

// Import commands
import { BalanceCommand } from "./commands/balance";
import { WithdrawCommand } from "./commands/withdraw";
import { TargetsCommand } from "./commands/targets";
import { WarCommand } from "./commands/war";
import { HelpCommand } from "./commands/help";
import { LinkCommand } from "./commands/link";

async function syncDiscordCommands() {
  const applicationId = process.env.DISCORD_APPLICATION_ID;
  const botToken = process.env.DISCORD_BOT_TOKEN;
  const guildId = process.env.DISCORD_GUILD_ID; // Optional: for guild-specific commands
  const convexUrl = process.env.CONVEX_URL || process.env.VITE_CONVEX_URL;

  if (!applicationId || !botToken || !convexUrl) {
    console.error("Missing required environment variables:");
    console.error("- DISCORD_APPLICATION_ID");
    console.error("- DISCORD_BOT_TOKEN");
    console.error("- CONVEX_URL (or VITE_CONVEX_URL)");
    process.exit(1);
  }

  try {
    console.log("🤖 Syncing Discord slash commands...");

    const creator = new SlashCreator({
      applicationID: applicationId,
      token: botToken,
    });

    // Register commands
    creator.registerCommand(new BalanceCommand(creator, convexUrl));
    creator.registerCommand(new WithdrawCommand(creator, convexUrl));
    creator.registerCommand(new TargetsCommand(creator, convexUrl));
    creator.registerCommand(new WarCommand(creator, convexUrl));
    creator.registerCommand(new HelpCommand(creator, convexUrl));
    creator.registerCommand(new LinkCommand(creator, convexUrl));

    // Sync commands
    if (guildId) {
      console.log(`📡 Syncing commands for guild: ${guildId}`);
      await creator.syncCommandsIn(guildId);
    } else {
      console.log("🌍 Syncing global commands (may take up to 1 hour to propagate)");
      await creator.syncCommands();
    }

    console.log("✅ Discord commands synced successfully!");
    process.exit(0);
  } catch (error) {
    console.error("❌ Failed to sync Discord commands:", error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  syncDiscordCommands();
}

export { syncDiscordCommands };
