import { Command<PERSON>ontext, BaseSlashCreator } from "slash-create";
import { BaseCommand } from "./base";

export class Link<PERSON>ommand extends BaseCommand {
  constructor(creator: BaseSlashCreator, convexUrl: string) {
    super(creator, convexUrl, {
      name: "link",
      description: "Check your account linking status",
      options: [],
    });
  }

  async run(ctx: CommandContext) {
    const discordId = ctx.user.id;

    try {
      // Handle the link command through Convex
      const result = await this.handleBotCommand(discordId, "link") as any;

      if (result?.success) {
        if (result.embed) {
          return this.createEmbedResponse(result.embed);
        } else {
          return this.createSuccessResponse(result.message || "Account linking status retrieved successfully");
        }
      } else {
        return this.createErrorResponse(result?.message || "❌ Failed to retrieve account linking status");
      }
    } catch (error) {
      console.error("Link command error:", error);
      return this.createErrorResponse("❌ An error occurred while checking your account linking status.");
    }
  }
}
