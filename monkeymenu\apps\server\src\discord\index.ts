import { <PERSON>flare<PERSON><PERSON>kerServer, SlashCreator } from "slash-create/web";
import type { AppBindings } from "../lib/types";
import {
	BalanceCommand,
	SendVerifyEmbedCommand,
	VerifyCommand,
	WithdrawCommand,
} from "./commands";

// Extend SlashCreator options to include our environment
interface ExtendedSlashCreator extends SlashCreator {
	options: SlashCreator["options"] & { env?: AppBindings["Bindings"] };
}

const cfServer = new CloudflareWorkerServer();
let creator: ExtendedSlashCreator;

// Since we only get our secrets on fetch, set them before running
function makeCreator(env: AppBindings["Bindings"]) {
	creator = new SlashCreator({
		applicationID: env.DISCORD_APPLICATION_ID,
		publicKey: env.DISCORD_PUBLIC_KEY,
		token: env.DISCORD_BOT_TOKEN,
	}) as ExtendedSlashCreator;

	// Pass environment to commands
	creator.options.env = env;

	creator.withServer(cfServer);

	// Register commands
	creator.registerCommand(WithdrawCommand);
	creator.registerCommand(BalanceCommand);
	creator.registerCommand(VerifyCommand);
	creator.registerCommand(SendVerifyEmbedCommand);

	creator.on("warn", (message) => console.warn(message));
	creator.on("error", (error) =>
		console.error(error.stack || error.toString()),
	);
	creator.on("commandRun", (command, _, ctx) =>
		console.info(
			`${ctx.user.username}#${ctx.user.discriminator} (${ctx.user.id}) ran command ${command.commandName}`,
		),
	);
	creator.on("commandError", (command, error) =>
		console.error(
			`Command ${command.commandName} errored:`,
			error.stack || error.toString(),
		),
	);

	// Note: Button interactions removed - now using Link buttons that redirect to web endpoints
}

export function createDiscordBot(env: AppBindings["Bindings"]) {
	if (!creator) makeCreator(env);
	return { creator, server: cfServer };
}

// Export command definitions for registration
export const DISCORD_COMMANDS = [
	{
		name: "withdraw",
		description: "Request a withdrawal from the faction bank",
		options: [
			{
				type: 4, // INTEGER
				name: "amount",
				description: "The amount you want to withdraw",
				required: true,
				min_value: 1,
			},
		],
	},
	{
		name: "balance",
		description: "Check the current faction bank balance",
	},
	{
		name: "verify",
		description:
			"Verify your account and sync Discord roles (or verify another user)",
		options: [
			{
				type: 6, // USER
				name: "user",
				description: "User to verify (requires role management permission)",
				required: false,
			},
		],
	},
	{
		name: "sendverifyembed",
		description: "Send a verification embed to a channel (System Admin only)",
		options: [
			{
				type: 7, // CHANNEL
				name: "channel",
				description:
					"Channel to send the verification embed to (defaults to current channel)",
				required: false,
				channel_types: [0], // GUILD_TEXT
			},
		],
	},
];
