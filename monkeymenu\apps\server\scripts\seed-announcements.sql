-- =============================================================================
-- ANNOUNCEMENTS SEEDING
-- Welcome announcement for new system launch
-- =============================================================================

-- Only insert if announcement with this title doesn't already exist
INSERT INTO announcement (title, content, category, author_id, created_at, updated_at) 
SELECT 
    'Welcome to MonkeyMenu!',
'🎉 **Welcome to your new faction management system!**

MonkeyMenu is now live and ready to help streamline our faction operations. Here''s what you can do:

## 🎯 **New Features Available**
- Target list management with 47+ pre-loaded targets
- Faction banking oversight with request management  
- Comprehensive guides system with 5 detailed tutorials
- Real-time announcements and updates
- Full role-based permission system

## 🚀 **Getting Started**
1. Complete your profile setup and API key verification
2. Explore the guides section for detailed instructions
3. Check out the pre-loaded target lists for faction operations
4. Set up Discord integration for notifications

## 🔐 **Security & Permissions**
Your access level is determined by your faction role (from Recruit to System Admin). Contact leadership if you need additional permissions.

## 🆘 **Need Help?**
Check the comprehensive guides section or reach out to faction leadership for assistance.

Let''s make this faction even stronger together! 💪🐒',
    'general',
    'system-user-monkeymenu',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
WHERE NOT EXISTS (
    SELECT 1 FROM announcement WHERE title = 'Welcome to MonkeyMenu!'
);

-- Verification
SELECT 'Announcements seeding completed!' as message;
SELECT COUNT(*) as total_announcements FROM announcement; 