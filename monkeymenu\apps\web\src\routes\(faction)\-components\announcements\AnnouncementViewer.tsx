import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Calendar, Clock, Megaphone, User } from "lucide-react";
import ReactMarkdown from "react-markdown";
import rehypeHighlight from "rehype-highlight";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";
import type { AnnouncementData } from "./types";

interface AnnouncementViewerProps {
	announcement: AnnouncementData | null;
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

export function AnnouncementViewer({
	announcement,
	open,
	onOpenChange,
}: AnnouncementViewerProps) {
	if (!announcement) return null;

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<Megaphone className="h-5 w-5" />
						{announcement.announcement.title}
					</DialogTitle>
					<DialogDescription>
						Read the full announcement content below.
					</DialogDescription>
					<div className="flex items-center gap-4 text-muted-foreground text-sm">
						<div className="flex items-center gap-1">
							<User className="h-4 w-4" />
							{announcement.author?.name || "Unknown"}
						</div>
						<div className="flex items-center gap-1">
							<Calendar className="h-4 w-4" />
							{new Date(
								announcement.announcement.createdAt,
							).toLocaleDateString()}
						</div>
						{announcement.announcement.updatedAt !==
							announcement.announcement.createdAt && (
							<div className="flex items-center gap-1">
								<Clock className="h-4 w-4" />
								Updated{" "}
								{new Date(
									announcement.announcement.updatedAt,
								).toLocaleDateString()}
							</div>
						)}
					</div>
				</DialogHeader>
				<div className="prose prose-sm dark:prose-invert max-w-none">
					<ReactMarkdown
						remarkPlugins={[remarkGfm]}
						rehypePlugins={[rehypeHighlight, rehypeRaw]}
					>
						{announcement.announcement.content}
					</ReactMarkdown>
				</div>
			</DialogContent>
		</Dialog>
	);
}
