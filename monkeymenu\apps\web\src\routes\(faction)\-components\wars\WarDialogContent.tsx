import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
	Clock,
	Crown,
	Flame,
	Swords,
	TrendingUp,
	Trophy,
	User,
	Users,
} from "lucide-react";
import { useMemo } from "react";
import type {
	ChainReportFromServer,
	CombinedChainStats,
	Faction,
	FactionMember,
	MemberStats,
	WarDialogContentProps,
	WarOverviewStats,
} from "./types";
import { MY_FACTION_ID, formatDuration } from "./utils";

export function WarDialogContent({
	warReport,
	chainReports,
	attacksData,
	warId,
	statusLabel,
	badgeVariant,
	onOpenChainDetails,
	onOpenPlayerPerformance,
	onOpenTimeline,
}: WarDialogContentProps) {
	// Guard against invalid data
	if (
		!warReport ||
		!warReport.factions ||
		!Array.isArray(warReport.factions) ||
		warReport.factions.length === 0
	) {
		return (
			<div className="flex items-center justify-center p-8">
				<div className="text-center">
					<div className="text-muted-foreground">Loading war report...</div>
				</div>
			</div>
		);
	}
	// Calculate overview stats
	const overviewStats: WarOverviewStats = useMemo(() => {
		const duration = warReport.end
			? formatDuration(warReport.end - warReport.start)
			: "Ongoing";

		const winner =
			warReport.factions.find((f: Faction) => f.id === warReport.winner)
				?.name ?? "Unknown";

		const status = warReport.forfeit ? "Forfeit" : "Completed";

		const totalChains = chainReports?.chainReports?.length || 0;
		const totalRespect =
			chainReports?.combinedChainStats?.reduce(
				(sum: number, player: CombinedChainStats) => sum + player.totalRespect,
				0,
			) || 0;
		return {
			duration,
			winner,
			status,
			totalChains,
			totalRespect,
		};
	}, [warReport, chainReports]);

	return (
		<div className="space-y-6">
			{/* War Overview Header - Always Visible */}
			<div className="rounded-lg border bg-card p-6">
				<div className="mb-4 flex items-center justify-between">
					<div className="flex items-center gap-3">
						<div className="text-2xl">⚔️</div>
						<div>
							<h3 className="font-bold text-xl">War #{warId}</h3>
							<Badge variant={badgeVariant} className="mt-1">
								{statusLabel}
							</Badge>
						</div>
					</div>
					<div className="text-right">
						<div className="text-muted-foreground text-sm">Winner</div>
						<div className="font-semibold text-lg">{overviewStats.winner}</div>
					</div>
				</div>

				{/* Key Metrics Grid */}
				<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
					<div className="rounded-lg bg-muted/50 p-3 text-center">
						<div className="font-bold text-2xl text-foreground">
							{overviewStats.duration}
						</div>
						<div className="text-muted-foreground text-sm">Duration</div>
					</div>
					<div className="rounded-lg bg-muted/50 p-3 text-center">
						<div className="font-bold text-2xl text-foreground">
							{overviewStats.totalChains}
						</div>
						<div className="text-muted-foreground text-sm">Chains</div>
					</div>
					<div className="rounded-lg bg-muted/50 p-3 text-center">
						<div className="font-bold text-2xl text-foreground">
							{overviewStats.totalRespect.toFixed(1)}
						</div>
						<div className="text-muted-foreground text-sm">Total Respect</div>
					</div>
				</div>
			</div>

			{/* Faction vs Faction Summary */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Users className="h-5 w-5" />
						Faction Performance Summary
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid gap-4 md:grid-cols-2">
						{warReport.factions.map((faction: Faction) => {
							const isOurFaction = faction.id === MY_FACTION_ID;
							const isWinner = faction.id === warReport.winner;

							return (
								<div
									key={faction.id}
									className={`rounded-lg border-2 p-4 ${
										isOurFaction
											? "border-primary bg-primary/5"
											: "border-border bg-muted/50"
									}`}
								>
									<div className="mb-3 flex items-center justify-between">
										<div className="flex items-center gap-2">
											{isWinner && (
												<Crown className="h-4 w-4 text-amber-600 dark:text-amber-400" />
											)}
											<span className="font-semibold">{faction.name}</span>
											{isOurFaction && (
												<Badge variant="outline" className="text-xs">
													Our Faction
												</Badge>
											)}
										</div>
									</div>

									<div className="grid grid-cols-2 gap-4 text-sm">
										<div>
											<div className="text-muted-foreground">Score</div>
											<div className="font-bold text-lg">{faction.score}</div>
										</div>
										<div>
											<div className="text-muted-foreground">War Hits</div>
											<div className="font-bold text-lg">{faction.attacks}</div>
										</div>
									</div>

									<div className="mt-3">
										<div className="mb-1 text-muted-foreground text-xs">
											Active Members: {faction.members.length}
										</div>
										<Progress
											value={
												(faction.score /
													Math.max(
														...warReport.factions.map((f: Faction) => f.score),
													)) *
												100
											}
											className="h-2"
										/>
									</div>
								</div>
							);
						})}
					</div>
				</CardContent>
			</Card>

			{/* Tabbed Interface for Detailed Analysis */}
			<Tabs defaultValue="chains" className="w-full">
				<TabsList className="grid w-full grid-cols-3">
					<TabsTrigger value="chains" className="flex items-center gap-2">
						<span>🔗</span>
						<span className="hidden sm:inline">Chain Performance</span>
						<span className="sm:hidden">Chains</span>
					</TabsTrigger>
					<TabsTrigger value="attacks" className="flex items-center gap-2">
						<span>⚔️</span>
						<span className="hidden sm:inline">Individual Attacks</span>
						<span className="sm:hidden">Attacks</span>
					</TabsTrigger>
					<TabsTrigger value="members" className="flex items-center gap-2">
						<span>👥</span>
						<span className="hidden sm:inline">Member Details</span>
						<span className="sm:hidden">Members</span>
					</TabsTrigger>
				</TabsList>

				{/* Chain Performance Tab */}
				<TabsContent value="chains" className="space-y-4">
					{chainReports && chainReports.combinedChainStats.length > 0 && (
						<>
							{/* Combined Chain Performance Summary */}
							<Card>
								<CardHeader>
									<div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
										<CardTitle className="flex items-center gap-2">
											<TrendingUp className="h-5 w-5" />
											<span className="hidden sm:inline">
												Combined Chain Performance
											</span>
											<span className="sm:hidden">Chain Performance</span>
										</CardTitle>
										<Badge variant="outline" className="w-fit">
											{chainReports.combinedChainStats.length} players
										</Badge>
									</div>
									<CardDescription>
										Aggregated performance across all chains in this war
									</CardDescription>
								</CardHeader>
								<CardContent>
									{/* Desktop Table View */}
									<div className="hidden lg:block">
										<Table>
											<TableHeader>
												<TableRow>
													<TableHead>Player</TableHead>
													<TableHead>Chains</TableHead>
													<TableHead>War Hits</TableHead>
													<TableHead>Total Respect</TableHead>
													<TableHead>Avg Respect</TableHead>
													<TableHead>Best Respect</TableHead>
													<TableHead />
												</TableRow>
											</TableHeader>
											<TableBody>
												{chainReports.combinedChainStats
													.sort(
														(a: CombinedChainStats, b: CombinedChainStats) =>
															b.totalRespect - a.totalRespect,
													)
													.slice(0, 10) // Show top 10
													.map((player: CombinedChainStats) => (
														<TableRow key={player.playerId}>
															<TableCell className="font-medium">
																{player.playerName ||
																	`Player #${player.playerId}`}
															</TableCell>
															<TableCell>{player.chainCount}</TableCell>
															<TableCell>{player.totalWarHits}</TableCell>
															<TableCell className="font-semibold">
																{player.totalRespect.toFixed(2)}
															</TableCell>
															<TableCell>
																{player.averageRespect.toFixed(2)}
															</TableCell>
															<TableCell>
																{player.bestRespect.toFixed(2)}
															</TableCell>
															<TableCell>
																<Button
																	variant="ghost"
																	size="sm"
																	onClick={(e) => {
																		e.preventDefault();
																		onOpenPlayerPerformance(player.playerId);
																	}}
																	className="h-6 px-2"
																>
																	<User className="h-3 w-3" />
																</Button>
															</TableCell>
														</TableRow>
													))}
											</TableBody>
										</Table>
									</div>

									{/* Mobile Card View */}
									<div className="space-y-3 lg:hidden">
										{chainReports.combinedChainStats
											.sort(
												(a: CombinedChainStats, b: CombinedChainStats) =>
													b.totalRespect - a.totalRespect,
											)
											.slice(0, 10) // Show top 10
											.map((player: CombinedChainStats) => (
												<Card key={player.playerId} className="p-4">
													<div className="space-y-3">
														{/* Player Header */}
														<div className="flex items-center justify-between">
															<div className="min-w-0 flex-1">
																<div className="truncate font-medium">
																	{player.playerName ||
																		`Player #${player.playerId}`}
																</div>
																<div className="text-muted-foreground text-sm">
																	{player.chainCount} chains •{" "}
																	{player.totalWarHits} war hits
																</div>
															</div>
															<Button
																variant="outline"
																size="sm"
																onClick={(e) => {
																	e.preventDefault();
																	onOpenPlayerPerformance(player.playerId);
																}}
																className="shrink-0"
															>
																<User className="h-4 w-4" />
															</Button>
														</div>

														{/* Stats Grid */}
														<div className="grid grid-cols-3 gap-3 text-center">
															<div>
																<div className="text-muted-foreground text-sm">
																	Total Respect
																</div>
																<div className="font-semibold">
																	{player.totalRespect.toFixed(2)}
																</div>
															</div>
															<div>
																<div className="text-muted-foreground text-sm">
																	Avg Respect
																</div>
																<div className="font-medium">
																	{player.averageRespect.toFixed(2)}
																</div>
															</div>
															<div>
																<div className="text-muted-foreground text-sm">
																	Best Respect
																</div>
																<div className="font-medium">
																	{player.bestRespect.toFixed(2)}
																</div>
															</div>
														</div>
													</div>
												</Card>
											))}
									</div>
								</CardContent>
							</Card>

							{/* Individual Chains */}
							<Card>
								<CardHeader>
									<div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
										<CardTitle className="flex items-center gap-2">
											<Flame className="h-5 w-5" />
											<span className="hidden sm:inline">
												Individual Chains
											</span>
											<span className="sm:hidden">Chains</span>
										</CardTitle>
										<Badge variant="outline" className="w-fit">
											{chainReports.chainReports.length} chains
										</Badge>
									</div>
								</CardHeader>
								<CardContent className="space-y-3">
									{chainReports.chainReports.map(
										(cr: ChainReportFromServer) => (
											<div
												key={cr.id}
												className="rounded-lg border p-4 transition-colors hover:bg-muted/50"
											>
												<div className="flex items-center justify-between">
													<div className="flex items-center gap-3">
														<div className="text-lg">🔗</div>
														<div>
															<div className="font-medium">Chain #{cr.id}</div>
															{cr.details && (
																<div className="text-muted-foreground text-sm">
																	{cr.details.chain} hits •{" "}
																	{cr.details.respect.toFixed(1)} respect
																</div>
															)}
														</div>
													</div>
													<Button
														variant="outline"
														size="sm"
														onClick={() => onOpenChainDetails(cr.id)}
													>
														View Details
													</Button>
												</div>
											</div>
										),
									)}
								</CardContent>
							</Card>
						</>
					)}
				</TabsContent>

				{/* Individual Attacks Tab */}
				<TabsContent value="attacks" className="space-y-4">
					{attacksData && (
						<>
							{/* Attack Summary Overview */}
							<Card>
								<CardHeader>
									<CardTitle className="flex items-center gap-2">
										<Swords className="h-5 w-5" />
										Non-Chain Attack Summary
									</CardTitle>
								</CardHeader>
								<CardContent className="space-y-4">
									<div className="grid grid-cols-2 gap-4 md:grid-cols-4">
										<div className="rounded-lg bg-muted/50 p-3 text-center">
											<div className="font-bold text-2xl text-foreground">
												{attacksData.assists}
											</div>
											<div className="text-muted-foreground text-sm">
												Assists
											</div>
										</div>
										<div className="rounded-lg bg-muted/50 p-3 text-center">
											<div className="font-bold text-2xl text-foreground">
												{attacksData.insideHits}
											</div>
											<div className="text-muted-foreground text-sm">
												Inside Hits
											</div>
										</div>
										<div className="rounded-lg bg-muted/50 p-3 text-center">
											<div className="font-bold text-2xl text-foreground">
												{attacksData.outsideHits}
											</div>
											<div className="text-muted-foreground text-sm">
												Outside Hits
											</div>
										</div>
										<div className="rounded-lg bg-muted/50 p-3 text-center">
											<div className="font-bold text-2xl text-foreground">
												{attacksData.totalRespect.toFixed(1)}
											</div>
											<div className="text-muted-foreground text-sm">
												Total Respect
											</div>
										</div>
									</div>

									{/* Attack Type Breakdown */}
									<div className="space-y-3">
										<h4 className="font-medium text-muted-foreground text-sm">
											Attack Type Distribution
										</h4>
										<div className="space-y-2">
											<div className="flex items-center justify-between">
												<span className="text-sm">Inside Hits</span>
												<span className="font-medium text-sm">
													{(
														(attacksData.insideHits /
															(attacksData.insideHits +
																attacksData.outsideHits +
																attacksData.assists)) *
														100
													).toFixed(1)}
													%
												</span>
											</div>
											<Progress
												value={
													(attacksData.insideHits /
														(attacksData.insideHits +
															attacksData.outsideHits +
															attacksData.assists)) *
													100
												}
												className="h-2"
											/>
											<div className="flex items-center justify-between">
												<span className="text-sm">Outside Hits</span>
												<span className="font-medium text-sm">
													{(
														(attacksData.outsideHits /
															(attacksData.insideHits +
																attacksData.outsideHits +
																attacksData.assists)) *
														100
													).toFixed(1)}
													%
												</span>
											</div>
											<Progress
												value={
													(attacksData.outsideHits /
														(attacksData.insideHits +
															attacksData.outsideHits +
															attacksData.assists)) *
													100
												}
												className="h-2"
											/>
											<div className="flex items-center justify-between">
												<span className="text-sm">Assists</span>
												<span className="font-medium text-sm">
													{(
														(attacksData.assists /
															(attacksData.insideHits +
																attacksData.outsideHits +
																attacksData.assists)) *
														100
													).toFixed(1)}
													%
												</span>
											</div>
											<Progress
												value={
													(attacksData.assists /
														(attacksData.insideHits +
															attacksData.outsideHits +
															attacksData.assists)) *
													100
												}
												className="h-2"
											/>
										</div>
									</div>
								</CardContent>
							</Card>

							{/* Top Individual Performers */}
							{attacksData.memberStats &&
								attacksData.memberStats.length > 0 && (
									<Card>
										<CardHeader>
											<div className="space-y-3">
												<div className="flex items-center justify-between">
													<CardTitle className="flex items-center gap-2">
														<Trophy className="h-5 w-5" />
														<span className="hidden sm:inline">
															Top Individual Performers
														</span>
														<span className="sm:hidden">Top Performers</span>
													</CardTitle>
													<Button
														variant="outline"
														size="sm"
														onClick={() => onOpenPlayerPerformance()}
														className="flex items-center gap-2"
													>
														<User className="h-4 w-4" />
														<span className="hidden sm:inline">
															Detailed View
														</span>
														<span className="sm:hidden">Details</span>
													</Button>
												</div>
												<Badge variant="outline" className="w-fit">
													{attacksData.memberStats.length} members
												</Badge>
											</div>
										</CardHeader>
										<CardContent>
											{/* Desktop Table View */}
											<div className="hidden lg:block">
												<Table>
													<TableHeader>
														<TableRow>
															<TableHead>Player</TableHead>
															<TableHead>Total Attacks</TableHead>
															<TableHead>Inside Hits</TableHead>
															<TableHead>Outside Hits</TableHead>
															<TableHead>Assists</TableHead>
															<TableHead>Total Respect</TableHead>
															<TableHead>Avg Respect</TableHead>
															<TableHead />
														</TableRow>
													</TableHeader>
													<TableBody>
														{attacksData.memberStats
															.sort(
																(a: MemberStats, b: MemberStats) =>
																	b.totalRespect - a.totalRespect,
															)
															.slice(0, 10)
															.map((member: MemberStats) => (
																<TableRow key={member.attackerId}>
																	<TableCell className="font-medium">
																		{member.attackerName ||
																			`Player #${member.attackerId}`}
																	</TableCell>
																	<TableCell>{member.totalAttacks}</TableCell>
																	<TableCell>{member.insideHits}</TableCell>
																	<TableCell>{member.outsideHits}</TableCell>
																	<TableCell>{member.assists}</TableCell>
																	<TableCell className="font-semibold">
																		{member.totalRespect.toFixed(2)}
																	</TableCell>
																	<TableCell>
																		{member.totalAttacks > 0
																			? (
																					member.totalRespect /
																					member.totalAttacks
																				).toFixed(2)
																			: "0.00"}
																	</TableCell>
																	<TableCell>
																		<Button
																			variant="ghost"
																			size="sm"
																			onClick={(e) => {
																				e.preventDefault();
																				onOpenPlayerPerformance(
																					member.attackerId,
																				);
																			}}
																			className="h-6 px-2"
																		>
																			<User className="h-3 w-3" />
																		</Button>
																	</TableCell>
																</TableRow>
															))}
													</TableBody>
												</Table>
											</div>

											{/* Mobile Card View */}
											<div className="space-y-3 lg:hidden">
												{attacksData.memberStats
													.sort(
														(a: MemberStats, b: MemberStats) =>
															b.totalRespect - a.totalRespect,
													)
													.slice(0, 10)
													.map((member: MemberStats) => (
														<Card key={member.attackerId} className="p-4">
															<div className="space-y-3">
																{/* Member Header */}
																<div className="flex items-center justify-between">
																	<div className="min-w-0 flex-1">
																		<div className="truncate font-medium">
																			{member.attackerName ||
																				`Player #${member.attackerId}`}
																		</div>
																		<div className="flex items-center gap-2">
																			<Badge
																				variant="outline"
																				className="text-xs"
																			>
																				{member.totalAttacks} attacks
																			</Badge>
																		</div>
																	</div>
																	<Button
																		variant="outline"
																		size="sm"
																		onClick={(e) => {
																			e.preventDefault();
																			onOpenPlayerPerformance(
																				member.attackerId,
																			);
																		}}
																		className="shrink-0"
																	>
																		<User className="h-4 w-4" />
																	</Button>
																</div>

																{/* Attack Stats Grid */}
																<div className="grid grid-cols-4 gap-2 text-center text-sm">
																	<div>
																		<div className="text-muted-foreground text-xs">
																			Inside
																		</div>
																		<div className="font-medium">
																			{member.insideHits}
																		</div>
																	</div>
																	<div>
																		<div className="text-muted-foreground text-xs">
																			Outside
																		</div>
																		<div className="font-medium">
																			{member.outsideHits}
																		</div>
																	</div>
																	<div>
																		<div className="text-muted-foreground text-xs">
																			Assists
																		</div>
																		<div className="font-medium">
																			{member.assists}
																		</div>
																	</div>
																	<div>
																		<div className="text-muted-foreground text-xs">
																			Avg
																		</div>
																		<div className="font-medium">
																			{member.totalAttacks > 0
																				? (
																						member.totalRespect /
																						member.totalAttacks
																					).toFixed(2)
																				: "0.00"}
																		</div>
																	</div>
																</div>

																{/* Total Respect */}
																<div className="text-center">
																	<div className="text-muted-foreground text-sm">
																		Total Respect
																	</div>
																	<div className="font-semibold text-lg">
																		{member.totalRespect.toFixed(2)}
																	</div>
																</div>
															</div>
														</Card>
													))}
											</div>
										</CardContent>
									</Card>
								)}

							{/* Performance Insights */}
							<Card>
								<CardHeader>
									<div className="flex items-center justify-between">
										<CardTitle className="flex items-center gap-2">
											<TrendingUp className="h-5 w-5" />
											Performance Insights
										</CardTitle>
										<Button
											variant="outline"
											size="sm"
											onClick={onOpenTimeline}
											className="flex items-center gap-2"
										>
											<Clock className="h-4 w-4" />
											Timeline View
										</Button>
									</div>
								</CardHeader>
								<CardContent className="space-y-4">
									<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
										<div className="rounded-lg border p-4 text-center">
											<div className="font-bold text-lg">
												{attacksData.memberStats
													? (
															attacksData.totalRespect /
															attacksData.memberStats.length
														).toFixed(2)
													: "0.00"}
											</div>
											<div className="text-muted-foreground text-sm">
												Avg Respect per Member
											</div>
										</div>
										<div className="rounded-lg border p-4 text-center">
											<div className="font-bold text-lg">
												{attacksData.memberStats
													? Math.max(
															...attacksData.memberStats.map(
																(m) => m.totalRespect,
															),
														).toFixed(2)
													: "0.00"}
											</div>
											<div className="text-muted-foreground text-sm">
												Highest Individual Respect
											</div>
										</div>
										<div className="rounded-lg border p-4 text-center">
											<div className="font-bold text-lg">
												{(
													(attacksData.insideHits /
														(attacksData.insideHits +
															attacksData.outsideHits)) *
													100
												).toFixed(1)}
												%
											</div>
											<div className="text-muted-foreground text-sm">
												Inside Hit Rate
											</div>
										</div>
									</div>
								</CardContent>
							</Card>
						</>
					)}
				</TabsContent>

				{/* Members Tab */}
				<TabsContent value="members" className="space-y-4">
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Users className="h-5 w-5" />
								All Faction Members
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="grid gap-6 md:grid-cols-2">
								{warReport.factions.map((faction: Faction) => {
									const isOurFaction = faction.id === MY_FACTION_ID;

									return (
										<div
											key={faction.id}
											className={`rounded-lg border p-4 ${
												isOurFaction
													? "border-primary bg-primary/5"
													: "border-border bg-muted/50"
											}`}
										>
											<div className="mb-4 flex items-center gap-2">
												<span className="font-semibold text-lg">
													{faction.name}
												</span>
												{isOurFaction && (
													<Badge variant="outline" className="text-xs">
														Our Faction
													</Badge>
												)}
											</div>

											<div className="space-y-3">
												{faction.members.map((member: FactionMember) => (
													<div
														key={member.id}
														className="flex items-center justify-between rounded-lg border bg-background p-3"
													>
														<div className="min-w-0 flex-1">
															<div className="truncate font-medium">
																{member.name}
															</div>
															<div className="text-muted-foreground text-sm">
																Level {member.level}
															</div>
														</div>
														<div className="text-right">
															<div className="font-semibold">
																{member.score}
															</div>
															<div className="text-muted-foreground text-sm">
																{member.attacks} attacks
															</div>
														</div>
													</div>
												))}
											</div>
										</div>
									);
								})}
							</div>
						</CardContent>
					</Card>
				</TabsContent>
			</Tabs>
		</div>
	);
}
