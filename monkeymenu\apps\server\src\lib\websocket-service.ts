/**
 * Service for broadcasting real-time events to legacy WebSocket clients.
 *
 * NOTE: Most features have migrated to LiveStore. The implementation here
 * remains to keep compatibility with any still-connected clients until the
 * full cut-over is complete.
 */
export class WebSocketService {
	async broadcastWithdrawalCreated(_payload: unknown) {
		console.warn(
			"⚠️ [DEPRECATED] WebSocketService.broadcastWithdrawalCreated() is deprecated. Migration to LiveStore complete. Remove this call.",
		);
	}
	async broadcastWithdrawalUpdated(_payload: unknown) {
		console.warn(
			"⚠️ [DEPRECATED] WebSocketService.broadcastWithdrawalUpdated() is deprecated. Migration to LiveStore complete. Remove this call.",
		);
	}
	async broadcastBalanceUpdated(_payload: unknown) {
		console.warn(
			"⚠️ [DEPRECATED] WebSocketService.broadcastBalanceUpdated() is deprecated. Migration to LiveStore complete. Remove this call.",
		);
	}
	async broadcastTargetAdded(_payload: unknown) {
		console.warn(
			"⚠️ [DEPRECATED] WebSocketService.broadcastTargetAdded() is deprecated. Migration to LiveStore complete. Remove this call.",
		);
	}
	async broadcastTargetRemoved(_payload: unknown) {
		console.warn(
			"⚠️ [DEPRECATED] WebSocketService.broadcastTargetRemoved() is deprecated. Migration to LiveStore complete. Remove this call.",
		);
	}
	async broadcastTargetStatusUpdated(_payload: unknown) {
		console.warn(
			"⚠️ [DEPRECATED] WebSocketService.broadcastTargetStatusUpdated() is deprecated. Migration to LiveStore complete. Remove this call.",
		);
	}
	async broadcastTargetListUpdated(_payload: unknown) {
		console.warn(
			"⚠️ [DEPRECATED] WebSocketService.broadcastTargetListUpdated() is deprecated. Migration to LiveStore complete. Remove this call.",
		);
	}
	async broadcastTargetStatusBatch(_payload: unknown) {
		console.warn(
			"⚠️ [DEPRECATED] WebSocketService.broadcastTargetStatusBatch() is deprecated. Migration to LiveStore complete. Remove this call.",
		);
	}
}
