import { cn } from "@/lib/utils";
import { Search } from "lucide-react";
import * as React from "react";
import { Input } from "./input";

export interface SearchInputProps
	extends React.InputHTMLAttributes<HTMLInputElement> {
	onClear?: () => void;
}

const SearchInput = React.forwardRef<HTMLInputElement, SearchInputProps>(
	({ className, onClear, ...props }, ref) => {
		return (
			<div className="relative">
				<Search className="absolute top-2.5 left-2 h-4 w-4 text-muted-foreground" />
				<Input ref={ref} className={cn("pl-8", className)} {...props} />
				{props.value && onClear && (
					<button
						type="button"
						onClick={onClear}
						className="absolute top-2.5 right-2 h-4 w-4 text-muted-foreground hover:text-foreground"
					>
						×
					</button>
				)}
			</div>
		);
	},
);
SearchInput.displayName = "SearchInput";

export { SearchInput };
