import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { ThemeSelector } from "@/components/ui/theme-selector";

export function ThemeSettings() {
	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<span>🎨</span>
					Theme Settings
				</CardTitle>
				<div className="text-muted-foreground text-sm">
					Customize the theme and appearance of the application
				</div>
			</CardHeader>
			<CardContent>
				<div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
					<div className="flex-1">
						<h3 className="font-semibold text-lg">Appearance</h3>
						<p className="text-muted-foreground">
							Choose your preferred theme style and color mode
						</p>
					</div>
					<div className="flex flex-col gap-2 sm:flex-row">
						<ThemeSelector />
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
