// Performance monitoring and optimization utilities

interface PerformanceMetrics {
  componentRenderTime: number;
  apiResponseTime: number;
  bundleSize?: number;
  memoryUsage?: number;
}

// Performance monitoring class
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, PerformanceMetrics[]> = new Map();

  static getInstance(): PerformanceMonitor {
    if (!this.instance) {
      this.instance = new PerformanceMonitor();
    }
    return this.instance;
  }

  // Mark the start of a performance measurement
  startMeasurement(name: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      this.recordMetric(name, { componentRenderTime: duration, apiResponseTime: 0 });
    };
  }

  // Record a metric
  recordMetric(name: string, metric: PerformanceMetrics): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    this.metrics.get(name)!.push(metric);
    
    // Keep only last 100 measurements
    const measurements = this.metrics.get(name)!;
    if (measurements.length > 100) {
      measurements.shift();
    }
  }

  // Get average metrics for a component
  getAverageMetrics(name: string): PerformanceMetrics | null {
    const measurements = this.metrics.get(name);
    if (!measurements || measurements.length === 0) return null;

    const sum = measurements.reduce(
      (acc, metric) => ({
        componentRenderTime: acc.componentRenderTime + metric.componentRenderTime,
        apiResponseTime: acc.apiResponseTime + metric.apiResponseTime,
      }),
      { componentRenderTime: 0, apiResponseTime: 0 }
    );

    return {
      componentRenderTime: sum.componentRenderTime / measurements.length,
      apiResponseTime: sum.apiResponseTime / measurements.length,
    };
  }

  // Get all metrics
  getAllMetrics(): Record<string, PerformanceMetrics> {
    const result: Record<string, PerformanceMetrics> = {};
    
    for (const [name] of this.metrics) {
      const avg = this.getAverageMetrics(name);
      if (avg) {
        result[name] = avg;
      }
    }
    
    return result;
  }

  // Clear all metrics
  clear(): void {
    this.metrics.clear();
  }
}

// React hook for performance monitoring
import React from 'react';
export function usePerformanceMonitor(componentName: string) {
  const monitor = PerformanceMonitor.getInstance();
  
  return {
    startMeasurement: () => monitor.startMeasurement(componentName),
    getMetrics: () => monitor.getAverageMetrics(componentName),
  };
}

// Debounce utility for performance optimization
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

// Throttle utility for performance optimization
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
}

// Virtual scrolling utility for large lists
export interface VirtualScrollOptions {
  itemHeight: number;
  containerHeight: number;
  items: any[];
  overscan?: number;
}

export function useVirtualScroll<T>({
  itemHeight,
  containerHeight,
  items,
  overscan = 5,
}: VirtualScrollOptions & { items: T[] }) {
  const [scrollTop, setScrollTop] = React.useState(0);

  const visibleCount = Math.ceil(containerHeight / itemHeight);
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(items.length, startIndex + visibleCount + overscan * 2);

  const visibleItems = items.slice(startIndex, endIndex);
  const totalHeight = items.length * itemHeight;
  const offsetY = startIndex * itemHeight;

  return {
    visibleItems,
    totalHeight,
    offsetY,
    onScroll: (e: React.UIEvent<HTMLDivElement>) => {
      setScrollTop(e.currentTarget.scrollTop);
    },
  };
}

// Lazy loading utility
export function useLazyLoad(threshold = 0.1) {
  const [isIntersecting, setIsIntersecting] = React.useState(false);
  const ref = React.useRef<HTMLElement>(null);

  React.useEffect(() => {
    if (!ref.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      },
      { threshold }
    );

    observer.observe(ref.current);

    return () => observer.disconnect();
  }, [threshold]);

  return { ref, isIntersecting };
}

// Bundle size analyzer (development only)
export function analyzeBundleSize() {
  if (process.env.NODE_ENV !== 'development') return;
  
  // This would typically integrate with webpack-bundle-analyzer
  console.log('Bundle analysis would run here in development');
}

// Memory usage monitor
export function monitorMemoryUsage() {
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    return {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
    };
  }
  return null;
}

// Web Vitals monitoring
export function initWebVitals() {
  // This would typically integrate with web-vitals library
  if (typeof window === 'undefined') return;
  
  // Monitor LCP, FID, CLS, etc.
  const observer = new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      console.log(`${entry.name}: ${entry.startTime}ms`);
    });
  });
  
  observer.observe({ entryTypes: ['measure', 'navigation'] });
}