// Guide categories with emojis and descriptions
export const GUIDE_CATEGORIES = [
  {
    value: "getting-started",
    label: "Getting Started",
    emoji: "🚀",
    description: "Basic guides for new users"
  },
  {
    value: "banking",
    label: "Banking",
    emoji: "🏦",
    description: "Banking system and investment guides"
  },
  {
    value: "target-finder",
    label: "Target Finder",
    emoji: "🎯",
    description: "Finding and attacking targets"
  },
  {
    value: "discord",
    label: "Discord",
    emoji: "💬",
    description: "Discord integration and notifications"
  },
  {
    value: "security",
    label: "Security",
    emoji: "🔒",
    description: "Account security and best practices"
  },
  {
    value: "api",
    label: "API",
    emoji: "🔌",
    description: "API usage and development guides"
  },
  {
    value: "advanced",
    label: "Advanced",
    emoji: "⚡",
    description: "Advanced features and techniques"
  },
  {
    value: "troubleshooting",
    label: "Troubleshooting",
    emoji: "🔧",
    description: "Common issues and solutions"
  }
] as const;

export type GuideCategory = typeof GUIDE_CATEGORIES[number]['value'];

// Helper function to get category info
export function getCategoryInfo(categoryValue: string) {
  const category = GUIDE_CATEGORIES.find(cat => cat.value === categoryValue);
  return category || {
    value: categoryValue,
    label: categoryValue,
    emoji: "📖",
    description: "Custom category"
  };
}

// Helper function to get category display name with emoji
export function getCategoryDisplay(categoryValue: string) {
  const info = getCategoryInfo(categoryValue);
  return `${info.emoji} ${info.label}`;
}

// Extract preview text from markdown content
export function extractPreviewText(content: string, maxLength: number = 150): string {
  // Remove markdown formatting
  let text = content
    // Remove headers
    .replace(/^#{1,6}\s+/gm, '')
    // Remove bold/italic
    .replace(/\*\*(.*?)\*\*/g, '$1')
    .replace(/\*(.*?)\*/g, '$1')
    // Remove code blocks
    .replace(/```[\s\S]*?```/g, '[code block]')
    // Remove inline code
    .replace(/`([^`]+)`/g, '$1')
    // Remove links
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
    // Remove images
    .replace(/!\[([^\]]*)\]\([^)]+\)/g, '[image: $1]')
    // Remove blockquotes
    .replace(/^>\s+/gm, '')
    // Remove list markers
    .replace(/^[\*\-\+]\s+/gm, '• ')
    .replace(/^\d+\.\s+/gm, '• ')
    // Clean up whitespace
    .replace(/\n+/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();

  if (text.length <= maxLength) return text;
  
  // Find the last complete word within the limit
  const truncated = text.substring(0, maxLength);
  const lastSpace = truncated.lastIndexOf(' ');
  
  if (lastSpace > maxLength * 0.8) {
    return truncated.substring(0, lastSpace) + '...';
  }
  
  return truncated + '...';
}

// Calculate reading time estimate
export function calculateReadingTime(content: string): number {
  const wordsPerMinute = 200;
  const words = content.trim().split(/\s+/).length;
  const minutes = Math.ceil(words / wordsPerMinute);
  return Math.max(1, minutes);
}

// Format relative time (e.g., "2 days ago")
export function formatRelativeTime(timestamp: number): string {
  const now = Date.now();
  const diff = now - timestamp;
  
  const minute = 60 * 1000;
  const hour = minute * 60;
  const day = hour * 24;
  const week = day * 7;
  const month = day * 30;
  const year = day * 365;
  
  if (diff < minute) {
    return 'Just now';
  } else if (diff < hour) {
    const minutes = Math.floor(diff / minute);
    return `${minutes}m ago`;
  } else if (diff < day) {
    const hours = Math.floor(diff / hour);
    return `${hours}h ago`;
  } else if (diff < week) {
    const days = Math.floor(diff / day);
    return `${days}d ago`;
  } else if (diff < month) {
    const weeks = Math.floor(diff / week);
    return `${weeks}w ago`;
  } else if (diff < year) {
    const months = Math.floor(diff / month);
    return `${months}mo ago`;
  } else {
    const years = Math.floor(diff / year);
    return `${years}y ago`;
  }
}

// Validate guide form data
export function validateGuideForm(data: {
  title: string;
  content: string;
  category: string;
  tags: string[];
}) {
  const errors: Record<string, string> = {};
  
  if (!data.title.trim()) {
    errors.title = 'Title is required';
  } else if (data.title.length < 3) {
    errors.title = 'Title must be at least 3 characters';
  } else if (data.title.length > 100) {
    errors.title = 'Title must be less than 100 characters';
  }
  
  if (!data.content.trim()) {
    errors.content = 'Content is required';
  } else if (data.content.length < 50) {
    errors.content = 'Content must be at least 50 characters';
  } else if (data.content.length > 50000) {
    errors.content = 'Content must be less than 50,000 characters';
  }
  
  if (!data.category.trim()) {
    errors.category = 'Category is required';
  }
  
  if (data.tags.some(tag => tag.length > 20)) {
    errors.tags = 'Tags must be less than 20 characters each';
  }
  
  if (data.tags.length > 10) {
    errors.tags = 'Maximum 10 tags allowed';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

// Search and filter utilities
export function filterGuides(
  guides: any[],
  filters: {
    searchTerm?: string;
    category?: string;
    author?: string;
    published?: boolean;
  }
) {
  return guides.filter(guide => {
    // Search term filter
    if (filters.searchTerm) {
      const term = filters.searchTerm.toLowerCase();
      const matchesTitle = guide.title.toLowerCase().includes(term);
      const matchesContent = guide.content.toLowerCase().includes(term);
      const matchesTags = guide.tags.some((tag: string) => 
        tag.toLowerCase().includes(term)
      );
      
      if (!matchesTitle && !matchesContent && !matchesTags) {
        return false;
      }
    }
    
    // Category filter
    if (filters.category && guide.category !== filters.category) {
      return false;
    }
    
    // Author filter
    if (filters.author && guide.author?.username !== filters.author) {
      return false;
    }
    
    // Published filter
    if (filters.published !== undefined && guide.isPublished !== filters.published) {
      return false;
    }
    
    return true;
  });
}

// Sort guides by various criteria
export function sortGuides(
  guides: any[],
  sortBy: 'newest' | 'oldest' | 'popular' | 'alphabetical' | 'updated'
) {
  const sorted = [...guides];
  
  switch (sortBy) {
    case 'newest':
      return sorted.sort((a, b) => b.createdAt - a.createdAt);
    
    case 'oldest':
      return sorted.sort((a, b) => a.createdAt - b.createdAt);
    
    case 'popular':
      return sorted.sort((a, b) => b.viewCount - a.viewCount);
    
    case 'alphabetical':
      return sorted.sort((a, b) => a.title.localeCompare(b.title));
    
    case 'updated':
      return sorted.sort((a, b) => b.updatedAt - a.updatedAt);
    
    default:
      return sorted;
  }
}

// Generate guide URL slug
export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '')
    .substring(0, 50);
}

// Check if user can edit guide
export function canUserEditGuide(guide: any, user: any): boolean {
  if (!user) return false;
  
  // Admin can edit any guide
  if (user.permissions?.includes('admin')) return true;
  
  // Author can edit their own guide
  if (guide.authorId === user._id) return true;
  
  // Users with guides:manage permission can edit any guide
  if (user.permissions?.includes('guides:manage')) return true;
  
  return false;
}