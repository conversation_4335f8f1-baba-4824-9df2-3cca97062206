"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AzureFunctionV4Server = exports.AzureFunctionServer = void 0;
const server_1 = require("../server");
const multipartData_1 = require("../util/multipartData");
/**
 * A server for Azure Function integration
 * @see https://docs.microsoft.com/en-us/azure/azure-functions/
 * @deprecated Use `AzureFunctionV4Server` as that will supercede this server in the future.
 */
class AzureFunctionServer extends server_1.Server {
    constructor(moduleExports, target = 'interactions') {
        super({ alreadyListening: true });
        moduleExports[target] = this._onRequest.bind(this);
    }
    _onRequest(context, req) {
        if (!this._handler) {
            context.res.status = 503;
            context.res.send('Server has no handler');
        }
        if (req.method !== 'POST') {
            context.res.status = 405;
            context.res.send('Server only supports POST requests.');
        }
        this._handler({
            headers: req.headers,
            body: req.body,
            request: req,
            response: context.res
        }, async (response) => {
            context.res.status = response.status || 200;
            if (response.files) {
                const data = new multipartData_1.MultipartData();
                context.res.header('Content-Type', 'multipart/form-data; boundary=' + data.boundary);
                for (const i in response.files)
                    data.attach(`files[${i}]`, response.files[i].file, response.files[i].name);
                data.attach('payload_json', JSON.stringify(response.body));
                context.res.send(Buffer.concat(data.finish()));
            }
            else {
                context.res.header('Content-Type', 'application/json');
                context.res.send(response.body);
            }
        }, context);
    }
    /** @private */
    createEndpoint(path, handler) {
        this._handler = handler;
    }
}
exports.AzureFunctionServer = AzureFunctionServer;
/**
 * A server for Azure Function V4 integration
 * @see https://docs.microsoft.com/en-us/azure/azure-functions/
 */
class AzureFunctionV4Server extends server_1.Server {
    constructor(app, target = 'interactions') {
        super({ alreadyListening: true });
        app.http('interactions', {
            methods: ['POST'],
            authLevel: 'anonymous',
            route: target,
            handler: this._onRequest.bind(this)
        });
    }
    async _onRequest(request, context) {
        if (!this._handler)
            return { status: 503, body: 'Server has no handler' };
        if (request.method !== 'POST')
            return { status: 405, body: 'Server only supports POST requests.' };
        const body = await request.text();
        return new Promise((resolve, reject) => {
            this._handler({
                headers: Object.fromEntries(request.headers.entries()),
                body: body ? JSON.parse(body) : body,
                request: request,
                response: null,
                rawBody: body
            }, async (response) => {
                if (response.files) {
                    const data = new multipartData_1.MultipartData();
                    for (const i in response.files)
                        data.attach(`files[${i}]`, response.files[i].file, response.files[i].name);
                    data.attach('payload_json', JSON.stringify(response.body));
                    resolve({
                        status: response.status || 200,
                        headers: { 'Content-Type': `multipart/form-data; boundary=${data.boundary}` },
                        body: Buffer.concat(data.finish())
                    });
                }
                else {
                    resolve({
                        status: response.status || 200,
                        headers: { 'Content-Type': 'application/json' },
                        jsonBody: response.body
                    });
                }
            }, context).catch(reject);
        });
    }
    /** @private */
    createEndpoint(path, handler) {
        this._handler = handler;
    }
}
exports.AzureFunctionV4Server = AzureFunctionV4Server;
