-- =============================================================================
-- PERMISSIONS SYSTEM SEEDING
-- Based on permissions.ts and shared/permissions.ts
-- =============================================================================

-- Create system user for content authorship (matches guides.ts getOrCreateSystemUser)
INSERT OR IGNORE INTO user (id, name, email, email_verified, created_at, updated_at) 
VALUES (
    'system-user-monkeymenu',
    'MonkeyMenu System',
    '<EMAIL>',
    1,
    (unixepoch() * 1000),
    (unixepoch() * 1000)
);

-- Insert all permissions (matches shared/permissions.ts PERMISSIONS constant)
INSERT OR IGNORE INTO permission (name, display_name, description, category, created_at) VALUES
-- Guide permissions
('guides.view', 'View Guides', 'View Guides permission', 'guides', (unixepoch() * 1000)),
('guides.manage', 'Manage Guides', 'Manage Guides permission', 'guides', (unixepoch() * 1000)),

-- Dashboard permissions
('dashboard.view', 'View Faction Dashboard', 'View Faction Dashboard permission', 'dashboard', (unixepoch() * 1000)),

-- Announcement permissions
('announcements.view', 'View Announcements', 'View Announcements permission', 'announcements', (unixepoch() * 1000)),
('announcements.manage', 'Manage Announcements', 'Manage Announcements permission', 'announcements', (unixepoch() * 1000)),

-- Administration permissions
('admin.view', 'View Admin Dashboard', 'View Admin Dashboard permission', 'admin', (unixepoch() * 1000)),
('admin.users.suspend', 'Suspend/Restore Users', 'Suspend/Restore Users permission', 'admin', (unixepoch() * 1000)),
('admin.users.recheck', 'Recheck User API Keys', 'Recheck User API Keys permission', 'admin', (unixepoch() * 1000)),
('admin.users.delete', 'Delete Users', 'Delete Users permission', 'admin', (unixepoch() * 1000)),

-- Discord command permissions
('discord.manage.verification', 'Manage Discord Verification', 'Manage Discord Verification permission', 'discord', (unixepoch() * 1000)),

-- Banking permissions
('banking.view', 'View Banking', 'View Banking permission', 'banking', (unixepoch() * 1000)),
('banking.request', 'Create Withdrawal Requests', 'Create Withdrawal Requests permission', 'banking', (unixepoch() * 1000)),
('banking.requests.manage', 'Manage Withdrawal Requests', 'Manage Withdrawal Requests permission', 'banking', (unixepoch() * 1000)),

-- Target Finder permissions
('target.finder.view', 'View Target Finder', 'View Target Finder permission', 'target_finder', (unixepoch() * 1000)),
('target.finder.manage.shared_lists', 'Manage Shared Target Lists', 'Manage Shared Target Lists permission', 'target_finder', (unixepoch() * 1000)),

-- War permissions
('wars.view', 'View War Reports', 'View War Reports permission', 'wars', (unixepoch() * 1000));

-- Insert faction roles (matches shared/permissions.ts FACTION_ROLES constant)
INSERT OR IGNORE INTO faction_role (name, display_name, hierarchy_level, description, created_at, updated_at) VALUES
('system-admin', 'System Administrator', 10, 'System Administrator faction role', (unixepoch() * 1000), (unixepoch() * 1000)),
('leader', 'Leader', 9, 'Leader faction role', (unixepoch() * 1000), (unixepoch() * 1000)),
('co-leader', 'Co-leader', 8, 'Co-leader faction role', (unixepoch() * 1000), (unixepoch() * 1000)),
('monkey-mentor', 'Monkey Mentor', 7, 'Monkey Mentor faction role', (unixepoch() * 1000), (unixepoch() * 1000)),
('gorilla', 'Gorilla', 6, 'Gorilla faction role', (unixepoch() * 1000), (unixepoch() * 1000)),
('primate-liaison', 'Primate Liaison', 5, 'Primate Liaison faction role', (unixepoch() * 1000), (unixepoch() * 1000)),
('baboon', 'Baboon', 4, 'Baboon faction role', (unixepoch() * 1000), (unixepoch() * 1000)),
('orangutan', 'Orangutan', 3, 'Orangutan faction role', (unixepoch() * 1000), (unixepoch() * 1000)),
('chimpanzee', 'Chimpanzee', 2, 'Chimpanzee faction role', (unixepoch() * 1000), (unixepoch() * 1000)),
('recruit', 'Recruit', 1, 'Recruit faction role', (unixepoch() * 1000), (unixepoch() * 1000));

-- =============================================================================
-- ROLE-PERMISSION ASSIGNMENTS (from permissions.ts DEFAULT_ROLE_PERMISSIONS)
-- =============================================================================

-- System Admin gets ALL permissions
INSERT OR IGNORE INTO role_permission (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM faction_role WHERE name = 'system-admin'),
    id as permission_id,
    (unixepoch() * 1000)
FROM permission;

-- Leader gets most permissions (all except Discord management)
INSERT OR IGNORE INTO role_permission (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM faction_role WHERE name = 'leader'),
    id as permission_id,
    (unixepoch() * 1000)
FROM permission 
WHERE name IN (
    'guides.view', 'guides.manage', 'dashboard.view', 'announcements.view', 'announcements.manage',
    'admin.view', 'admin.users.suspend', 'admin.users.recheck', 'admin.users.delete',
    'banking.view', 'banking.request', 'banking.requests.manage',
    'target.finder.view', 'target.finder.manage.shared_lists',
    'wars.view'
);

-- Co-Leader gets same as Leader
INSERT OR IGNORE INTO role_permission (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM faction_role WHERE name = 'co-leader'),
    id as permission_id,
    (unixepoch() * 1000)
FROM permission 
WHERE name IN (
    'guides.view', 'guides.manage', 'dashboard.view', 'announcements.view', 'announcements.manage',
    'admin.view', 'admin.users.suspend', 'admin.users.recheck', 'admin.users.delete',
    'banking.view', 'banking.request', 'banking.requests.manage',
    'target.finder.view', 'target.finder.manage.shared_lists',
    'wars.view'
);

-- Monkey Mentor gets same as Leader/Co-Leader
INSERT OR IGNORE INTO role_permission (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM faction_role WHERE name = 'monkey-mentor'),
    id as permission_id,
    (unixepoch() * 1000)
FROM permission 
WHERE name IN (
    'guides.view', 'guides.manage', 'dashboard.view', 'announcements.view', 'announcements.manage',
    'admin.view', 'admin.users.suspend', 'admin.users.recheck', 'admin.users.delete',
    'banking.view', 'banking.request', 'banking.requests.manage',
    'target.finder.view', 'target.finder.manage.shared_lists',
    'wars.view'
);

-- Gorilla gets view and banking permissions
INSERT OR IGNORE INTO role_permission (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM faction_role WHERE name = 'gorilla'),
    id as permission_id,
    (unixepoch() * 1000)
FROM permission 
WHERE name IN (
    'guides.view', 'dashboard.view', 'announcements.view',
    'banking.view', 'banking.request', 'banking.requests.manage',
    'target.finder.view', 'target.finder.manage.shared_lists',
    'wars.view'
);

-- Primate Liaison gets view and banking permissions
INSERT OR IGNORE INTO role_permission (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM faction_role WHERE name = 'primate-liaison'),
    id as permission_id,
    (unixepoch() * 1000)
FROM permission 
WHERE name IN (
    'guides.view', 'dashboard.view', 'announcements.view',
    'banking.view', 'banking.request',
    'target.finder.view',
    'wars.view'
);

-- Baboon gets basic view and banking permissions
INSERT OR IGNORE INTO role_permission (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM faction_role WHERE name = 'baboon'),
    id as permission_id,
    (unixepoch() * 1000)
FROM permission 
WHERE name IN (
    'guides.view', 'dashboard.view', 'announcements.view',
    'banking.view', 'banking.request',
    'target.finder.view',
    'wars.view'
);

-- Orangutan gets basic view and banking permissions
INSERT OR IGNORE INTO role_permission (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM faction_role WHERE name = 'orangutan'),
    id as permission_id,
    (unixepoch() * 1000)
FROM permission 
WHERE name IN (
    'guides.view', 'dashboard.view', 'announcements.view',
    'banking.view', 'banking.request',
    'target.finder.view',
    'wars.view'
);

-- Chimpanzee gets basic view permissions
INSERT OR IGNORE INTO role_permission (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM faction_role WHERE name = 'chimpanzee'),
    id as permission_id,
    (unixepoch() * 1000)
FROM permission 
WHERE name IN (
    'guides.view', 'dashboard.view', 'announcements.view',
    'banking.view', 'banking.request',
    'target.finder.view',
    'wars.view'
);

-- Recruit gets minimal view permissions
INSERT OR IGNORE INTO role_permission (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM faction_role WHERE name = 'recruit'),
    id as permission_id,
    (unixepoch() * 1000)
FROM permission 
WHERE name IN (
    'guides.view', 'dashboard.view', 'announcements.view',
    'banking.view',
    'target.finder.view',
    'wars.view'
);

-- Verification
SELECT 'Permissions seeding completed!' as message;
SELECT COUNT(*) as total_permissions FROM permission;
SELECT COUNT(*) as total_roles FROM faction_role;  
SELECT COUNT(*) as total_role_permissions FROM role_permission; 