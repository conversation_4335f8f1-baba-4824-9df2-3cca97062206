import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Skeleton } from "./Skeleton";
import type { AdminStatsProps } from "./types";

export function AdminStats({ stats }: AdminStatsProps) {
	return (
		<div className="grid gap-4 md:grid-cols-3">
			{stats.map((stat) => (
				<Card key={stat.title}>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="font-medium text-sm">{stat.title}</CardTitle>
						<stat.icon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="font-bold text-2xl">
							{stat.isLoading ? <Skeleton className="h-8 w-16" /> : stat.value}
						</div>
						<p className="text-muted-foreground text-xs">{stat.description}</p>
					</CardContent>
				</Card>
			))}
		</div>
	);
}
