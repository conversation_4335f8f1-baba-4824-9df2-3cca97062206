import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { trpc } from "@/lib/trpc-client";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useRef, useState } from "react";
import { ApiSetupStep } from "./ApiSetupStep";
import { CompletionStep } from "./CompletionStep";
import { DiscordSetupStep } from "./DiscordSetupStep";
import { FeatureTourStep } from "./FeatureTourStep";
import { ThemeSetupStep } from "./ThemeSetupStep";
import { WelcomeStep } from "./WelcomeStep";
import { ONBOARDING_STEPS, getInitialStepFromRedirect } from "./config";
import type { OnboardingStep } from "./types";

export function Onboarding() {
	const initialStepFromRedirect = getInitialStepFromRedirect();
	const [currentStep, setCurrentStep] = useState<OnboardingStep>(() => {
		return (initialStepFromRedirect as OnboardingStep) || "welcome";
	});
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [invalidApiKey, setInvalidApiKey] = useState(false);
	const [isFinalizingSetup, setIsFinalizingSetup] = useState(false);
	const [pollingProgress, setPollingProgress] = useState(0);
	const [isDiscordLinked, setIsDiscordLinked] = useState(false);

	// Ref to track if we've already done initial profile-based step determination
	const hasSetInitialStepRef = useRef(false);

	// Get user profile to check onboarding status
	const { data: profile } = useQuery({
		...trpc.user.getProfile.queryOptions(),
	});

	// Effect to check initial Discord linking status - runs once on mount
	useEffect(() => {
		async function checkDiscordLinking() {
			try {
				const { authClient } = await import("@/lib/auth-client");
				const accounts = await authClient.listAccounts();

				if ("data" in accounts && accounts.data) {
					const discordAccount = accounts.data.find(
						(account: { provider: string }) => account.provider === "discord",
					);
					setIsDiscordLinked(!!discordAccount);
				}
			} catch (error) {
				console.error("Error checking Discord linking status:", error);
			}
		}

		checkDiscordLinking();
	}, []);

	// Effect to determine step based on user's profile, if not set by redirect
	useEffect(() => {
		if (
			profile &&
			!initialStepFromRedirect &&
			currentStep === "welcome" &&
			!hasSetInitialStepRef.current
		) {
			hasSetInitialStepRef.current = true;
			if (
				profile.tornUser?.tornApiKeyVerified &&
				profile.tornUser?.tornUserId
			) {
				setCurrentStep("feature-tour");
			} else if (
				profile.tornUser?.tornApiKey &&
				!profile.tornUser?.tornApiKeyVerified
			) {
				setCurrentStep("api-setup");
			}
		}
	}, [profile, initialStepFromRedirect, currentStep]);

	const currentStepIndex = ONBOARDING_STEPS.findIndex(
		(step) => step.id === currentStep,
	);
	const progress = ((currentStepIndex + 1) / ONBOARDING_STEPS.length) * 100;

	const handleNextStep = () => {
		const nextIndex = currentStepIndex + 1;
		if (nextIndex < ONBOARDING_STEPS.length) {
			setCurrentStep(ONBOARDING_STEPS[nextIndex].id);
		}
	};

	const handlePrevStep = () => {
		const prevIndex = currentStepIndex - 1;
		if (prevIndex >= 0) {
			setCurrentStep(ONBOARDING_STEPS[prevIndex].id);
		}
	};

	const renderStepContent = () => {
		switch (currentStep) {
			case "welcome":
				return (
					<WelcomeStep
						onNextStep={handleNextStep}
						onPrevStep={handlePrevStep}
						isDiscordLinked={isDiscordLinked}
					/>
				);

			case "theme-setup":
				return (
					<ThemeSetupStep
						onNextStep={handleNextStep}
						onPrevStep={handlePrevStep}
					/>
				);

			case "api-setup":
				return (
					<ApiSetupStep
						onNextStep={handleNextStep}
						onPrevStep={handlePrevStep}
						isDiscordLinked={isDiscordLinked}
						isSubmitting={isSubmitting}
						setIsSubmitting={setIsSubmitting}
						invalidApiKey={invalidApiKey}
						setInvalidApiKey={setInvalidApiKey}
						isFinalizingSetup={isFinalizingSetup}
						setIsFinalizingSetup={setIsFinalizingSetup}
						pollingProgress={pollingProgress}
						setPollingProgress={setPollingProgress}
					/>
				);

			case "feature-tour":
				return (
					<FeatureTourStep
						onNextStep={handleNextStep}
						onPrevStep={handlePrevStep}
					/>
				);

			case "discord-setup":
				return (
					<DiscordSetupStep
						onNextStep={handleNextStep}
						onPrevStep={handlePrevStep}
						isDiscordLinked={isDiscordLinked}
						currentStep={currentStep}
					/>
				);

			case "completion":
				return (
					<CompletionStep
						onNextStep={handleNextStep}
						onPrevStep={handlePrevStep}
						isDiscordLinked={isDiscordLinked}
					/>
				);
		}
	};

	return (
		<div className="space-y-6">
			{/* Header - matching app pattern */}
			<div>
				<h1 className="font-bold text-3xl text-foreground">🚀 Account Setup</h1>
				<p className="text-muted-foreground">
					Let's get your MonkeyMenu account configured
				</p>
			</div>

			{/* Progress Bar */}
			<Card>
				<CardContent className="p-6">
					<div className="space-y-3">
						<div className="flex justify-between text-sm">
							<span className="text-muted-foreground">
								Step {currentStepIndex + 1} of {ONBOARDING_STEPS.length}
							</span>
							<span className="text-muted-foreground">
								{Math.round(progress)}%
							</span>
						</div>
						<Progress value={progress} className="h-2" />
						<div className="flex justify-between text-muted-foreground text-xs">
							{ONBOARDING_STEPS.map((step, index) => (
								<span
									key={step.id}
									className={index <= currentStepIndex ? "text-primary" : ""}
								>
									{step.title}
								</span>
							))}
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Step Content */}
			{renderStepContent()}
		</div>
	);
}
