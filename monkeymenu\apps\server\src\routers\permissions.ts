import { FACTION_ROLES, PERMISSIONS } from "@monkeymenu/shared";
import { desc, eq } from "drizzle-orm";
import { z } from "zod";
import { user } from "../db/schema/auth";
import {
	type Permission, // <-- add this import
	factionRole,
	permission,
	rolePermission,
	userRole,
} from "../db/schema/permissions";
import { tornUser } from "../db/schema/torn";
import { getUserPermissionContext } from "../lib/permissions";
import {
	factionPermissionProcedure,
	publicProcedure,
	requirePermission,
	router,
} from "../lib/trpc";

export const permissionsRouter = router({
	// Get all roles with their permissions
	getRoles: factionPermissionProcedure.query(async ({ ctx }) => {
		// Fetch all roles
		const roles = await ctx.db
			.select()
			.from(factionRole)
			.orderBy(desc(factionRole.hierarchyLevel));

		// Fetch all role-permission mappings with permission details
		const rolePerms = await ctx.db
			.select({
				roleId: rolePermission.roleId,
				permission: permission,
			})
			.from(rolePermission)
			.innerJoin(permission, eq(rolePermission.permissionId, permission.id));

		// Group permissions by roleId
		const permsByRole: Record<number, Permission[]> = {};
		for (const rp of rolePerms) {
			if (!permsByRole[rp.roleId]) permsByRole[rp.roleId] = [];
			permsByRole[rp.roleId].push(rp.permission);
		}

		// Return roles with their permissions as an array
		return roles.map((role) => ({
			role,
			permissions: permsByRole[role.id] || [],
		}));
	}),

	// Get all permissions
	getPermissions: factionPermissionProcedure.query(async ({ ctx }) => {
		return await ctx.db
			.select()
			.from(permission)
			.orderBy(permission.category, permission.name);
	}),

	// Get user roles with user info and suspension status
	getUserRoles: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.ADMIN_VIEW.name))
		.query(async ({ ctx }) => {
			return await ctx.db
				.select({
					userRole: userRole,
					user: {
						id: user.id,
						name: user.name,
						email: user.email,
						image: user.image,
					},
					role: factionRole,
					suspensionStatus: {
						accessSuspended: tornUser.accessSuspended,
						accessSuspensionReason: tornUser.accessSuspensionReason,
						accessSuspendedAt: tornUser.accessSuspendedAt,
						suspensionType: tornUser.suspensionType,
					},
				})
				.from(userRole)
				.innerJoin(user, eq(userRole.userId, user.id))
				.innerJoin(factionRole, eq(userRole.roleId, factionRole.id))
				.leftJoin(tornUser, eq(user.id, tornUser.id))
				.where(eq(userRole.isActive, true))
				.orderBy(desc(factionRole.hierarchyLevel));
		}),

	// Get current user's permissions - use publicProcedure to handle unauthenticated users
	getMyPermissions: publicProcedure.query(async ({ ctx }) => {
		// If user is not authenticated, return default permissions
		if (!ctx.session?.userId) {
			return {
				userId: null,
				roleId: null,
				roleName: null,
				roleLevel: 0,
				permissions: [] as string[], // Explicitly type as string array
			};
		}

		// If user is authenticated, get their full permission context
		try {
			const permissionContext = await getUserPermissionContext(
				ctx.db,
				ctx.session.userId,
			);
			return permissionContext;
		} catch (error) {
			// If there's an error getting permissions, return default permissions
			console.error("Error getting user permissions:", error);
			return {
				userId: ctx.session.userId,
				roleId: null,
				roleName: null,
				roleLevel: 0,
				permissions: [] as string[], // Explicitly type as string array
			};
		}
	}),

	// Assign role to user
	assignRole: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.ADMIN_VIEW.name))
		.input(
			z.object({
				userId: z.string(),
				roleId: z.number(),
			}),
		)
		.mutation(async ({ ctx, input }) => {
			// Check if assigner has higher role than target role
			const targetRole = await ctx.db
				.select()
				.from(factionRole)
				.where(eq(factionRole.id, input.roleId))
				.get();

			if (!targetRole) {
				throw new Error("Role not found");
			}

			// Only allow assigning roles at or below your own level (unless you're Leader)
			if (
				ctx.permissions.roleName !== FACTION_ROLES.LEADER.name &&
				targetRole.hierarchyLevel > ctx.permissions.roleLevel
			) {
				throw new Error("Cannot assign a role above your own level");
			}

			// D1 doesn't support Drizzle transactions, use sequential operations
			await ctx.db
				.update(userRole)
				.set({ isActive: false })
				.where(eq(userRole.userId, input.userId));

			try {
				const result = await ctx.db
					.insert(userRole)
					.values({
						userId: input.userId,
						roleId: input.roleId,
						assignedBy: ctx.session.userId,
						isActive: true,
					})
					.returning()
					.get();
				return result;
			} catch (error) {
				// Handle unique constraint violation from concurrent assignment
				if (
					error instanceof Error &&
					error.message.includes("UNIQUE constraint failed")
				) {
					throw new Error(
						"Role assignment conflict: User may already have an active role. Please try again.",
					);
				}
				throw error;
			}
		}),

	// Remove user role
	removeRole: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.ADMIN_VIEW.name))
		.input(
			z.object({
				userId: z.string(),
			}),
		)
		.mutation(async ({ ctx, input }) => {
			// Get the user's current role to check permissions
			const currentUserRole = await ctx.db
				.select({
					role: factionRole,
				})
				.from(userRole)
				.innerJoin(factionRole, eq(userRole.roleId, factionRole.id))
				.where(eq(userRole.userId, input.userId))
				.get();

			if (currentUserRole) {
				// Only allow removing roles below your own level (unless you're Leader)
				if (
					ctx.permissions.roleName !== FACTION_ROLES.LEADER.name &&
					currentUserRole.role.hierarchyLevel > ctx.permissions.roleLevel
				) {
					throw new Error("Cannot remove a role above your own level");
				}
			}

			await ctx.db
				.update(userRole)
				.set({ isActive: false })
				.where(eq(userRole.userId, input.userId));

			return { success: true };
		}),

	// Update role permissions (System Admin and Leader only)
	updateRolePermissions: factionPermissionProcedure
		.use(requirePermission(PERMISSIONS.ADMIN_VIEW.name))
		.input(
			z.object({
				roleId: z.number(),
				permissionIds: z.array(z.number()),
			}),
		)
		.mutation(async ({ ctx, input }) => {
			// Only System Admin and Leader can modify permissions
			if (
				ctx.permissions.roleName !== FACTION_ROLES.SYSTEM_ADMIN.name &&
				ctx.permissions.roleName !== FACTION_ROLES.LEADER.name
			) {
				throw new Error(
					"Only System Administrators and Leaders can modify role permissions",
				);
			}

			// D1 doesn't support Drizzle transactions, use sequential operations
			await ctx.db
				.delete(rolePermission)
				.where(eq(rolePermission.roleId, input.roleId));

			if (input.permissionIds.length > 0) {
				const newPermissions = input.permissionIds.map((permissionId) => ({
					roleId: input.roleId,
					permissionId,
				}));

				await ctx.db.insert(rolePermission).values(newPermissions);
			}

			return { success: true };
		}),
});
