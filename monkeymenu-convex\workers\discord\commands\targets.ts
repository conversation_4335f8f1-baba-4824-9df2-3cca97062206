import { CommandContext, CommandOptionType, BaseSlashCreator } from "slash-create";
import { BaseCommand } from "./base";

export class TargetsCommand extends BaseCommand {
  constructor(creator: BaseSlashCreator, convexUrl: string) {
    super(creator, convexUrl, {
      name: "targets",
      description: "Show available targets with optional filters",
      options: [
        {
          type: CommandOptionType.STRING,
          name: "filter",
          description: "Filter targets (e.g., 'level 20-40', 'status active')",
          required: false,
        },
      ],
    });
  }

  async run(ctx: CommandContext) {
    const discordId = ctx.user.id;
    const filter = ctx.options.filter as string;
    const args = filter ? filter.split(" ") : [];

    try {
      // Handle the targets command through Convex
      const result = await this.handleBotCommand(discordId, "targets", args) as any;

      if (result?.success) {
        if (result.embed) {
          return this.createEmbedResponse(result.embed);
        } else {
          return this.createSuccessResponse(result.message || "Targets retrieved successfully");
        }
      } else {
        return this.createErrorResponse(result?.message || "❌ Failed to retrieve targets");
      }
    } catch (error) {
      console.error("Targets command error:", error);
      return this.createErrorResponse("❌ An error occurred while retrieving targets.");
    }
  }
}
