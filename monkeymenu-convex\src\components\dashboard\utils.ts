// Helper function to format currency
export const formatCurrency = (amount: number): string => {
	return new Intl.NumberFormat("en-US", {
		style: "currency",
		currency: "USD",
		minimumFractionDigits: 0,
		maximumFractionDigits: 0,
	}).format(amount);
};

// Helper function to format date
export const formatDate = (dateString: string): string => {
	return new Date(dateString).toLocaleDateString("en-US", {
		month: "short",
		day: "numeric",
		hour: "2-digit",
		minute: "2-digit",
	});
};

// Helper function to get preview text from markdown content
export const getPreviewText = (content: string, maxLength = 100): string => {
	// Remove markdown syntax for preview
	const cleanText = content
		.replace(/#{1,6}\s/g, "") // Remove headers
		.replace(/\*{1,2}(.*?)\*{1,2}/g, "$1") // Remove bold/italic
		.replace(/\[(.*?)\]\(.*?\)/g, "$1") // Remove links
		.replace(/`(.*?)`/g, "$1") // Remove inline code
		.replace(/\n/g, " ") // Replace newlines with spaces
		.trim();

	return cleanText.length > maxLength
		? `${cleanText.substring(0, maxLength)}...`
		: cleanText;
};