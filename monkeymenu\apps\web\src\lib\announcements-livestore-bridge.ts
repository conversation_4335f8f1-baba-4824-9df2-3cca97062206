import { useStore } from "@livestore/react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useCallback, useEffect, useRef } from "react";
import { events } from "../livestore/schema";
import { trpc } from "./trpc-client";

/**
 * Enhanced bridge hook that syncs tRPC announcements data with LiveStore
 * Maintains all existing functionality while adding real-time sync capabilities
 */
export function useAnnouncementsLiveStoreSync() {
	const { store } = useStore();
	const queryClient = useQueryClient();
	const syncedAnnouncementsRef = useRef(new Set<string>());

	// Sync announcements from tRPC to LiveStore
	const { data: announcements } = useQuery({
		...trpc.announcements.getAll.queryOptions(),
		staleTime: 30000, // 30 seconds - announcements don't change frequently
	});

	// === Helper to sync announcements (memoized) ===
	const syncAnnouncementsToLiveStore = useCallback(
		(announcementsToSync: typeof announcements | undefined) => {
			if (!announcementsToSync?.length) return;

			for (const item of announcementsToSync) {
				const announcementId = item.announcement.id.toString();
				if (!syncedAnnouncementsRef.current.has(announcementId)) {
					try {
						store.commit(
							events.announcementCreated({
								id: announcementId,
								title: item.announcement.title,
								content: item.announcement.content,
								category: item.announcement.category,
								authorId: item.announcement.authorId,
								authorName: item.author.name,
								createdAt: new Date(item.announcement.createdAt),
							}),
						);
						syncedAnnouncementsRef.current.add(announcementId);
					} catch (error) {
						console.error(
							`Failed to sync announcement ${announcementId} to LiveStore:`,
							error,
						);
						// If commit fails, don't add to set so we can retry
					}
				}
			}
		},
		[store],
	);

	// Initialize LiveStore with real announcements data only if not already present
	useEffect(() => {
		const initializeWithRealData = async () => {
			try {
				if (announcements?.length) {
					console.log(
						`[LiveStore] Initializing with ${announcements.length} announcements`,
					);
					syncAnnouncementsToLiveStore(announcements);
				}
			} catch (error) {
				console.error("Failed to initialize LiveStore with real data:", error);
			}
		};

		const timeoutId = setTimeout(initializeWithRealData, 100);
		return () => clearTimeout(timeoutId);
	}, [announcements, syncAnnouncementsToLiveStore]);

	// Reset sync tracking when announcements data changes
	useEffect(() => {
		if (announcements) {
			console.log(
				"[LiveStore] Announcements updated, clearing sync tracking for fresh sync",
			);
			syncedAnnouncementsRef.current.clear();
		}
	}, [announcements]);

	// Sync announcements to LiveStore when tRPC data updates
	useEffect(() => {
		syncAnnouncementsToLiveStore(announcements);
	}, [announcements, syncAnnouncementsToLiveStore]);

	// Listen to React Query cache updates for the announcements query
	useEffect(() => {
		const unsubscribe = queryClient.getQueryCache().subscribe((event) => {
			// We only care about changes to the ["announcements", "getAll"] query
			if (
				event.query?.queryKey?.[0] === "announcements" &&
				event.query?.queryKey?.[1] === "getAll" &&
				event.query.state.data
			) {
				syncAnnouncementsToLiveStore(
					event.query.state.data as typeof announcements,
				);
			}
		});

		return unsubscribe;
	}, [queryClient, syncAnnouncementsToLiveStore]);

	// Utility functions for manual sync (useful for immediate updates after mutations)
	const syncNewAnnouncement = (announcement: {
		id: string;
		title: string;
		content: string;
		category: string;
		authorId: string;
		authorName: string;
		createdAt: Date;
	}) => {
		try {
			console.log(
				`[LiveStore] Manually syncing new announcement ${announcement.id}`,
			);
			store.commit(
				events.announcementCreated({
					id: announcement.id,
					title: announcement.title,
					content: announcement.content,
					category: announcement.category,
					authorId: announcement.authorId,
					authorName: announcement.authorName,
					createdAt: announcement.createdAt,
				}),
			);
			syncedAnnouncementsRef.current.add(announcement.id);
		} catch (error) {
			console.error("Failed to sync new announcement to LiveStore:", error);
		}
	};

	const syncAnnouncementUpdate = (update: {
		id: string;
		title: string;
		content: string;
		category: string;
		updatedAt: Date;
	}) => {
		try {
			console.log(
				`[LiveStore] Manually syncing announcement update ${update.id}`,
			);
			store.commit(
				events.announcementUpdated({
					id: update.id,
					title: update.title,
					content: update.content,
					category: update.category,
					updatedAt: update.updatedAt,
				}),
			);
		} catch (error) {
			console.error("Failed to sync announcement update to LiveStore:", error);
		}
	};

	const syncAnnouncementDeletion = (id: string) => {
		try {
			console.log(`[LiveStore] Manually syncing announcement deletion ${id}`);
			store.commit(
				events.announcementDeleted({
					id,
				}),
			);
			syncedAnnouncementsRef.current.delete(id);
		} catch (error) {
			console.error(
				"Failed to sync announcement deletion to LiveStore:",
				error,
			);
		}
	};

	return {
		syncNewAnnouncement,
		syncAnnouncementUpdate,
		syncAnnouncementDeletion,
	};
}

/**
 * Hook to listen to WebSocket updates and sync them to LiveStore
 */
export function useAnnouncementsWebSocketLiveStoreSync() {
	useEffect(() => {
		// Note: WebSocket integration would go here
		// This would integrate with any existing announcements WebSocket hook
		// to sync real-time updates to LiveStore

		return () => {
			// Cleanup if needed
		};
	}, []);
}
