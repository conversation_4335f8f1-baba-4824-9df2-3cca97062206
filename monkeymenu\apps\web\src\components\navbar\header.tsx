import { ChevronDown } from "lucide-react";
import { useState } from "react";

import { useHasMinimumRole, useHasPermission } from "@/hooks/usePermissions";
import { authClient } from "@/lib/auth-client";
import { trpc } from "@/lib/trpc-client";
import { PERMISSION_NAMES, ROLE_LEVELS } from "@monkeymenu/shared";
import { useQuery } from "@tanstack/react-query";
import { Link } from "@tanstack/react-router";

import { ThemeToggle } from "@/components/navbar/theme-toggle";
import { UserMenu } from "@/components/navbar/user-menu";
import { Button } from "@/components/ui/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export function Header() {
	const { data: session } = authClient.useSession();
	const [isOpen, setIsOpen] = useState(false);

	// Query user profile to check faction membership
	const { data: userProfile } = useQuery({
		...trpc.user.getProfile.queryOptions(),
		enabled: !!session?.user?.id,
	});

	// Check permissions for faction features
	const { hasPermission: canViewDashboard } = useHasPermission(
		PERMISSION_NAMES.DASHBOARD_VIEW,
	);
	const { hasPermission: canViewGuides } = useHasPermission(
		PERMISSION_NAMES.GUIDES_VIEW,
	);
	const { hasPermission: canViewAnnouncements } = useHasPermission(
		PERMISSION_NAMES.ANNOUNCEMENTS_VIEW,
	);
	const { hasPermission: canViewTargetFinder } = useHasPermission(
		PERMISSION_NAMES.TARGET_FINDER_VIEW,
	);
	const { hasPermission: canViewWars } = useHasPermission(
		PERMISSION_NAMES.WARS_VIEW,
	);
	const { hasPermission: canViewBanking } = useHasPermission(
		PERMISSION_NAMES.BANKING_VIEW,
	);
	const { hasMinimumRole: hasAdminAccess } = useHasMinimumRole(
		ROLE_LEVELS.MONKEY_MENTOR,
	);

	// Determine available links based on user authentication, faction membership, and permissions
	const links = session?.user
		? [
				{ to: "/", label: "Home" },

				...(userProfile?.tornUser?.tornFactionId
					? [
							...(canViewDashboard
								? [{ to: "/dashboard", label: "Dashboard" }]
								: []),
							...(canViewGuides ? [{ to: "/guides", label: "Guides" }] : []),
							...(canViewAnnouncements
								? [{ to: "/announcements", label: "Announcements" }]
								: []),
							...(canViewTargetFinder
								? [{ to: "/target-finder", label: "Target Finder" }]
								: []),
							...(canViewWars ? [{ to: "/wars", label: "Wars" }] : []),
							...(canViewBanking ? [{ to: "/banking", label: "Banking" }] : []),
							...(hasAdminAccess ? [{ to: "/admin", label: "Admin" }] : []),
						]
					: []),
			]
		: [{ to: "/", label: "Home" }];

	return (
		<div>
			<div className="flex flex-row items-center justify-between px-2 py-1">
				{/* Mobile Navigation Menu */}
				<div className="md:hidden">
					<DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
						<DropdownMenuTrigger asChild>
							<Button
								variant="ghost"
								className="flex items-center gap-1 text-lg"
							>
								Menu
								<ChevronDown
									className={`h-4 w-4 transition-transform duration-200 ${
										isOpen ? "rotate-180" : ""
									}`}
								/>
								<span className="sr-only">Open menu</span>
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align="start">
							{links.map(({ to, label }) => (
								<DropdownMenuItem key={to} asChild className="text-lg">
									<Link to={to} className="w-full">
										{label}
									</Link>
								</DropdownMenuItem>
							))}
						</DropdownMenuContent>
					</DropdownMenu>
				</div>

				{/* Desktop Navigation */}
				<nav className="hidden items-center gap-1 md:flex">
					{links.map(({ to, label }) => (
						<Button key={to} className="text-lg" variant="ghost" asChild>
							<Link to={to}>{label}</Link>
						</Button>
					))}
				</nav>

				<div className="flex items-center gap-2">
					<ThemeToggle />
					{session ? (
						<UserMenu />
					) : (
						<Button variant="outline" asChild>
							<Link to="/sign-in">Sign In</Link>
						</Button>
					)}
				</div>
			</div>
			<hr />
		</div>
	);
}
