export interface AnnouncementCategory {
	id: string;
	label: string;
	emoji: string;
}

export interface AnnouncementData {
	announcement: {
		id: number;
		title: string;
		content: string;
		category: string;
		createdAt: string;
		updatedAt: string;
	};
	author?: {
		name: string;
	};
}

export interface AnnouncementFormData {
	title: string;
	content: string;
	category: string;
	isUrgent: boolean;
}

export interface AnnouncementStats {
	[key: string]: number;
}

export type ViewMode = "grid" | "list";
