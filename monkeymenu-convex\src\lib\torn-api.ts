import { z } from 'zod';


// Torn API response schemas
const TornUserSchema = z.object({
  player_id: z.number(),
  name: z.string(),
  level: z.number(),
  faction: z.object({
    faction_id: z.number(),
    faction_name: z.string(),
  }).optional(),
  avatar: z.string().optional(),
  last_action: z.object({
    status: z.string(),
    timestamp: z.number(),
  }),
});


export type TornUserData = z.infer<typeof TornUserSchema>;

export interface TornUser {
  player_id: number
  name: string
  level: number
  gender: string
  property: string
  signup: string
  awards: number
  friends: number
  enemies: number
  forum_posts: number
  karma: number
  age: number
  role: string
  donator: number
  property_id: number
  life: {
    current: number
    maximum: number
    increment: number
    interval: number
    ticktime: number
    fulltime: number
  }
  status: {
    description: string
    details: string
    state: string
    color: string
    until: number
  }
  job: {
    position: string
    company_id: number
    company_name: string
    company_type: number
  }
  faction: {
    position: string
    faction_id: number
    days_in_faction: number
    faction_name: string
    faction_tag: string
  }
  married: {
    spouse_id: number
    spouse_name: string
    duration: number
  }
  icons: {
    icon6: string
    icon8: string
    icon9: string
    icon27: string
    icon31: string
    icon32: string
  }
  personalstats: {
    bazaarcustomers: number
    bazaarsales: number
    bazaarprofit: number
    useractivity: number
    attackswon: number
    attackslost: number
    attacksdraw: number
    bestkillstreak: number
    moneymugged: number
    largestmug: number
    defendswon: number
    defendslost: number
    defendsstalemated: number
    roundsfired: number
    yourunaway: number
    theyrunaway: number
    highestbeaten: number
    peoplebusted: number
    failedbusts: number
    peoplebought: number
    peopleboughtspent: number
    virusescoded: number
    cityfinds: number
    traveltimes: number
    bountiesplaced: number
    bountiesreceived: number
    totalbountyreward: number
    revives: number
    revivesreceived: number
    medicalitemsused: number
    medicalcooldownused: number
    boostsused: number
    drugsused: number
    overdosed: number
    meritsbought: number
    logins: number
    personalsplaced: number
    classifiedadsplaced: number
    mailssent: number
    friendmailssent: number
    factionmailssent: number
    companymailssent: number
    spousemailssent: number
    cantaken: number
    exttaken: number
    kettaken: number
    lsdtaken: number
    opitaken: number
    shrtaken: number
    spetaken: number
    pcptaken: number
    xantaken: number
    victaken: number
    chahits: number
    heahits: number
    axehits: number
    grehits: number
    machits: number
    pishits: number
    rifhits: number
    shohits: number
    smghits: number
    piehits: number
    slahits: number
    argtravel: number
    mextravel: number
    dubtravel: number
    hawtravel: number
    japtravel: number
    lontravel: number
    soutravel: number
    switravel: number
    chitravel: number
    cantravel: number
    dumpfinds: number
    dumpsearches: number
    itemsdumped: number
    daysbeendonator: number
    caytravel: number
    thetravel: number
  }
}

export interface TornApiResponse<T> {
  error?: {
    code: number
    error: string
  }
  [key: string]: T | any
}

export class TornApiClient {
  private apiKey: string
  private baseUrl = 'https://api.torn.com'

  constructor(apiKey: string) {
    this.apiKey = apiKey
  }

  async getUserProfile(userId: number): Promise<TornUser> {
    const response = await fetch(
      `${this.baseUrl}/user/${userId}?selections=profile,personalstats&key=${this.apiKey}`
    )
    
    if (!response.ok) {
      throw new Error(`Torn API error: ${response.status}`)
    }
    
    const data = await response.json()
    
    if (data.error) {
      throw new Error(`Torn API error: ${data.error.error}`)
    }
    
    return data as TornUser
  }

  async validateUser(userId: number): Promise<boolean> {
    try {
      await this.getUserProfile(userId)
      return true
    } catch {
      return false
    }
  }

  async getFactionInfo(factionId: number) {
    const response = await fetch(
      `${this.baseUrl}/faction/${factionId}?selections=basic&key=${this.apiKey}`
    )
    
    if (!response.ok) {
      throw new Error(`Torn API error: ${response.status}`)
    }
    
    const data = await response.json()
    
    if (data.error) {
      throw new Error(`Torn API error: ${data.error.error}`)
    }
    
    return data
  }
}