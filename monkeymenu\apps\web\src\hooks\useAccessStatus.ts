import { trpc } from "@/lib/trpc-client";
import { TOAST_MESSAGES } from "@monkeymenu/shared";
import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { toast } from "sonner";

export function useAccessStatus() {
	const {
		data: accessStatus,
		isLoading,
		error,
		refetch,
	} = useQuery({
		...trpc.user.checkAccessStatus.queryOptions(),
		refetchInterval: 30000, // Check every 30 seconds
		refetchOnWindowFocus: true,
	});

	// Show toast when access is suspended
	useEffect(() => {
		if (accessStatus?.suspended && !isLoading) {
			toast.error(TOAST_MESSAGES.API_KEY_SUSPENDED);
		}
	}, [accessStatus?.suspended, isLoading]);

	return {
		accessStatus,
		isLoading,
		isAccessStatusLoading: isLoading, // Explicitly named for clarity
		error,
		refetch,
		isSuspended: accessStatus?.suspended || false,
	};
}
