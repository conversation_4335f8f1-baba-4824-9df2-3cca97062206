import { z } from 'zod';

const TORN_API_BASE = 'https://api.torn.com';

// Torn API response schemas
const TornUserSchema = z.object({
  player_id: z.number(),
  name: z.string(),
  level: z.number(),
  faction: z.object({
    faction_id: z.number(),
    faction_name: z.string(),
  }).optional(),
  avatar: z.string(),
});


export type TornUserData = z.infer<typeof TornUserSchema>;

export class TornAPI {
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async getUserData(tornId: number): Promise<TornUserData> {
    const url = `${TORN_API_BASE}/user/${tornId}?selections=profile&key=${this.apiKey}`;
    
    try {
      const response = await fetch(url);
      const data = await response.json();

      if (data.error) {
        throw new Error(`Torn API Error: ${data.error.error}`);
      }

      return TornUserSchema.parse(data);
    } catch (error) {
      console.error('Error fetching Torn user data:', error);
      throw error;
    }
  }

  async validateUser(tornId: number): Promise<boolean> {
    try {
      await this.getUserData(tornId);
      return true;
    } catch {
      return false;
    }
  }
}

export const tornAPI = new TornAPI(import.meta.env.VITE_TORN_API_KEY || '');