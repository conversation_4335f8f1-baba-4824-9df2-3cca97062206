import { useState } from 'react';
import { useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { usePermissions } from '../../hooks/usePermissions';

export function TargetListManagement() {
  const { hasPermission } = usePermissions();
  const [isInitializingLists, setIsInitializingLists] = useState(false);
  const [isCleaningDuplicates, setIsCleaningDuplicates] = useState(false);

  // Target lists initialization
  const initializeSharedLists = useMutation(api.targets.initializeSharedLists);
  const cleanupDuplicateTargets = useMutation(api.targets.cleanupDuplicateTargets);

  // Only show to admins
  if (!hasPermission('admin.view')) {
    return null;
  }

  const handleInitializeSharedLists = async () => {
    if (!confirm('Initialize and populate shared target lists? This will:\n\n1. Create "Faction Shared Targets A" and "Faction Shared Targets B" if they don\'t exist\n2. Populate them with 50 pre-selected targets from the old app (25 in each list)\n\nProceed?')) {
      return;
    }

    setIsInitializingLists(true);
    try {
      const result = await initializeSharedLists({});
      alert(result.message + `\n\nDetails:\n- Created List A: ${result.createdA}\n- Created List B: ${result.createdB}\n- Targets added to A: ${result.targetsAddedA}\n- Targets added to B: ${result.targetsAddedB}`);
    } catch (error: any) {
      console.error('Failed to initialize shared lists:', error);
      alert('Failed to initialize shared lists: ' + error.message);
    } finally {
      setIsInitializingLists(false);
    }
  };

  const handleCleanupDuplicates = async () => {
    if (!confirm('Cleanup duplicate targets? This will:\n\n1. Find targets that appear multiple times in the same list\n2. Remove the duplicate entries (keeping the oldest)\n\nThis action cannot be undone. Proceed?')) {
      return;
    }

    setIsCleaningDuplicates(true);
    try {
      const result = await cleanupDuplicateTargets({});
      alert(result.message + `\n\nDetails:\n${result.details.map(d => `- ${d.listName}: ${d.duplicatesRemoved} duplicates removed`).join('\n')}`);
    } catch (error: any) {
      console.error('Failed to cleanup duplicates:', error);
      alert('Failed to cleanup duplicates: ' + error.message);
    } finally {
      setIsCleaningDuplicates(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Target Lists Setup */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Admin: Target Lists Setup</h3>
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start">
              <div className="text-blue-600 mr-3 mt-1">ℹ️</div>
              <div>
                <p className="text-sm text-blue-800 font-medium mb-1">
                  Shared Target Lists Initialization
                </p>
                <p className="text-sm text-blue-700">
                  This will create and populate the shared faction target lists that all members can use:
                </p>
                <ul className="text-sm text-blue-700 mt-2 list-disc list-inside">
                  <li>Faction Shared Targets A (25 pre-selected targets)</li>
                  <li>Faction Shared Targets B (25 pre-selected targets)</li>
                </ul>
                <p className="text-sm text-blue-700 mt-2">
                  These lists will appear at the top of the target list dropdown for all users with 50 total targets from the old app.
                </p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <button
              onClick={handleInitializeSharedLists}
              disabled={isInitializingLists}
              className="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              {isInitializingLists ? 'Initializing...' : 'Initialize & Populate Shared Target Lists'}
            </button>
            <p className="text-sm text-gray-600">
              Safe to run multiple times - will only create lists and add targets if they don't exist
            </p>
          </div>
        </div>
      </div>

      {/* Target Cleanup */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Admin: Target Cleanup</h3>
        <div className="space-y-4">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start">
              <div className="text-yellow-600 mr-3 mt-1">⚠️</div>
              <div>
                <p className="text-sm text-yellow-800 font-medium mb-1">
                  Duplicate Target Cleanup
                </p>
                <p className="text-sm text-yellow-700">
                  Remove duplicate targets that appear multiple times in the same list. This can happen during data migration or if targets are accidentally added multiple times.
                </p>
                <p className="text-sm text-yellow-700 mt-2">
                  Only run this if you're experiencing issues with duplicate targets in your lists.
                </p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <button
              onClick={handleCleanupDuplicates}
              disabled={isCleaningDuplicates}
              className="bg-yellow-600 text-white px-6 py-2 rounded-md hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              {isCleaningDuplicates ? 'Cleaning...' : 'Cleanup Duplicate Targets'}
            </button>
            <p className="text-sm text-gray-600">
              Removes duplicate entries while keeping the oldest version of each target
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
