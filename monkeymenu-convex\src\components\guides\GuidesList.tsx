import { useState, useEffect } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { GuideCard } from './GuideCard';
import { Id } from '../../../convex/_generated/dataModel';
import { GUIDE_CATEGORIES, getCategoryDisplay } from './utils';

interface GuidesListProps {
  category?: string;
  publishedOnly?: boolean;
  onEdit?: (guide: any) => void;
  onDelete?: (guideId: Id<"guides">) => void;
  onView?: (guideId: Id<"guides">) => void;
  showActions?: boolean;
  viewMode?: 'grid' | 'list';
}

export const GuidesList: React.FC<GuidesListProps> = ({
  category,
  publishedOnly = true,
  onEdit,
  onDelete,
  onView,
  showActions = false,
  viewMode = 'grid',
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(category || '');

  const guides = useQuery(api.guides.getGuides, { 
    category: selectedCategory || undefined,
    publishedOnly 
  });
  const categories = useQuery(api.guides.getCategories);
  const searchResults = useQuery(
    api.guides.searchGuides, 
    searchTerm.trim() 
      ? { searchTerm: searchTerm.trim(), category: selectedCategory || undefined }
      : "skip"
  );

  const displayGuides = searchTerm.trim() ? searchResults : guides;

  if (guides === undefined || categories === undefined) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-md p-6 animate-pulse">
            <div className="h-6 bg-gray-200 rounded mb-3"></div>
            <div className="h-4 bg-gray-200 rounded mb-2 w-1/3"></div>
            <div className="h-20 bg-gray-200 rounded mb-4"></div>
            <div className="flex gap-2">
              <div className="h-6 bg-gray-200 rounded w-16"></div>
              <div className="h-6 bg-gray-200 rounded w-16"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search and Filter Controls */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search guides..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="sm:w-48">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Categories</option>
              {GUIDE_CATEGORIES.map(cat => (
                <option key={cat.value} value={cat.value}>
                  {getCategoryDisplay(cat.value)}
                </option>
              ))}
              {categories?.filter(cat => !GUIDE_CATEGORIES.find(c => c.value === cat)).map(cat => (
                <option key={cat} value={cat}>📖 {cat}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Results Summary */}
      {displayGuides && (
        <div className="text-sm text-gray-600">
          {searchTerm.trim() ? (
            <span>Found {displayGuides.length} guides matching "{searchTerm}"</span>
          ) : (
            <span>Showing {displayGuides.length} guides</span>
          )}
          {selectedCategory && (
            <span> in category "{selectedCategory}"</span>
          )}
        </div>
      )}

      {/* Guides Grid/List */}
      {displayGuides && displayGuides.length > 0 ? (
        <div className={
          viewMode === 'grid' 
            ? "grid gap-6 md:grid-cols-2 lg:grid-cols-3"
            : "space-y-4"
        }>
          {displayGuides.map((guide) => (
            <GuideCard
              key={guide._id}
              guide={guide}
              onEdit={onEdit}
              onDelete={onDelete}
              onView={onView}
              showActions={showActions}
              viewMode={viewMode}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No guides found</h3>
          <p className="text-gray-600">
            {searchTerm.trim() 
              ? "Try adjusting your search terms or filters."
              : "No guides have been created yet."
            }
          </p>
        </div>
      )}
    </div>
  );
};