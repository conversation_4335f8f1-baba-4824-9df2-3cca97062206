import { router } from "../lib/trpc";
import { adminRouter } from "./admin";
import { announcementsRouter } from "./announcements";
import { bankingRouter } from "./banking";
import { botRouter } from "./bot";

import { guidesRouter } from "./guides";
import { permissionsRouter } from "./permissions";
import { targetFinderRouter } from "./targetFinder";
import { userRouter } from "./user";
import { warsRouter } from "./wars";

export const appRouter = router({
	user: userRouter,
	guides: guidesRouter,
	announcements: announcementsRouter,
	permissions: permissionsRouter,
	targetFinder: targetFinderRouter,
	admin: adminRouter,
	banking: bankingRouter,
	bot: botRouter,
	wars: warsRouter,
});
export type AppRouter = typeof appRouter;
