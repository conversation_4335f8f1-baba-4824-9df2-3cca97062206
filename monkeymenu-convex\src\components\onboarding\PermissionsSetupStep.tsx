import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { ArrowRight, CheckCircle, Clock } from "lucide-react";
import type { StepComponentProps } from "./types";

interface PermissionsSetupStepProps extends StepComponentProps {
  permissions?: string[];
  isProcessing?: boolean;
}

export function PermissionsSetupStep({ 
  onNextStep, 
  onPrevStep, 
  permissions = [],
  isProcessing = false 
}: PermissionsSetupStepProps) {
  
  const hasPermissions = permissions.length > 0;

  return (
    <Card>
      <CardContent className="space-y-6 p-8">
        <div className="space-y-2 text-center">
          <h2 className="font-bold text-2xl tracking-tight">Setting Up Permissions</h2>
          <p className="mx-auto max-w-md text-muted-foreground leading-relaxed">
            We're configuring your access level based on your faction membership
            and role.
          </p>
        </div>

        <div className="space-y-4">
          {isProcessing ? (
            <div className="flex items-center justify-center gap-2 rounded-lg bg-blue-50 p-4 text-blue-600 dark:bg-blue-950/20 dark:text-blue-400">
              <Clock className="h-5 w-5 animate-spin" />
              <span className="font-medium">Processing your permissions...</span>
            </div>
          ) : hasPermissions ? (
            <div className="space-y-3">
              <div className="flex items-center justify-center gap-2 rounded-lg bg-green-50 p-4 text-green-600 dark:bg-green-950/20 dark:text-green-400">
                <CheckCircle className="h-5 w-5" />
                <span className="font-medium">Permissions configured!</span>
              </div>
              
              <div className="text-center">
                <h3 className="font-semibold text-sm mb-2">Your Access Level:</h3>
                <div className="space-y-1 text-xs text-muted-foreground">
                  {permissions.map((permission) => (
                    <div key={permission} className="flex items-center justify-center gap-2">
                      <span className="w-1.5 h-1.5 bg-green-500 rounded-full"></span>
                      {permission}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center text-muted-foreground">
              <p className="text-sm">
                Please contact an administrator if you need access to specific features.
              </p>
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter className="flex gap-3 p-8 pt-0">
        <Button
          type="button"
          variant="outline"
          onClick={onPrevStep}
          disabled={isProcessing}
          className="flex-1"
        >
          Back
        </Button>
        <Button
          onClick={onNextStep}
          disabled={isProcessing}
          className="flex-1"
        >
          {isProcessing ? "Processing..." : "Continue"}
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  )
}