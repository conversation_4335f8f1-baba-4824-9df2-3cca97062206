import { createContext, useContext, useEffect, useState } from "react";

// Import theme CSS files
import "../styles/themes/default.css";
import "../styles/themes/t3-chat.css";
import "../styles/themes/claude.css";

type ThemeStyle = "default" | "t3-chat" | "claude";

type ThemeStyleProviderProps = {
	children: React.ReactNode;
	defaultThemeStyle?: ThemeStyle;
	storageKey?: string;
};

type ThemeStyleProviderState = {
	themeStyle: ThemeStyle;
	setThemeStyle: (themeStyle: ThemeStyle) => void;
};

const initialState: ThemeStyleProviderState = {
	themeStyle: "default",
	setThemeStyle: () => null,
};

const ThemeStyleProviderContext =
	createContext<ThemeStyleProviderState>(initialState);

export function ThemeStyleProvider({
	children,
	defaultThemeStyle = "default",
	storageKey = "theme-style",
	...props
}: ThemeStyleProviderProps) {
	const [themeStyle, setThemeStyle] = useState<ThemeStyle>(
		() => (localStorage.getItem(storageKey) as ThemeStyle) || defaultThemeStyle,
	);
	const [mounted, setMounted] = useState(false);

	useEffect(() => {
		setMounted(true);
	}, []);

	useEffect(() => {
		const root = window.document.documentElement;

		// Remove all theme-* classes
		for (const cls of root.classList) {
			if (cls.startsWith("theme-")) {
				root.classList.remove(cls);
			}
		}

		// Add the new theme class
		root.classList.add(`theme-${themeStyle}`);

		// Store in localStorage
		localStorage.setItem(storageKey, themeStyle);
	}, [themeStyle, storageKey]);

	// Don't render until mounted to avoid hydration mismatch
	if (!mounted) {
		return null;
	}

	const value = {
		themeStyle,
		setThemeStyle: (themeStyle: ThemeStyle) => {
			setThemeStyle(themeStyle);
		},
	};

	return (
		<ThemeStyleProviderContext.Provider {...props} value={value}>
			{children}
		</ThemeStyleProviderContext.Provider>
	);
}

export const useThemeStyle = () => {
	const context = useContext(ThemeStyleProviderContext);

	if (context === undefined)
		throw new Error("useThemeStyle must be used within a ThemeStyleProvider");

	return context;
};

// Available theme styles
export const THEME_STYLES = [
	{ value: "default" as const, label: "Default" },
	{ value: "t3-chat" as const, label: "T3 Chat" },
	{ value: "claude" as const, label: "Claude" },
] as const;

export type { ThemeStyle };
