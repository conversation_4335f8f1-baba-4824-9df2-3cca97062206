import React from 'react';
import { useSession } from '../../hooks/useSession';
import { usePermissions } from '../../hooks/usePermissions';

interface CanPerformActionProps {
  permission: string;
  resource?: {
    ownerId?: string;
    authorId?: string;
    userId?: string;
  };
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function CanPerformAction({ 
  permission, 
  resource, 
  children, 
  fallback = null 
}: CanPerformActionProps) {
  const { convexUser } = useSession();
  const { hasPermission } = usePermissions();
  
  // Check if user has the permission
  const hasRequiredPermission = hasPermission(permission);
  
  // Check ownership if resource is provided
  let isOwner = false;
  if (resource && convexUser) {
    const resourceOwnerId = resource.ownerId || resource.authorId || resource.userId;
    isOwner = resourceOwnerId === convexUser._id;
  }
  
  // Allow if user has permission OR if they own the resource (for edit/delete actions)
  const canPerform = hasRequiredPermission || isOwner;
  
  if (!canPerform) {
    return <>{fallback}</>;
  }
  
  return <>{children}</>;
}