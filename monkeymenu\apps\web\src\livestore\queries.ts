import { queryDb } from "@livestore/livestore";
import { tables } from "./schema";

// Query all withdrawal requests for the current user
export const myWithdrawals$ = queryDb(
	() =>
		tables.withdrawalRequests
			.orderBy([{ col: "createdAt", direction: "desc" }])
			.limit(50),
	{ label: "myWithdrawals" },
);

// Query all withdrawal requests (for admins)
export const allWithdrawals$ = queryDb(
	() =>
		tables.withdrawalRequests
			.orderBy([{ col: "createdAt", direction: "desc" }])
			.limit(100),
	{ label: "allWithdrawals" },
);

// Query withdrawal requests by status
export const withdrawalsByStatus$ = (
	status: "PENDING" | "ACCEPTED" | "DECLINED" | "all",
) =>
	queryDb(
		() => {
			const query = tables.withdrawalRequests
				.orderBy([{ col: "createdAt", direction: "desc" }])
				.limit(100);
			return status === "all" ? query : query.where({ status });
		},
		{ label: `withdrawalsByStatus_${status}`, deps: [status] },
	);

// Query pending withdrawals (for admin dashboard)
export const pendingWithdrawals$ = queryDb(
	() =>
		tables.withdrawalRequests
			.where({ status: "PENDING" })
			.orderBy([{ col: "createdAt", direction: "desc" }]),
	{ label: "pendingWithdrawals" },
);

// Query faction balance - fixed to properly handle SQLite queries
export const factionBalance$ = queryDb(
	() => tables.factionBalance.where({ id: "faction_balance" }).limit(1),
	{ label: "factionBalance" },
);

// Query UI state
export const bankingUIState$ = queryDb(() => tables.bankingUIState.limit(1), {
	label: "bankingUIState",
});

// Query recent activity (last 10 withdrawals)
export const recentBankingActivity$ = queryDb(
	() =>
		tables.withdrawalRequests
			.orderBy([{ col: "updatedAt", direction: "desc" }])
			.limit(10),
	{ label: "recentBankingActivity" },
);

// Query withdrawal by ID
export const withdrawalById$ = (id: string) =>
	queryDb(() => tables.withdrawalRequests.where({ id }).limit(1), {
		label: `withdrawal_${id}`,
		deps: [id],
	});

// Query user's withdrawal count
export const userWithdrawalCount$ = (userId: string) =>
	queryDb(
		() => tables.withdrawalRequests.where({ requestedById: userId }).count(),
		{ label: `userWithdrawalCount_${userId}`, deps: [userId] },
	);

// ===== TARGET FINDER QUERIES =====

// Query all target lists
export const targetLists$ = queryDb(
	() =>
		tables.targetLists
			.orderBy([{ col: "createdAt", direction: "desc" }])
			.limit(100),
	{ label: "targetLists" },
);

// Query target lists by user (for custom lists)
export const targetListsByUser$ = (userId: string) =>
	queryDb(
		() =>
			tables.targetLists
				.where({ userId })
				.orderBy([{ col: "createdAt", direction: "desc" }]),
		{ label: `targetListsByUser_${userId}`, deps: [userId] },
	);

// Query shared target lists (no userId)
export const sharedTargetLists$ = queryDb(
	() =>
		tables.targetLists
			.where({ userId: null })
			.orderBy([{ col: "createdAt", direction: "desc" }]),
	{ label: "sharedTargetLists" },
);

// Query targets by list ID
export const targetsByListId$ = (listId: string) =>
	queryDb(
		() =>
			tables.targets
				.where({ listId })
				.orderBy([{ col: "createdAt", direction: "desc" }])
				.limit(100),
		{ label: `targetsByListId_${listId}`, deps: [listId] },
	);

// Query targets by status within a list
// Note: For complex status filtering (hospitalized/error), client-side filtering is required
// since LiveStore doesn't support LIKE queries
export const targetsByListAndStatus$ = (
	listId: string,
	status: "okay" | "hospitalized" | "error" | "all",
) =>
	queryDb(
		() => {
			if (!listId) {
				throw new Error("listId is required for targetsByListAndStatus query");
			}

			const baseQuery = tables.targets
				.where({ listId })
				.orderBy([{ col: "createdAt", direction: "desc" }])
				.limit(100);

			// Only apply direct database filtering for exact matches
			if (status === "okay") {
				return baseQuery.where({ status: "Okay" });
			}

			// For "hospitalized", "error", and "all" - return all targets
			// and let the component handle filtering
			return baseQuery;
		},
		{
			label: `targetsByListAndStatus_${listId}_${status}`,
			deps: [listId, status],
		},
	);

// Query target by ID
export const targetById$ = (id: string) =>
	queryDb(() => tables.targets.where({ id }).limit(1), {
		label: `target_${id}`,
		deps: [id],
	});

// Query target by torn ID
export const targetByTornId$ = (tornId: string) =>
	queryDb(() => tables.targets.where({ tornId }).limit(1), {
		label: `targetByTornId_${tornId}`,
		deps: [tornId],
	});

// Query all targets (for searching across lists)
export const allTargets$ = queryDb(
	() =>
		tables.targets
			.orderBy([{ col: "updatedAt", direction: "desc" }])
			.limit(500),
	{ label: "allTargets" },
);

// Query recent target activity (last 20 targets updated)
export const recentTargetActivity$ = queryDb(
	() =>
		tables.targets.orderBy([{ col: "updatedAt", direction: "desc" }]).limit(20),
	{ label: "recentTargetActivity" },
);

// Query chain status
export const chainStatus$ = queryDb(
	() => tables.chainStatus.where({ id: "chain_status" }).limit(1),
	{ label: "chainStatus" },
);

// Query target finder cooldown
export const targetFinderCooldown$ = queryDb(
	() =>
		tables.targetFinderCooldown
			.where({ id: "target_finder_cooldown" })
			.limit(1),
	{ label: "targetFinderCooldown" },
);

// Query target finder UI state
export const targetFinderUIState$ = queryDb(
	() => tables.targetFinderUIState.limit(1),
	{
		label: "targetFinderUIState",
	},
);

// Query targets with specific status patterns (for client-side filtering)
export const targetsWithOkayStatus$ = (listId: string) =>
	queryDb(
		() =>
			tables.targets
				.where({ listId, status: "Okay" })
				.orderBy([{ col: "createdAt", direction: "desc" }]),
		{ label: `targetsWithOkayStatus_${listId}`, deps: [listId] },
	);

// Query target counts by list
export const targetCountByListId$ = (listId: string) =>
	queryDb(() => tables.targets.where({ listId }).count(), {
		label: `targetCountByListId_${listId}`,
		deps: [listId],
	});

// Query target list by name
export const targetListByName$ = (name: string) =>
	queryDb(() => tables.targetLists.where({ name }).limit(1), {
		label: `targetListByName_${name}`,
		deps: [name],
	});

// Query external target lists
export const externalTargetLists$ = queryDb(
	() =>
		tables.targetLists
			.where({ isExternal: 1 })
			.orderBy([{ col: "createdAt", direction: "desc" }]),
	{ label: "externalTargetLists" },
);

// Query internal target lists (non-external)
export const internalTargetLists$ = queryDb(
	() =>
		tables.targetLists
			.where({ isExternal: 0 })
			.orderBy([{ col: "createdAt", direction: "desc" }]),
	{ label: "internalTargetLists" },
);

// Query targets that need status updates (older than 5 minutes)
export const staleTargets$ = queryDb(
	() => {
		const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
		return tables.targets
			.where({ updatedAt: { $lt: fiveMinutesAgo.getTime() } })
			.limit(50);
	},
	{ label: "staleTargets" },
);

// ===== ANNOUNCEMENTS QUERIES =====

// Query all announcements
export const announcements$ = queryDb(
	() =>
		tables.announcements
			.orderBy([{ col: "createdAt", direction: "desc" }])
			.limit(100),
	{ label: "announcements" },
);

// Query announcements by category
export const announcementsByCategory$ = (category: string) =>
	queryDb(
		() => {
			const query = tables.announcements
				.orderBy([{ col: "createdAt", direction: "desc" }])
				.limit(100);
			return category === "all" ? query : query.where({ category });
		},
		{ label: `announcementsByCategory_${category}`, deps: [category] },
	);

// Query announcement by ID
export const announcementById$ = (id: string) =>
	queryDb(() => tables.announcements.where({ id }).limit(1), {
		label: `announcement_${id}`,
		deps: [id],
	});

// Query recent announcements (last 10)
export const recentAnnouncements$ = queryDb(
	() =>
		tables.announcements
			.orderBy([{ col: "createdAt", direction: "desc" }])
			.limit(10),
	{ label: "recentAnnouncements" },
);

// Query announcements by priority (disabled - priority column doesn't exist)
// export const announcementsByPriority$ = (priority: string) =>
// 	queryDb(
// 		() =>
// 			tables.announcements
// 				.where({ priority })
// 				.orderBy([{ col: "createdAt", direction: "desc" }]),
// 		{ label: `announcementsByPriority_${priority}`, deps: [priority] },
// 	);

// Query announcements UI state
export const announcementsUIState$ = queryDb(
	() => tables.announcementsUIState.limit(1),
	{
		label: "announcementsUIState",
	},
);

// ===== ADMIN QUERIES =====

// Query admin system stats
export const adminSystemStats$ = queryDb(
	() => tables.adminSystemStats.where({ id: "admin_system_stats" }).limit(1),
	{ label: "adminSystemStats" },
);

// Query admin audit log (recent permission changes)
export const adminAuditLog$ = queryDb(
	() =>
		tables.adminAuditLog
			.orderBy([{ col: "assignedAt", direction: "desc" }])
			.limit(50),
	{ label: "adminAuditLog" },
);

// Query admin audit log by user
export const adminAuditLogByUser$ = (userId: string) =>
	queryDb(
		() =>
			tables.adminAuditLog
				.where({ assignedToUserId: userId })
				.orderBy([{ col: "assignedAt", direction: "desc" }])
				.limit(20),
		{ label: `adminAuditLogByUser_${userId}`, deps: [userId] },
	);

// Query all admin users
export const adminUsers$ = queryDb(
	() =>
		tables.adminUsers
			.orderBy([{ col: "createdAt", direction: "desc" }])
			.limit(100),
	{ label: "adminUsers" },
);

// Query admin users by status
export const adminUsersByStatus$ = (status: "all" | "active" | "suspended") =>
	queryDb(
		() => {
			const query = tables.adminUsers
				.orderBy([{ col: "createdAt", direction: "desc" }])
				.limit(100);
			return status === "all"
				? query
				: status === "suspended"
					? query.where({ suspended: 1 })
					: query.where({ suspended: 0 });
		},
		{ label: `adminUsersByStatus_${status}`, deps: [status] },
	);

// Query admin users by role
export const adminUsersByRole$ = (roleId: string) =>
	queryDb(
		() => {
			const query = tables.adminUsers
				.orderBy([{ col: "createdAt", direction: "desc" }])
				.limit(100);
			return roleId === "all" ? query : query.where({ roleId });
		},
		{ label: `adminUsersByRole_${roleId}`, deps: [roleId] },
	);

// Query admin user by ID
export const adminUserById$ = (id: string) =>
	queryDb(() => tables.adminUsers.where({ id }).limit(1), {
		label: `adminUser_${id}`,
		deps: [id],
	});

// Query suspended admin users
export const suspendedAdminUsers$ = queryDb(
	() =>
		tables.adminUsers
			.where({ suspended: 1 })
			.orderBy([{ col: "suspendedAt", direction: "desc" }]),
	{ label: "suspendedAdminUsers" },
);

// Query admin users with specific role level
export const adminUsersByRoleLevel$ = (minLevel: number) =>
	queryDb(
		() =>
			tables.adminUsers
				.where({ roleHierarchyLevel: { $gte: minLevel } })
				.orderBy([{ col: "roleHierarchyLevel", direction: "desc" }]),
		{ label: `adminUsersByRoleLevel_${minLevel}`, deps: [minLevel] },
	);

// Query recent admin activity (last 20 audit entries)
export const recentAdminActivity$ = queryDb(
	() =>
		tables.adminAuditLog
			.orderBy([{ col: "assignedAt", direction: "desc" }])
			.limit(20),
	{ label: "recentAdminActivity" },
);

// Query admin user count
export const adminUserCount$ = queryDb(() => tables.adminUsers.count(), {
	label: "adminUserCount",
});

// Query suspended user count
export const suspendedUserCount$ = queryDb(
	() => tables.adminUsers.where({ suspended: 1 }).count(),
	{ label: "suspendedUserCount" },
);

// Query admin UI state
export const adminUIState$ = queryDb(() => tables.adminUIState.limit(1), {
	label: "adminUIState",
});
