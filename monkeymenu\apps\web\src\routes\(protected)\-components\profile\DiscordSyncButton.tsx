import { Button } from "@/components/ui/button";
import { trpc } from "@/lib/trpc-client";
import { useMutation, useQuery } from "@tanstack/react-query";
import { CheckCircle, Loader2 } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";

export function DiscordSyncButton() {
	const [isSyncing, setIsSyncing] = useState(false);
	const [hasAutoSynced, setHasAutoSynced] = useState(false);

	// Get user data to check if sync is available
	const profile = useQuery(trpc.user.getProfile.queryOptions());
	const permissions = useQuery(
		trpc.permissions.getMyPermissions.queryOptions(),
	);

	// Check if we should auto-start verification from URL parameter
	const shouldAutoSync =
		typeof window !== "undefined" &&
		window.location.search.includes("verify=true") &&
		!hasAutoSynced;

	// Sync mutation
	const syncMutation = useMutation(
		trpc.bot.verifyDiscordUser.mutationOptions({
			onSuccess: () => {
				toast.success("Discord roles synced successfully!");
				// Refetch user data to get updated info
				profile.refetch();
				permissions.refetch();
				// Clear the URL parameter to prevent re-syncing
				if (typeof window !== "undefined") {
					const url = new URL(window.location.href);
					url.searchParams.delete("verify");
					window.history.replaceState({}, "", url.toString());
				}
			},
			onError: (error: { message?: string }) => {
				toast.error(`Sync failed: ${error.message}`);
			},
			onSettled: () => {
				setIsSyncing(false);
				setHasAutoSynced(true);
			},
		}),
	);

	const handleSync = useCallback(async () => {
		setIsSyncing(true);
		syncMutation.mutate();
	}, [syncMutation]);

	// Auto-start sync if coming from Discord button
	useEffect(() => {
		if (shouldAutoSync && !isSyncing) {
			// Small delay to let the page load
			const timer = setTimeout(() => {
				handleSync();
			}, 1000);
			return () => clearTimeout(timer);
		}
	}, [shouldAutoSync, isSyncing, handleSync]);

	// Check if sync is available
	const hasApiKey = profile.data?.tornUser?.tornApiKeyVerified;
	const hasDiscord = !!profile.data?.user;
	const canSync = hasApiKey && hasDiscord;

	if (!hasDiscord) {
		return null; // Don't show sync button if Discord isn't linked
	}

	return (
		<Button
			variant="outline"
			size="sm"
			onClick={handleSync}
			disabled={isSyncing || !canSync}
			className="flex items-center gap-2"
		>
			{isSyncing ? (
				<>
					<Loader2 className="h-4 w-4 animate-spin" />
					Syncing...
				</>
			) : (
				<>
					<CheckCircle className="h-4 w-4" />
					Sync Roles
				</>
			)}
		</Button>
	);
}
