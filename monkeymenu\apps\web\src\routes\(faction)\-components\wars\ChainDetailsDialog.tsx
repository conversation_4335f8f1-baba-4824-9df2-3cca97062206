import { Badge } from "@/components/ui/badge";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { trpc } from "@/lib/trpc-client";
import { useQuery } from "@tanstack/react-query";
import { Clock, Crown, Swords, Target, Users, Zap } from "lucide-react";
import type { ChainAttacker } from "./types";
import { formatDateTime, formatDuration } from "./utils";

interface ChainDetailsDialogProps {
	isOpen: boolean;
	onClose: () => void;
	warId: number;
	chainId: number;
}

export function ChainDetailsDialog({
	isOpen,
	onClose,
	warId,
	chainId,
}: ChainDetailsDialogProps) {
	const chainDetailsQuery = useQuery({
		...trpc.wars.getIndividualChainDetails.queryOptions({ warId, chainId }),
		enabled: isOpen && chainId > 0,
	});

	const chainData = chainDetailsQuery.data;

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-h-[90vh] w-full max-w-6xl overflow-y-auto px-4 sm:px-6">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<span className="text-2xl">🔗</span>
						Chain #{chainId} - Detailed Analysis
					</DialogTitle>
					<DialogDescription>
						Deep dive into chain performance, attack sequence, and metrics
						{chainData && (
							<div className="mt-2 text-muted-foreground text-xs">
								Chain Report: {chainData.chainReport.details ? "✓" : "✗"} •
								Attackers: {chainData.chainReport.attackers?.length || 0} •
								Attack Sequence: {chainData.attackSequence.length} • Timeline
								Periods: {chainData.chainMetrics.timelineBreakdown.length}
							</div>
						)}
					</DialogDescription>
				</DialogHeader>

				{chainDetailsQuery.isLoading && (
					<div className="space-y-6">
						<div className="grid gap-4 md:grid-cols-3">
							{["chain-skeleton-1", "chain-skeleton-2", "chain-skeleton-3"].map(
								(key) => (
									<Skeleton key={key} className="h-24 w-full" />
								),
							)}
						</div>
						<Skeleton className="h-48 w-full" />
					</div>
				)}

				{chainDetailsQuery.error && (
					<div className="rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-950/20">
						<p className="text-red-800 dark:text-red-200">
							Failed to load chain details: {String(chainDetailsQuery.error)}
						</p>
					</div>
				)}

				{chainData && (
					<div className="space-y-6">
						{/* Chain Overview Cards */}
						<div className="grid gap-4 md:grid-cols-3">
							<Card>
								<CardHeader className="pb-2">
									<CardTitle className="flex items-center gap-2 text-base">
										<Swords className="h-4 w-4" />
										Chain Summary
									</CardTitle>
								</CardHeader>
								<CardContent className="space-y-2">
									<div className="flex justify-between text-sm">
										<span>Total Hits:</span>
										<strong>{chainData.chainReport.details.chain}</strong>
									</div>
									<div className="flex justify-between text-sm">
										<span>Total Respect:</span>
										<strong>
											{chainData.chainReport.details.respect.toFixed(1)}
										</strong>
									</div>
									<div className="flex justify-between text-sm">
										<span>War Hits:</span>
										<strong>{chainData.chainReport.details.war}</strong>
									</div>
									<div className="flex justify-between text-sm">
										<span>Average/Hit:</span>
										<strong>
											{(
												chainData.chainReport.details.respect /
												chainData.chainReport.details.chain
											).toFixed(2)}
										</strong>
									</div>
								</CardContent>
							</Card>

							<Card>
								<CardHeader className="pb-2">
									<CardTitle className="flex items-center gap-2 text-base">
										<Users className="h-4 w-4" />
										Participation
									</CardTitle>
								</CardHeader>
								<CardContent className="space-y-2">
									<div className="flex justify-between text-sm">
										<span>Core Contributors:</span>
										<strong className="text-green-600">
											{
												chainData.chainMetrics.participationDepth
													.coreContributors
											}
										</strong>
									</div>
									<div className="flex justify-between text-sm">
										<span>Regular Contributors:</span>
										<strong className="text-blue-600">
											{
												chainData.chainMetrics.participationDepth
													.regularContributors
											}
										</strong>
									</div>
									<div className="flex justify-between text-sm">
										<span>Casual Contributors:</span>
										<strong className="text-gray-600">
											{
												chainData.chainMetrics.participationDepth
													.casualContributors
											}
										</strong>
									</div>
								</CardContent>
							</Card>

							<Card>
								<CardHeader className="pb-2">
									<CardTitle className="flex items-center gap-2 text-base">
										<Zap className="h-4 w-4" />
										Efficiency
									</CardTitle>
								</CardHeader>
								<CardContent className="space-y-2">
									<div className="flex justify-between text-sm">
										<span>Avg Time/Hit:</span>
										<strong>
											{formatDuration(
												chainData.chainMetrics.efficiencyMetrics
													.averageTimeBetweenHits,
											)}
										</strong>
									</div>
									<div className="flex justify-between text-sm">
										<span>Peak Hour:</span>
										<strong>
											Hour{" "}
											{
												chainData.chainMetrics.efficiencyMetrics.peakActivity
													.hour
											}
										</strong>
									</div>
									<div className="flex justify-between text-sm">
										<span>Respect/Hour:</span>
										<strong>
											{chainData.chainMetrics.efficiencyMetrics.respectPerHour.toFixed(
												1,
											)}
										</strong>
									</div>
								</CardContent>
							</Card>
						</div>

						<Tabs defaultValue="timeline" className="w-full">
							<TabsList className="grid w-full grid-cols-3">
								<TabsTrigger value="timeline">Timeline</TabsTrigger>
								<TabsTrigger value="attackers">Attackers</TabsTrigger>
								<TabsTrigger value="sequence">Attack Sequence</TabsTrigger>
							</TabsList>

							<TabsContent value="timeline" className="space-y-4">
								<Card>
									<CardHeader>
										<CardTitle className="flex items-center gap-2">
											<Clock className="h-5 w-5" />
											Hourly Breakdown
										</CardTitle>
									</CardHeader>
									<CardContent>
										{chainData.chainMetrics.timelineBreakdown &&
										chainData.chainMetrics.timelineBreakdown.length > 0 ? (
											<div className="space-y-3">
												{chainData.chainMetrics.timelineBreakdown.map(
													(hour) => (
														<div key={hour.hourBlock} className="space-y-2">
															<div className="flex items-center justify-between text-sm">
																<span>Hour {hour.hourBlock + 1}</span>
																<span className="font-medium">
																	{hour.attackCount} attacks •{" "}
																	{hour.respectEarned.toFixed(1)} respect
																</span>
															</div>
															<Progress
																value={
																	(hour.attackCount /
																		Math.max(
																			1, // Prevent division by zero
																			...chainData.chainMetrics.timelineBreakdown.map(
																				(h) => h.attackCount,
																			),
																		)) *
																	100
																}
																className="h-2"
															/>
															<div className="flex justify-between text-muted-foreground text-xs">
																<span>
																	{hour.uniqueAttackers} unique attackers
																</span>
																<span>
																	{hour.attackCount > 0
																		? (
																				hour.respectEarned / hour.attackCount
																			).toFixed(2)
																		: "0"}{" "}
																	avg respect
																</span>
															</div>
														</div>
													),
												)}
											</div>
										) : (
											<div className="py-8 text-center">
												<Clock className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
												<p className="text-muted-foreground">
													No timeline data available for this chain
												</p>
											</div>
										)}
									</CardContent>
								</Card>
							</TabsContent>

							<TabsContent value="attackers" className="space-y-4">
								<Card>
									<CardHeader>
										<CardTitle className="flex items-center gap-2">
											<Crown className="h-5 w-5" />
											Top Contributors
										</CardTitle>
									</CardHeader>
									<CardContent>
										{chainData.chainReport.attackers &&
										chainData.chainReport.attackers.length > 0 ? (
											<>
												{/* Desktop Table View */}
												<div className="hidden lg:block">
													<Table>
														<TableHeader>
															<TableRow>
																<TableHead>Player</TableHead>
																<TableHead>Total Hits</TableHead>
																<TableHead>War Hits</TableHead>
																<TableHead>Assists</TableHead>
																<TableHead>Total Respect</TableHead>
																<TableHead>Best Hit</TableHead>
															</TableRow>
														</TableHeader>
														<TableBody>
															{chainData.chainReport.attackers
																.sort(
																	(a: ChainAttacker, b: ChainAttacker) =>
																		b.respect.total - a.respect.total,
																)
																.map((attacker: ChainAttacker) => (
																	<TableRow key={attacker.id}>
																		<TableCell className="font-medium">
																			{attacker.name ||
																				`Player #${attacker.id}`}
																		</TableCell>
																		<TableCell>
																			{attacker.attacks.total}
																		</TableCell>
																		<TableCell>
																			{attacker.attacks.war}
																		</TableCell>
																		<TableCell>
																			{attacker.attacks.assists}
																		</TableCell>
																		<TableCell className="font-semibold">
																			{attacker.respect.total.toFixed(2)}
																		</TableCell>
																		<TableCell>
																			{attacker.respect.best.toFixed(2)}
																		</TableCell>
																	</TableRow>
																))}
														</TableBody>
													</Table>
												</div>

												{/* Mobile Card View */}
												<div className="space-y-3 lg:hidden">
													{chainData.chainReport.attackers
														.sort(
															(a: ChainAttacker, b: ChainAttacker) =>
																b.respect.total - a.respect.total,
														)
														.map((attacker: ChainAttacker) => (
															<Card key={attacker.id} className="p-4">
																<div className="space-y-3">
																	{/* Player Header */}
																	<div className="flex items-center justify-between">
																		<div className="min-w-0 flex-1">
																			<div className="truncate font-medium">
																				{attacker.name ||
																					`Player #${attacker.id}`}
																			</div>
																			<div className="text-muted-foreground text-sm">
																				{attacker.attacks.total} total hits •{" "}
																				{attacker.attacks.war} war hits
																			</div>
																		</div>
																		<div className="text-right">
																			<div className="font-semibold">
																				{attacker.respect.total.toFixed(2)}
																			</div>
																			<div className="text-muted-foreground text-sm">
																				total respect
																			</div>
																		</div>
																	</div>

																	{/* Stats Grid */}
																	<div className="grid grid-cols-2 gap-3 text-center">
																		<div>
																			<div className="text-muted-foreground text-sm">
																				Assists
																			</div>
																			<div className="font-medium">
																				{attacker.attacks.assists}
																			</div>
																		</div>
																		<div>
																			<div className="text-muted-foreground text-sm">
																				Best Hit
																			</div>
																			<div className="font-medium">
																				{attacker.respect.best.toFixed(2)}
																			</div>
																		</div>
																	</div>
																</div>
															</Card>
														))}
												</div>
											</>
										) : (
											<div className="py-8 text-center">
												<Users className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
												<p className="text-muted-foreground">
													No attacker data available
												</p>
											</div>
										)}
									</CardContent>
								</Card>
							</TabsContent>

							<TabsContent value="sequence" className="space-y-4">
								<Card>
									<CardHeader>
										<CardTitle className="flex items-center gap-2">
											<Target className="h-5 w-5" />
											Attack Sequence ({chainData.attackSequence.length}{" "}
											attacks)
										</CardTitle>
									</CardHeader>
									<CardContent>
										{chainData.attackSequence &&
										chainData.attackSequence.length > 0 ? (
											<div className="max-h-96 space-y-2 overflow-y-auto">
												{chainData.attackSequence.map((attack, index) => {
													// Calculate chain progress by counting attacks that advance the chain
													// Exclude only the results that don't advance the chain in Torn
													const chainProgress = chainData.attackSequence
														.slice(0, index + 1)
														.filter(
															(a) =>
																a.result !== "Assist" &&
																a.result !== "Lost" &&
																a.result !== "Escape" &&
																a.result !== "Interrupted" &&
																a.result !== "Defeated" &&
																a.result !== "Timeout" &&
																a.result !== "Stalemate",
														).length;

													return (
														<div key={attack.attackId} className="space-y-2">
															{/* Main Chain Attack */}
															<div className="flex items-start gap-3 rounded-lg border p-3">
																<div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 font-bold text-xs">
																	{attack.chainPosition}
																</div>
																<div className="flex-1 space-y-2">
																	<div className="space-y-1">
																		<div className="flex items-center gap-2 text-sm">
																			<span className="truncate font-medium">
																				{attack.attacker.name ||
																					`Player #${attack.attacker.id}`}
																			</span>
																			<span className="text-muted-foreground">
																				→
																			</span>
																			<span className="truncate">
																				{attack.defender.name ||
																					`Player #${attack.defender.id}`}
																			</span>
																		</div>
																		<div className="flex flex-wrap items-center gap-1">
																			{attack.isWarTarget && (
																				<Badge
																					variant="destructive"
																					className="text-xs"
																				>
																					War Target
																				</Badge>
																			)}
																			{attack.assists &&
																				attack.assists.length > 0 && (
																					<Badge
																						variant="secondary"
																						className="text-xs"
																					>
																						<span className="hidden sm:inline">
																							+{attack.assists.length} assist
																							{attack.assists.length !== 1
																								? "s"
																								: ""}
																						</span>
																						<span className="sm:hidden">
																							+{attack.assists.length}
																						</span>
																					</Badge>
																				)}
																		</div>
																	</div>
																	<div className="flex flex-wrap items-center gap-2 text-muted-foreground text-xs sm:gap-4">
																		<span>
																			{formatDateTime(attack.timestamp)}
																		</span>
																		<span>Result: {attack.result}</span>
																		<span>
																			Respect: {attack.respect.toFixed(2)}
																		</span>
																		<span>Chain: {chainProgress}</span>
																	</div>
																</div>
															</div>

															{/* Related Assists */}
															{attack.assists && attack.assists.length > 0 && (
																<div className="ml-11 space-y-1">
																	{attack.assists.map((assist) => (
																		<div
																			key={assist.attackId}
																			className="flex items-center gap-3 rounded-lg border border-dashed bg-muted/30 p-2 text-sm"
																		>
																			<div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 font-bold text-blue-700 text-xs dark:bg-blue-900/20 dark:text-blue-400">
																				A
																			</div>
																			<div className="flex-1 space-y-1">
																				<div className="flex items-center gap-2">
																					<span className="font-medium text-sm">
																						{assist.attacker.name ||
																							`Player #${assist.attacker.id}`}
																					</span>
																					<span className="text-muted-foreground text-xs">
																						assisted
																					</span>
																				</div>
																				<div className="flex items-center gap-4 text-muted-foreground text-xs">
																					<span>
																						{formatDateTime(assist.timestamp)}
																					</span>
																					<span>Result: {assist.result}</span>
																					<span>
																						Respect: {assist.respect.toFixed(2)}
																					</span>
																				</div>
																			</div>
																		</div>
																	))}
																</div>
															)}
														</div>
													);
												})}
											</div>
										) : (
											<div className="py-8 text-center">
												<Target className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
												<p className="text-muted-foreground">
													No attack sequence data available for this chain
												</p>
											</div>
										)}
									</CardContent>
								</Card>
							</TabsContent>
						</Tabs>
					</div>
				)}
			</DialogContent>
		</Dialog>
	);
}
