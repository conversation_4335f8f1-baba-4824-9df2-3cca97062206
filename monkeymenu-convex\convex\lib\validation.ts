import { ConvexError } from "convex/values";

// Validation helpers for banking operations
export function validateAmount(amount: number): void {
  if (typeof amount !== 'number' || amount <= 0) {
    throw new ConvexError("Amount must be a positive number");
  }
  
  if (!Number.isFinite(amount)) {
    throw new ConvexError("Amount must be a finite number");
  }
  
  if (amount > ************) { // 1 trillion limit
    throw new ConvexError("Amount exceeds maximum limit");
  }
  
  // Check for decimal precision (max 2 decimal places for cash)
  if (Math.round(amount * 100) !== amount * 100) {
    throw new ConvexError("Amount cannot have more than 2 decimal places");
  }
}

export function validateCurrency(currency: string): void {
  const allowedCurrencies = ['cash', 'points', 'tokens'];
  
  if (!allowedCurrencies.includes(currency)) {
    throw new ConvexError(`Invalid currency. Allowed currencies: ${allowedCurrencies.join(', ')}`);
  }
}

export function validateTransactionType(type: string): void {
  const allowedTypes = ['deposit', 'withdrawal', 'transfer', 'fee', 'withdrawal_cancelled'];
  
  if (!allowedTypes.includes(type)) {
    throw new ConvexError(`Invalid transaction type. Allowed types: ${allowedTypes.join(', ')}`);
  }
}

export function validateWithdrawalStatus(status: string): void {
  const allowedStatuses = ['PENDING', 'ACCEPTED', 'DECLINED', 'COMPLETED', 'CANCELLED'];
  
  if (!allowedStatuses.includes(status)) {
    throw new ConvexError(`Invalid withdrawal status. Allowed statuses: ${allowedStatuses.join(', ')}`);
  }
}

export function validateTransactionStatus(status: string): void {
  const allowedStatuses = ['pending', 'completed', 'failed', 'cancelled'];
  
  if (!allowedStatuses.includes(status)) {
    throw new ConvexError(`Invalid transaction status. Allowed statuses: ${allowedStatuses.join(', ')}`);
  }
}

export function sanitizeReference(reference?: string): string | undefined {
  if (!reference) return undefined;
  
  // Remove any potentially harmful characters and limit length
  return reference
    .replace(/[<>\"'&]/g, '') // Remove HTML/script injection characters
    .trim()
    .substring(0, 100); // Limit to 100 characters
}

export function validateDiscordId(discordId?: string): void {
  if (discordId && (!/^\d{17,19}$/.test(discordId))) {
    throw new ConvexError("Invalid Discord ID format");
  }
}

export function validateDiscordTag(discordTag?: string): void {
  if (discordTag && discordTag.length > 50) {
    throw new ConvexError("Discord tag too long");
  }
}