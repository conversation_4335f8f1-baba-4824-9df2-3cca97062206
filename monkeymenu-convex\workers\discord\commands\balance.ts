import { CommandContext, BaseSlashCreator } from "slash-create";
import { BaseCommand } from "./base";

export class BalanceCommand extends BaseCommand {
  constructor(creator: BaseSlashCreator, convexUrl: string) {
    super(creator, convexUrl, {
      name: "balance",
      description: "Check your account balances",
      options: [],
    });
  }

  async run(ctx: CommandContext) {
    const discordId = ctx.user.id;

    try {
      // Handle the balance command through Convex
      const result = await this.handleBotCommand(discordId, "balance") as any;

      if (result?.success) {
        if (result.embed) {
          return this.createEmbedResponse(result.embed);
        } else {
          return this.createSuccessResponse(result.message || "Balance retrieved successfully");
        }
      } else {
        return this.createErrorResponse(result?.message || "❌ Failed to retrieve balance");
      }
    } catch (error) {
      console.error("Balance command error:", error);
      return this.createErrorResponse("❌ An error occurred while checking your balance.");
    }
  }
}
